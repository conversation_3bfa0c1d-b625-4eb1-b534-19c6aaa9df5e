#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import fnmatch
import datetime

def should_ignore(path, ignore_patterns):
    """检查路径是否应该被忽略"""
    # 检查文件或目录名是否匹配忽略模式
    for pattern in ignore_patterns:
        if fnmatch.fnmatch(os.path.basename(path), pattern):
            return True
    
    # 检查路径是否包含忽略的目录
    for pattern in ignore_dirs:
        if pattern in path.split(os.sep):
            return True
    
    return False

def get_file_content(file_path):
    """获取文件内容，如果文件太大或是二进制文件则返回提示信息"""
    # 检查文件大小
    if os.path.getsize(file_path) > 1024 * 1024:  # 大于1MB的文件
        return "【文件过大，内容已省略】"
    
    # 尝试以文本方式读取文件
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            # 检查是否为二进制文件（包含过多不可打印字符）
            if '\0' in content or sum(c.isprintable() for c in content[:1000]) / max(1, len(content[:1000])) < 0.8:
                return "【二进制或不可读文件，内容已省略】"
            return content
    except Exception as e:
        return f"【读取文件出错: {str(e)}】"

def generate_codebase_content(root_dir, output_file):
    """递归遍历目录，生成代码库内容文件"""
    with open(output_file, 'w', encoding='utf-8') as out_file:
        # 写入文件头部信息
        out_file.write(f"# 代码库内容导出\n")
        out_file.write(f"# 生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        out_file.write(f"# 根目录: {os.path.abspath(root_dir)}\n\n")
        
        # 遍历目录
        for dirpath, dirnames, filenames in os.walk(root_dir):
            # 过滤掉要忽略的目录
            dirnames[:] = [d for d in dirnames if not should_ignore(os.path.join(dirpath, d), ignore_patterns)]
            
            # 处理当前目录中的文件
            for filename in filenames:
                file_path = os.path.join(dirpath, filename)
                
                # 检查是否应该忽略此文件
                if should_ignore(file_path, ignore_patterns):
                    continue
                
                # 获取相对路径
                rel_path = os.path.relpath(file_path, root_dir)
                
                # 写入文件路径和分隔符
                out_file.write(f"\n\n{'=' * 80}\n")
                out_file.write(f"文件: {rel_path}\n")
                out_file.write(f"{'=' * 80}\n\n")
                
                # 获取并写入文件内容
                content = get_file_content(file_path)
                out_file.write(content)

if __name__ == "__main__":
    # 当前脚本所在的目录作为根目录
    root_directory = os.path.dirname(os.path.abspath(__file__))
    output_filename = os.path.join(root_directory, "codebase_content.txt")
    
    # 定义要忽略的文件和目录模式
    ignore_patterns = [
        "*.pyc", "*.pyo", "*.pyd", "*.so", "*.dll", "*.class",  # 编译文件
        "*.exe", "*.bin", "*.dat", "*.db", "*.sqlite", "*.sqlite3",  # 二进制和数据库文件
        "*.log", "*.cache", "*.bak", "*.swp", "*.tmp",  # 日志和临时文件
        "*.zip", "*.tar", "*.gz", "*.rar", "*.7z",  # 压缩文件
        "*.jpg", "*.jpeg", "*.png", "*.gif", "*.bmp", "*.ico", "*.svg",  # 图片文件
        "*.mp3", "*.mp4", "*.avi", "*.mov", "*.wav",  # 媒体文件
        "*.pdf", "*.doc", "*.docx", "*.xls", "*.xlsx", "*.ppt", "*.pptx",  # 文档文件
        "package-lock.json", "yarn.lock", "pnpm-lock.yaml",  # 依赖锁文件
        ".DS_Store", "Thumbs.db",  # 系统文件
        "codebase_content.txt",  # 排除生成的输出文件
        "generate_codebase_content.py",  # 排除脚本自身
    ]
    
    # 定义要忽略的目录
    ignore_dirs = [
        "node_modules", ".git", ".github", ".vscode", ".idea",  # 开发工具和版本控制
        "dist", "build", "out", "target", "coverage",  # 构建和输出目录
        ".nuxt", ".output", ".cache", "__pycache__",  # 缓存目录
        "venv", "env", ".env", ".venv", "virtualenv",  # 虚拟环境
        "server", "prisma", "i18n","style" # 老的后端服务
    ]
    
    print(f"开始生成代码库内容文件...")
    generate_codebase_content(root_directory, output_filename)
    print(f"代码库内容已生成到: {output_filename}")
