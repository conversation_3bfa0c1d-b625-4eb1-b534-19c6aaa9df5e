import { defineStore } from 'pinia'

/**
 * 销项发票状态管理
 */
const useOutputInvoiceStore = defineStore('outputInvoice', {
  state: () => ({
    // 当前选中的发票
    selectedInvoice: null,
    // 当前选中的状态
    currentStatus: 'all',
    // 统计数据
    statistics: {
      all: 0,
      issued: 0,
      canceled: 0
    },
    // 销项发票模拟数据
    mockData: [
      {
        id: "10001",
        merchantName: "北京科技有限公司",
        invoiceTitle: "北京科技有限公司",
        invoiceTime: "2025-05-20 10:30:25",
        invoiceCode: "1234567890",
        orderTime: "2025-05-19 14:22:10",
        totalAmount: 1299.00,
        systemOrderNo: "ORD20250528001",
        thirdPartyOrderNo: "JD20250528001",
        invoiceStatus: "completed",
        invoiceType: "blue",
        invoiceMethod: "manual",
        relatedInvoiceId: "",
        isReconciled: true,
        reconciledAmount: 1299.00,
        totalReconciledAmount: 1299.00,
        taxAmount: 168.87,
        productCount: 2,
        reconciledTime: "2025-05-21 09:15:30",
        reconciledStatus: "full", // full: 全部勾兑, partial: 部分勾兑
        // 添加商品明细
        goodsItems: [
          {
            id: "G10001-1",
            name: "笔记本电脑",
            model: "X15-Pro",
            unit: "台",
            quantity: 1,
            price: 1150.44,
            amount: 1150.44,
            taxRate: 0.13,
            taxAmount: 149.56
          },
          {
            id: "G10001-2",
            name: "软件授权",
            model: "Office",
            unit: "套",
            quantity: 1,
            price: 148.56,
            amount: 148.56,
            taxRate: 0.13,
            taxAmount: 19.31
          }
        ]
      },
      {
        id: "10002",
        merchantName: "上海贸易有限公司",
        invoiceTitle: "上海贸易有限公司",
        invoiceTime: "2025-05-27 14:22:10",
        invoiceCode: "1234567891",
        orderTime: "2025-05-26 09:15:30",
        totalAmount: 2499.50,
        systemOrderNo: "ORD20250527002",
        thirdPartyOrderNo: "JD20250527002",
        invoiceStatus: "completed",
        invoiceType: "blue",
        invoiceMethod: "auto",
        relatedInvoiceId: "",
        isReconciled: true,
        reconciledAmount: 2499.50,
        totalReconciledAmount: 2499.50,
        taxAmount: 324.94,
        productCount: 2,
        reconciledTime: "2025-05-28 10:45:12",
        reconciledStatus: "full",
        // 添加商品明细
        goodsItems: [
          {
            id: "G10002-1",
            name: "服务器",
            model: "S2000",
            unit: "台",
            quantity: 1,
            price: 2212.83,
            amount: 2212.83,
            taxRate: 0.13,
            taxAmount: 287.67
          },
          {
            id: "G10002-2",
            name: "网络安装服务",
            model: "专业版",
            unit: "次",
            quantity: 1,
            price: 286.67,
            amount: 286.67,
            taxRate: 0.13,
            taxAmount: 37.27
          }
        ]
      },
      {
        id: "10003",
        merchantName: "广州电子科技有限公司",
        invoiceTitle: "广州电子科技有限公司",
        invoiceTime: "2025-05-26 09:15:30",
        invoiceCode: "1234567892",
        orderTime: "2025-05-25 16:40:18",
        totalAmount: 899.00,
        systemOrderNo: "ORD20250526003",
        thirdPartyOrderNo: "JD20250526003",
        invoiceStatus: "completed",
        invoiceType: "blue",
        invoiceMethod: "auto",
        relatedInvoiceId: "",
        isReconciled: false,
        reconciledAmount: 0,
        totalReconciledAmount: 899.00,
        taxAmount: 116.87,
        productCount: 1,
        reconciledTime: "",
        reconciledStatus: "none",
        // 添加商品明细
        goodsItems: [
          {
            id: "G10003-1",
            name: "打印机",
            model: "P500",
            unit: "台",
            quantity: 1,
            price: 796.46,
            amount: 796.46,
            taxRate: 0.13,
            taxAmount: 103.54
          },
          {
            id: "G10003-2",
            name: "墨盒",
            model: "I-255",
            unit: "个",
            quantity: 2,
            price: 51.27,
            amount: 102.54,
            taxRate: 0.13,
            taxAmount: 13.33
          }
        ]
      },
      {
        id: "10004",
        merchantName: "深圳数码科技有限公司",
        invoiceTitle: "深圳数码科技有限公司",
        invoiceTime: "2025-05-25 16:40:18",
        invoiceCode: "1234567893",
        orderTime: "2025-05-24 11:05:42",
        totalAmount: 3699.00,
        systemOrderNo: "ORD20250525004",
        thirdPartyOrderNo: "JD20250525004",
        invoiceStatus: "completed",
        invoiceType: "red",
        invoiceMethod: "manual",
        relatedInvoiceId: "10001,10002,10003",
        isReconciled: true,
        reconciledAmount: 2500.00,
        totalReconciledAmount: 3699.00,
        taxAmount: 428.87,
        productCount: 2,
        reconciledTime: "2025-05-26 14:30:20",
        reconciledStatus: "partial",
        // 添加商品明细
        goodsItems: [
          {
            id: "G10004-1",
            name: "办公桌",
            model: "D-200",
            unit: "张",
            quantity: 5,
            price: 459.86,
            amount: 2299.30,
            taxRate: 0.13,
            taxAmount: 298.91
          },
          {
            id: "G10004-2",
            name: "办公椅",
            model: "C-100",
            unit: "把",
            quantity: 5,
            price: 199.94,
            amount: 999.70,
            taxRate: 0.13,
            taxAmount: 129.96
          }
        ]
      },
      {
        id: "10005",
        merchantName: "杭州网络科技有限公司",
        invoiceTitle: "杭州网络科技有限公司",
        invoiceTime: "2025-05-24 11:05:42",
        invoiceCode: "1234567894",
        orderTime: "2025-05-23 08:30:15",
        totalAmount: 1599.00,
        systemOrderNo: "ORD20250524005",
        thirdPartyOrderNo: "JD20250524005",
        invoiceStatus: "cancelled",
        invoiceType: "void",
        invoiceMethod: "auto",
        relatedInvoiceId: "",
        isReconciled: false,
        reconciledAmount: 0,
        totalReconciledAmount: 1599.00,
        taxAmount: 207.87,
        productCount: 1,
        reconciledTime: "",
        reconciledStatus: "none",
        // 添加商品明细
        goodsItems: [
          {
            id: "G10005-1",
            name: "软件开发服务",
            model: "",
            unit: "项",
            quantity: 1,
            price: 1599.00,
            amount: 1599.00,
            taxRate: 0.13,
            taxAmount: 207.87
          }
        ]
      },
      {
        id: "10006",
        merchantName: "武汉科技有限公司",
        invoiceTitle: "武汉科技有限公司",
        invoiceTime: "2025-05-23 09:20:35",
        invoiceCode: "1234567895",
        orderTime: "2025-05-22 10:15:27",
        totalAmount: 2199.00,
        systemOrderNo: "ORD20250523006",
        thirdPartyOrderNo: "JD20250523006",
        invoiceStatus: "completed",
        invoiceType: "red",
        invoiceMethod: "manual",
        relatedInvoiceId: "10007,10002",
        isReconciled: true,
        reconciledAmount: 2199.00,
        totalReconciledAmount: 2199.00,
        taxAmount: 285.87,
        productCount: 1,
        reconciledTime: "2025-05-24 15:30:45",
        reconciledStatus: "full",
        // 添加商品明细
        goodsItems: [
          {
            id: "G10006-1",
            name: "办公设备",
            model: "OP-2000",
            unit: "套",
            quantity: 1,
            price: 2199.00,
            amount: 2199.00,
            taxRate: 0.13,
            taxAmount: 285.87
          }
        ]
      },
      {
        id: "10007",
        merchantName: "武汉科技有限公司",
        invoiceTitle: "武汉科技有限公司",
        invoiceTime: "2025-05-22 15:40:12",
        invoiceCode: "1234567896",
        orderTime: "2025-05-22 10:15:27",
        totalAmount: 2199.00,
        systemOrderNo: "ORD20250523006",
        thirdPartyOrderNo: "JD20250523006",
        invoiceStatus: "completed",
        invoiceType: "blue",
        invoiceMethod: "manual",
        relatedInvoiceId: "",
        isReconciled: false,
        reconciledAmount: 0,
        totalReconciledAmount: 2199.00,
        taxAmount: 285.87,
        productCount: 1,
        reconciledTime: "",
        reconciledStatus: "none",
        // 添加商品明细
        goodsItems: [
          {
            id: "G10007-1",
            name: "办公设备",
            model: "OP-2000",
            unit: "套",
            quantity: 1,
            price: 2199.00,
            amount: 2199.00,
            taxRate: 0.13,
            taxAmount: 285.87
          }
        ]
      }
    ]
  }),
  
  actions: {
    /**
     * 设置销项发票的选中发票
     * @param {Object} invoice 发票对象
     */
    setSelectedInvoice(invoice) {
      this.selectedInvoice = invoice
    },
    
    /**
     * 设置销项发票的当前状态
     * @param {String} status 状态值
     */
    setStatus(status) {
      this.currentStatus = status
    },
    
    /**
     * 更新销项发票统计数据
     * @param {Object} statistics 统计数据对象
     */
    updateStatistics(statistics) {
      this.statistics = {
        ...this.statistics,
        ...statistics
      }
    },
    
    /**
     * 获取销项发票模拟数据
     * @param {Object} params 查询参数
     * @returns {Object} 分页数据结果
     */
    getOutputInvoiceData(params = {}) {
      const { page = 1, limit = 10, ...filters } = params
      
      // 过滤数据
      let filteredData = [...this.mockData]
      
      // 根据商户名称过滤
      if (filters.merchantName) {
        filteredData = filteredData.filter(item => item.merchantName.includes(filters.merchantName))
      }
      
      // 根据发票抬头过滤
      if (filters.invoiceTitle) {
        filteredData = filteredData.filter(item => item.invoiceTitle.includes(filters.invoiceTitle))
      }
      
      // 根据发票代码过滤
      if (filters.invoiceCode) {
        filteredData = filteredData.filter(item => item.invoiceCode.includes(filters.invoiceCode))
      }
      
      // 根据系统订单号过滤
      if (filters.systemOrderNo) {
        filteredData = filteredData.filter(item => item.systemOrderNo.includes(filters.systemOrderNo))
      }
      
      // 根据第三方订单号过滤
      if (filters.thirdPartyOrderNo) {
        filteredData = filteredData.filter(item => item.thirdPartyOrderNo.includes(filters.thirdPartyOrderNo))
      }
      
      // 根据发票状态过滤
      if (filters.invoiceStatus) {
        filteredData = filteredData.filter(item => item.invoiceStatus === filters.invoiceStatus)
      }
      
      // 根据发票类型过滤
      if (filters.invoiceType) {
        filteredData = filteredData.filter(item => item.invoiceType === filters.invoiceType)
      }
      
      // 根据开票方式过滤
      if (filters.invoiceMethod) {
        filteredData = filteredData.filter(item => item.invoiceMethod === filters.invoiceMethod)
      }
      
      // 根据勾兑状态过滤
      if (filters.isReconciled !== undefined && filters.isReconciled !== '') {
        const isReconciled = filters.isReconciled === true || filters.isReconciled === 'true'
        filteredData = filteredData.filter(item => item.isReconciled === isReconciled)
      }
      
      // 根据开票时间范围过滤
      if (filters.startInvoiceTime && filters.endInvoiceTime) {
        filteredData = filteredData.filter(item => {
          const itemTime = new Date(item.invoiceTime).getTime()
          return itemTime >= filters.startInvoiceTime && itemTime <= filters.endInvoiceTime
        })
      }
      
      // 根据订单时间范围过滤
      if (filters.startOrderTime && filters.endOrderTime) {
        filteredData = filteredData.filter(item => {
          const itemTime = new Date(item.orderTime).getTime()
          return itemTime >= filters.startOrderTime && itemTime <= filters.endOrderTime
        })
      }
      
      // 计算统计数据
      const all = this.mockData.length
      const issued = this.mockData.filter(item => item.invoiceStatus === 'completed').length
      const canceled = this.mockData.filter(item => item.invoiceStatus === 'cancelled').length
      
      // 更新统计数据
      this.updateStatistics({ all, issued, canceled })
      
      // 分页处理
      const startIndex = (page - 1) * limit
      const endIndex = startIndex + limit
      const paginatedData = filteredData.slice(startIndex, endIndex)
      
      return {
        code: 0,
        message: "success",
        data: {
          items: paginatedData,
          total: filteredData.length,
          page: params.page || 1,
          pageSize: params.pageSize || 10
        }
      }
    },
    
    /**
     * 获取发票详情
     * @param {String} id 发票ID
     * @returns {Object|null} 发票详情对象
     */
    getInvoiceDetail(id) {
      return this.mockData.find(item => item.id === id) || null
    },
    
    /**
     * 更新发票状态
     * @param {String} id 发票ID
     * @param {String} status 新状态
     * @returns {Boolean} 是否更新成功
     */
    updateInvoiceStatus(id, status) {
      const index = this.mockData.findIndex(item => item.id === id)
      if (index === -1) return false
      
      this.mockData[index] = {
        ...this.mockData[index],
        invoiceStatus: status,
        updateTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
      }
      
      return true
    },
    
    /**
     * 更新发票勾兑状态
     * @param {String} id 发票ID
     * @param {Number} reconciledAmount 勾兑金额
     * @returns {Boolean} 是否更新成功
     */
    updateReconcileStatus(id, reconciledAmount) {
      const index = this.mockData.findIndex(item => item.id === id)
      if (index === -1) return false
      
      const currentReconciledAmount = this.mockData[index].reconciledAmount || 0
      const totalAmount = this.mockData[index].totalAmount
      const newReconciledAmount = currentReconciledAmount + reconciledAmount
      
      this.mockData[index] = {
        ...this.mockData[index],
        isReconciled: true,
        reconciledAmount: newReconciledAmount,
        reconciledTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
        reconciledStatus: newReconciledAmount >= totalAmount ? 'full' : 'partial'
      }
      
      return true
    }
  }
})

export default useOutputInvoiceStore
