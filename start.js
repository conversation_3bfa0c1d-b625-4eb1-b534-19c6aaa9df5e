/**
 * 启动脚本 - 同时启动前端和服务端
 * 前端端口: 3000
 * 服务端端口: 4000
 */

import { spawn } from 'child_process';
import { dirname, resolve } from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录
const __dirname = dirname(fileURLToPath(import.meta.url));

// 定义颜色代码
const colors = {
  frontend: '\x1b[36m', // 青色
  backend: '\x1b[32m',  // 绿色
  error: '\x1b[31m',    // 红色
  reset: '\x1b[0m'      // 重置颜色
};

// 日志前缀
const logPrefix = {
  frontend: `${colors.frontend}[前端]\x1b[0m`,
  backend: `${colors.backend}[服务端]\x1b[0m`
};

// 启动前端服务 (Nuxt)
function startFrontend() {
  console.log(`${logPrefix.frontend} 正在启动前端服务，端口: 3000...`);
  
  // 使用 node .output/server/index.mjs 启动 Nuxt 生产服务
  const frontend = spawn('node', ['.output/server/index.mjs'], {
    cwd: __dirname,
    env: {
      ...process.env,
      PORT: 3000,
      HOST: '0.0.0.0'
    }
  });

  frontend.stdout.on('data', (data) => {
    console.log(`${logPrefix.frontend} ${data.toString().trim()}`);
  });

  frontend.stderr.on('data', (data) => {
    console.error(`${logPrefix.frontend} ${colors.error}错误:${colors.reset} ${data.toString().trim()}`);
  });

  frontend.on('close', (code) => {
    if (code !== 0) {
      console.error(`${logPrefix.frontend} ${colors.error}前端服务已退出，退出码: ${code}${colors.reset}`);
    } else {
      console.log(`${logPrefix.frontend} 前端服务已正常退出`);
    }
  });

  return frontend;
}

// 启动后端服务 (Express)
function startBackend() {
  console.log(`${logPrefix.backend} 正在启动服务端，端口: 4000...`);
  
  // 启动服务端
  const backend = spawn('node', ['index.js'], {
    cwd: resolve(__dirname, 'server'),
    env: {
      ...process.env,
      PORT: 4000
    }
  });

  backend.stdout.on('data', (data) => {
    console.log(`${logPrefix.backend} ${data.toString().trim()}`);
  });

  backend.stderr.on('data', (data) => {
    console.error(`${logPrefix.backend} ${colors.error}错误:${colors.reset} ${data.toString().trim()}`);
  });

  backend.on('close', (code) => {
    if (code !== 0) {
      console.error(`${logPrefix.backend} ${colors.error}服务端已退出，退出码: ${code}${colors.reset}`);
    } else {
      console.log(`${logPrefix.backend} 服务端已正常退出`);
    }
  });

  return backend;
}

// 处理进程退出
function handleProcessExit(frontendProcess, backendProcess) {
  process.on('SIGINT', () => {
    console.log('\n正在关闭所有服务...');
    
    // 关闭前端进程
    if (frontendProcess && !frontendProcess.killed) {
      frontendProcess.kill('SIGINT');
    }
    
    // 关闭后端进程
    if (backendProcess && !backendProcess.killed) {
      backendProcess.kill('SIGINT');
    }
    
    // 给进程一些时间来清理
    setTimeout(() => {
      console.log('所有服务已关闭');
      process.exit(0);
    }, 1000);
  });
}

// 主函数
function main() {
  console.log('正在启动聚灵云4.0应用...');
  
  // 启动前端和后端服务
  const frontendProcess = startFrontend();
  const backendProcess = startBackend();
  
  // 处理进程退出
  handleProcessExit(frontendProcess, backendProcess);
  
  console.log('\n应用启动中，请等待服务就绪...');
  console.log(`前端服务将在: http://localhost:3000`);
  console.log(`服务端将在: http://localhost:4000`);
}

// 执行主函数
main();
