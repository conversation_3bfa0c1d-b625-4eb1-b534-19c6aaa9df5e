import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Pie<PERSON>hart, RadarChart, <PERSON>auge<PERSON>hart } from 'echarts/charts'
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  DataZoomComponent,
  GraphicComponent,
} from 'echarts/components'

import MaCrud from '~/components/base/ma-crud/index.vue'
// import MaForm from '~/components/base/ma-form/index.vue'
// import MaChart from '~/components/base/ma-charts/index.vue'
// import MaUpload from '~/components/base/ma-upload/index.vue'
// import MaTreeSlider from '~/components/base/ma-treeSlider/index.vue'
// import MaResource from '~/components/base/ma-resource/index.vue'
// import MaResourceButton from '~/components/base/ma-resource/button.vue'
// import MaUser from '~/components/base/ma-user/index.vue'
// import MaEditor from '~/components/base/ma-editor/index.vue'
// import MaWangEditor from '~/components/base/ma-wangEditor/index.vue'
// import MaIcon from '~/components/base/ma-icon/index.vue'
// import MaCodeEditor from '~/components/base/ma-codeEditor/index.vue'
// import MaUserInfo from '~/components/base/ma-userInfo/index.vue'
// import MaCityLinkage from '~/components/base/ma-cityLinkage/index.vue'


export default defineNuxtPlugin((app) => {
  use([
    CanvasRenderer,
    BarChart,
    LineChart,
    PieChart,
    RadarChart,
    GaugeChart,
    GridComponent,
    TooltipComponent,
    LegendComponent,
    DataZoomComponent,
    GraphicComponent,
  ]);
  app.vueApp.component('MaCrud', MaCrud)
    // .component('MaChart', MaChart)
    // .component('MaForm', MaForm)
    // .component('MaUpload', MaUpload)
    // .component('MaTreeSlider', MaTreeSlider)
    // .component('MaResource', MaResource)
    // .component('MaResourceButton', MaResourceButton)
    // .component('MaUser', MaUser)
    // .component('MaEditor', MaEditor)
    // .component('MaWangEditor', MaWangEditor)
    // .component('MaIcon', MaIcon)
    // .component('MaCodeEditor', MaCodeEditor)
    // .component('MaUserInfo', MaUserInfo)
    // .component('MaCityLinkage', MaCityLinkage)
})
