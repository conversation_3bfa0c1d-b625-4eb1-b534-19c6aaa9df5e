/**
 * API配置插件
 * 确保前端代码正确使用API服务器地址
 */
import { baseUrl, apiPrefix } from '~/api/config'

export default defineNuxtPlugin((nuxtApp) => {
  const config = useRuntimeConfig()
  
  // 获取API基础URL，优先使用运行时配置
  const apiBaseUrl = config.public.VITE_API_BASE_URL || baseUrl
  
  // 提供给全局使用的API配置
  nuxtApp.provide('apiConfig', {
    baseUrl: apiBaseUrl,
    apiPrefix: apiPrefix,
    fullBaseUrl: `${apiBaseUrl}${apiPrefix}`
  })
  
  console.log('API配置已加载:', `${apiBaseUrl}${apiPrefix}`)
})
