import tool from '@/utils/tool'
import * as common from '@/utils/common'
import { request } from '@/utils/request'

export default defineNuxtPlugin((app) => {
    window.Request = request
    app.vueApp.config.globalProperties.$tool = tool
    app.vueApp.config.globalProperties.$common = common
    app.vueApp.config.globalProperties.$title = import.meta.env.VITE_APP_TITLE
    app.vueApp.config.globalProperties.$url = import.meta.env.VITE_APP_BASE
})