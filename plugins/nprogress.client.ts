// ~/plugins/nprogress.client.ts
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.hooks.hook('page:start', () => {
    NProgress.start()
  })

  nuxtApp.hooks.hook('page:finish', () => {
    NProgress.done()
  })

  nuxtApp.hooks.hook('app:error', () => {
    NProgress.done()
  })

  nuxtApp.hooks.hook('vue:error', (err) => {
    console.error('路由错误:', err)
    NProgress.done()
  })
})
