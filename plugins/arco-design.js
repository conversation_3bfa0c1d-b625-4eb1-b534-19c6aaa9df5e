// 按需引入 Arco Design Vue 组件库 - 开发环境优化
import ArcoVue from '@arco-design/web-vue';
// 引入 Arco Design 的样式
import '@arco-design/web-vue/dist/arco.css';
// 可选：引入图标库
import ArcoVueIcon from '@arco-design/web-vue/es/icon';

// Nuxt 3 插件格式
export default defineNuxtPlugin({
  name: 'arco-design',
  parallel: true, // 并行加载以提高性能
  setup(nuxtApp) {
    // 注册 Arco Design Vue
    nuxtApp.vueApp.use(ArcoVue);
    // 注册图标库
    nuxtApp.vueApp.use(ArcoVueIcon);
  }
});
