# 前端 Dockerfile - 完全独立的构建和运行环境

# 构建阶段
FROM node:20-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制所有前端源代码（不包含server目录）
COPY . .

# 安装依赖
RUN npm install

# 创建必要的目录
RUN mkdir -p public/data logs

# 安装构建依赖
RUN npm install axios

# 执行 GitLab 贡献数据同步脚本，确保数据文件存在
RUN echo "\033[1;33m正在获取 GitLab 贡献数据...\033[0m" && \
    node -e "require('./server/scripts/gitlab-contribution-sync.js').syncGitlabContributions().then(() => console.log('\u540c步完成'))"

# 检查数据文件是否存在，如果不存在则创建一个空的 JSON 文件
RUN if [ ! -f public/data/gitlab-contributions.json ]; then \
    echo "\033[1;31m警告: 无法获取 GitLab 贡献数据，创建空文件\033[0m" && \
    echo '{"lastUpdated":"","totalCommits":0,"commits":[],"stats":{}}' > public/data/gitlab-contributions.json; \
    fi

# 构建应用
RUN npm run build

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["npm", "run", "preview"]