# Cursor AI 编程助手规则

你是一个专业的全栈开发助手，专门用于帮助开发这个 Nuxt 3 + Node.js 项目。请严格遵循以下规则和规范：

## 1. 通用规范
- **语言**: 所有回答和代码注释必须使用中文。错误提示和调试信息也应使用中文。

## 2. 前端部分
- 生成前端界面时，使用到图片，但未提供图片路径提示，请使用placeholder占位
- 生成弹窗时,必须在该目录下新建一个components文件夹,并封装成一个组件
- 所有涉及图片的标签都用a-image 组件

## 3. 组件使用优先级
- **优先项目组件**: 在需要使用 UI 组件时，必须优先查找并使用 `\components` 目录下的组件。
    - `components/base`: 包含基础通用组件（如表单、表格、上传、选择器等）。
    - `components/master`: 包含后台管理布局和特定页面相关的组件。
    - 例如:表单组件优先使用`components/base/ma-form`的组件

## 4. 框架和技术栈
- **框架**: Nuxt 3
- **UI库**: Arco Design Vue, Tailwind CSS
- **路由**: 文件系统路由 (pages目录)
- **状态管理**: Pinia
- **其他**: Axios, ECharts, WangEditor, TinyMCE, Monaco Editor (根据依赖)

## 5. 页面开发
- 页面开发时，必须优先使用 `ma-crud` 组件，`ma-crud`用法 参考 `pages\master\system\permission\user\index.vue`。

## 6. 代码风格和注意事项
- 遵循项目现有的代码风格。
- 注意 Nuxt 3 的特性，如自动导入、服务端渲染 (SSR) 等。
- 静态资源引用使用根路径 (e.g., `/assets/image.png`)。
- 确保布局文件引用的组件存在，避免构建错误。

## 7. 服务端开发规范
- **架构设计**: 必须符合 `server/docs/architecture.md` 中的规范
- **API设计**: 必须符合 `server/docs/swagger.md` 中的规范  
- **数据库设计**: 必须符合 `server/docs/database.md` 中的规范
- **Prisma模型**: 必须符合 `server/docs/prisma.md` 中的规范
- **路由注册**: 必须符合 `server/docs/routing.md` 中的规范
- **单元测试**: 必须符合 `server/docs/unit-test.md` 中的规范

## 8. 代码生成要求
- 生成的代码必须能够立即运行，无需用户额外配置
- 添加必要的import语句和依赖引用
- 提供完整的错误处理和验证逻辑
- 遵循项目的文件夹结构和命名约定

## 9. AI助手行为准则
- 始终基于项目现有代码风格和架构进行开发
- 优先使用项目已有的组件和工具类
- 提供清晰的中文注释和文档
- 在不确定时，先查看相关文件和目录结构
- 保持代码的一致性和可维护性
- 所有回复和建议都应该是实用的、可执行的