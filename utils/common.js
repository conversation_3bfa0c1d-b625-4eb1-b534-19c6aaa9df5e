import checkAuth from "@/directives/auth/auth";
import checkRole from "@/directives/role/role";
import useClipboard from "vue-clipboard3";
import { Notification, Message } from "@arco-design/web-vue";
import { nextTick } from "vue";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { useTagStore, useKeepAliveStore, useUserStore } from "@/store";
import tool from "@/utils/tool";

export const refreshTag = (router) => {
  const route = router.currentRoute.value;
  const keepStore = useKeepAliveStore();
  NProgress.start();
  keepStore.removeKeepAlive(route);
  keepStore.hidden();
  nextTick(() => {
    keepStore.addKeepAlive(route);
    keepStore.display();
    NProgress.done();
  });
};

export const addTag = (tag) => {
  const tagStore = useTagStore();
  const keepStore = useKeepAliveStore();
  tagStore.addTag(tag);
  keepStore.addKeepAlive(tag);
};

export const closeTag = (tag, router) => {
  const tagStore = useTagStore();
  const keepStore = useKeepAliveStore();
  const t = tagStore.removeTag(tag);
  keepStore.removeKeepAlive(tag);
  router.push({ path: t.path, query: tool.getRequestParams(t.path) });
};

/**
 * 关闭当前标签页并跳转到指定页面
 * @param {object} router - 路由实例
 * @param {string} targetPath - 目标跳转路径
 * @param {object} targetQuery - 目标页面的查询参数，可选
 * @description 关闭当前活动的标签页，然后跳转到指定的页面
 */
export const closeCurrentTagAndNavigate = (
  router,
  targetPath,
  targetQuery = {}
) => {
  const tagStore = useTagStore();
  const keepStore = useKeepAliveStore();
  const currentRoute = router.currentRoute.value;

  // 构建当前标签页对象
  const currentTag = {
    name: currentRoute.name,
    path: currentRoute.path,
    title: currentRoute.meta?.title || currentRoute.name,
    affix: currentRoute.meta?.affix || false,
  };

  // 关闭当前标签页
  tagStore.removeTag(currentTag);
  keepStore.removeKeepAlive(currentTag);

  // 跳转到指定页面
  router.push({ path: targetPath, query: targetQuery });
};

export const modifyTag = (tag, newTitle) => {
  const tagStore = useTagStore();
  tagStore.updateTagTitle(tag.path, newTitle);
};

/**
 * 带标签的路由跳转函数
 * @param {object} router - 路由实例
 * @param {string} path - 目标路径
 * @param {object} query - 查询参数，可选
 * @description 自动从路由定义中获取标题，并添加到标签中
 */
export const navigateWithTag = (router, path, query = {}) => {
  // 先跳转到目标页面
  router.push({ path, query });

  // 获取路由定义中的元数据
  const userStore = useUserStore();
  const findRouteByPath = (routers, targetPath) => {
    if (!routers || !Array.isArray(routers)) return null;

    for (const route of routers) {
      // 精确匹配路径
      if (route.path === targetPath) {
        return route;
      }

      // 处理动态路由匹配
      // 例如：/master/order/orderManage/detail/:id 应该匹配 /master/order/orderManage/detail/123
      if (route.path && route.path.includes(":")) {
        const routePathPattern = route.path.replace(/\/:[^/]+/g, "/[^/]+");
        const regExp = new RegExp(
          "^" + routePathPattern.replace(/\//g, "\\/") + "$"
        );
        if (regExp.test(targetPath)) {
          return route;
        }
      }

      // 递归检查子路由
      if (route.children && route.children.length > 0) {
        const found = findRouteByPath(route.children, targetPath);
        if (found) return found;
      }
    }
    return null;
  };

  // 在所有路由中查找目标路径
  const route = findRouteByPath(userStore.routers, path);

  // 添加调试信息
  console.log("navigateWithTag - 目标路径:", path);
  console.log("navigateWithTag - 找到的路由:", route);

  if (route) {
    // 从路由定义中获取名称和标题
    addTag({
      name: route.name,
      path:
        path +
        (Object.keys(query).length > 0
          ? "?" + new URLSearchParams(query).toString()
          : ""),
      title: route.meta?.title || path.split("/").pop(),
      affix: route.meta?.affix || false,
    });
  } else {
    // 如果没有找到路由定义，使用默认值
    console.log("navigateWithTag - 未找到路由定义，使用默认值");

    // 从路径中提取名称和标题
    const pathParts = path.split("/");
    const lastPart = pathParts.pop() || "";
    const secondLastPart = pathParts.pop() || "";

    // 生成一个合理的名称和标题
    const name = secondLastPart + "-" + lastPart;
    const title =
      lastPart.charAt(0).toUpperCase() +
      lastPart
        .slice(1)
        .replace(/([A-Z])/g, " $1")
        .trim();

    addTag({
      name: name,
      path:
        path +
        (Object.keys(query).length > 0
          ? "?" + new URLSearchParams(query).toString()
          : ""),
      title: title,
      affix: false,
    });
  }
};

export const success = (title, content) => {
  Notification.success({ title, content, closable: true });
};

export const info = (title, content) => {
  Notification.info({ title, content, closable: true });
};

export const error = (title, content) => {
  Notification.error({ title, content, closable: true });
};

// 检查权限函数
export const auth = (name) => checkAuth(name);

// 检查角色函数
export const role = (name) => checkRole(name);

// 复制
export const copy = (text) => {
  try {
    useClipboard().toClipboard(text);
    Message.success("已成功复制到剪切板");
  } catch (e) {
    Message.error("复制失败");
  }
};

function transitionJsonToString(jsonObj, callback) {
  // 转换后的jsonObj受体对象
  var _jsonObj = null;
  // 判断传入的jsonObj对象是不是字符串，如果是字符串需要先转换为对象，再转换为字符串，这样做是为了保证转换后的字符串为双引号
  if (Object.prototype.toString.call(jsonObj) !== "[object String]") {
    try {
      _jsonObj = JSON.stringify(jsonObj);
    } catch (error) {
      // 转换失败错误信息
      console.error("您传递的json数据格式有误，请核对...");
      console.error(error);
      callback(error);
    }
  } else {
    try {
      jsonObj = jsonObj.replace(/(\')/g, '"');
      _jsonObj = JSON.stringify(JSON.parse(jsonObj));
    } catch (error) {
      // 转换失败错误信息
      console.error("您传递的json数据格式有误，请核对....");
      console.error(error);
      // callback(error);
    }
  }
  return _jsonObj;
}

export const formatJson = (jsonObj, callback) => {
  // 正则表达式匹配规则变量
  var reg = null;
  // 转换后的字符串变量
  var formatted = "";
  // 换行缩进位数
  var pad = 0;
  // 一个tab对应空格位数
  var PADDING = "  ";
  // json对象转换为字符串变量
  var jsonString = transitionJsonToString(jsonObj, callback);
  if (!jsonString) {
    return jsonString;
  }
  // 存储需要特殊处理的字符串段
  var _index = [];
  // 存储需要特殊处理的“再数组中的开始位置变量索引
  var _indexStart = null;
  // 存储需要特殊处理的“再数组中的结束位置变量索引
  var _indexEnd = null;
  // 将jsonString字符串内容通过\r\n符分割成数组
  var jsonArray = [];
  // 正则匹配到{,}符号则在两边添加回车换行
  jsonString = jsonString.replace(/([\{\}])/g, "\r\n$1\r\n");
  // 正则匹配到[,]符号则在两边添加回车换行
  jsonString = jsonString.replace(/([\[\]])/g, "\r\n$1\r\n");
  // 正则匹配到,符号则在两边添加回车换行
  jsonString = jsonString.replace(/(\,)/g, "$1\r\n");
  // 正则匹配到要超过一行的换行需要改为一行
  jsonString = jsonString.replace(/(\r\n\r\n)/g, "\r\n");
  // 正则匹配到单独处于一行的,符号时需要去掉换行，将,置于同行
  jsonString = jsonString.replace(/\r\n\,/g, ",");
  // 特殊处理双引号中的内容
  jsonArray = jsonString.split("\r\n");
  jsonArray.forEach(function (node, index) {
    // 获取当前字符串段中"的数量
    var num = node.match(/\"/g) ? node.match(/\"/g).length : 0;
    // 判断num是否为奇数来确定是否需要特殊处理
    if (num % 2 && !_indexStart) {
      _indexStart = index;
    }
    if (num % 2 && _indexStart && _indexStart != index) {
      _indexEnd = index;
    }
    // 将需要特殊处理的字符串段的其实位置和结束位置信息存入，并对应重置开始时和结束变量
    if (_indexStart && _indexEnd) {
      _index.push({
        start: _indexStart,
        end: _indexEnd,
      });
      _indexStart = null;
      _indexEnd = null;
    }
  });
  // 开始处理双引号中的内容，将多余的"去除
  _index.reverse().forEach(function (item, index) {
    var newArray = jsonArray.slice(item.start, item.end + 1);
    jsonArray.splice(item.start, item.end + 1 - item.start, newArray.join(""));
  });
  // 奖处理后的数组通过\r\n连接符重组为字符串
  jsonString = jsonArray.join("\r\n");
  // 将匹配到:后为回车换行加大括号替换为冒号加大括号
  jsonString = jsonString.replace(/\:\r\n\{/g, ":{");
  // 将匹配到:后为回车换行加中括号替换为冒号加中括号
  jsonString = jsonString.replace(/\:\r\n\[/g, ":[");
  // 将上述转换后的字符串再次以\r\n分割成数组
  jsonArray = jsonString.split("\r\n");
  // 将转换完成的字符串根据PADDING值来组合成最终的形态
  jsonArray.forEach(function (item, index) {
    // console.log(item);
    var i = 0;
    // 表示缩进的位数，以tab作为计数单位
    var indent = 0;
    // 表示缩进的位数，以空格作为计数单位
    var padding = "";
    if (item.match(/\{$/) || item.match(/\[$/)) {
      // 匹配到以{和[结尾的时候indent加1
      indent += 1;
    } else if (
      item.match(/\}$/) ||
      item.match(/\]$/) ||
      item.match(/\},$/) ||
      item.match(/\],$/)
    ) {
      // 匹配到以}和]结尾的时候indent减1
      if (pad !== 0) {
        pad -= 1;
      }
    } else {
      indent = 0;
    }
    for (i = 0; i < pad; i++) {
      padding += PADDING;
    }
    formatted += padding + item + "\n";
    pad += indent;
  });
  // 返回的数据需要去除两边的空格
  return formatted.trim();
};

// 判断是否弹出层全屏
export const setModalSizeEvent = (callback) => {
  callback({ isFull: window.screen.width < 768, width: window.screen.width });
};
// 加载远程js
export const loadScript = (src, callback) => {
  const s = document.createElement("script");
  s.type = "text/javascript";
  s.src = src;
  s.onload = s.onreadystatechange = function () {
    if (
      !this.readyState ||
      this.readyState === "loaded" ||
      this.readyState === "complete"
    ) {
      callback && callback();
      s.onload = s.onreadystatechange = null;
    }
  };
  document.body.appendChild(s);
};

// 加载远程css
export const loadCss = (href, callback) => {
  const s = document.createElement("link");
  s.type = "text/css";
  s.rel = "stylesheet";
  s.media = "all";
  s.href = href;
  s.onload = s.onreadystatechange = function () {
    if (
      !this.readyState ||
      this.readyState === "loaded" ||
      this.readyState === "complete"
    ) {
      callback && callback();
      s.onload = s.onreadystatechange = null;
    }
  };
  document.body.appendChild(s);
};

export const discount = (discount, price) => {
  return (
    (price * (discount === "0.00" || discount === 0 ? 10 : discount)) /
    10
  ).toFixed(2);
};

export const versionCompare = (v1, v2) => {
  // 将版本号转换成数字数组
  v1 = v1.split(".");
  v2 = v2.split(".");
  // 对齐版本号的长度
  while (v1.length < v2.length) v1.push("0");
  while (v2.length < v1.length) v2.push("0");
  // 转换成数字数组
  v1 = v1.map(Number);
  v2 = v2.map(Number);

  for (let i = 0; i < v1.length; i++) {
    if (v1[i] < v2[i]) return -1; // v1 < v2
    if (v1[i] > v2[i]) return 1; // v1 > v2
  }
  return 0; // v1 == v2
};
