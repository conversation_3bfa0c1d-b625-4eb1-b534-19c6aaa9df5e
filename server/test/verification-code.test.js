/**
 * 验证码验证功能测试
 */
const VerificationCodeService = require('../apps/master/system/integration/services/VerificationCodeService');
const RedisUtil = require('../core/utils/RedisUtil');

// 初始化 Redis
async function initRedis() {
  try {
    await RedisUtil.init();
    console.log('Redis 初始化成功');
    return true;
  } catch (error) {
    console.error('Redis 初始化失败:', error);
    return false;
  }
}

// 测试验证码存储和验证
async function testVerificationCode() {
  console.log('===== 开始测试验证码功能 =====');
  
  // 初始化 Redis
  const redisInitialized = await initRedis();
  if (!redisInitialized) {
    console.error('Redis 初始化失败，测试终止');
    return;
  }
  
  // 测试数据
  const phoneNumber = '15626222177';
  const testCode = '941397';
  const type = 'register';
  
  try {
    // 1. 测试 Redis 连接
    console.log('\n1. 测试 Redis 连接');
    const testResult = await RedisUtil.testConnection();
    console.log('Redis 连接测试结果:', testResult ? '成功' : '失败');
    
    if (!testResult) {
      console.error('Redis 连接测试失败，测试终止');
      return;
    }
    
    // 2. 直接在 Redis 中设置验证码
    console.log('\n2. 直接在 Redis 中设置验证码');
    const codeKey = `verification_code:${type}:${phoneNumber}`;
    await RedisUtil.getClient().set(codeKey, testCode, { EX: 300 });
    console.log(`已在 Redis 中设置键 ${codeKey}，值为 ${testCode}`);
    
    // 3. 从 Redis 中读取验证码
    console.log('\n3. 从 Redis 中读取验证码');
    const savedCode = await RedisUtil.getClient().get(codeKey);
    console.log(`从 Redis 中读取的验证码: ${savedCode}`);
    console.log(`类型: ${typeof savedCode}, 长度: ${savedCode ? savedCode.length : 0}`);
    
    // 4. 手动比较验证码
    console.log('\n4. 手动比较验证码');
    console.log(`输入验证码: ${testCode}, 类型: ${typeof testCode}, 长度: ${testCode.length}`);
    console.log(`存储验证码: ${savedCode}, 类型: ${typeof savedCode}, 长度: ${savedCode.length}`);
    
    const isEqual = testCode === savedCode;
    console.log(`直接比较结果: ${isEqual ? '相等' : '不相等'}`);
    
    const normalizedTestCode = String(testCode).trim();
    const normalizedSavedCode = String(savedCode).trim();
    
    console.log(`规范化后的输入验证码: ${normalizedTestCode}, 长度: ${normalizedTestCode.length}`);
    console.log(`规范化后的存储验证码: ${normalizedSavedCode}, 长度: ${normalizedSavedCode.length}`);
    
    const isNormalizedEqual = normalizedTestCode === normalizedSavedCode;
    console.log(`规范化后比较结果: ${isNormalizedEqual ? '相等' : '不相等'}`);
    
    // 5. 使用验证码服务验证
    console.log('\n5. 使用验证码服务验证');
    const verificationCodeService = new VerificationCodeService();
    
    try {
      const verifyResult = await verificationCodeService.verifyCode(phoneNumber, testCode, type);
      console.log(`验证码服务验证结果: ${verifyResult ? '成功' : '失败'}`);
    } catch (error) {
      console.error('验证码服务验证失败:', error.message);
      
      // 6. 字符编码检查
      console.log('\n6. 字符编码检查');
      console.log('输入验证码的字符编码:');
      for (let i = 0; i < testCode.length; i++) {
        console.log(`  字符 '${testCode[i]}' 的编码: ${testCode.charCodeAt(i)}`);
      }
      
      console.log('存储验证码的字符编码:');
      for (let i = 0; i < savedCode.length; i++) {
        console.log(`  字符 '${savedCode[i]}' 的编码: ${savedCode.charCodeAt(i)}`);
      }
    }
    
    // 7. 清理测试数据
    console.log('\n7. 清理测试数据');
    await RedisUtil.getClient().del(codeKey);
    console.log(`已删除测试键 ${codeKey}`);
    
  } catch (error) {
    console.error('测试过程中发生错误:', error);
  } finally {
    // 关闭 Redis 连接
    await RedisUtil.disconnect();
    console.log('\nRedis 连接已关闭');
    console.log('===== 测试结束 =====');
  }
}

// 执行测试
testVerificationCode().catch(console.error);
