/**
 * IP地址工具
 */
const axios = require('axios');

/**
 * 根据IP获取地理位置
 * @param {String} ip IP地址
 * @returns {Promise<String>} 地理位置
 */
async function getLocationByIp(ip) {
  try {
    // 如果是本地IP，直接返回
    if (ip === '127.0.0.1' || ip === 'localhost' || ip.startsWith('192.168.') || ip.startsWith('10.')) {
      return '本地网络';
    }

    // 调用IP地址查询API
    // 这里使用免费的IP地址查询API，实际使用时可以替换为更可靠的服务
    const response = await axios.get(`http://ip-api.com/json/${ip}?lang=zh-CN`);
    
    if (response.data && response.data.status === 'success') {
      const { country, regionName, city } = response.data;
      return `${country} ${regionName} ${city}`.trim();
    }
    
    return '未知位置';
  } catch (error) {
    console.error('获取IP地址位置信息失败:', error);
    return '未知位置';
  }
}

module.exports = {
  getLocationByIp
};
