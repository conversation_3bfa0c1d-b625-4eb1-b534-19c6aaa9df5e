/**
 * 格式工具类
 * 提供字段格式转换等通用功能
 */

/**
 * 将小驼峰格式字段名转换为下划线格式
 * @param {Object} data 小驼峰格式数据
 * @returns {Object} 下划线格式数据
 */
function camelToSnake(data) {
  if (!data) return data;
  
  const result = {};
  Object.keys(data).forEach(key => {
    // 将小驼峰转换为下划线格式（例如：userType -> user_type）
    const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
    result[snakeKey] = data[key];
  });
  
  return result;
}

/**
 * 将下划线格式字段名转换为小驼峰格式
 * @param {Object} data 下划线格式数据
 * @returns {Object} 小驼峰格式数据
 */
function snakeToCamel(data) {
  if (!data) return data;
  
  const result = {};
  Object.keys(data).forEach(key => {
    // 将下划线格式转换为小驼峰格式（例如：user_type -> userType）
    const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    result[camelKey] = data[key];
  });
  
  return result;
}

/**
 * 递归将下划线格式字段名转换为小驼峰格式
 * 支持嵌套对象和数组的递归转换
 * @param {*} data 需要处理的数据
 * @returns {*} 处理后的数据
 */
function recursiveSnakeToCamel(data) {
  if (data === null || data === undefined) return data;
  
  // 处理数组
  if (Array.isArray(data)) {
    return data.map(item => recursiveSnakeToCamel(item));
  }
  
  // 处理对象，排除Date类型
  if (typeof data === 'object' && !(data instanceof Date)) {
    // 先使用现有的snakeToCamel函数处理当前层级
    const camelData = snakeToCamel(data);
    
    // 然后递归处理每个属性
    const result = {};
    for (const key in camelData) {
      result[key] = recursiveSnakeToCamel(camelData[key]);
    }
    
    return result;
  }
  
  // 其他类型直接返回
  return data;
}

/**
 * 递归将小驼峰格式字段名转换为下划线格式
 * 支持嵌套对象和数组的递归转换
 * @param {*} data 需要处理的数据
 * @returns {*} 处理后的数据
 */
function recursiveCamelToSnake(data) {
  if (data === null || data === undefined) return data;
  
  // 处理数组
  if (Array.isArray(data)) {
    return data.map(item => recursiveCamelToSnake(item));
  }
  
  // 处理对象，排除Date类型
  if (typeof data === 'object' && !(data instanceof Date)) {
    // 先使用现有的camelToSnake函数处理当前层级
    const snakeData = camelToSnake(data);
    
    // 然后递归处理每个属性
    const result = {};
    for (const key in snakeData) {
      result[key] = recursiveCamelToSnake(snakeData[key]);
    }
    
    return result;
  }
  
  // 其他类型直接返回
  return data;
}

module.exports = {
  camelToSnake,
  snakeToCamel,
  recursiveSnakeToCamel,
  recursiveCamelToSnake
};
