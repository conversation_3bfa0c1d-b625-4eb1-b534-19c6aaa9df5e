/**
 * 雪花ID生成器
 * 16位数字ID，由以下部分组成：
 * - 时间戳（41位）：当前时间戳减去开始时间戳的差值
 * - 机器ID（5位）：支持最多32台机器
 * - 数据中心ID（5位）：支持最多32个数据中心
 * - 序列号（12位）：同一毫秒内最多生成4096个ID
 */

// 开始时间戳（2024-01-01）
const START_TIMESTAMP = 1704067200000;

// 各部分占用的位数
const TIMESTAMP_BITS = 41;   // 时间戳占用位数
const DATACENTER_BITS = 5;   // 数据中心占用位数
const WORKER_BITS = 5;       // 机器标识占用位数
const SEQUENCE_BITS = 12;    // 序列号占用位数

// 各部分最大值
const MAX_DATACENTER_ID = -1 ^ (-1 << DATACENTER_BITS); // 数据中心最大值
const MAX_WORKER_ID = -1 ^ (-1 << WORKER_BITS);         // 机器ID最大值
const MAX_SEQUENCE = -1 ^ (-1 << SEQUENCE_BITS);        // 序列号最大值

// 时间戳、数据中心、机器ID的位移
const WORKER_ID_SHIFT = SEQUENCE_BITS;                                     // 机器ID左移位数
const DATACENTER_ID_SHIFT = SEQUENCE_BITS + WORKER_BITS;                  // 数据中心ID左移位数
const TIMESTAMP_LEFT_SHIFT = SEQUENCE_BITS + WORKER_BITS + DATACENTER_BITS; // 时间戳左移位数

class SnowflakeIdGenerator {
  constructor(datacenterId = 1, workerId = 1) {
    // 验证数据中心ID和机器ID是否合法
    if (datacenterId > MAX_DATACENTER_ID || datacenterId < 0) {
      throw new Error(`数据中心ID不能大于${MAX_DATACENTER_ID}或小于0`);
    }
    if (workerId > MAX_WORKER_ID || workerId < 0) {
      throw new Error(`机器ID不能大于${MAX_WORKER_ID}或小于0`);
    }

    this.datacenterId = datacenterId;
    this.workerId = workerId;
    this.sequence = 0;
    this.lastTimestamp = -1;
  }

  /**
   * 生成下一个ID
   */
  nextId() {
    let timestamp = Date.now();

    // 如果当前时间小于上一次ID生成的时间戳，说明系统时钟回退过，抛出异常
    if (timestamp < this.lastTimestamp) {
      throw new Error('系统时钟回退，拒绝生成ID');
    }

    // 如果是同一时间生成的，则进行序列号递增
    if (timestamp === this.lastTimestamp) {
      this.sequence = (this.sequence + 1) & MAX_SEQUENCE;
      // 序列号已达到最大值，等待下一毫秒
      if (this.sequence === 0) {
        timestamp = this.tilNextMillis(this.lastTimestamp);
      }
    } else {
      // 时间戳改变，序列号重置
      this.sequence = 0;
    }

    this.lastTimestamp = timestamp;

    // 生成ID
    const timeDiff = BigInt(timestamp - START_TIMESTAMP);
    const id = (timeDiff << BigInt(TIMESTAMP_LEFT_SHIFT)) |
               (BigInt(this.datacenterId) << BigInt(DATACENTER_ID_SHIFT)) |
               (BigInt(this.workerId) << BigInt(WORKER_ID_SHIFT)) |
               BigInt(this.sequence);

    return id;
  }

  /**
   * 等待下一毫秒
   */
  tilNextMillis(lastTimestamp) {
    let timestamp = Date.now();
    while (timestamp <= lastTimestamp) {
      timestamp = Date.now();
    }
    return timestamp;
  }
}

// 创建一个默认的雪花ID生成器实例
const defaultGenerator = new SnowflakeIdGenerator();

/**
 * 生成雪花ID
 * @returns {bigint} 18位雪花ID（64位整型，十进制通常为18位）
 */
function generateSnowflakeId() {
  return defaultGenerator.nextId();
}

module.exports = {
  generateSnowflakeId,
  SnowflakeIdGenerator
};
