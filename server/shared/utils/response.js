/**
 * 统一响应工具
 */
class ResponseUtil {
  /**
   * 成功响应
   * @param {any} data 响应数据
   * @param {string} message 成功消息
   */
  static success(data = null, message = '操作成功') {
    return {
      success: true,
      message,
      data
    };
  }

  /**
   * 错误响应
   * @param {string} message 错误消息
   * @param {Error} error 错误对象
   */
  static error(message = '操作失败', error = null) {
    const response = {
      success: false,
      message
    };

    if (process.env.NODE_ENV === 'development' && error) {
      response.error = error.message;
      response.stack = error.stack;
    }

    return response;
  }

  /**
   * 分页数据响应
   * @param {Array} items 数据项
   * @param {number} total 总数
   * @param {number} page 当前页
   * @param {number} pageSize 每页大小
   */
  static page(items, total, page, pageSize) {
    return {
      success: true,
      data: {
        items,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }
}

module.exports = ResponseUtil;
