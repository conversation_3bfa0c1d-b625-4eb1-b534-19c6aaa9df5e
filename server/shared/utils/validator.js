/**
 * 请求参数验证工具
 */

/**
 * 验证请求体参数
 * @param {Object} body 请求体
 * @param {Object} schema Joi验证模式
 * @returns {Object} 验证结果，包含error和value
 */
function validateRequestBody(body, schema) {
  return schema.validate(body, { abortEarly: false });
}

/**
 * 验证请求查询参数
 * @param {Object} query 请求查询参数
 * @param {Object} schema Joi验证模式
 * @returns {Object} 验证结果，包含error和value
 */
function validateRequestQuery(query, schema) {
  return schema.validate(query, { abortEarly: false });
}

/**
 * 验证请求路径参数
 * @param {Object} params 请求路径参数
 * @param {Object} schema Joi验证模式
 * @returns {Object} 验证结果，包含error和value
 */
function validateRequestParams(params, schema) {
  return schema.validate(params, { abortEarly: false });
}

module.exports = {
  validateRequestBody,
  validateRequestQuery,
  validateRequestParams
};
