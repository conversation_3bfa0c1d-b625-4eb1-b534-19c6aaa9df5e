/**
 * 请求工具
 */

/**
 * 获取客户端IP地址
 * @param {Object} req 请求对象
 * @returns {String} IP地址
 */
function getClientIp(req) {
  // 获取客户端IP地址
  const ip = req.headers['x-forwarded-for'] || 
             req.headers['x-real-ip'] || 
             req.connection.remoteAddress || 
             req.socket.remoteAddress || 
             req.connection.socket.remoteAddress || 
             '127.0.0.1';
  
  // 如果是IPv6格式的IP地址，需要处理一下
  return ip.indexOf(':') >= 0 ? ip.split(':').pop() : ip;
}

module.exports = {
  getClientIp
};
