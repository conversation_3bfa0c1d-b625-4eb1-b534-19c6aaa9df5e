/**
 * 随机工具函数
 */

/**
 * 生成指定长度的随机字符串
 * @param {Number} length 随机字符串长度
 * @param {Boolean} alphanumeric 是否包含字母和数字，默认为false（仅数字）
 * @returns {String} 随机字符串
 */
function generateRandomString(length, alphanumeric = false) {
  const chars = alphanumeric 
    ? '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ' 
    : '0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * chars.length);
    result += chars.charAt(randomIndex);
  }
  
  return result;
}

/**
 * 生成8位数的随机数，范围为10000000-19999999
 * @returns {Number} 8位随机数
 */
function generateFirstRandom() {
  return 10000000 + Math.floor(Math.random() * 10000000);
}

/**
 * 生成8位数的随机数，范围为10000000-19999999
 * @returns {Number} 8位随机数
 */
function generateSecondRandom() {
  return 10000000 + Math.floor(Math.random() * 10000000);
}

/**
 * 生成指定范围内的随机整数
 * @param {Number} min 最小值（包含）
 * @param {Number} max 最大值（不包含）
 * @returns {Number} 随机整数
 */
function getRandomInt(min, max) {
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(Math.random() * (max - min)) + min;
}

module.exports = {
  generateRandomString,
  generateFirstRandom,
  generateSecondRandom,
  getRandomInt
};
