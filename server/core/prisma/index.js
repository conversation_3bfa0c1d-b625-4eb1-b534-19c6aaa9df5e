const { PrismaClient } = require('@prisma/client');

class PrismaManager {
  constructor() {
    this.clients = new Map();
  }

  /**
   * 获取指定模块的 Prisma 客户端
   * @param {string} moduleName 模块名称
   * @returns {PrismaClient}
   */
  getClient(moduleName) {
    if (!this.clients.has(moduleName)) {
      const client = new PrismaClient({
        datasources: {
          db: {
            url: process.env.DATABASE_URL
          }
        }
      });
      this.clients.set(moduleName, client);
    }
    return this.clients.get(moduleName);
  }

  /**
   * 断开所有数据库连接
   */
  async disconnect() {
    for (const [_, client] of this.clients) {
      await client.$disconnect();
    }
    this.clients.clear();
  }
}

module.exports = new PrismaManager();
