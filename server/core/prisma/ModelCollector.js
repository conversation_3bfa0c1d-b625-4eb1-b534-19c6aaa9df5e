/**
 * Prisma模型收集器
 * 用于自动收集和加载分散在各模块中的Prisma模型定义
 */
const fs = require('fs');
const path = require('path');
const glob = require('glob');

class ModelCollector {
  /**
   * 收集所有模块的Prisma模型定义
   * @param {string} basePath 基础路径，默认为项目根目录
   * @returns {Object} 收集到的模型定义信息
   */
  static collectModels(basePath = process.cwd()) {
    // 定义模型文件搜索模式
    const searchPatterns = [
      // 主模型定义
      path.join(basePath, 'prisma', '*.prisma'),
      // 各模块中的模型定义
      path.join(basePath, 'apps', '**', 'prisma', 'models', '**', '*.prisma')
    ];

    // 收集所有匹配的模型文件
    const modelFiles = [];
    searchPatterns.forEach(pattern => {
      const files = glob.sync(pattern);
      modelFiles.push(...files);
    });

    // 输出收集到的模型文件
    console.log(`[ModelCollector] 收集到 ${modelFiles.length} 个Prisma模型文件`);

    return {
      modelFiles,
      count: modelFiles.length
    };
  }

  /**
   * 生成临时合并的schema文件
   * @param {Array} modelFiles 模型文件列表
   * @param {string} outputPath 输出路径
   * @returns {string} 生成的文件路径
   */
  static generateMergedSchema(modelFiles, outputPath = path.join(process.cwd(), 'prisma', '.merged-schema.prisma')) {
    // 读取主模式文件作为基础
    const mainSchemaPath = path.join(process.cwd(), 'prisma', 'schema.prisma');
    let mainSchema = fs.readFileSync(mainSchemaPath, 'utf8');
    
    // 收集所有模型定义
    const modelDefinitions = [];
    modelFiles.forEach(file => {
      // 跳过主schema文件
      if (file === mainSchemaPath) return;
      
      try {
        const content = fs.readFileSync(file, 'utf8');
        // 提取模型定义部分
        const modelContent = content.replace(/generator .+?\}/gs, '')
                                   .replace(/datasource .+?\}/gs, '')
                                   .trim();
        
        if (modelContent) {
          modelDefinitions.push(`// 从 ${path.relative(process.cwd(), file)} 导入的模型`);
          modelDefinitions.push(modelContent);
        }
      } catch (err) {
        console.error(`[ModelCollector] 读取模型文件 ${file} 失败:`, err);
      }
    });
    
    // 合并所有模型定义
    const mergedSchema = mainSchema + '\n\n' + modelDefinitions.join('\n\n');
    
    // 确保输出目录存在
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // 写入合并后的schema文件
    fs.writeFileSync(outputPath, mergedSchema);
    console.log(`[ModelCollector] 生成合并schema文件: ${outputPath}`);
    
    return outputPath;
  }

  /**
   * 运行模型收集和合并
   * @returns {string} 生成的合并schema文件路径
   */
  static run() {
    const { modelFiles } = this.collectModels();
    const mergedSchemaPath = this.generateMergedSchema(modelFiles);
    return mergedSchemaPath;
  }
}

module.exports = ModelCollector;
