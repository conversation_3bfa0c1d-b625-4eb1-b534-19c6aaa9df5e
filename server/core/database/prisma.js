/**
 * Prisma 客户端单例模式
 * 避免创建过多的数据库连接
 */
const { PrismaClient } = require('@prisma/client');

// 检查全局变量中是否已存在 Prisma 实例
const globalForPrisma = global;

// 如果不存在，则创建一个新的实例
if (!globalForPrisma.prisma) {
  globalForPrisma.prisma = new PrismaClient({
    // 可以在这里添加日志配置
    log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],
  });
  
  // 如果是开发环境，输出提示信息
  if (process.env.NODE_ENV === 'development') {
    console.log('创建新的 Prisma 客户端实例');
  }
  
  // 连接数据库
  globalForPrisma.prisma.$connect()
    .then(() => console.log('数据库连接成功!'))
    .catch(err => {
      console.error('数据库连接错误:', err);
      process.exit(1);
    });
}

// 导出全局 Prisma 实例
const prisma = globalForPrisma.prisma;

module.exports = { prisma };
