/**
 * RabbitMQ 初始化模块
 * 用于初始化 RabbitMQ 连接和注册消费者
 */
const rabbitMQService = require('../services/RabbitMQService');
const consumerManager = require('./ConsumerManager');
const logger = require('../utils/logger').createLogger('RabbitMQ初始化');
const OrderConsumer = require('../../apps/master/consumers/OrderConsumer');

// 获取配置信息
const config = require('../../config');
// 默认启用MQ，可通过环境变量或配置文件禁用
const enableMQ = process.env.ENABLE_MQ !== 'false' && (config.rabbitmq?.enable !== false);

/**
 * 初始化 RabbitMQ 连接
 * @returns {Promise<void>}
 */
async function initRabbitMQ() {
  try {
    logger.info('正在初始化 RabbitMQ 连接...');
    await rabbitMQService.connect();
    logger.info('RabbitMQ 连接初始化完成');
    return true;
  } catch (error) {
    logger.error('RabbitMQ 连接初始化失败:', error);
    return false;
  }
}

/**
 * 注册所有消费者
 */
function registerConsumers() {
  try {
    logger.info('正在注册消费者...');
    
    // 注册订单消费者
    consumerManager.register('order', new OrderConsumer());
    
    // 在这里注册其他消费者
    // consumerManager.register('notification', new NotificationConsumer());
    // consumerManager.register('email', new EmailConsumer());
    
    logger.info('消费者注册完成');
  } catch (error) {
    logger.error('注册消费者失败:', error);
  }
}

/**
 * 启动所有消费者
 * @returns {Promise<void>}
 */
async function startConsumers() {
  try {
    logger.info('正在启动消费者...');
    await consumerManager.startAll();
    logger.info('所有消费者启动完成');
    return true;
  } catch (error) {
    logger.error('启动消费者失败:', error);
    return false;
  }
}

/**
 * 停止所有消费者
 * @returns {Promise<void>}
 */
async function stopConsumers() {
  try {
    logger.info('正在停止消费者...');
    await consumerManager.stopAll();
    logger.info('所有消费者已停止');
    return true;
  } catch (error) {
    logger.error('停止消费者失败:', error);
    return false;
  }
}

/**
 * 初始化 RabbitMQ 并启动消费者
 * @returns {Promise<boolean>} 初始化是否成功
 */
async function initialize() {
  try {
    // 检查是否启用MQ
    if (!enableMQ) {
      logger.info('RabbitMQ 功能已禁用，跳过初始化');
      return false;
    }
    
    // 初始化 RabbitMQ 连接
    const connected = await initRabbitMQ();
    if (!connected) {
      logger.warn('由于 RabbitMQ 连接失败，消费者将不会启动');
      return false;
    }
    
    // 注册消费者
    registerConsumers();
    
    // 启动消费者
    await startConsumers();
    
    return true;
  } catch (error) {
    logger.error('初始化 RabbitMQ 和消费者失败:', error);
    return false;
  }
}

/**
 * 关闭 RabbitMQ 连接
 * @returns {Promise<void>}
 */
async function shutdown() {
  try {
    // 停止所有消费者
    await stopConsumers();
    
    // 关闭 RabbitMQ 连接
    await rabbitMQService.close();
    
    logger.info('RabbitMQ 连接已关闭');
  } catch (error) {
    logger.error('关闭 RabbitMQ 连接失败:', error);
  }
}

module.exports = {
  initialize,
  shutdown,
  registerConsumers,
  startConsumers,
  stopConsumers
};
