/**
 * 消费者管理器
 * 用于统一管理所有消费者的启动和停止
 */
const logger = require('../utils/logger').createLogger('消费者管理器');

class ConsumerManager {
  constructor() {
    this.consumers = new Map();
    this.isStarted = false;
  }

  /**
   * 注册消费者
   * @param {string} name 消费者名称
   * @param {Object} consumer 消费者实例
   */
  register(name, consumer) {
    if (this.consumers.has(name)) {
      logger.warn(`消费者 ${name} 已存在，将被覆盖`);
    }
    this.consumers.set(name, consumer);
    logger.info(`消费者 ${name} 已注册`);
  }

  /**
   * 启动所有消费者
   * @returns {Promise<void>}
   */
  async startAll() {
    if (this.isStarted) {
      logger.warn('消费者管理器已经启动，忽略重复启动请求');
      return;
    }

    logger.info('正在启动所有消费者...');
    const startPromises = [];

    for (const [name, consumer] of this.consumers.entries()) {
      startPromises.push(
        consumer.start()
          .then(() => {
            logger.info(`消费者 ${name} 启动成功`);
          })
          .catch(error => {
            logger.error(`消费者 ${name} 启动失败:`, error);
          })
      );
    }

    await Promise.allSettled(startPromises);
    this.isStarted = true;
    logger.info(`所有消费者启动完成，共 ${this.consumers.size} 个消费者`);
  }

  /**
   * 停止所有消费者
   * @returns {Promise<void>}
   */
  async stopAll() {
    if (!this.isStarted) {
      logger.warn('消费者管理器未启动，忽略停止请求');
      return;
    }

    logger.info('正在停止所有消费者...');
    const stopPromises = [];

    for (const [name, consumer] of this.consumers.entries()) {
      stopPromises.push(
        consumer.stop()
          .then(() => {
            logger.info(`消费者 ${name} 停止成功`);
          })
          .catch(error => {
            logger.error(`消费者 ${name} 停止失败:`, error);
          })
      );
    }

    await Promise.allSettled(stopPromises);
    this.isStarted = false;
    logger.info('所有消费者已停止');
  }

  /**
   * 获取消费者
   * @param {string} name 消费者名称
   * @returns {Object|undefined} 消费者实例
   */
  getConsumer(name) {
    return this.consumers.get(name);
  }

  /**
   * 获取所有消费者名称
   * @returns {Array<string>} 消费者名称列表
   */
  getConsumerNames() {
    return Array.from(this.consumers.keys());
  }
}

// 创建单例
const consumerManager = new ConsumerManager();

module.exports = consumerManager;
