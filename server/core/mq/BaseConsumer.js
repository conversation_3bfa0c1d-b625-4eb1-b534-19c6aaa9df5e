/**
 * RabbitMQ 消费者基类
 */
const rabbitMQService = require('../services/RabbitMQService');
const logger = require('../utils/logger').createLogger('MQ消费者');

class BaseConsumer {
  /**
   * 构造函数
   * @param {string} queueKey 队列键名
   * @param {Object} options 消费者选项
   */
  constructor(queueKey, options = {}) {
    this.queueKey = queueKey;
    this.options = {
      noAck: false,
      ...options
    };
    this.consumerTag = null;
  }

  /**
   * 启动消费者
   * @returns {Promise<void>}
   */
  async start() {
    try {
      const { consumerTag } = await rabbitMQService.consume(
        this.queueKey,
        this.processMessage.bind(this),
        this.options
      );
      this.consumerTag = consumerTag;
      logger.info(`消费者已启动，队列: ${this.queueKey}, 标签: ${consumerTag}`);
    } catch (error) {
      logger.error(`启动消费者失败，队列: ${this.queueKey}`, error);
      throw error;
    }
  }

  /**
   * 处理消息的抽象方法，子类必须实现
   * @param {Object} message 消息内容
   * @param {Object} originalMessage 原始消息对象
   * @returns {Promise<void>}
   */
  async processMessage(message, originalMessage) {
    throw new Error('子类必须实现 processMessage 方法');
  }

  /**
   * 停止消费者
   * @returns {Promise<void>}
   */
  async stop() {
    if (this.consumerTag) {
      try {
        await rabbitMQService.channel.cancel(this.consumerTag);
        logger.info(`消费者已停止，队列: ${this.queueKey}, 标签: ${this.consumerTag}`);
        this.consumerTag = null;
      } catch (error) {
        logger.error(`停止消费者失败，队列: ${this.queueKey}`, error);
        throw error;
      }
    }
  }
}

module.exports = BaseConsumer;
