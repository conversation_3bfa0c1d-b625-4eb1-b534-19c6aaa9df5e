/**
 * JWT认证中间件
 * 用于验证用户请求中的JWT令牌
 */
const jwt = require('jsonwebtoken');
const redisUtil = require('../utils/RedisUtil');
const authConfig = require('../../config/auth.config');
const RouteManager = require('../routes/RouteManager');

// JWT配置
const JWT_SECRET = process.env.JWT_SECRET || 'julingcloud-secret-key';

/**
 * 验证JWT令牌的中间件
 * 将解析后的用户信息存储在req.user中
 */
async function authMiddleware(req, res, next) {
  // 获取当前请求路径
  const requestPath = req.originalUrl;
  
  // 检查是否是公开路径
  const publicPaths = authConfig.publicPaths || [];
  const isPublicPath = publicPaths.some(path => {
    // 如果路径以 * 结尾，进行前缀匹配
    if (path.endsWith('*')) {
      const prefix = path.slice(0, -1);
      return requestPath.startsWith(prefix);
    }
    // 否则进行完全匹配
    return requestPath === path;
  });
  
  // 如果是公开路径，直接放行
  if (isPublicPath) {
    console.log(`[鉴权中间件] 公开路径，跳过鉴权: ${requestPath}`);
    return next();
  }
  
  // 获取请求头中的Authorization令牌
  const authHeader = req.headers.authorization;
  
  console.log(`[鉴权中间件] 请求路径: ${requestPath}`);
  console.log(`[鉴权中间件] 请求头:`, req.headers);
  console.log(`[鉴权中间件] Authorization头: ${authHeader}`);
  
  // 如果没有提供认证令牌，返回401错误
  if (!authHeader) {
    console.log(`[鉴权中间件] 缺失Authorization头，路径: ${requestPath}`);
    return res.status(401).json({
      code: 401,
      message: '请先登录后再访问',
      data: null
    });
  }
  
  // 从Authorization头中提取令牌
  // 格式为: "Bearer <token>"
  const token = authHeader.split(' ')[1];
  
  // 如果令牌格式错误，返回401错误
  if (!token) {
    return res.status(401).json({
      code: 401,
      message: '认证令牌格式错误',
      data: null
    });
  }
  
  try {
    // 验证令牌
    const decoded = jwt.verify(token, JWT_SECRET);
    
    // 检查令牌是否在黑名单中
    const isBlacklisted = await redisUtil.isTokenBlacklisted(token);
    if (isBlacklisted) {
      return res.status(401).json({
        code: 401,
        message: '令牌已失效，请重新登录',
        data: null
      });
    }
    
    // 将解码后的用户信息存储在请求对象中
    req.user = decoded;
    
    // 检查令牌是否过期
    const currentTime = Math.floor(Date.now() / 1000);
    if (decoded.exp && decoded.exp < currentTime) {
      return res.status(401).json({
        code: 401,
        message: '登录已过期，请重新登录',
        data: null
      });
    }
    
    // 继续处理请求
    next();
  } catch (error) {
    console.error('令牌验证失败:', error.message);
    
    // 根据错误类型返回适当的响应
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        code: 401,
        message: '登录已过期，请重新登录',
        data: null
      });
    } else if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        code: 401,
        message: '无效的登录凭证',
        data: null
      });
    } else {
      return res.status(500).json({
        code: 500,
        message: '认证服务器内部错误',
        data: null
      });
    }
  }
}

module.exports = authMiddleware;
