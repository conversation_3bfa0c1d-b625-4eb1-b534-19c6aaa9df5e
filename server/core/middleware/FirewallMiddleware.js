/**
 * 防火墙中间件
 * 根据IP规则控制访问权限
 */
const { prisma } = require('../database/prisma');
const { getClientIp } = require('../../shared/utils/request');

/**
 * IP地址匹配工具类
 */
class IpMatcher {
  /**
   * 检查IP是否匹配规则
   * @param {string} clientIp 客户端IP
   * @param {string} ruleIp 规则IP
   * @param {string} ruleMask 规则掩码
   * @param {number} matchType 匹配类型：1-精确匹配，2-IP段匹配，3-通配符匹配
   * @returns {boolean} 是否匹配
   */
  static isMatch(clientIp, ruleIp, ruleMask, matchType) {
    try {
      switch (matchType) {
        case 1: // 精确匹配
          return clientIp === ruleIp;
        
        case 2: // IP段匹配
          return this.isInSubnet(clientIp, ruleIp, ruleMask);
        
        case 3: // 通配符匹配
          return this.isWildcardMatch(clientIp, ruleIp);
        
        default:
          return false;
      }
    } catch (error) {
      console.error('[防火墙中间件] IP匹配错误:', error);
      return false;
    }
  }

  /**
   * 检查IP是否在子网内
   * @param {string} ip 客户端IP
   * @param {string} subnet 子网IP
   * @param {string} mask 子网掩码
   * @returns {boolean} 是否在子网内
   */
  static isInSubnet(ip, subnet, mask) {
    if (!mask) return ip === subnet;
    
    try {
      const ipNum = this.ipToNumber(ip);
      const subnetNum = this.ipToNumber(subnet);
      const maskNum = this.ipToNumber(mask);
      
      return (ipNum & maskNum) === (subnetNum & maskNum);
    } catch (error) {
      console.error('[防火墙中间件] 子网匹配错误:', error);
      return false;
    }
  }

  /**
   * 通配符匹配
   * @param {string} ip 客户端IP
   * @param {string} pattern 通配符模式
   * @returns {boolean} 是否匹配
   */
  static isWildcardMatch(ip, pattern) {
    try {
      // 将通配符模式转换为正则表达式
      const regex = new RegExp(
        '^' + pattern.replace(/\./g, '\\.').replace(/\*/g, '.*') + '$'
      );
      return regex.test(ip);
    } catch (error) {
      console.error('[防火墙中间件] 通配符匹配错误:', error);
      return false;
    }
  }

  /**
   * 将IP地址转换为数字
   * @param {string} ip IP地址
   * @returns {number} 数字形式的IP
   */
  static ipToNumber(ip) {
    const parts = ip.split('.');
    if (parts.length !== 4) {
      throw new Error('Invalid IP address format');
    }
    
    return parts.reduce((acc, part, index) => {
      const num = parseInt(part, 10);
      if (num < 0 || num > 255) {
        throw new Error('Invalid IP address part');
      }
      return acc + (num << (8 * (3 - index)));
    }, 0) >>> 0; // 使用无符号右移确保结果为正数
  }
}

/**
 * 防火墙规则缓存
 */
class FirewallRuleCache {
  constructor() {
    this.cache = null;
    this.lastUpdate = 0;
    this.cacheTimeout = 60000; // 缓存1分钟
  }

  /**
   * 获取防火墙规则
   * @returns {Array} 规则列表
   */
  async getRules() {
    const now = Date.now();
    
    // 如果缓存有效，直接返回
    if (this.cache && (now - this.lastUpdate) < this.cacheTimeout) {
      return this.cache;
    }

    try {
      // 从数据库获取启用的规则，按优先级排序
      const rules = await prisma.baseFirewallRule.findMany({
        where: {
          status: 1, // 启用状态
          deleted_at: null
        },
        select: {
          id: true,
          rule_name: true,
          ip_address: true,
          ip_mask: true,
          rule_type: true,
          match_type: true,
          priority: true
        },
        orderBy: [
          { priority: 'desc' }, // 优先级高的在前
          { created_at: 'asc' }  // 创建时间早的在前
        ]
      });

      this.cache = rules;
      this.lastUpdate = now;
      
      console.log(`[防火墙中间件] 加载了 ${rules.length} 条规则`);
      return rules;
    } catch (error) {
      console.error('[防火墙中间件] 获取规则失败:', error);
      return this.cache || [];
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache = null;
    this.lastUpdate = 0;
  }
}

// 创建全局缓存实例
const ruleCache = new FirewallRuleCache();

/**
 * 防火墙中间件
 * @param {Object} options 配置选项
 * @returns {Function} 中间件函数
 */
function firewallMiddleware(options = {}) {
  const {
    exclude = [], // 排除的路径
    enableWhitelist = true, // 是否启用白名单
    enableBlacklist = true, // 是否启用黑名单
    defaultAction = 'allow' // 默认动作：allow-允许，deny-拒绝
  } = options;

  return async (req, res, next) => {
    try {
      // 跳过排除的路径
      if (exclude.some(path => req.path.includes(path))) {
        return next();
      }

      // 获取客户端IP
      const clientIp = getClientIp(req);
      console.log(`[防火墙中间件] 检查IP: ${clientIp}, 路径: ${req.path}`);

      // 获取防火墙规则
      const rules = await ruleCache.getRules();
      
      if (rules.length === 0) {
        console.log('[防火墙中间件] 无规则，允许访问');
        return next();
      }

      // 检查规则匹配
      let matchedRule = null;
      for (const rule of rules) {
        if (IpMatcher.isMatch(clientIp, rule.ip_address, rule.ip_mask, rule.match_type)) {
          matchedRule = rule;
          break; // 找到第一个匹配的规则就停止
        }
      }

      if (matchedRule) {
        console.log(`[防火墙中间件] 匹配规则: ${matchedRule.rule_name}, 类型: ${matchedRule.rule_type}`);
        
        // 更新命中统计（异步执行，不阻塞请求）
        updateHitCount(matchedRule.id).catch(error => {
          console.error('[防火墙中间件] 更新命中统计失败:', error);
        });

        // 根据规则类型决定是否允许访问
        if (matchedRule.rule_type === 1) { // 黑名单
          if (enableBlacklist) {
            console.log(`[防火墙中间件] 黑名单拒绝访问: ${clientIp}`);
            return res.status(403).json({
              code: 403,
              message: '访问被拒绝',
              data: null
            });
          }
        } else if (matchedRule.rule_type === 2) { // 白名单
          if (enableWhitelist) {
            console.log(`[防火墙中间件] 白名单允许访问: ${clientIp}`);
            return next();
          }
        }
      }

      // 没有匹配的规则，根据默认动作处理
      if (defaultAction === 'deny') {
        console.log(`[防火墙中间件] 默认拒绝访问: ${clientIp}`);
        return res.status(403).json({
          code: 403,
          message: '访问被拒绝',
          data: null
        });
      }

      console.log(`[防火墙中间件] 允许访问: ${clientIp}`);
      next();
    } catch (error) {
      console.error('[防火墙中间件] 处理错误:', error);
      // 发生错误时，为了安全起见，允许访问（避免误拦截）
      next();
    }
  };
}

/**
 * 更新规则命中统计
 * @param {BigInt} ruleId 规则ID
 */
async function updateHitCount(ruleId) {
  try {
    await prisma.baseFirewallRule.update({
      where: { id: ruleId },
      data: {
        hit_count: { increment: 1 },
        last_hit_at: BigInt(Date.now())
      }
    });
  } catch (error) {
    console.error('[防火墙中间件] 更新命中统计失败:', error);
  }
}

/**
 * 清除规则缓存
 */
function clearFirewallCache() {
  ruleCache.clearCache();
}

module.exports = {
  firewallMiddleware,
  clearFirewallCache,
  IpMatcher
};
