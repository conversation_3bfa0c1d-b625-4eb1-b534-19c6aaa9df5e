/**
 * 操作日志中间件
 * 记录用户的操作日志
 */
const { prisma } = require('../../core/database/prisma');
const { generateSnowflakeId } = require('../../shared/utils/snowflake');
const logConfig = require('../../config/log.config');

// 从配置文件中获取日志白名单和客户端映射
const { whitelist, clientMap, moduleMap } = logConfig;

/**
 * 操作日志中间件
 * 记录用户的操作日志
 */
function operationLogMiddleware(options = {}) {
  const { 
    exclude = [], // 排除的路径
    logResponse = false, // 是否记录响应内容
    logRequest = true, // 是否记录请求内容
    sensitiveFields = ['password', 'token', 'secret'] // 敏感字段
  } = options;
  
  // 合并配置文件中的白名单和传入的排除路径
  const excludePaths = [...whitelist, ...exclude];
  
  return async (req, res, next) => {
    // 跳过不需要记录的路径
    if (excludePaths.some(path => req.path.includes(path))) {
      console.log(`[日志中间件] 跳过路径: ${req.path}`);
      return next();
    }
    
    console.log(`[日志中间件] 开始处理请求: ${req.method} ${req.path}`);
    
    // 记录请求开始时间
    const startTime = Date.now();
    
    // 保存原始响应方法
    const originalSend = res.send;
    let responseBody;
    
    // 重写响应方法以捕获响应内容
    res.send = function(body) {
      responseBody = body;
      return originalSend.call(this, body);
    };
    
    // 继续处理请求
    next();
    
    // 请求结束后记录日志
    res.on('finish', async () => {
      try {
        console.log(`[日志中间件] 请求完成: ${req.method} ${req.path}, 状态码: ${res.statusCode}`);
        
        // 计算执行时间
        const executionTime = Date.now() - startTime;
        console.log(`[日志中间件] 执行时间: ${executionTime}ms`);
        
        // 获取用户信息
        const userId = req.user?.id || 0;
        const username = req.user?.username || '未登录用户';
        
        // 提取请求数据（过滤敏感信息）
        let requestData = null;
        if (logRequest) {
          try {
            requestData = {
              query: req.query,
              body: filterSensitiveData(req.body, sensitiveFields),
              params: req.params
            };
          } catch (dataError) {
            console.error(`[日志中间件] 提取请求数据失败:`, dataError);
            requestData = { error: '无法提取请求数据' };
          }
        }
        
        // 提取响应数据
        let responseData = null;
        if (logResponse && responseBody) {
          try {
            console.log(`[日志中间件] 开始解析响应数据`);
            responseData = typeof responseBody === 'string' 
              ? JSON.parse(responseBody) 
              : responseBody;
          } catch (e) {
            console.error(`[日志中间件] 解析响应数据失败:`, e);
            responseData = { message: '无法解析响应内容' };
          }
        }
        
        // 确定操作模块、客户端和类型
        // 使用原始请求路径，避免中间件修改导致的路径不完整
        const fullPath = req.originalUrl || (req.baseUrl + req.path);
        console.log(`[日志中间件] 原始请求路径: ${fullPath}`);
        
        const pathParts = fullPath.split('/').filter(Boolean);
        console.log(`[日志中间件] 解析路径部分:`, pathParts);
        
        // 改进模块识别逻辑
        let module = '未知模块';
        
        // 识别客户端类型
        let client = clientMap['default'] || '未知客户端';
        
        // 如果是API路径
        if (pathParts[0] === 'api') {
          if (pathParts.length >= 5 && pathParts[1] === 'v1') {
            // 使用配置文件中的客户端映射
            const clientKey = pathParts[2];
            if (clientMap[clientKey]) {
              client = clientMap[clientKey];
            }
            
            // 使用配置文件中的模块映射
            if (pathParts[2] === 'master') {
              if (pathParts.length >= 6 && pathParts[3] === 'system') {
                // 系统管理模块
                if (pathParts.length >= 6 && pathParts[4]) {
                  const systemModule = pathParts[4];
                  
                  // 处理集成模块的特殊情况
                  if (systemModule === 'integration' && pathParts.length >= 7 && pathParts[5]) {
                    const integrationSubModule = pathParts[5];
                    if (moduleMap.system && moduleMap.system.integration && moduleMap.system.integration[integrationSubModule]) {
                      module = moduleMap.system.integration[integrationSubModule];
                    } else if (moduleMap.system && moduleMap.system.integration && moduleMap.system.integration.default) {
                      module = moduleMap.system.integration.default;
                    } else {
                      module = '第三方集成';
                    }
                  } else if (moduleMap.system && moduleMap.system[systemModule]) {
                    module = moduleMap.system[systemModule];
                  } else {
                    module = '系统管理';
                  }
                } else {
                  module = '系统管理';
                }
              } else if (pathParts.length >= 5 && pathParts[3]) {
                // master模块的其他功能
                const masterModule = pathParts[3];
                if (moduleMap[masterModule]) {
                  module = moduleMap[masterModule];
                } else {
                  module = '后台管理';
                }
              } else {
                module = '后台管理';
              }
            } else {
              // 其他业务模块
              const businessModule = pathParts[2];
              if (moduleMap[businessModule]) {
                module = moduleMap[businessModule];
              } else {
                module = moduleMap.default;
              }
            }
          } else {
            module = '接口服务';
          }
        } else {
          // 非API路径
          const nonApiPath = pathParts[0];
          if (nonApiPath === 'uploads') {
            module = '静态资源';
          } else if (nonApiPath === 'public') {
            module = '公共资源';
          } else {
            module = '未知模块';
          }
        }
        
        // 确保 module 和 client 是字符串类型
        module = typeof module === 'string' ? module : '未知模块';
        client = typeof client === 'string' ? client : '未知客户端';
        
        console.log(`[日志中间件] 识别的模块: ${module}`);
        console.log(`[日志中间件] 识别的客户端: ${client}`);
        
        // 获取基本操作类型，不包含查询参数
        const operation = determineOperation(req.method, pathParts);
        
        // 记录完整操作描述到日志，但不存入数据库
        const fullOperationDesc = `${module}/${operation}${req.query ? '?' + new URLSearchParams(req.query).toString() : ''}`;
        console.log(`[日志中间件] 开始创建日志记录: ${fullOperationDesc}`);
        
        try {
          // 将客户端信息添加到请求数据中
          if (requestData) {
            requestData._client = client;
          }
          
          // 创建日志记录
          if (prisma.systemOperationLog) {
            await prisma.systemOperationLog.create({
              data: {
                id: generateSnowflakeId(), // 使用雪花算法生成ID
                user_id: BigInt(userId),
                username,
                module,
                operation,
              method: req.method,
              path: req.path,
              ip: getClientIp(req),
              user_agent: req.headers['user-agent'],
              // client 字段暂时不保存到数据库，因为 Prisma 客户端尚未更新
              // 将客户端信息添加到请求数据中，以便在日志中查看
              request_data: {
                ...requestData,
                _client: client, // 添加下划线前缀，表示这是我们添加的元数据
                _fullPath: req.originalUrl || (req.baseUrl + req.path), // 保存完整请求路径
                _fullOperation: fullOperationDesc // 保存完整操作描述
              },
              response_data: responseData || undefined,
              status: res.statusCode >= 200 && res.statusCode < 400 ? 1 : 0,
              error_message: res.statusCode >= 400 ? responseData?.message || undefined : undefined,
              execution_time: executionTime,
              created_at: BigInt(Date.now())
              }
            });
          } else {
            console.log(`[日志中间件] 跳过日志记录创建，因为 systemOperationLog 模型不存在`);
          }
          console.log(`[日志中间件] 日志处理完成`);
        } catch (dbError) {
          console.error(`[日志中间件] 创建日志记录失败:`, dbError);
          // 不抛出异常，确保不影响主要业务流程
        }
      } catch (error) {
        console.error(`[日志中间件] 记录操作日志失败:`, error);
        console.error(`[日志中间件] 错误堆栈:`, error.stack);
        // 不抛出异常，确保不影响主要业务流程
      }
    });
  };
}

/**
 * 过滤敏感数据
 */
function filterSensitiveData(data, sensitiveFields) {
  if (!data) return data;
  
  const filtered = { ...data };
  sensitiveFields.forEach(field => {
    if (filtered[field]) {
      filtered[field] = '******';
    }
  });
  
  return filtered;
}

/**
 * 获取客户端IP
 */
function getClientIp(req) {
  return req.headers['x-forwarded-for'] || 
         req.connection.remoteAddress || 
         req.socket.remoteAddress || 
         req.connection.socket.remoteAddress;
}

/**
 * 确定操作类型
 * 注意：此函数返回的字符串长度不应超过100个字符（数据库字段限制）
 */
function determineOperation(method, pathParts) {
  // 获取资源名称（路径的最后一部分，并限制长度）
  let resource = pathParts[pathParts.length - 1] || 'unknown';
  
  // 限制资源名称长度，避免超过数据库字段限制
  if (resource.length > 30) {
    resource = resource.substring(0, 30);
  }
  
  // 特殊处理文件上传相关操作
  if (pathParts.length >= 6 && 
      pathParts[0] === 'api' && 
      pathParts[4] === 'integration' && 
      pathParts[5] === 'upload') {
    
    if (resource === 'file' && method === 'POST') {
      return '文件上传';
    } else if (resource === 'credentials' && method === 'POST') {
      return '获取上传凭证';
    } else if (resource === 'files' && method === 'GET') {
      return '查询文件列表';
    }
  }
  
  // 根据请求方法确定操作类型
  // 确保返回的字符串不会超过数据库字段限制
  switch (method) {
    case 'GET':
      return pathParts.length > 2 && !isNaN(pathParts[pathParts.length - 1]) 
        ? `查询${resource}详情` 
        : `查询${resource}列表`;
    case 'POST':
      return `创建${resource}`;
    case 'PUT':
      return `更新${resource}`;
    case 'DELETE':
      return `删除${resource}`;
    default:
      return `${method}操作`;
  }
}

module.exports = operationLogMiddleware;
