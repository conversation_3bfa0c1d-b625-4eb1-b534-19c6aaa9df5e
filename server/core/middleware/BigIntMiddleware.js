/**
 * BigInt 中间件
 * 用于处理 JSON 响应中的 BigInt 类型
 */

/**
 * 重写 JSON.stringify 方法，处理 BigInt 类型
 */
const setupBigIntHandler = () => {
  const originalJSONStringify = JSON.stringify;
  
  // 重写 JSON.stringify 方法
  JSON.stringify = function(value, replacer, space) {
    // 自定义 replacer 函数，处理 BigInt 类型
    const customReplacer = (key, value) => {
      // 如果是 BigInt 类型，转换为字符串
      if (typeof value === 'bigint') {
        return value.toString();
      }
      
      // 如果有自定义的 replacer，则调用它
      if (replacer) {
        return typeof replacer === 'function' ? replacer(key, value) : value;
      }
      
      return value;
    };
    
    return originalJSONStringify(value, customReplacer, space);
  };
};

/**
 * BigInt 中间件
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 * @param {Function} next 下一个中间件
 */
function bigIntMiddleware(req, res, next) {
  // 只需要在应用启动时执行一次
  if (!global.bigIntHandlerSetup) {
    setupBigIntHandler();
    global.bigIntHandlerSetup = true;
    console.log('已设置 BigInt 序列化处理器');
  }
  
  next();
}

module.exports = bigIntMiddleware;
