const jwt = require('jsonwebtoken');

/**
 * 可选认证中间件
 * 如果请求包含有效的token，则解析用户信息；如果没有token或token无效，则继续执行但不设置用户信息
 * 这样可以让某些接口在用户登录时提供额外功能，未登录时也能正常使用
 */
const optionalAuthMiddleware = (req, res, next) => {
  try {
    // 从请求头中获取token
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // 没有token，继续执行但不设置用户信息
      req.user = null;
      return next();
    }
    
    const token = authHeader.substring(7); // 移除 'Bearer ' 前缀
    
    if (!token) {
      // token为空，继续执行但不设置用户信息
      req.user = null;
      return next();
    }
    
    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    
    // 设置用户信息到请求对象
    req.user = decoded;
    
    next();
  } catch (error) {
    // token验证失败，继续执行但不设置用户信息
    console.warn('可选认证中间件：token验证失败，继续执行:', error.message);
    req.user = null;
    next();
  }
};

module.exports = optionalAuthMiddleware;
