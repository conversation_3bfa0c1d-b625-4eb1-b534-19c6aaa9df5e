const path = require('path');
const fs = require('fs').promises;
const EventEmitter = require('events');
const routeManager = require('../routes/RouteManager');

/**
 * 模块管理器
 * 负责模块的加载、初始化和生命周期管理
 */
class ModuleManager extends EventEmitter {
  constructor() {
    super();
    this.modules = new Map(); // 存储所有已注册的模块
    this.loadedModules = new Map(); // 存储已加载的模块实例
    this.moduleConfigs = new Map(); // 存储模块配置
  }

  /**
   * 扫描并加载所有模块
   */
  async scanModules() {
    try {
      // 扫描 apps 目录下的所有模块
      const modulesPath = path.join(process.cwd(), 'apps');
      const moduleDirs = await fs.readdir(modulesPath);

      // 读取每个模块的配置
      for (const dir of moduleDirs) {
        const modulePath = path.join(modulesPath, dir);
        const stat = await fs.stat(modulePath);
        
        if (stat.isDirectory()) {
          // 读取模块配置
          const configPath = path.join(modulePath, 'module.json');
          try {
            const configContent = await fs.readFile(configPath, 'utf-8');
            const config = JSON.parse(configContent);
            
            // 如果模块配置中没有指定 enabled 字段，默认为 true
            if (config.enabled === undefined) {
              config.enabled = true;
            }
            
            this.moduleConfigs.set(config.name, {
              ...config,
              path: modulePath
            });
          } catch (err) {
            console.warn(`无法加载模块 ${dir} 的配置: ${err.message}`);
          }
        }
      }

      // 按依赖顺序排序模块
      const sortedModules = this.sortModulesByDependencies();
      
      console.log('\n=== 模块状态 ===');
      // 初始化启用的模块
      for (const moduleConfig of sortedModules) {
        if (moduleConfig.enabled) {
          console.log(`[已启用] ${moduleConfig.name} - ${moduleConfig.description}`);
          await this.initializeModule(moduleConfig);
        } else {
          console.log(`[已禁用] ${moduleConfig.name} - ${moduleConfig.description}`);
        }
      }
      console.log('================\n');

      this.emit('modulesLoaded', Array.from(this.loadedModules.keys()));
    } catch (err) {
      console.error('模块扫描失败:', err);
      throw err;
    }
  }

  /**
   * 按依赖关系对模块进行排序
   */
  sortModulesByDependencies() {
    const visited = new Set();
    const sorted = [];

    const visit = (moduleName) => {
      if (visited.has(moduleName)) return;
      
      const moduleConfig = this.moduleConfigs.get(moduleName);
      if (!moduleConfig) return;

      visited.add(moduleName);

      // 先处理依赖
      for (const dep of (moduleConfig.dependencies || [])) {
        visit(dep);
      }

      sorted.push(moduleConfig);
    };

    // 遍历所有模块
    for (const [moduleName] of this.moduleConfigs) {
      visit(moduleName);
    }

    return sorted;
  }

  /**
   * 初始化单个模块
   */
  async initializeModule(moduleConfig) {
    try {
      // 检查依赖是否满足
      for (const dep of (moduleConfig.dependencies || [])) {
        if (!this.loadedModules.has(dep)) {
          throw new Error(`模块 ${moduleConfig.name} 依赖的模块 ${dep} 未加载`);
        }
      }

      // 加载模块
      const ModuleClass = require(path.join(moduleConfig.path, 'index.js'));
      const moduleInstance = new ModuleClass(moduleConfig, this);

      // 初始化模块
      await moduleInstance.init();

      // 存储已加载的模块实例
      this.loadedModules.set(moduleConfig.name, moduleInstance);
      this.emit('moduleLoaded', moduleConfig.name);

      console.log(`[成功] 模块 ${moduleConfig.name} 初始化完成`);
    } catch (err) {
      console.error(`模块 ${moduleConfig.name} 初始化失败:`, err);
      throw err;
    }
  }

  /**
   * 获取已加载的模块实例
   */
  getModule(moduleName) {
    return this.loadedModules.get(moduleName);
  }

  /**
   * 获取所有已加载的模块
   */
  getAllModules() {
    return Array.from(this.loadedModules.values());
  }

  /**
   * 启用模块
   * @param {string} moduleName 模块名称
   */
  async enableModule(moduleName) {
    const config = this.moduleConfigs.get(moduleName);
    if (!config) {
      throw new Error(`模块 ${moduleName} 不存在`);
    }

    if (!config.enabled) {
      config.enabled = true;
      
      // 更新配置文件
      const configPath = path.join(config.path, 'module.json');
      await fs.writeFile(configPath, JSON.stringify(config, null, 2), 'utf-8');
      
      // 初始化模块
      await this.initializeModule(config);
      console.log(`模块 ${moduleName} 已启用`);
    }
  }

  /**
   * 禁用模块
   * @param {string} moduleName 模块名称
   */
  async disableModule(moduleName) {
    const config = this.moduleConfigs.get(moduleName);
    if (!config) {
      throw new Error(`模块 ${moduleName} 不存在`);
    }

    if (config.enabled) {
      // 检查是否有其他模块依赖此模块
      for (const [name, otherConfig] of this.moduleConfigs) {
        if (otherConfig.enabled && otherConfig.dependencies?.includes(moduleName)) {
          throw new Error(`无法禁用模块 ${moduleName}，因为模块 ${name} 依赖于它`);
        }
      }

      config.enabled = false;
      
      // 更新配置文件
      const configPath = path.join(config.path, 'module.json');
      await fs.writeFile(configPath, JSON.stringify(config, null, 2), 'utf-8');
      
      // 清理模块资源
      const module = this.loadedModules.get(moduleName);
      if (module) {
        await module.cleanup();
        this.loadedModules.delete(moduleName);
      }
      console.log(`模块 ${moduleName} 已禁用`);
    }
  }

  /**
   * 注册所有模块的路由
   * @param {express.Application} app - Express应用实例
   */
  registerRoutes(app) {
    const API_VERSION = '/api/v1';
    
    for (const module of this.getAllModules()) {
      if (module.router && module.config.baseUrl) {
        // 将模块路由添加版本前缀
        const moduleBaseUrl = module.config.baseUrl.replace('/api', API_VERSION);
        
        // 添加到路由管理器
        routeManager.use(module.router, moduleBaseUrl);
        console.log(`已注册模块路由: ${module.config.name} -> ${moduleBaseUrl}`);
      }
    }
    
    // 初始化路由管理器并应用到应用
    app.use(routeManager.initialize());
  }
}

module.exports = ModuleManager;
