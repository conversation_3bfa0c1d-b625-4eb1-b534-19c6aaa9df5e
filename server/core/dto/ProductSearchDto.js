const Joi = require('joi');
const BaseDto = require('./BaseDto');

/**
 * 商品搜索数据传输对象
 */
class ProductSearchDto extends BaseDto {
  /**
   * 获取商品搜索验证模式
   * @returns {Object} Joi验证模式
   */
  static getSchema() {
    return Joi.object({
      keyword: Joi.string().allow('').optional().description('搜索关键词'),
      category: Joi.string().allow('').optional().description('商品分类'),
      brand: Joi.string().allow('').optional().description('品牌'),
      minPrice: Joi.number().min(0).optional().description('最低价格'),
      maxPrice: Joi.number().min(0).optional().description('最高价格'),
      tags: Joi.array().items(Joi.string()).optional().description('标签列表'),
      attributes: Joi.array().items(
        Joi.object({
          name: Joi.string().required().description('属性名'),
          value: Joi.string().required().description('属性值')
        })
      ).optional().description('属性过滤'),
      sort: Joi.string().valid('price_asc', 'price_desc', 'sales_desc', 'created_at_desc').optional().default('created_at_desc').description('排序方式'),
      page: Joi.number().integer().min(1).optional().default(1).description('页码'),
      pageSize: Joi.number().integer().min(1).max(100).optional().default(10).description('每页数量')
    });
  }

  /**
   * 验证商品搜索参数
   * @param {Object} data 搜索参数
   * @returns {Object} 验证结果
   */
  static validate(data) {
    return super.validate(data, this.getSchema());
  }
}

module.exports = ProductSearchDto;
