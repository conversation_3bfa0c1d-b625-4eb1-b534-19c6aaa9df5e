const Joi = require('joi');
const { camelToSnake } = require('../../shared/utils/format');

/**
 * 基础数据传输对象
 * 所有DTO都可以继承此类，以获取通用验证和格式转换功能
 */
class BaseDto {
  /**
   * 验证数据并转换字段格式
   * @param {Object} schema Joi验证模式
   * @param {Object} data 要验证的数据
   * @param {Object} options 验证选项
   * @returns {Object} 验证结果，包含转换后的数据
   */
  validate(schema, data, options = { abortEarly: false }) {
    const validationResult = schema.validate(data, options);

    // 如果验证通过，转换字段名格式
    if (!validationResult.error && validationResult.value) {
      validationResult.value = camelToSnake(validationResult.value);
    }

    return validationResult;
  }

  /**
   * 静态方法：验证数据并转换字段格式
   * @param {Object} data 要验证的数据
   * @param {Object} schema Joi验证模式
   * @param {Object} options 验证选项
   * @returns {Object} 验证结果，包含转换后的数据
   */
  static validate(data, schema, options = { abortEarly: false }) {
    const validationResult = schema.validate(data, options);

    // 如果验证通过，转换字段名格式
    if (!validationResult.error && validationResult.value) {
      validationResult.value = camelToSnake(validationResult.value);
    }

    return validationResult;
  }
}

module.exports = BaseDto;