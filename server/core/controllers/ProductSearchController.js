const BaseController = require('./BaseController');
const ElasticsearchService = require('../services/ElasticsearchService');
const ProductSearchDto = require('../dto/ProductSearchDto');
const config = require('../../config');

/**
 * 商品搜索控制器
 * 处理商品搜索相关的请求
 */
class ProductSearchController extends BaseController {
  constructor(prisma) {
    super(prisma);
    this.productIndex = config.elasticsearch.elasticsearch.indices.product.index;
  }

  /**
   * 搜索商品
   * @param {Object} req - Express请求对象
   * @param {Object} res - Express响应对象
   */
  async search(req, res) {
    try {
      // 验证搜索参数
      const validationResult = ProductSearchDto.validate(req.query);
      if (validationResult.error) {
        return this.fail(res, validationResult.error.message, null, 400);
      }

      const searchParams = validationResult.value;
      const { page, page_size, keyword, category, brand, min_price, max_price, tags, attributes, sort } = searchParams;

      // 构建Elasticsearch查询
      const query = this.buildSearchQuery(keyword, category, brand, min_price, max_price, tags, attributes);
      
      // 构建排序条件
      const sortOptions = this.buildSortOptions(sort);

      // 计算分页参数
      const from = (page - 1) * page_size;
      const size = page_size;

      // 执行搜索
      const searchResult = await ElasticsearchService.search(
        this.productIndex,
        query,
        from,
        size,
        sortOptions
      );

      // 构建响应数据
      const products = searchResult.hits.hits.map(hit => ({
        ...hit._source,
        score: hit._score
      }));

      const total = searchResult.hits.total.value || 0;

      // 返回搜索结果
      return this.successList(
        res,
        products,
        total,
        page,
        page_size,
        '搜索成功'
      );
    } catch (error) {
      console.error('搜索商品时出错:', error);
      return this.fail(res, '搜索失败', null, 500);
    }
  }

  /**
   * 构建搜索查询
   * @param {string} keyword - 搜索关键词
   * @param {string} category - 商品分类
   * @param {string} brand - 品牌
   * @param {number} minPrice - 最低价格
   * @param {number} maxPrice - 最高价格
   * @param {Array} tags - 标签列表
   * @param {Array} attributes - 属性过滤
   * @returns {Object} Elasticsearch查询对象
   */
  buildSearchQuery(keyword, category, brand, minPrice, maxPrice, tags, attributes) {
    // 构建查询条件
    const must = [];
    const filter = [];

    // 关键词搜索
    if (keyword) {
      must.push({
        multi_match: {
          query: keyword,
          fields: ['name^3', 'description'],
          type: 'best_fields',
          fuzziness: 'AUTO'
        }
      });
    }

    // 分类过滤
    if (category) {
      filter.push({
        term: { category_path: category }
      });
    }

    // 品牌过滤
    if (brand) {
      filter.push({
        term: { brand: brand }
      });
    }

    // 价格范围过滤
    if (minPrice !== undefined || maxPrice !== undefined) {
      const rangeQuery = { range: { price: {} } };
      
      if (minPrice !== undefined) {
        rangeQuery.range.price.gte = minPrice;
      }
      
      if (maxPrice !== undefined) {
        rangeQuery.range.price.lte = maxPrice;
      }
      
      filter.push(rangeQuery);
    }

    // 标签过滤
    if (tags && tags.length > 0) {
      filter.push({
        terms: { tags: tags }
      });
    }

    // 属性过滤
    if (attributes && attributes.length > 0) {
      attributes.forEach(attr => {
        filter.push({
          nested: {
            path: 'attributes',
            query: {
              bool: {
                must: [
                  { term: { 'attributes.name': attr.name } },
                  { term: { 'attributes.value': attr.value } }
                ]
              }
            }
          }
        });
      });
    }

    // 只显示上架状态的商品
    filter.push({
      term: { status: 'active' }
    });

    // 构建完整的查询
    const query = {
      bool: {
        must,
        filter
      }
    };

    return query;
  }

  /**
   * 构建排序条件
   * @param {string} sortOption - 排序选项
   * @returns {Array} 排序条件数组
   */
  buildSortOptions(sortOption) {
    const sortMap = {
      'price_asc': [{ price: 'asc' }],
      'price_desc': [{ price: 'desc' }],
      'sales_desc': [{ sales: 'desc' }],
      'created_at_desc': [{ created_at: 'desc' }]
    };

    return sortMap[sortOption] || sortMap['created_at_desc'];
  }

  /**
   * 获取商品搜索建议
   * @param {Object} req - Express请求对象
   * @param {Object} res - Express响应对象
   */
  async suggest(req, res) {
    try {
      const { keyword } = req.query;
      
      if (!keyword) {
        return this.success(res, [], '获取搜索建议成功');
      }

      // 构建搜索建议查询
      const suggestionQuery = {
        prefix: {
          'name.keyword': {
            value: keyword
          }
        }
      };

      // 执行搜索
      const searchResult = await ElasticsearchService.search(
        this.productIndex,
        suggestionQuery,
        0,
        10,
        []
      );

      // 提取建议结果
      const suggestions = searchResult.hits.hits.map(hit => ({
        id: hit._source.id,
        name: hit._source.name
      }));

      return this.success(res, suggestions, '获取搜索建议成功');
    } catch (error) {
      console.error('获取搜索建议时出错:', error);
      return this.fail(res, '获取搜索建议失败', null, 500);
    }
  }
}

module.exports = ProductSearchController;
