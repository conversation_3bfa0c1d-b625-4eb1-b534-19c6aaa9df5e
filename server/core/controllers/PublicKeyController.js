const BaseController = require('./BaseController');
const encryptionUtil = require('../utils/EncryptionUtil');

/**
 * 公钥控制器
 * 负责提供RSA公钥用于前端加密
 */
class PublicKeyController extends BaseController {
  constructor() {
    super();
  }
  
  /**
   * 获取公钥
   * @swagger
   * /api/v1/common/public-key:
   *   get:
   *     tags: [公共服务/安全]
   *     summary: 获取RSA公钥
   *     description: 获取用于密码加密的RSA公钥
   *     responses:
   *       200:
   *         description: 成功获取公钥
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 0
   *                 message:
   *                   type: string
   *                   example: 获取公钥成功
   *                 data:
   *                   type: object
   *                   properties:
   *                     publicKey:
   *                       type: string
   *                       description: RSA公钥
   */
  getPublicKey(req, res) {
    try {
      // 获取RSA公钥
      const publicKey = encryptionUtil.getPublicKey();
      
      // 返回公钥
      this.success(res, { publicKey }, '获取公钥成功');
    } catch (err) {
      console.error('获取公钥失败:', err.message);
      this.fail(res, '获取公钥失败', 500);
    }
  }
}

module.exports = PublicKeyController;
