const BaseController = require('./BaseController');
const CaptchaService = require('../services/CaptchaService');

/**
 * 验证码控制器
 * 负责生成和验证验证码
 * 多端共用的公共控制器
 */
class CaptchaController extends BaseController {
  constructor() {
    super();
    this.captchaService = new CaptchaService();
  }
  
  /**
   * 生成验证码
   * @swagger
   * /api/v1/common/captcha/generate:
   *   get:
   *     tags: [公共服务/验证码]
   *     summary: 生成验证码
   *     description: 生成图形验证码，用于登录验证
   *     parameters:
   *       - in: query
   *         name: width
   *         schema:
   *           type: integer
   *         description: 验证码图片宽度
   *       - in: query
   *         name: height
   *         schema:
   *           type: integer
   *         description: 验证码图片高度
   *     responses:
   *       200:
   *         description: 成功生成验证码
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 0
   *                 message:
   *                   type: string
   *                   example: 验证码生成成功
   *                 data:
   *                   type: object
   *                   properties:
   *                     id:
   *                       type: string
   *                       description: 验证码ID，用于验证
   *                     image:
   *                       type: string
   *                       description: 验证码SVG图片
   */
  async generateCaptcha(req, res) {
    try {
      // 从请求参数中获取自定义选项
      const { width, height } = req.query;
      
      // 构建验证码选项
      const options = {};
      if (width) options.width = parseInt(width);
      if (height) options.height = parseInt(height);
      
      // 获取客户端IP
      const clientIp = req.headers['x-forwarded-for'] || 
                      req.connection.remoteAddress ||
                      req.socket.remoteAddress;
      
      // 检查IP是否被锁定（尝试次数过多）
      //const isLocked = await this.captchaService.isIpLocked(clientIp);
      //if (isLocked) {
      //  return this.fail(res, '尝试次数过多，请稍后再试', 429);
      //}
      
      // 生成验证码
      const captcha = await this.captchaService.generateCaptcha(options);
      
      // 返回成功响应，包含验证码ID和图片
      this.success(res, captcha, '验证码生成成功');
    } catch (err) {
      // 处理错误
      const { message, code } = this.handleError(err);
      this.fail(res, message, code);
    }
  }
  
  /**
   * 处理错误
   * @param {Error} err - 错误对象
   * @returns {Object} 包含错误消息和状态码的对象
   */
  handleError(err) {
    console.error('验证码控制器错误:', err.message);
    
    // 判断错误类型和消息
    let message = err.message || '验证码服务错误';
    let code = 500;
    
    if (message.includes('过期') || message.includes('无效')) {
      code = 400;
    }
    
    return { message, code };
  }
}

module.exports = CaptchaController;
