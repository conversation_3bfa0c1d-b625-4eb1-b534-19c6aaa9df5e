/**
 * 基础控制器类
 * 提供通用的响应处理、错误处理和分页功能
 */
class BaseController {
    constructor(prisma = null) {
        this.prisma = prisma;
    }

    /**
     * 成功响应
     * @param {Object} res - Express响应对象
     * @param {*} data - 响应数据
     * @param {String} message - 成功消息
     * @param {Number} code - 状态码
     */
    success(res, data = null, message = '操作成功', code = 200) {
        // 处理 BigInt 类型，将其转换为字符串
        const replacer = (key, value) => {
            if (typeof value === 'bigint') {
                return value.toString();
            }
            return value;
        };

        // 使用 replacer 先将数据转换为 JSON 字符串，然后再解析回 JSON 对象
        const jsonString = JSON.stringify({ code, message, data }, replacer);
        const jsonData = JSON.parse(jsonString);

        return res.status(200).json(jsonData);
    }

    /**
     * 失败响应
     * @param {Object} res - Express响应对象
     * @param {string} message - 错误消息
     * @param {*} data - 响应数据
     * @param {number} code - 错误代码
     * @returns {Object} 统一格式的错误响应
     */
    fail(res, message = '操作失败', data = null, code = 500) {
        // 处理 BigInt 类型，将其转换为字符串
        const replacer = (key, value) => {
          if (typeof value === 'bigint') {
            return value.toString();
          }
          return value;
        };

        // 使用 replacer 先将数据转换为 JSON 字符串，然后再解析回 JSON 对象
        const jsonString = JSON.stringify({ code, message, data }, replacer);
        const jsonData = JSON.parse(jsonString);

        // 使用传入的code作为HTTP状态码
        return res.status(code).json(jsonData);
    }

    /**
     * 处理分页参数
     * @param {Object} query - 请求查询参数
     * @returns {Object} 标准化的分页参数
     */
    getPagination(query) {
        const page = parseInt(query.page) || 1;
        const pageSize = parseInt(query.pageSize) || 10;
        return {
            skip: (page - 1) * pageSize,
            take: pageSize,
            page,
            pageSize
        };
    }

    /**
     * 构建分页响应
     * @param {Array} list - 数据列表
     * @param {number} total - 总记录数
     * @param {Object} pagination - 分页参数
     * @returns {Object} 标准化的分页响应数据
     */
    buildPaginationResponse(list, total, { page, pageSize }) {
        return {
            list,
            pagination: {
                total,
                page,
                pageSize,
                totalPages: Math.ceil(total / pageSize)
            }
        };
    }

    /**
     * 返回标准列表数据
     * @param {Response} res - Express响应对象
     * @param {Array} items - 列表数据项
     * @param {number} total - 总记录数
     * @param {number} currentPage - 当前页码
     * @param {number} pageSize - 每页记录数
     * @param {string} message - 响应消息
     * @param {number} code - 状态码
     */
    successList(res, items, total, currentPage, pageSize, message = '请求成功', code = 200) {
        // 处理BigInt序列化
        const serializedItems = JSON.parse(JSON.stringify(items, (key, value) => {
            if (typeof value === 'bigint') {
                return value.toString();
            }
            return value;
        }));

        const totalPage = Math.ceil(total / pageSize);
        res.status(code).json({
            success: true,
            message,
            code,
            data: {
                items: serializedItems,
                pageInfo: {
                    total,
                    currentPage,
                    totalPage
                }
            }
        });
    }

    /**
     * 处理数据库错误
     * @param {Error} error - 数据库错误对象
     * @returns {Object} 标准化的错误信息
     */
    handleDbError(error) {
        // Prisma 错误处理
        if (error.code) {
            switch (error.code) {
                case 'P2002': // 唯一约束冲突
                    return {
                        message: '记录已存在',
                        code: 400
                    };
                case 'P2003': // 外键约束失败
                    return {
                        message: '关联记录不存在',
                        code: 400
                    };
                case 'P2025': // 记录不存在
                    return {
                        message: '记录不存在',
                        code: 404
                    };
                default:
                    return {
                        message: '数据库操作失败',
                        code: 500
                    };
            }
        }
        
        return {
            message: error.message || '未知错误',
            code: 500
        };
    }

    /**
     * 验证请求参数
     * @param {Object} data - 请求数据
     * @param {Array} requiredFields - 必填字段列表
     * @returns {Object} 验证结果
     */
    validateFields(data, requiredFields) {
        const missingFields = requiredFields.filter(field => data[field] === undefined || data[field] === null || data[field] === '');
        if (missingFields.length > 0) {
            return {
                valid: false,
                message: `缺少必填字段: ${missingFields.join(', ')}`
            };
        }
        return { valid: true };
    }
}

module.exports = BaseController;
