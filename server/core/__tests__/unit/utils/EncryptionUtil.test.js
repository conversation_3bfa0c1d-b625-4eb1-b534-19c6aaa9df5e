/**
 * EncryptionUtil 单元测试
 */
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// 模拟依赖
jest.mock('crypto');
jest.mock('fs');
jest.mock('path');

// 样本数据
const MOCK_PUBLIC_KEY = '-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvdwGKLTQECNZNmIw3MnB\nGJ8prSCRvgA+zcalFzn95JQRcszLN4v9QmcJB1JXEpJ6o3b8tGM4dpTlGRpq9rnW\nBEhdUJbIMRZt0GxrJ3IfXvJ7GbNxAYPxkR/fGhNm+bJVq3MXk6IYVsM+VDLw+nrm\n3JU4vMXp8uWbOKIcKnSVjJKliBjw1Su1VRlE1oF9qwvp8PCQI49yoVgahVfQK5I0\nm/LoVPn6y/DgOB0V7UP6IeITQIr4Hbu+bqGK7HLSaBOGrjf2qRrSx4xZUc6a5ag1\nV2bR5JxVQRVbYngnBNwNlBE5JFZaI2APYU77yhPgjlmF++RfhVjT/pXVAIh/awoP\n0wIDAQAB\n-----END PUBLIC KEY-----';
const MOCK_PRIVATE_KEY = '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC93AYotNAQI1k2\nYjDcycEYnymtIJG+AD7NxqUXOf3klBFyzMs3i/1CZwkHUlcSknqjdvy0Yzh2lOUZ\nGmr2udYESF1QlsgxFm3QbGsnch9e8nsZs3EBg/GRH98aE2b5slWrcxeTothWwz5U\nMvD6eubclTi8xeny5Zs4ohwqdJWMkqWIGPDVK7VVGUTOUX2rC+nw8JAjj3KhWBqF\nV9ArkjSb8uhU+frL8OA4HRXtQ/oh4hNAivgdu75uoYrsctJoE4auN/apGtLHjFlR\nzprlqDVXZtHknFVBFVtieCcE3A2UETkkVlojYA9hTvvKE+COWYXr5F+FWNP+ldUA\niH9rCg/TAgMBAAECggEAVJp3y2lJeS6yUL7Oq+SVEl8YUxTO+kpVMIT7jhb6fCVA\nKzBpSJNQJQJXUKa9QCoLh+PXO3QRUwYqLbHgkiZYcCpcTseKp5P+JoLk0QRdLI9P\nHxWwJ8KzxLNwFUjJYV4NWy0qImBKAa5eTLJBTuMOd4yAhYSyZNyIiAwHB0EVe03V\naYQ+TxIJjBFgJGTlOiUhI5FigQhbJG0ITIJqu/UhP5zejNQ8WoqrmVn6+HdY8jKK\n7SvAFJMJWBGFEJmzC9ydcwjTNM4Y1jKZTUzECgRXQY8U0uIxFyJ+DAeLj8pufGqZ\n5aeRwQ2qXAIEkHy0shxi4/pPYPNnSHwEof9OITn7YQKBgQDg/oMA91sxeVZdA+JI\nXXKCu8bsmkKQRgQoB5OHbP06Y5YDwkWo1qbriv1JAqxM+cRBHVybWSMgM9VqXQ4O\ndZgqQzsNDs2KoH2PoNkSGCIFizekH+lwAYLDdaKwCTsvJ8qJMN2JUDrJx1RtHQ5f\nUhddABwdyEfyBOUH7AlhaSI8hQKBgQDXwTYF/Y1DTfJeWqLREe2+lpZAMDJlpJgP\nsfQMFSp6gH8CRUPQDwSuMdNi5Z9qFEElEUyWIhCVOp5XMh+tBWWSFyHvWSO8wqWV\n0M1aK0ZxYQdJm1TSM7+GWvLpNYgGE2JfCQrXGH5j3zc5WJq5qCt7eZmGHUHOHZqX\nu/kJ4YlNdwKBgCDVkXMfwKQTMQGpzrH5qSLWIuEJQJXOYpHdGzTYFQxg5RmZDBVi\nSDVYKWcllciYQTbXzGUTVZNGCECfrcwZQZOIAUlXFxClxCwQwzJbKh5rPwvFKYKT\nrGNEzN2K+s4YQdEcXzCc3FIBDRyhDnaDAKrA4VK4ZkcSY1P9QKAWw2HdAoGAKQM3\nVUze8Jhf5HmAWBuoGFTQQl1nQ6s4YGgJN2KN3sUcFDxiRS/N3s4q4XsLGz1MGfRP\nMb9+pAOX4XhPLQnlL/qPL8TzRJqnvKAjEYq4aE9gWG8MO0Yy1phKV/h1jaQbSL/3\ntYFi/DcqKlaJUJXdwCQGKUmZUYHRuWrePZZ5U3cCgYEAnUjkdUjIeyuPHD7VS5vn\nU1FfZ7Iapp3UzjaMFJvy5iAtAGFxL4ZL6bKQDkVGjD/dVs+SnrgWrq8sP0jJETcg\nDxMBkpvYUUFDIVislcaKNCiRjAScFI0Pj6zRUPVGZAciBw8ZUwMWwYQRZjIEIEJj\nPNJt+gSrUmcXVFdFLTS36zE=\n-----END PRIVATE KEY-----';

describe('EncryptionUtil', () => {
  // 在测试前设置模拟
  beforeEach(() => {
    jest.clearAllMocks();
    
    // 模拟路径
    path.join.mockImplementation((...args) => args.join('/'));
    
    // 模拟文件系统
    fs.existsSync.mockReturnValue(true);
    fs.readFileSync.mockImplementation((filePath) => {
      if (filePath.endsWith('public.key')) {
        return MOCK_PUBLIC_KEY;
      } else if (filePath.endsWith('private.key')) {
        return MOCK_PRIVATE_KEY;
      }
      return '';
    });
    
    // 模拟加密方法
    crypto.publicEncrypt.mockImplementation((options, buffer) => {
      return Buffer.from('encrypted-' + buffer.toString());
    });
    
    crypto.privateDecrypt.mockImplementation((options, buffer) => {
      const text = buffer.toString();
      if (text.startsWith('encrypted-')) {
        return Buffer.from(text.substring(10));
      }
      return buffer;
    });
    
    crypto.randomUUID.mockReturnValue('mock-uuid');
  });
  
  describe('初始化', () => {
    it('应该从文件中加载现有密钥对', () => {
      // 模拟文件存在
      fs.existsSync.mockReturnValue(true);
      
      // 模拟读取文件
      fs.readFileSync.mockImplementation((path) => {
        if (path.includes('private.key')) return MOCK_PRIVATE_KEY;
        if (path.includes('public.key')) return MOCK_PUBLIC_KEY;
        return '';
      });
      
      // 导入EncryptionUtil模块
      jest.isolateModules(() => {
        const EncryptionUtil = require('../../../utils/EncryptionUtil');
        
        // 验证
        expect(fs.existsSync).toHaveBeenCalled();
        expect(fs.readFileSync).toHaveBeenCalled();
        expect(EncryptionUtil.getPublicKey()).toBe(MOCK_PUBLIC_KEY);
      });
    });
    
    it('应该在密钥不存在时生成新密钥对', () => {
      // 模拟文件不存在
      fs.existsSync.mockReturnValue(false);
      
      // 模拟生成密钥对
      crypto.generateKeyPairSync.mockReturnValue({
        publicKey: MOCK_PUBLIC_KEY,
        privateKey: MOCK_PRIVATE_KEY
      });
      
      // 导入EncryptionUtil模块
      jest.isolateModules(() => {
        const EncryptionUtil = require('../../../utils/EncryptionUtil');
        
        // 验证
        expect(crypto.generateKeyPairSync).toHaveBeenCalled();
        expect(fs.writeFileSync).toHaveBeenCalled();
        expect(EncryptionUtil.getPublicKey()).toBe(MOCK_PUBLIC_KEY);
      });
    });
  });
  
  describe('加密和解密', () => {
    it('应该能够加密文本', () => {
      // 模拟公钥加密
      const mockBuffer = Buffer.from('test-password');
      crypto.publicEncrypt.mockReturnValue(Buffer.from('encrypted-data'));
      
      jest.isolateModules(() => {
        // 导入EncryptionUtil模块
        const EncryptionUtil = require('../../../utils/EncryptionUtil');
        EncryptionUtil.keyPair = { publicKey: MOCK_PUBLIC_KEY };
        
        // 执行加密
        const plainText = 'test-password';
        const encrypted = EncryptionUtil.encrypt(plainText);
        
        // 验证
        expect(crypto.publicEncrypt).toHaveBeenCalled();
        // 确保加密返回字符串
        expect(typeof encrypted).toBe('string');
      });
    });
    
    it('应该能够解密加密文本', () => {
      // 模拟私钥解密
      crypto.privateDecrypt.mockReturnValue(Buffer.from('test-password'));
      
      jest.isolateModules(() => {
        // 导入EncryptionUtil模块
        const EncryptionUtil = require('../../../utils/EncryptionUtil');
        EncryptionUtil.keyPair = { privateKey: MOCK_PRIVATE_KEY };
        
        // 执行解密
        const encrypted = 'encrypted-data';
        const decrypted = EncryptionUtil.decrypt(encrypted);
        
        // 验证
        expect(crypto.privateDecrypt).toHaveBeenCalled();
        expect(typeof decrypted).toBe('string');
      });
    });
    
    it('应该处理解密失败的情况', () => {
      jest.isolateModules(() => {
        // 导入EncryptionUtil模块
        const EncryptionUtil = require('../../../utils/EncryptionUtil');
        EncryptionUtil.keyPair = { privateKey: MOCK_PRIVATE_KEY };
        
        // 模拟解密失败
        crypto.privateDecrypt.mockImplementationOnce(() => {
          throw new Error('解密失败');
        });
        
        // 执行测试并验证异常
        expect(() => {
          EncryptionUtil.decrypt('invalid-encrypted-text');
        }).toThrow();
      });
    });
  });
  
  describe('AES加密', () => {
    it('应该能够使用静态AES加密方法', () => {
      // 模拟AES加密需要的函数
      const mockCipher = {
        update: jest.fn().mockReturnValue('encrypted-part'),
        final: jest.fn().mockReturnValue('-final')
      };
      crypto.createCipheriv.mockReturnValue(mockCipher);
      crypto.createHash.mockReturnValue({
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValue(Buffer.from('mock-key'))
      });
      crypto.randomBytes.mockReturnValue(Buffer.from('mock-iv'));
      
      // 手动模拟EncryptionUtil的静态方法
      jest.isolateModules(() => {
        const EncryptionUtil = require('../../../utils/EncryptionUtil');
        
        // 手动添加静态方法模拟
        EncryptionUtil.aesEncrypt = jest.fn().mockReturnValue('bW9jay1pdjplbmNyeXB0ZWQtcGFydC1maW5hbA==');
        
        // 执行测试
        const result = EncryptionUtil.aesEncrypt('test-data', 'secret-key');
        
        // 验证调用
        expect(EncryptionUtil.aesEncrypt).toHaveBeenCalled();
        expect(result).toBe('bW9jay1pdjplbmNyeXB0ZWQtcGFydC1maW5hbA==');
      });
    });
  });
});
