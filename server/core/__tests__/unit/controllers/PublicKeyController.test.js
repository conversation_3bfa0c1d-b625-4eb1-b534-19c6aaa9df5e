/**
 * PublicKeyController 单元测试
 */
const PublicKeyController = require('../../../controllers/PublicKeyController');
const encryptionUtil = require('../../../utils/EncryptionUtil');

// 模拟依赖
jest.mock('../../../utils/EncryptionUtil', () => ({
  getPublicKey: jest.fn()
}));

describe('PublicKeyController', () => {
  let controller;
  let mockReq;
  let mockRes;
  
  beforeEach(() => {
    // 清理所有 mock
    jest.clearAllMocks();
    
    // 创建被测试的控制器实例
    controller = new PublicKeyController();
    
    // 模拟请求和响应对象
    mockReq = {};
    
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    
    // 模拟控制器基类方法
    controller.success = jest.fn();
    controller.fail = jest.fn();
  });
  
  /**
   * 获取公钥方法测试
   */
  describe('getPublicKey()', () => {
    it('应该成功返回公钥', () => {
      // 准备测试数据
      const mockPublicKey = '-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvdwGKLTQECNZNmIw3MnB\nGJ8prSCRvgA+zcalFzn95JQRcszLN4v9QmcJB1JXEpJ6o3b8tGM4dpTlGRpq9rnW\nBEhdUJbIMRZt0GxrJ3IfXvJ7GbNxAYPxkR/fGhNm+bJVq3MXk6IYVsM+VDLw+nrm\n3JU4vMXp8uWbOKIcKnSVjJKliBjw1Su1VRlE1oF9qwvp8PCQI49yoVgahVfQK5I0\nm/LoVPn6y/DgOB0V7UP6IeITQIr4Hbu+bqGK7HLSaBOGrjf2qRrSx4xZUc6a5ag1\nV2bR5JxVQRVbYngnBNwNlBE5JFZaI2APYU77yhPgjlmF++RfhVjT/pXVAIh/awoP\n0wIDAQAB\n-----END PUBLIC KEY-----';
      
      // 模拟 encryptionUtil.getPublicKey() 返回公钥
      encryptionUtil.getPublicKey.mockReturnValue(mockPublicKey);
      
      // 执行测试
      controller.getPublicKey(mockReq, mockRes);
      
      // 验证结果
      expect(encryptionUtil.getPublicKey).toHaveBeenCalled();
      expect(controller.success).toHaveBeenCalledWith(
        mockRes,
        { publicKey: mockPublicKey },
        '获取公钥成功'
      );
    });
    
    it('应该处理获取公钥失败的情况', () => {
      // 模拟 encryptionUtil.getPublicKey() 抛出异常
      encryptionUtil.getPublicKey.mockImplementation(() => {
        throw new Error('获取公钥失败');
      });
      
      // 执行测试
      controller.getPublicKey(mockReq, mockRes);
      
      // 验证结果
      expect(encryptionUtil.getPublicKey).toHaveBeenCalled();
      expect(controller.fail).toHaveBeenCalledWith(
        mockRes,
        '获取公钥失败',
        500
      );
    });
  });
});
