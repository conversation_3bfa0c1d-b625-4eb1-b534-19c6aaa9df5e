const { Client } = require('@elastic/elasticsearch');
const config = require('../../config');

/**
 * Elasticsearch服务类
 * 用于处理与Elasticsearch的连接和操作
 */
class ElasticsearchService {
  constructor() {
    this.client = null;
    this.config = config.elasticsearch.elasticsearch;
    this.init();
  }

  /**
   * 初始化Elasticsearch客户端
   */
  init() {
    try {
      this.client = new Client({
        node: this.config.node,
        auth: this.config.auth.username ? {
          username: this.config.auth.username,
          password: this.config.auth.password
        } : null,
        maxRetries: this.config.maxRetries,
        requestTimeout: this.config.requestTimeout,
        // 添加兼容性设置，确保与ES 8.x版本兼容
        tls: {
          rejectUnauthorized: false
        }
      });
      console.log('Elasticsearch客户端初始化成功');
    } catch (error) {
      console.error('Elasticsearch客户端初始化失败:', error);
    }
  }

  /**
   * 检查索引是否存在
   * @param {string} index 索引名称
   * @returns {Promise<boolean>} 是否存在
   */
  async indexExists(index) {
    try {
      const result = await this.client.indices.exists({ index });
      return result;
    } catch (error) {
      console.error(`检查索引 ${index} 是否存在时出错:`, error);
      return false;
    }
  }

  /**
   * 创建索引
   * @param {string} index 索引名称
   * @param {Object} settings 索引设置
   * @param {Object} mappings 索引映射
   * @returns {Promise<boolean>} 是否成功
   */
  async createIndex(index, settings = {}, mappings = {}) {
    try {
      const indexExists = await this.indexExists(index);
      if (indexExists) {
        console.log(`索引 ${index} 已存在`);
        return true;
      }

      const result = await this.client.indices.create({
        index,
        settings,
        mappings
      });

      console.log(`索引 ${index} 创建成功:`, result);
      return true;
    } catch (error) {
      console.error(`创建索引 ${index} 时出错:`, error);
      return false;
    }
  }

  /**
   * 删除索引
   * @param {string} index 索引名称
   * @returns {Promise<boolean>} 是否成功
   */
  async deleteIndex(index) {
    try {
      const indexExists = await this.indexExists(index);
      if (!indexExists) {
        console.log(`索引 ${index} 不存在`);
        return true;
      }

      const result = await this.client.indices.delete({ index });
      console.log(`索引 ${index} 删除成功:`, result);
      return true;
    } catch (error) {
      console.error(`删除索引 ${index} 时出错:`, error);
      return false;
    }
  }

  /**
   * 添加或更新文档
   * @param {string} index 索引名称
   * @param {string} id 文档ID
   * @param {Object} document 文档内容
   * @returns {Promise<Object>} 操作结果
   */
  async indexDocument(index, id, document) {
    try {
      const result = await this.client.index({
        index,
        id,
        document: document
      });
      return result;
    } catch (error) {
      console.error(`索引文档时出错:`, error);
      throw error;
    }
  }

  /**
   * 批量添加或更新文档
   * @param {string} index 索引名称
   * @param {Array<Object>} documents 文档数组，每个文档必须包含id字段
   * @returns {Promise<Object>} 操作结果
   */
  async bulkIndex(index, documents) {
    try {
      const operations = documents.flatMap(doc => [
        { index: { _index: index, _id: doc.id } },
        doc
      ]);

      const result = await this.client.bulk({ operations });
      return result;
    } catch (error) {
      console.error(`批量索引文档时出错:`, error);
      throw error;
    }
  }

  /**
   * 获取文档
   * @param {string} index 索引名称
   * @param {string} id 文档ID
   * @returns {Promise<Object>} 文档内容
   */
  async getDocument(index, id) {
    try {
      const result = await this.client.get({
        index,
        id
      });
      return result;
    } catch (error) {
      if (error.meta && error.meta.statusCode === 404) {
        return null;
      }
      console.error(`获取文档时出错:`, error);
      throw error;
    }
  }

  /**
   * 删除文档
   * @param {string} index 索引名称
   * @param {string} id 文档ID
   * @returns {Promise<Object>} 操作结果
   */
  async deleteDocument(index, id) {
    try {
      const result = await this.client.delete({
        index,
        id
      });
      return result;
    } catch (error) {
      console.error(`删除文档时出错:`, error);
      throw error;
    }
  }

  /**
   * 搜索文档
   * @param {string} index 索引名称
   * @param {Object} query 查询条件
   * @param {number} from 起始位置
   * @param {number} size 返回数量
   * @param {Array<Object>} sort 排序条件
   * @returns {Promise<Object>} 搜索结果
   */
  async search(index, query, from = 0, size = 10, sort = []) {
    try {
      const result = await this.client.search({
        index,
        from,
        size,
        sort,
        query
      });
      return result;
    } catch (error) {
      console.error(`搜索文档时出错:`, error);
      throw error;
    }
  }
}

module.exports = new ElasticsearchService();
