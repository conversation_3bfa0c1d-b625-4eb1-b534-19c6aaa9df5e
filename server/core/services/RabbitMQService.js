/**
 * RabbitMQ 服务类
 * 用于处理与 RabbitMQ 的连接和通信
 */
const amqp = require('amqplib');
const config = require('../../config/rabbitmq.config');
const logger = require('../utils/logger').createLogger('RabbitMQ服务');

class RabbitMQService {
  constructor() {
    this.connection = null;
    this.channel = null;
    this.connected = false;
    this.connecting = false;
    this.reconnectTimeout = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 10;
    this.reconnectInterval = 5000; // 5秒
  }

  /**
   * 连接到 RabbitMQ 服务器
   * @returns {Promise<void>}
   */
  async connect() {
    if (this.connected || this.connecting) {
      return;
    }

    this.connecting = true;
    try {
      logger.info('正在连接到 RabbitMQ...');
      this.connection = await amqp.connect(config.connection);
      
      this.connection.on('error', (err) => {
        logger.error('RabbitMQ 连接错误:', err);
        this.handleDisconnect();
      });
      
      this.connection.on('close', () => {
        logger.warn('RabbitMQ 连接已关闭');
        this.handleDisconnect();
      });
      
      this.channel = await this.connection.createChannel();
      this.connected = true;
      this.connecting = false;
      this.reconnectAttempts = 0;
      
      logger.info('已成功连接到 RabbitMQ');
      
      // 设置交换机
      await this.setupExchanges();
      
      // 设置队列
      await this.setupQueues();
      
      return this.channel;
    } catch (error) {
      this.connecting = false;
      logger.error('RabbitMQ 连接失败:', error);
      this.handleDisconnect();
      throw error;
    }
  }

  /**
   * 处理断开连接的情况
   * @private
   */
  handleDisconnect() {
    this.connected = false;
    this.channel = null;
    
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
    }
    
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      logger.info(`尝试重新连接到 RabbitMQ (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
      this.reconnectTimeout = setTimeout(() => this.connect(), this.reconnectInterval);
    } else {
      logger.error('已达到最大重连次数，停止尝试连接 RabbitMQ');
    }
  }

  /**
   * 设置交换机
   * @private
   * @returns {Promise<void>}
   */
  async setupExchanges() {
    try {
      for (const key in config.exchanges) {
        const exchange = config.exchanges[key];
        await this.channel.assertExchange(
          exchange.name,
          exchange.type,
          exchange.options
        );
        logger.info(`已创建交换机: ${exchange.name}`);
      }
    } catch (error) {
      logger.error('设置交换机失败:', error);
      throw error;
    }
  }

  /**
   * 设置队列
   * @private
   * @returns {Promise<void>}
   */
  async setupQueues() {
    try {
      for (const key in config.queues) {
        const queue = config.queues[key];
        await this.channel.assertQueue(queue.name, queue.options);
        logger.info(`已创建队列: ${queue.name}`);
        
        // 如果队列有绑定键，则绑定到对应的交换机
        if (queue.bindingKey) {
          const exchangeName = config.exchanges[key]?.name || config.exchanges.default.name;
          await this.channel.bindQueue(queue.name, exchangeName, queue.bindingKey);
          logger.info(`已将队列 ${queue.name} 绑定到交换机 ${exchangeName} (bindingKey: ${queue.bindingKey})`);
        }
      }
    } catch (error) {
      logger.error('设置队列失败:', error);
      throw error;
    }
  }

  /**
   * 发送消息到指定的交换机
   * @param {string} exchangeKey 交换机键名
   * @param {string} routingKey 路由键
   * @param {Object} message 消息内容
   * @param {Object} options 发送选项
   * @returns {Promise<boolean>}
   */
  async publish(exchangeKey, routingKey, message, options = {}) {
    if (!this.connected) {
      await this.connect();
    }
    
    try {
      const exchange = config.exchanges[exchangeKey] || config.exchanges.default;
      const content = Buffer.from(JSON.stringify(message));
      const defaultOptions = { persistent: true };
      const publishOptions = { ...defaultOptions, ...options };
      
      const result = this.channel.publish(exchange.name, routingKey, content, publishOptions);
      logger.info(`消息已发送到交换机 ${exchange.name}，路由键: ${routingKey}`);
      return result;
    } catch (error) {
      logger.error('发送消息失败:', error);
      throw error;
    }
  }

  /**
   * 发送消息到指定的队列
   * @param {string} queueKey 队列键名
   * @param {Object} message 消息内容
   * @param {Object} options 发送选项
   * @returns {Promise<boolean>}
   */
  async sendToQueue(queueKey, message, options = {}) {
    if (!this.connected) {
      await this.connect();
    }
    
    try {
      const queue = config.queues[queueKey];
      if (!queue) {
        throw new Error(`队列 ${queueKey} 不存在`);
      }
      
      const content = Buffer.from(JSON.stringify(message));
      const defaultOptions = { persistent: true };
      const sendOptions = { ...defaultOptions, ...options };
      
      const result = this.channel.sendToQueue(queue.name, content, sendOptions);
      logger.info(`消息已发送到队列 ${queue.name}`);
      return result;
    } catch (error) {
      logger.error('发送消息到队列失败:', error);
      throw error;
    }
  }

  /**
   * 消费指定队列的消息
   * @param {string} queueKey 队列键名
   * @param {Function} callback 回调函数
   * @param {Object} options 消费选项
   * @returns {Promise<Object>} 消费者标签
   */
  async consume(queueKey, callback, options = {}) {
    if (!this.connected) {
      await this.connect();
    }
    
    try {
      const queue = config.queues[queueKey];
      if (!queue) {
        throw new Error(`队列 ${queueKey} 不存在`);
      }
      
      const defaultOptions = { noAck: false };
      const consumeOptions = { ...defaultOptions, ...options };
      
      const { consumerTag } = await this.channel.consume(
        queue.name,
        async (msg) => {
          if (msg === null) {
            logger.warn(`消费者被取消，队列: ${queue.name}`);
            return;
          }
          
          try {
            const content = JSON.parse(msg.content.toString());
            await callback(content, msg);
            this.channel.ack(msg);
          } catch (error) {
            logger.error(`处理消息失败，队列: ${queue.name}`, error);
            // 根据错误类型决定是否重新入队
            if (error.retryable !== false) {
              this.channel.nack(msg, false, true);
            } else {
              this.channel.nack(msg, false, false);
            }
          }
        },
        consumeOptions
      );
      
      logger.info(`已开始消费队列 ${queue.name}，消费者标签: ${consumerTag}`);
      return { consumerTag };
    } catch (error) {
      logger.error(`开始消费队列失败，队列键: ${queueKey}`, error);
      throw error;
    }
  }

  /**
   * 关闭 RabbitMQ 连接
   * @returns {Promise<void>}
   */
  async close() {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
    
    if (this.channel) {
      await this.channel.close();
      this.channel = null;
    }
    
    if (this.connection) {
      await this.connection.close();
      this.connection = null;
    }
    
    this.connected = false;
    logger.info('RabbitMQ 连接已关闭');
  }
}

// 创建单例
const rabbitMQService = new RabbitMQService();

module.exports = rabbitMQService;
