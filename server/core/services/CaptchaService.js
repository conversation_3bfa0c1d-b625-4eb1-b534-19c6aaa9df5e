const svgCaptcha = require('svg-captcha');
const { createClient } = require('redis');
const crypto = require('crypto');

/**
 * 验证码服务
 * 负责生成验证码和验证验证码
 * 多端共用的公共服务
 */
class CaptchaService {
  constructor() {
    // 初始化Redis客户端（实际应用中应从配置中读取连接信息）
    this.redisClient = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379'
    });
    
    // 连接Redis
    this.connectRedis();
  }
  
  /**
   * 连接Redis
   */
  async connectRedis() {
    try {
      // 处理连接事件
      this.redisClient.on('error', (err) => {
        console.error('Redis 连接错误:', err);
      });
      
      // 尝试连接
      await this.redisClient.connect();
    } catch (err) {
      console.error('无法连接到Redis，使用内存存储:', err.message);
      // 如果Redis连接失败，使用内存存储（仅用于开发环境）
      this.captchaStore = new Map();
    }
  }
  
  /**
   * 生成验证码
   * @param {Object} options - 验证码配置选项
   * @returns {Object} 验证码数据对象，包含验证码ID和SVG图片
   */
  async generateCaptcha(options = {}) {
    // 默认配置和传入配置合并
    const captchaOptions = {
      size: 4, // 验证码长度
      ignoreChars: '0o1il', // 排除容易混淆的字符
      noise: 2, // 干扰线条数量
      color: true, // 彩色验证码
      background: '#f0f0f0', // 背景色
      width: 120, // 宽度
      height: 40, // 高度
      ...options
    };
    
    // 生成验证码
    const captcha = svgCaptcha.create(captchaOptions);
    
    // 生成唯一ID
    const captchaId = crypto.randomUUID();
    
    // 存储验证码（redis或内存）
    await this.storeCaptcha(captchaId, captcha.text.toLowerCase());
    
    return {
      id: captchaId,
      image: captcha.data // SVG图像数据
    };
  }
  
  /**
   * 存储验证码
   * @param {string} captchaId - 验证码ID
   * @param {string} captchaText - 验证码文本
   * @param {number} expirySeconds - 过期时间（秒）
   */
  async storeCaptcha(captchaId, captchaText, expirySeconds = 300) {
    try {
      if (this.redisClient && this.redisClient.isOpen) {
        // 存储到Redis，设置过期时间
        await this.redisClient.set(`captcha:${captchaId}`, captchaText, {
          EX: expirySeconds
        });
      } else {
        // 存储到内存
        this.captchaStore.set(captchaId, {
          text: captchaText,
          expiry: Date.now() + (expirySeconds * 1000)
        });
      }
    } catch (err) {
      console.error('存储验证码失败:', err.message);
      throw new Error('验证码生成失败');
    }
  }
  
  /**
   * 验证验证码
   * @param {string} captchaId - 验证码ID
   * @param {string} userInput - 用户输入的验证码
   * @returns {Promise<boolean>} 验证结果
   */
  async verifyCaptcha(captchaId, userInput) {
    try {
      if (!captchaId || !userInput) {
        return false;
      }
      
      // 将用户输入转为小写
      const inputText = userInput.toLowerCase();
      
      let storedCaptcha;
      
      // 从存储中获取验证码
      if (this.redisClient && this.redisClient.isOpen) {
        // 从Redis获取
        storedCaptcha = await this.redisClient.get(`captcha:${captchaId}`);
        
        // 验证后立即删除，防止重复使用
        if (storedCaptcha) {
          await this.redisClient.del(`captcha:${captchaId}`);
        }
      } else {
        // 从内存获取
        const captchaData = this.captchaStore.get(captchaId);
        
        if (captchaData) {
          // 检查是否过期
          if (Date.now() > captchaData.expiry) {
            this.captchaStore.delete(captchaId);
            return false;
          }
          
          storedCaptcha = captchaData.text;
          // 验证后立即删除，防止重复使用
          this.captchaStore.delete(captchaId);
        }
      }
      
      return storedCaptcha === inputText;
    } catch (err) {
      console.error('验证码验证失败:', err.message);
      return false;
    }
  }
  
  /**
   * 获取IP尝试次数并增加计数
   * @param {string} ip - 客户端IP
   * @returns {Promise<number>} 尝试次数
   */
  async incrementIpAttempts(ip) {
    try {
      const key = `login:attempts:${ip}`;
      
      if (this.redisClient && this.redisClient.isOpen) {
        // 增加计数
        const attempts = await this.redisClient.incr(key);
        
        // 设置过期时间（1小时）
        if (attempts === 1) {
          await this.redisClient.expire(key, 3600);
        }
        
        return attempts;
      } else {
        // 内存存储
        if (!this.ipAttempts) {
          this.ipAttempts = new Map();
        }
        
        const ipData = this.ipAttempts.get(ip) || { count: 0, expiry: Date.now() + 3600000 };
        
        // 检查是否已过期
        if (Date.now() > ipData.expiry) {
          ipData.count = 0;
          ipData.expiry = Date.now() + 3600000;
        }
        
        ipData.count++;
        this.ipAttempts.set(ip, ipData);
        
        return ipData.count;
      }
    } catch (err) {
      console.error('增加IP尝试次数失败:', err.message);
      return 0;
    }
  }
  
  /**
   * 检查IP是否被锁定
   * @param {string} ip - 客户端IP
   * @returns {Promise<boolean>} 是否被锁定
   */
  async isIpLocked(ip) {
    try {
      const key = `login:attempts:${ip}`;
      let attempts = 0;
      
      if (this.redisClient && this.redisClient.isOpen) {
        // 从Redis获取
        const value = await this.redisClient.get(key);
        attempts = value ? parseInt(value) : 0;
      } else {
        // 从内存获取
        if (!this.ipAttempts) {
          return false;
        }
        
        const ipData = this.ipAttempts.get(ip);
        if (!ipData || Date.now() > ipData.expiry) {
          return false;
        }
        
        attempts = ipData.count;
      }
      
      // 5次以上尝试触发锁定
      return attempts >= 5;
    } catch (err) {
      console.error('检查IP锁定状态失败:', err.message);
      return false;
    }
  }
  
  /**
   * 关闭Redis连接
   */
  async close() {
    if (this.redisClient && this.redisClient.isOpen) {
      await this.redisClient.quit();
    }
  }
}

module.exports = CaptchaService;
