const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');
const path = require('path');

class SwaggerConfig {
  constructor() {
    this.options = {
      swaggerDefinition: {
        openapi: '3.0.0',
        info: {
          title: '聚灵云平台 API 文档',
          version: '1.0.0',
          description: '聚灵云平台后端 API 接口文档'
        },
        servers: [
          {
            url: 'http://localhost:4000/api/v1',
            description: '开发服务器 (v1)'
          }
        ],
        basePath: '/api/v1',
        version: '1.0.0',
        components: {
          schemas: require('./schemas/schemas.js'),
          parameters: require('./parameters/CommonParam.js'),
          responses: {
            ...require('./responses/CommonResponse.js'),
            ...require('./responses/AuthResponses.js'),
            ...require(path.resolve(process.cwd(), 'apps/master/swagger/responses/UserManagementResponse.js'))
          },
          securitySchemes: {
            bearerAuth: {
              type: "http",
              scheme: "bearer",
              bearerFormat: "JWT",
              description: "JWT认证，在请求头中添加 'Authorization: Bearer {token}'"
            }
          }
        },
        tags: [
          { name: '用户管理', description: '用户管理相关接口' },
          { name: '供应商认证', description: '供应商认证相关接口' }
        ]
      },
      apis: [
        // 路由
        './apps/*/routes/*Route.js',
        // 核心路由
        './core/routes/*.js',
        './core/controllers/*.js'
      ]
    };
  }

  // 注册模块的 API 文档
  registerModule(name, apiPaths = [], options = {}) {
    console.log(`开始注册 ${name} 模块的 API 文档`);
    console.log('原始路径:', apiPaths);
    
    if (Array.isArray(apiPaths)) {
      // 将相对路径转换为绝对路径
      const absolutePaths = apiPaths.map(p => {
        if (p.startsWith('/')) {
          return p;
        }
        const absPath = path.join(process.cwd(), p);
        console.log(`转换路径: ${p} -> ${absPath}`);
        return absPath;
      });
      
      this.options.apis = [...this.options.apis, ...absolutePaths];
      console.log('当前所有 API 路径:', this.options.apis);
      
      // 合并模块的 Swagger 配置
      if (options.options && options.options.swaggerDefinition) {
        const moduleDefinition = options.options.swaggerDefinition;
        
        // 合并 servers
        if (moduleDefinition.servers) {
          this.options.definition.servers = [
            ...this.options.definition.servers,
            ...moduleDefinition.servers
          ];
          console.log('合并后的 servers:', this.options.definition.servers);
        }
        
        // 合并 tags
        if (moduleDefinition.tags) {
          if (!this.options.definition.tags) {
            this.options.definition.tags = [];
          }
          this.options.definition.tags = [
            ...this.options.definition.tags,
            ...moduleDefinition.tags
          ];
          console.log('合并后的 tags:', this.options.definition.tags);
        }
        
        // 合并其他配置
        if (moduleDefinition.components) {
          this.options.definition.components = {
            ...this.options.definition.components,
            ...moduleDefinition.components
          };
          console.log('合并后的 components:', this.options.definition.components);
        }
      }
      
      console.log(`完成注册 ${name} 模块的 API 文档`);
    }
  }

  // 生成 Swagger 规范
  generateSpecs() {
    return swaggerJsdoc(this.options);
  }

  // 获取 Swagger UI 中间件
  getSwaggerUI() {
    const specs = this.generateSpecs();
    return {
      serve: swaggerUi.serve,
      setup: swaggerUi.setup(specs, { explorer: true })
    };
  }
}

module.exports = new SwaggerConfig();