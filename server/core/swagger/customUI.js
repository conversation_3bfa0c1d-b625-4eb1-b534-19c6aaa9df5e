/**
 * 自定义Swagger UI配置
 */
const express = require('express');
const path = require('path');
const fs = require('fs');

/**
 * 配置自定义Swagger UI
 * @param {Object} app Express应用实例
 * @param {Object} specs Swagger规范对象
 * @returns {Object} 配置好的中间件
 */
function setupCustomSwaggerUI(app, specs) {
  console.log('正在配置自定义Swagger UI...');

  // 创建静态资源目录
  const publicDir = path.join(process.cwd(), 'public');
  const swaggerDir = path.join(publicDir, 'swagger');
  
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
  }
  
  if (!fs.existsSync(swaggerDir)) {
    fs.mkdirSync(swaggerDir, { recursive: true });
  }

  // 提供静态文件服务
  app.use('/swagger', express.static(path.join(process.cwd(), 'public/swagger')));

  // 提供Swagger规范JSON
  app.get('/api-docs/swagger.json', (req, res) => {
    res.json(specs);
  });

  // 重定向/api-docs到自定义UI
  app.get('/api-docs', (req, res) => {
    res.redirect('/swagger');
  });

  console.log('自定义Swagger UI配置完成');
  
  return {
    // 返回空中间件，因为我们已经直接在app上注册了路由
    middleware: (req, res, next) => {
      next();
    }
  };
}

module.exports = {
  setupCustomSwaggerUI
};