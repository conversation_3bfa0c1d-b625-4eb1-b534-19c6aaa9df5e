/**
 * 数据模型定义
 */
module.exports = {
  UserDto: {
    type: 'object',
    properties: {
      id: {
        type: 'string',
        description: '用户ID'
      },
      username: {
        type: 'string',
        description: '用户名'
      },
      email: {
        type: 'string',
        description: '邮箱'
      },
      role: {
        type: 'string',
        description: '角色',
        enum: ['admin', 'user', 'manager', 'guest']
      },
      status: {
        type: 'string',
        description: '状态',
        enum: ['active', 'inactive', 'blocked']
      },
      createdAt: {
        type: 'string',
        format: 'date-time',
        description: '创建时间'
      },
      updatedAt: {
        type: 'string',
        format: 'date-time',
        description: '更新时间'
      }
    },
    example: {
      id: "1001",
      username: "admin",
      email: "<EMAIL>",
      role: "admin",
      status: "active",
      createdAt: "2023-01-01T08:00:00.000Z",
      updatedAt: "2023-01-10T15:30:00.000Z"
    }
  },

  UserCreateRequest: {
    type: 'object',
    required: ['username', 'email', 'password'],
    properties: {
      username: {
        type: 'string',
        description: '用户名'
      },
      email: {
        type: 'string',
        format: 'email',
        description: '邮箱'
      },
      password: {
        type: 'string',
        format: 'password',
        description: '密码'
      },
      role: {
        type: 'string',
        description: '角色'
      }
    }
  },

  UserUpdateRequest: {
    type: 'object',
    properties: {
      username: {
        type: 'string',
        description: '用户名'
      },
      email: {
        type: 'string',
        format: 'email',
        description: '邮箱'
      },
      password: {
        type: 'string',
        format: 'password',
        description: '密码'
      },
      role: {
        type: 'string',
        description: '角色'
      },
      status: {
        type: 'string',
        description: '状态'
      }
    }
  },

  UserLoginRequest: {
    type: 'object',
    required: ['username', 'password'],
    properties: {
      username: {
        type: 'string',
        description: '用户名'
      },
      password: {
        type: 'string',
        format: 'password',
        description: '密码'
      }
    }
  },

  UserLoginResponse: {
    description: '用户登录响应',
    content: {
      'application/json': {
        schema: {
          allOf: [
            { $ref: '#/components/schemas/Success' },
            {
              properties: {
                data: {
                  type: 'object',
                  properties: {
                    token: {
                      type: 'string',
                      description: '访问令牌'
                    }
                  }
                }
              }
            }
          ]
        }
      }
    }
  }
};
