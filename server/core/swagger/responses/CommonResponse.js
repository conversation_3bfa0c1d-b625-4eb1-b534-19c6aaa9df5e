/**
 * 通用响应模型
 */
module.exports = {
  /**
   * 成功响应
   */
  Success: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        default: true,
        example: true,
        description: '接口调用是否成功'
      },
      message: {
        type: 'string',
        description: '成功信息',
        example: '操作成功'
      },
      data: {
        type: 'object',
        description: '响应数据',
        example: {}
      }
    },
    required: ['success']
  },

  /**
   * 错误响应
   */
  Error: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        default: false,
        example: false,
        description: '接口调用是否成功'
      },
      message: {
        type: 'string',
        description: '错误信息',
        example: '操作失败'
      },
      code: {
        type: 'integer',
        description: '错误码',
        example: 40001,
        enum: [
          40001, 40002, 40003, 40004,
          40101, 40102,
          40301, 40302,
          40401, 40402,
          50001, 50002, 50003
        ]
      }
    },
    required: ['success', 'message', 'code']
  },

  /**
   * 分页响应
   */
  PaginatedResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        default: true,
        example: true,
        description: '接口调用是否成功'
      },
      data: {
        type: 'object',
        properties: {
          items: {
            type: 'array',
            description: '数据列表',
            example: []
          },
          total: {
            type: 'integer',
            description: '总记录数',
            example: 100,
            minimum: 0
          },
          page: {
            type: 'integer',
            description: '当前页码',
            example: 1,
            minimum: 1
          },
          pageSize: {
            type: 'integer',
            description: '每页记录数',
            example: 20,
            minimum: 1,
            maximum: 100
          }
        }
      }
    }
  },
  
  /**
   * 标准列表响应
   */
  ListResponse: {
    type: 'object',
    properties: {
      success: {
        type: 'boolean',
        example: true,
        description: '请求是否成功'
      },
      message: {
        type: 'string',
        description: '请求响应消息',
        example: '请求成功'
      },
      code: {
        type: 'integer',
        description: '响应状态码',
        example: 200
      },
      data: {
        type: 'object',
        properties: {
          items: {
            type: 'array',
            description: '列表数据项',
            example: []
          },
          pageInfo: {
            type: 'object',
            properties: {
              total: {
                type: 'integer',
                description: '总记录数',
                example: 100
              },
              currentPage: {
                type: 'integer',
                description: '当前页码',
                example: 1
              },
              totalPage: {
                type: 'integer',
                description: '总页数',
                example: 10
              }
            }
          }
        }
      }
    }
  }
};
