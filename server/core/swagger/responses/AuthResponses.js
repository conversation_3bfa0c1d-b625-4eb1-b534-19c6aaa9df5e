/**
 * 认证相关响应定义
 */
module.exports = {
  /**
   * 认证失败响应
   */
  AuthFailedResponse: {
    description: '认证失败',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              default: false,
              example: false
            },
            message: {
              type: 'string',
              example: '未登录或登录已过期'
            },
            code: {
              type: 'integer',
              example: 40101
            }
          }
        }
      }
    }
  },
  
  /**
   * 无权限响应
   */
  ForbiddenResponse: {
    description: '无访问权限',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              default: false,
              example: false
            },
            message: {
              type: 'string',
              example: '无权访问该资源'
            },
            code: {
              type: 'integer',
              example: 40301
            }
          }
        }
      }
    }
  },
  
  /**
   * 验证码错误响应
   */
  CaptchaErrorResponse: {
    description: '验证码错误',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              default: false,
              example: false
            },
            message: {
              type: 'string',
              example: '验证码错误或已过期'
            },
            code: {
              type: 'integer',
              example: 40004
            }
          }
        }
      }
    }
  }
};
