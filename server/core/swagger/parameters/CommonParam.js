/**
 * 通用参数定义
 */
module.exports = {
  PageParam: {
    in: 'query',
    name: 'page',
    schema: {
      type: 'integer',
      default: 1
    },
    description: '页码'
  },

  PageSizeParam: {
    in: 'query',
    name: 'pageSize',
    schema: {
      type: 'integer',
      default: 10
    },
    description: '每页记录数'
  },

  IdParam: {
    in: 'path',
    name: 'id',
    required: true,
    schema: {
      type: 'string'
    },
    description: '记录ID'
  }
};
