const express = require('express');
const router = express.Router();
const CaptchaController = require('../controllers/CaptchaController');
const captchaController = new CaptchaController();

/**
 * 验证码路由
 * 公共服务，多端共用
 */

/**
 * 生成验证码
 * @swagger
 * /api/v1/common/captcha/generate:
 *   get:
 *     tags: [公共服务/验证码]
 *     summary: 生成验证码
 *     description: 生成图形验证码，用于登录验证
 */
router.get('/generate', captchaController.generateCaptcha.bind(captchaController));

// Swagger 文档
const swaggerPaths = {
  '/api/v1/common/captcha/generate': {
    get: {
      tags: ['公共服务/验证码'],
      summary: '生成验证码',
      description: '生成图形验证码，用于登录验证',
      parameters: [
        {
          in: 'query',
          name: 'width',
          schema: {
            type: 'integer'
          },
          description: '验证码图片宽度'
        },
        {
          in: 'query',
          name: 'height',
          schema: {
            type: 'integer'
          },
          description: '验证码图片高度'
        }
      ],
      responses: {
        200: {
          description: '成功生成验证码',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  code: {
                    type: 'integer',
                    example: 0
                  },
                  message: {
                    type: 'string',
                    example: '验证码生成成功'
                  },
                  data: {
                    type: 'object',
                    properties: {
                      id: {
                        type: 'string',
                        description: '验证码ID，用于验证'
                      },
                      image: {
                        type: 'string',
                        description: '验证码SVG图片'
                      }
                    }
                  }
                }
              }
            }
          }
        },
        429: {
          description: '请求过于频繁',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  code: {
                    type: 'integer',
                    example: 429
                  },
                  message: {
                    type: 'string',
                    example: '尝试次数过多，请稍后再试'
                  },
                  data: {
                    type: 'null'
                  }
                }
              }
            }
          }
        }
      }
    }
  }
};

// 导出路由
module.exports = router;

// 导出 Swagger 路径
module.exports.paths = swaggerPaths;
