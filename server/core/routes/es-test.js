const express = require('express');
const ElasticsearchService = require('../services/ElasticsearchService');
const config = require('../../config');

const router = express.Router();
const productIndex = config.elasticsearch.elasticsearch.indices.product.index;

/**
 * @swagger
 * /api/es-test/ping:
 *   get:
 *     summary: 测试Elasticsearch连接
 *     tags: [ES测试]
 *     responses:
 *       200:
 *         description: 连接成功
 *       500:
 *         description: 连接失败
 */
router.get('/ping', async (req, res) => {
  try {
    const client = ElasticsearchService.client;
    const result = await client.ping();
    res.json({
      success: true,
      message: 'Elasticsearch连接成功',
      data: result
    });
  } catch (error) {
    console.error('Elasticsearch连接失败:', error);
    res.status(500).json({
      success: false,
      message: 'Elasticsearch连接失败',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/es-test/init-index:
 *   post:
 *     summary: 初始化商品索引
 *     tags: [ES测试]
 *     responses:
 *       200:
 *         description: 索引初始化成功
 *       500:
 *         description: 索引初始化失败
 */
router.post('/init-index', async (req, res) => {
  try {
    // 商品索引设置
    const settings = {
      analysis: {
        analyzer: {
          text_analyzer: {
            type: 'custom',
            tokenizer: 'standard',
            filter: ['lowercase', 'trim']
          }
        }
      },
      number_of_shards: 3,
      number_of_replicas: 1
    };

    // 商品索引映射
    const mappings = {
      properties: {
        id: { type: 'keyword' },
        name: { 
          type: 'text',
          analyzer: 'text_analyzer',
          fields: {
            keyword: { type: 'keyword' }
          }
        },
        description: { type: 'text', analyzer: 'text_analyzer' },
        price: { type: 'double' },
        originalPrice: { type: 'double' },
        stock: { type: 'integer' },
        sales: { type: 'integer' },
        category: { type: 'keyword' },
        categoryPath: { type: 'keyword' },
        brand: { type: 'keyword' },
        tags: { type: 'keyword' },
        attributes: {
          type: 'nested',
          properties: {
            name: { type: 'keyword' },
            value: { type: 'keyword' }
          }
        },
        images: { type: 'keyword' },
        mainImage: { type: 'keyword' },
        status: { type: 'keyword' },
        createdAt: { type: 'date' },
        updatedAt: { type: 'date' }
      }
    };

    const result = await ElasticsearchService.createIndex(productIndex, settings, mappings);
    
    if (result) {
      res.json({
        success: true,
        message: `商品索引 ${productIndex} 初始化成功`
      });
    } else {
      res.status(500).json({
        success: false,
        message: `商品索引 ${productIndex} 初始化失败`
      });
    }
  } catch (error) {
    console.error('初始化商品索引时出错:', error);
    res.status(500).json({
      success: false,
      message: '初始化商品索引时出错',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/es-test/add-test-data:
 *   post:
 *     summary: 添加测试商品数据
 *     tags: [ES测试]
 *     responses:
 *       200:
 *         description: 测试数据添加成功
 *       500:
 *         description: 测试数据添加失败
 */
router.post('/add-test-data', async (req, res) => {
  try {
    // 生成all_text字段的函数，将所有可搜索内容合并
    const generateAllText = (product) => {
      const texts = [
        product.name,
        product.description,
        product.brand,
        product.category,
        product.categoryPath,
        ...(product.tags || []),
        ...(product.images || []),
        product.mainImage
      ];
      
      // 添加属性的名称和值
      if (product.attributes && Array.isArray(product.attributes)) {
        product.attributes.forEach(attr => {
          texts.push(attr.name, attr.value);
        });
      }
      
      return texts.filter(Boolean).join(' ');
    };
    
    // 测试商品数据
    const testProducts = [
      {
        id: '1',
        name: '苹果iPhone 15 Pro Max',
        description: '苹果最新旗舰手机，搭载A17芯片，超长续航，专业摄影系统',
        price: 9999,
        originalPrice: 10999,
        stock: 100,
        sales: 500,
        category: 'smartphones',
        categoryPath: 'electronics/smartphones',
        brand: 'Apple',
        tags: ['手机', '苹果', '旗舰', '5G'],
        attributes: [
          { name: '颜色', value: '深空黑' },
          { name: '存储', value: '256GB' },
          { name: '屏幕尺寸', value: '6.7英寸' }
        ],
        images: ['/images/products/iphone15-1.jpg', '/images/products/iphone15-2.jpg'],
        mainImage: '/images/products/iphone15-1.jpg',
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '2',
        name: '华为Mate 60 Pro',
        description: '华为旗舰手机，搭载麒麟9000芯片，卫星通信，超长续航',
        price: 6999,
        originalPrice: 7999,
        stock: 50,
        sales: 300,
        category: 'smartphones',
        categoryPath: 'electronics/smartphones',
        brand: 'Huawei',
        tags: ['手机', '华为', '旗舰', '5G'],
        attributes: [
          { name: '颜色', value: '曜金黑' },
          { name: '存储', value: '512GB' },
          { name: '屏幕尺寸', value: '6.8英寸' }
        ],
        images: ['/images/products/mate60-1.jpg', '/images/products/mate60-2.jpg'],
        mainImage: '/images/products/mate60-1.jpg',
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '3',
        name: '小米14 Ultra',
        description: '小米影像旗舰，搭载骁龙8 Gen 3芯片，徕卡专业影像系统',
        price: 5999,
        originalPrice: 6499,
        stock: 200,
        sales: 400,
        category: 'smartphones',
        categoryPath: 'electronics/smartphones',
        brand: 'Xiaomi',
        tags: ['手机', '小米', '旗舰', '5G'],
        attributes: [
          { name: '颜色', value: '陶瓷白' },
          { name: '存储', value: '256GB' },
          { name: '屏幕尺寸', value: '6.7英寸' }
        ],
        images: ['/images/products/xiaomi14-1.jpg', '/images/products/xiaomi14-2.jpg'],
        mainImage: '/images/products/xiaomi14-1.jpg',
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '4',
        name: 'MacBook Pro M3 Max',
        description: 'Apple最强笔记本，搭载M3 Max芯片，专业级性能，超长续航',
        price: 19999,
        originalPrice: 21999,
        stock: 30,
        sales: 100,
        category: 'laptops',
        categoryPath: 'electronics/laptops',
        brand: 'Apple',
        tags: ['笔记本', '苹果', '专业', '高性能'],
        attributes: [
          { name: '颜色', value: '深空灰' },
          { name: '内存', value: '32GB' },
          { name: '存储', value: '1TB' },
          { name: '屏幕尺寸', value: '16英寸' }
        ],
        images: ['/images/products/macbook-1.jpg', '/images/products/macbook-2.jpg'],
        mainImage: '/images/products/macbook-1.jpg',
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '5',
        name: '华为MateBook X Pro',
        description: '华为轻薄旗舰笔记本，高性能，长续航，3K触控屏',
        price: 9999,
        originalPrice: 10999,
        stock: 60,
        sales: 150,
        category: 'laptops',
        categoryPath: 'electronics/laptops',
        brand: 'Huawei',
        tags: ['笔记本', '华为', '轻薄', '高性能'],
        attributes: [
          { name: '颜色', value: '翡冷翠' },
          { name: '内存', value: '16GB' },
          { name: '存储', value: '512GB' },
          { name: '屏幕尺寸', value: '14.2英寸' }
        ],
        images: ['/images/products/matebook-1.jpg', '/images/products/matebook-2.jpg'],
        mainImage: '/images/products/matebook-1.jpg',
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    // 为每个商品添加all_text字段
    const productsWithAllText = testProducts.map(product => {
      return {
        ...product,
        all_text: generateAllText(product)
      };
    });
    
    // 批量添加测试数据
    const result = await ElasticsearchService.bulkIndex(productIndex, productsWithAllText);
    
    res.json({
      success: true,
      message: '测试商品数据添加成功',
      data: {
        total: testProducts.length,
        result
      }
    });
  } catch (error) {
    console.error('添加测试商品数据时出错:', error);
    res.status(500).json({
      success: false,
      message: '添加测试商品数据时出错',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/es-test/search:
 *   get:
 *     summary: 测试商品搜索
 *     tags: [ES测试]
 *     parameters:
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: 商品分类
 *       - in: query
 *         name: brand
 *         schema:
 *           type: string
 *         description: 品牌
 *       - in: query
 *         name: minPrice
 *         schema:
 *           type: number
 *         description: 最低价格
 *       - in: query
 *         name: maxPrice
 *         schema:
 *           type: number
 *         description: 最高价格
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *     responses:
 *       200:
 *         description: 搜索成功
 *       500:
 *         description: 搜索失败
 */
router.get('/search', async (req, res) => {
  try {
    const { 
      keyword, 
      category, 
      brand, 
      minPrice, 
      maxPrice, 
      page = 1, 
      pageSize = 10,
      sort = 'created_at_desc'
    } = req.query;

    // 构建查询条件
    const must = [];
    const filter = [];

    // 关键词搜索 - 全文模糊搜索
    if (keyword) {
      must.push({
        multi_match: {
          query: keyword,
          fields: [
            'name^4',                // 商品名称权重最高
            'description^3',          // 商品描述权重次之
            'brand^2',                // 品牌
            'category^2',             // 分类
            'categoryPath^2',         // 分类路径
            'tags^2',                 // 标签
            'attributes.name^1.5',    // 属性名称
            'attributes.value^1.5',   // 属性值
            'images',                 // 图片路径
            'mainImage',              // 主图路径
            'all_text'                // 全文字段
          ],
          type: 'best_fields',
          fuzziness: 'AUTO',
          operator: 'or',             // 允许部分匹配
          minimum_should_match: '70%' // 至少匹配70%的关键词
        }
      });
    }

    // 分类过滤
    if (category) {
      filter.push({
        term: { 'categoryPath.keyword': category }
      });
    }

    // 品牌过滤
    if (brand) {
      filter.push({
        term: { 'brand.keyword': brand }
      });
    }

    // 价格范围过滤
    if (minPrice !== undefined || maxPrice !== undefined) {
      const rangeQuery = { range: { price: {} } };
      
      if (minPrice !== undefined) {
        rangeQuery.range.price.gte = parseFloat(minPrice);
      }
      
      if (maxPrice !== undefined) {
        rangeQuery.range.price.lte = parseFloat(maxPrice);
      }
      
      filter.push(rangeQuery);
    }

    // 只显示上架状态的商品
    filter.push({
      term: { status: 'active' }
    });

    // 构建完整的查询
    const query = {
      bool: {
        must,
        filter
      }
    };

    // 构建排序条件
    const sortMap = {
      'price_asc': [{ price: 'asc' }],
      'price_desc': [{ price: 'desc' }],
      'sales_desc': [{ sales: 'desc' }],
      'created_at_desc': [{ createdAt: 'desc' }]
    };
    const sortOptions = sortMap[sort] || sortMap['created_at_desc'];

    // 计算分页参数
    const from = (parseInt(page) - 1) * parseInt(pageSize);
    const size = parseInt(pageSize);

    // 执行搜索
    const searchResult = await ElasticsearchService.search(
      productIndex,
      query,
      from,
      size,
      sortOptions
    );

    // 构建响应数据
    const products = searchResult.hits.hits.map(hit => ({
      ...hit._source,
      score: hit._score
    }));

    const total = searchResult.hits.total.value || 0;

    res.json({
      success: true,
      message: '搜索成功',
      data: {
        items: products,
        pageInfo: {
          total,
          currentPage: parseInt(page),
          totalPage: Math.ceil(total / parseInt(pageSize))
        }
      }
    });
  } catch (error) {
    console.error('搜索商品时出错:', error);
    res.status(500).json({
      success: false,
      message: '搜索失败',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/es-test/delete-index:
 *   delete:
 *     summary: 删除商品索引
 *     tags: [ES测试]
 *     responses:
 *       200:
 *         description: 索引删除成功
 *       500:
 *         description: 索引删除失败
 */
router.delete('/delete-index', async (req, res) => {
  try {
    const result = await ElasticsearchService.deleteIndex(productIndex);
    
    if (result) {
      res.json({
        success: true,
        message: `商品索引 ${productIndex} 删除成功`
      });
    } else {
      res.status(500).json({
        success: false,
        message: `商品索引 ${productIndex} 删除失败`
      });
    }
  } catch (error) {
    console.error('删除商品索引时出错:', error);
    res.status(500).json({
      success: false,
      message: '删除商品索引时出错',
      error: error.message
    });
  }
});

module.exports = router;
