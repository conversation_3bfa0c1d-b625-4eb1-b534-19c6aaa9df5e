/**
 * 路由管理器
 * 统一管理公开路由和需要认证的路由
 */
const express = require('express');
const authMiddleware = require('../middleware/AuthMiddleware');
const path = require('path');
const fs = require('fs');

// 加载认证配置
let authConfig = { publicPaths: [] };
try {
  const configPath = path.join(process.cwd(), 'config', 'auth.config.js');
  if (fs.existsSync(configPath)) {
    authConfig = require(configPath);
  }
} catch (err) {
  console.warn('无法加载认证配置文件:', err.message);
}

class RouteManager {
  constructor() {
    // 存储需要跳过认证的路径
    this.publicPaths = [...(authConfig.publicPaths || [])];
    // 主路由器
    this.router = express.Router();
    
    console.log('已加载公开路径配置:', this.publicPaths);
  }

  /**
   * 添加公开路径，不需要JWT认证
   * @param {string|Array} paths - 路径或路径数组
   */
  addPublicPaths(paths) {
    if (Array.isArray(paths)) {
      this.publicPaths = [...this.publicPaths, ...paths];
    } else {
      this.publicPaths.push(paths);
    }
  }

  /**
   * 创建一个路由实例
   * @returns {express.Router} 路由实例
   */
  createRouter() {
    return express.Router();
  }

  /**
   * 应用路由，并自动处理认证
   * @param {express.Router} router - 要应用的路由实例
   * @param {string} prefix - 路由前缀
   */
  use(router, prefix = '') {
    this.router.use(prefix, router);
  }

  /**
   * 初始化路由管理器
   * 应用认证中间件并处理公开路径
   */
  initialize() {
    // 添加认证中间件，但对公开路径跳过认证
    this.router.use((req, res, next) => {
      // 检查请求路径是否在公开路径列表中
      const isPublicPath = this.publicPaths.some(path => {
        const fullPath = req.baseUrl + req.path;
        // 如果是精确匹配
        if (path === fullPath) return true;
        // 如果是路径前缀匹配（以/结尾）
        if (path.endsWith('/') && fullPath.startsWith(path)) return true;
        // 如果是带有*的通配符匹配
        if (path.includes('*')) {
          const regexStr = path.replace(/\*/g, '.*');
          const regex = new RegExp(`^${regexStr}$`);
          return regex.test(fullPath);
        }
        return false;
      });

      // 如果是公开路径，跳过认证
      if (isPublicPath) {
        return next();
      }

      // 应用认证中间件
      authMiddleware(req, res, next);
    });

    return this.router;
  }
}

// 导出单例实例
module.exports = new RouteManager();
