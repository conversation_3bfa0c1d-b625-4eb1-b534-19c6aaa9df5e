const express = require('express');
const router = express.Router();
const captchaRoutes = require('./CaptchaRoute');
const PublicKeyController = require('../controllers/PublicKeyController');
const WechatPayController = require('../../apps/master/controllers/WechatPayController');

/**
 * 公共路由
 * 包含多端共用的API
 */

// 实例化控制器
const publicKeyController = new PublicKeyController();
const wechatPayController = new WechatPayController();

// 挂载验证码路由
router.use('/captcha', captchaRoutes);

// 获取RSA公钥
router.get('/public-key', publicKeyController.getPublicKey.bind(publicKeyController));

// 微信支付创建订单接口（公共访问）
router.post('/wechat-pay/create-order', async (req, res) => {
  await wechatPayController.createOrder(req, res);
});

// Swagger 文档路径
const swaggerPaths = {
  ...captchaRoutes.paths,
  '/api/v1/common/wechat-pay/create-order': {
    post: {
      tags: ['公共服务/支付'],
      summary: '创建微信支付订单',
      description: '创建微信Native支付订单，返回支付二维码（公共接口，无需登录）',
      requestBody: {
        required: false,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                description: {
                  type: 'string',
                  description: '商品描述',
                  example: "微信支付测试"
                },
                amount: {
                  type: 'integer',
                  description: '支付金额(分)，不传则随机生成0.01-0.05元',
                  example: 1
                }
              }
            }
          }
        }
      },
      responses: {
        200: {
          description: '创建成功'
        },
        400: {
          description: '创建失败'
        }
      }
    }
  },
  '/api/v1/common/public-key': {
    get: {
      tags: ['公共服务/安全'],
      summary: '获取RSA公钥',
      description: '获取用于密码加密的RSA公钥',
      responses: {
        200: {
          description: '成功获取公钥',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  code: {
                    type: 'integer',
                    example: 0
                  },
                  message: {
                    type: 'string',
                    example: '获取公钥成功'
                  },
                  data: {
                    type: 'object',
                    properties: {
                      publicKey: {
                        type: 'string',
                        description: 'RSA公钥'
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
};

module.exports = router;
module.exports.paths = swaggerPaths;
