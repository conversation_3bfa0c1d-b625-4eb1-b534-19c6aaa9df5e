/**
 * 路由配置工具
 * 用于配置路由的认证和权限控制
 */
const authMiddleware = require('../middleware/AuthMiddleware');

/**
 * 路由配置类
 * 提供路由中间件的配置方法
 */
class RouterConfig {
  /**
   * 创建需要JWT认证的路由
   * @param {Object} router - Express路由对象
   * @returns {Object} 配置了JWT认证中间件的路由对象
   */
  static authRoute(router) {
    // 拦截所有请求并应用JWT认证中间件
    router.use(authMiddleware);
    return router;
  }
  
  /**
   * 创建公开路由（无需认证）
   * @param {Object} router - Express路由对象
   * @returns {Object} 原始路由对象（不应用认证中间件）
   */
  static publicRoute(router) {
    // 不应用任何认证中间件，直接返回
    return router;
  }
}

module.exports = RouterConfig;
