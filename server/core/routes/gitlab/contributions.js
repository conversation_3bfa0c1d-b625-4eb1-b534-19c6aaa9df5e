/**
 * GitLab贡献统计API路由
 * 从本地JSON文件获取GitLab贡献数据
 */
const express = require('express');
const fs = require('fs');
const path = require('path');
const { successResponse, errorResponse } = require('../../../shared/utils/response');

const router = express.Router();

/**
 * @api {get} /gitlab/contributions 获取GitLab贡献统计数据
 * @apiName GetGitlabContributions
 * @apiGroup GitLab
 * @apiDescription 从本地JSON文件获取GitLab贡献统计数据
 * 
 * @apiSuccess {Boolean} success 请求是否成功
 * @apiSuccess {String} message 响应消息
 * @apiSuccess {Object} data 贡献统计数据
 * @apiSuccess {String} data.lastUpdated 最后更新时间
 * @apiSuccess {Number} data.totalCommits 总提交数
 * @apiSuccess {Array} data.commits 提交记录列表
 * @apiSuccess {Object} data.stats 贡献统计数据
 */
router.get('/contributions', (req, res) => {
  try {
    // 贡献数据文件路径
    const contributionsFilePath = path.resolve(process.cwd(), 'public/data/gitlab-contributions.json');
    
    // 检查文件是否存在
    if (!fs.existsSync(contributionsFilePath)) {
      return res.json(errorResponse('贡献数据文件不存在，请先运行同步脚本'));
    }
    
    // 读取JSON文件
    const contributionsData = JSON.parse(fs.readFileSync(contributionsFilePath, 'utf-8'));
    
    return res.json(successResponse('获取GitLab贡献数据成功', contributionsData));
  } catch (error) {
    console.error('获取GitLab贡献数据失败:', error);
    return res.json(errorResponse(`获取GitLab贡献数据失败: ${error.message}`));
  }
});

module.exports = router;