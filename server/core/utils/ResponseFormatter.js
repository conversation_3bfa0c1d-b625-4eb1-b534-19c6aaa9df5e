/**
 * 响应格式化工具类
 * 提供统一的API响应格式
 */
class ResponseFormatter {

  /**
   * 格式化分页响应数据
   * 将PaginationUtil生成的分页数据转换为统一的响应格式
   * @param {Object} paginationData - PaginationUtil生成的分页数据
   * @param {string} message - 响应消息，默认为"请求成功"
   * @returns {Object} 统一格式的响应数据
   */
  static formatPaginationResponse(paginationData, message = '请求成功') {
    return {
      success: true,
      message: message,
      code: 200,
      data: {
        items: paginationData.list,
        pageInfo: {
          total: paginationData.pagination.total,
          currentPage: paginationData.pagination.page,
          totalPage: paginationData.pagination.totalPages
        }
      }
    };
  }

  /**
   * 格式化成功响应
   * @param {*} data - 响应数据
   * @param {string} message - 响应消息，默认为"请求成功"
   * @param {number} code - 响应状态码，默认为200
   * @returns {Object} 统一格式的响应数据
   */
  static formatSuccessResponse(data, message = '请求成功', code = 200) {
    return {
      success: true,
      message: message,
      code: code,
      data: data
    };
  }

  /**
   * 格式化错误响应
   * @param {string} message - 错误消息
   * @param {number} code - 错误状态码，默认为500
   * @param {*} error - 错误详情，可选
   * @returns {Object} 统一格式的错误响应
   */
  static formatErrorResponse(message, code = 500, error = null) {
    const response = {
      success: false,
      message: message,
      code: code
    };
    
    if (error) {
      response.error = error;
    }
    
    return response;
  }
}

module.exports = ResponseFormatter;
