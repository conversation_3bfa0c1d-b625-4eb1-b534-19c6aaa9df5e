/**
 * 日志工具模块
 * 提供统一的日志记录功能
 */
const winston = require('winston');
const path = require('path');
const fs = require('fs');

// 确保日志目录存在
const logDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 定义日志格式
const logFormat = winston.format.printf(({ level, message, timestamp, module }) => {
  return `${timestamp} [${level.toUpperCase()}] [${module || 'system'}] ${message}`;
});

// 创建基础日志记录器
const baseLogger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.errors({ stack: true }),
    logFormat
  ),
  transports: [
    // 控制台输出
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        logFormat
      )
    }),
    // 文件输出 - 错误日志
    new winston.transports.File({ 
      filename: path.join(logDir, 'error.log'), 
      level: 'error' 
    }),
    // 文件输出 - 所有日志
    new winston.transports.File({ 
      filename: path.join(logDir, 'combined.log') 
    })
  ]
});

/**
 * 创建一个带有模块名称的日志记录器
 * @param {string} moduleName 模块名称
 * @returns {object} 日志记录器对象
 */
function createLogger(moduleName) {
  return {
    info: (message, ...args) => baseLogger.info(message, { module: moduleName, ...args }),
    warn: (message, ...args) => baseLogger.warn(message, { module: moduleName, ...args }),
    error: (message, ...args) => baseLogger.error(message, { module: moduleName, ...args }),
    debug: (message, ...args) => baseLogger.debug(message, { module: moduleName, ...args })
  };
}

// 导出基本日志记录器和创建日志记录器的方法
module.exports = {
  info: (message, ...args) => baseLogger.info(message, ...args),
  warn: (message, ...args) => baseLogger.warn(message, ...args),
  error: (message, ...args) => baseLogger.error(message, ...args),
  debug: (message, ...args) => baseLogger.debug(message, ...args),
  createLogger
};
