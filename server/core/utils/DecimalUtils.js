/**
 * Decimal 类型处理工具类
 * 专门用于处理 Prisma Decimal 类型数据的转换和格式化
 * 
 * 主要功能：
 * 1. 将 Prisma Decimal 对象转换为字符串格式
 * 2. 处理 BigInt 类型数据
 * 3. 统一价格、金额等数值类型的格式化
 * 
 * 使用场景：
 * - 商品价格处理
 * - 订单金额计算  
 * - 财务数据格式化
 * - API 响应数据处理
 */

const { Prisma } = require('@prisma/client');

class DecimalUtils {
  
  /**
   * 将 Prisma Decimal 类型转换为字符串
   * 
   * @param {*} decimal Prisma Decimal 对象、数字、字符串等
   * @returns {string} 转换后的数字字符串
   * 
   * @example
   * const price = new Prisma.Decimal('99.99');
   * const result = DecimalUtils.convertToString(price); // '99.99'
   */
  static convertToString(decimal) {
    if (decimal === null || decimal === undefined) {
      return '0.00';
    }
    
    // 处理普通数字
    if (typeof decimal === 'number') {
      return decimal.toString();
    }
    
    // 处理字符串类型的数字
    if (typeof decimal === 'string') {
      const num = parseFloat(decimal);
      return isNaN(num) ? '0.00' : num.toString();
    }
    
    // 处理 Prisma Decimal 对象
    if (typeof decimal === 'object' && decimal !== null) {
      try {
        // 方法1: 标准的 Prisma.Decimal 实例
        if (decimal instanceof Prisma.Decimal) {
          return decimal.toString();
        }
        
        // 方法2: 检查是否有 toString 方法
        if (typeof decimal.toString === 'function') {
          const result = decimal.toString();
          // 验证结果是否为有效数字
          if (!isNaN(parseFloat(result))) {
            return result;
          }
        }
        
        // 方法3: 检查是否有 toNumber 方法
        if (typeof decimal.toNumber === 'function') {
          const result = decimal.toNumber();
          return result.toString();
        }
        
        // 方法4: 检查是否有 valueOf 方法
        if (typeof decimal.valueOf === 'function') {
          const result = decimal.valueOf();
          if (typeof result === 'number') {
            return result.toString();
          }
        }
        
        // 方法5: 处理 Decimal.js 的内部结构 {s, e, d}
        if (decimal.s !== undefined && decimal.e !== undefined && decimal.d !== undefined) {
          // 只在调试模式下输出日志
          if (process.env.NODE_ENV === 'development') {
            console.warn('【DecimalUtils】检测到 Decimal.js 对象结构:', {
              s: decimal.s,
              e: decimal.e,
              d: decimal.d,
              constructor: decimal.constructor?.name
            });
          }
          
          try {
            // 尝试通过 Decimal.js 的构造函数重新创建对象
            // 导入 Decimal.js 库
            const Decimal = require('decimal.js');
            
            // 使用内部数据重新构建 Decimal 对象
            const newDecimal = new Decimal(0);
            newDecimal.s = decimal.s;
            newDecimal.e = decimal.e;  
            newDecimal.d = [...decimal.d]; // 复制数组
            
            const result = newDecimal.toString();

            // 只在调试模式下输出日志
            if (process.env.NODE_ENV === 'development') {
              console.log('【DecimalUtils】使用 Decimal.js 重建对象成功:', {
                original: { s: decimal.s, e: decimal.e, d: decimal.d },
                result: result
              });
            }

            return result;
          } catch (decimalErr) {
            console.error('【DecimalUtils】使用 Decimal.js 重建失败:', decimalErr.message);
            
            // 如果 Decimal.js 重建失败，尝试正确的字符串解析
            try {
              const { s, e, d } = decimal;

              // 只在调试模式下输出详细日志
              if (process.env.NODE_ENV === 'development') {
                console.log('【DecimalUtils】开始手动解析Decimal结构:', { s, e, d });
              }

              // Decimal.js 内部表示解析
              // s: 符号（1为正，-1为负）
              // e: 第一个有效数字的位置（从0开始）
              // d: 数字数组，每个元素最多7位数字

              // 将数字数组转换为完整的数字字符串
              let digitStr = '';
              for (let i = 0; i < d.length; i++) {
                if (i === 0) {
                  // 第一个元素直接转换
                  digitStr += d[i].toString();
                } else {
                  // 后续元素需要补齐7位
                  digitStr += d[i].toString().padStart(7, '0');
                }
              }

              // 移除尾随的零
              digitStr = digitStr.replace(/0+$/, '');
              if (digitStr === '') digitStr = '0';

              // 根据指数e确定小数点位置
              // e表示第一个有效数字的位置，小数点在e+1位置
              const decimalPos = e + 1;

              let result;
              if (decimalPos <= 0) {
                // 小数点在数字前面，如0.01
                result = '0.' + '0'.repeat(-decimalPos) + digitStr;
              } else if (decimalPos >= digitStr.length) {
                // 小数点在数字后面，需要补零
                result = digitStr + '0'.repeat(decimalPos - digitStr.length);
              } else {
                // 小数点在数字中间
                const intPart = digitStr.slice(0, decimalPos);
                const fracPart = digitStr.slice(decimalPos);
                result = intPart + (fracPart ? '.' + fracPart : '');
              }

              // 应用符号
              if (s === -1) {
                result = '-' + result;
              }

              // 只在调试模式下输出详细日志
              if (process.env.NODE_ENV === 'development') {
                console.log('【DecimalUtils】手动解析成功:', {
                  original: { s, e, d },
                  digitStr,
                  decimalPos,
                  result
                });
              }

              return result;
            } catch (mathErr) {
              console.error('【DecimalUtils】手动解析失败:', mathErr.message);
              return '0.00';
            }
          }
        }
        
      } catch (error) {
        console.error('【DecimalUtils】转换 Decimal 类型失败:', error.message);
      }
    }
    
    console.warn('【DecimalUtils】无法识别的数据类型:', {
      type: typeof decimal,
      value: decimal,
      constructor: decimal?.constructor?.name
    });
    return '0.00';
  }
  
  /**
   * 将 Prisma Decimal 类型转换为数字
   * 注意：可能会有精度损失，建议谨慎使用
   * 
   * @param {*} decimal Prisma Decimal 对象
   * @returns {number} 转换后的数字
   */
  static convertToNumber(decimal) {
    const stringValue = this.convertToString(decimal);
    const numberValue = parseFloat(stringValue);
    return isNaN(numberValue) ? 0 : numberValue;
  }
  
  /**
   * 格式化价格，保留指定小数位数
   * 
   * @param {*} decimal Prisma Decimal 对象
   * @param {number} decimalPlaces 小数位数，默认2位
   * @returns {string} 格式化后的价格字符串
   * 
   * @example
   * const price = new Prisma.Decimal('99.999');
   * const result = DecimalUtils.formatPrice(price, 2); // '100.00'
   */
  static formatPrice(decimal, decimalPlaces = 2) {
    const numberValue = this.convertToNumber(decimal);
    return numberValue.toFixed(decimalPlaces);
  }
  
  /**
   * 处理 BigInt 类型的序列化问题
   * 
   * @param {*} data 需要处理的数据
   * @returns {*} 处理后的数据，BigInt 转换为字符串
   */
  static handleBigInt(data) {
    if (data === null || data === undefined) return data;
    
    if (typeof data === 'bigint') {
      return data.toString();
    }
    
    if (Array.isArray(data)) {
      return data.map(item => this.handleBigInt(item));
    }
    
    if (typeof data === 'object' && !(data instanceof Date)) {
      const result = {};
      for (const key in data) {
        if (Object.prototype.hasOwnProperty.call(data, key)) {
          result[key] = this.handleBigInt(data[key]);
        }
      }
      return result;
    }
    
    return data;
  }
  
  /**
   * 批量处理对象中的 Decimal 和 BigInt 字段
   * 
   * @param {Object} data 需要处理的数据对象
   * @param {Array<string>} decimalFields 需要处理的 Decimal 字段名数组
   * @returns {Object} 处理后的数据对象
   * 
   * @example
   * const product = {
   *   id: 123n,
   *   name: '商品名称',
   *   price: new Prisma.Decimal('99.99'),
   *   marketPrice: new Prisma.Decimal('199.99')
   * };
   * const result = DecimalUtils.processDataFields(product, ['price', 'marketPrice']);
   */
  static processDataFields(data, decimalFields = ['price', 'salesPrice', 'marketPrice', 'costPrice', 'amount']) {
    if (!data || typeof data !== 'object') {
      return data;
    }
    
    // 处理数组
    if (Array.isArray(data)) {
      return data.map(item => this.processDataFields(item, decimalFields));
    }
    
    // 处理对象
    const result = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        const value = data[key];
        
        // 处理 BigInt
        if (typeof value === 'bigint') {
          result[key] = value.toString();
        }
        // 处理指定的 Decimal 字段
        else if (decimalFields.includes(key)) {
          result[key] = this.convertToString(value);
        }
        // 递归处理嵌套对象
        else if (typeof value === 'object' && value !== null && !(value instanceof Date)) {
          result[key] = this.processDataFields(value, decimalFields);
        }
        // 普通字段直接复制
        else {
          result[key] = value;
        }
      }
    }
    
    return result;
  }
  
  /**
   * 验证是否为有效的价格数值
   * 
   * @param {*} value 需要验证的值
   * @returns {boolean} 是否为有效价格
   */
  static isValidPrice(value) {
    if (value === null || value === undefined) {
      return false;
    }
    
    const stringValue = this.convertToString(value);
    const numberValue = parseFloat(stringValue);
    
    return !isNaN(numberValue) && numberValue >= 0;
  }
  
  /**
   * 比较两个 Decimal 值的大小
   * 
   * @param {*} a 第一个值
   * @param {*} b 第二个值
   * @returns {number} -1: a < b, 0: a === b, 1: a > b
   */
  static compare(a, b) {
    const numA = this.convertToNumber(a);
    const numB = this.convertToNumber(b);
    
    if (numA < numB) return -1;
    if (numA > numB) return 1;
    return 0;
  }
}

module.exports = DecimalUtils; 