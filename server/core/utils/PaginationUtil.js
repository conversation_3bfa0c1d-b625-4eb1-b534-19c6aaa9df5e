/**
 * 分页工具类
 * 提供通用的分页功能
 */
class PaginationUtil {
  /**
   * 处理分页参数
   * @param {Object} query - 请求查询参数
   * @returns {Object} 处理后的分页参数
   */
  static getPaginationParams(query) {
    const page = parseInt(query.page) || 1;
    const pageSize = parseInt(query.pageSize) || 10;
    
    return {
      page: page < 1 ? 1 : page,
      pageSize: pageSize < 1 ? 10 : (pageSize > 100 ? 100 : pageSize),
      skip: (page - 1) * pageSize
    };
  }

  /**
   * 构建分页响应数据
   * @param {Array} list - 数据列表
   * @param {number} total - 总记录数
   * @param {number} page - 当前页码
   * @param {number} pageSize - 每页记录数
   * @returns {Object} 分页响应数据
   */
  static buildPaginationResponse(list, total, page, pageSize) {
    return {
      list,
      pagination: {
        total: typeof total === 'bigint' ? total.toString() : total,
        page,
        pageSize,
        totalPages: Math.ceil(Number(total) / pageSize)
      }
    };
  }
  
  /**
   * 格式化分页响应数据为统一的API响应格式
   * 直接从服务返回的数据构建完整的分页响应
   * @param {Object} result - 服务返回的结果，包含list和total
   * @param {number} page - 当前页码
   * @param {number} pageSize - 每页记录数
   * @param {string} message - 响应消息
   * @returns {Object} 统一格式的API响应
   */
  static formatPaginationResponse(result, page, pageSize, message = '请求成功') {
    // 转换BigInt
    const safeResult = this.convertBigIntToString(result);
    
    return {
      success: true,
      message: message,
      code: 200,
      data: {
        items: safeResult.list,
        pageInfo: {
          total: safeResult.total,
          currentPage: page,
          totalPage: Math.ceil(Number(safeResult.total) / pageSize)
        }
      }
    };
  }

  /**
   * 将BigInt转换为字符串
   * @param {Object} obj - 包含BigInt的对象
   * @returns {Object} 转换后的对象
   */
  static convertBigIntToString(obj) {
    if (obj === null || obj === undefined) {
      return obj;
    }
    
    if (typeof obj === 'bigint') {
      return obj.toString();
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => PaginationUtil.convertBigIntToString(item));
    }
    
    if (typeof obj === 'object') {
      const result = {};
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          result[key] = PaginationUtil.convertBigIntToString(obj[key]);
        }
      }
      return result;
    }
    
    return obj;
  }
}

module.exports = PaginationUtil;
