/**
 * Redis 工具类
 * 提供JWT黑名单和其他缓存功能
 */
const { createClient } = require('redis');
const jwt = require('jsonwebtoken');
require('dotenv').config();

class RedisUtil {
  constructor() {
    this.client = null;
    this.isConnected = false;
    this.init();
  }

  /**
   * 初始化Redis连接
   */
  async init() {
    try {
      // 直接使用.env文件中的配置值
      const host = process.env.REDIS_HOST;
      const port = process.env.REDIS_PORT;
      const password = process.env.REDIS_PASSWORD;
      const db = process.env.REDIS_DB;
      
      console.log(`Redis配置: ${host}:${port}, 数据库: ${db}`);
      
      const redisUrl = `redis://${host}:${port}`;
      
      this.client = createClient({
        url: redisUrl,
        database: db,
        password: password || undefined
      });

      // 错误处理
      this.client.on('error', (err) => {
        console.error('Redis 连接错误:', err);
        this.isConnected = false;
      });

      // 重连事件
      this.client.on('reconnecting', () => {
        console.warn('Redis 正在尝试重新连接...');
      });

      // 连接成功
      this.client.on('connect', () => {
        console.info('Redis 连接成功');
        this.isConnected = true;
      });

      await this.client.connect();
    } catch (err) {
      console.error('Redis 初始化失败:', err);
      // 失败后继续运行，应用将降级为非缓存模式
    }
  }

  /**
   * 将JWT添加到黑名单
   * @param {string} token JWT令牌
   * @param {number} expiresIn 过期时间（秒）
   */
  async addTokenToBlacklist(token, expiresIn) {
    if (!this.isConnected || !this.client) {
      console.warn('Redis未连接，无法添加令牌到黑名单');
      return false;
    }

    try {
      // 使用token作为键，存储在Redis中
      // 黑名单键前缀
      const key = `jwt:blacklist:${token}`;
      
      // 设置过期时间与JWT剩余有效期一致
      await this.client.set(key, '1', { EX: expiresIn });
      return true;
    } catch (err) {
      console.error('将令牌添加到黑名单失败:', err);
      return false;
    }
  }

  /**
   * 检查JWT是否在黑名单中
   * @param {string} token JWT令牌
   * @returns {Promise<boolean>} 如果在黑名单中返回true
   */
  async isTokenBlacklisted(token) {
    if (!this.isConnected || !this.client) {
      console.warn('Redis未连接，无法检查令牌是否在黑名单中');
      return false;
    }

    try {
      const key = `jwt:blacklist:${token}`;
      const result = await this.client.get(key);
      return result !== null;
    } catch (err) {
      console.error('检查令牌是否在黑名单中失败:', err);
      return false;
    }
  }

  /**
   * 保存用户登录令牌（用于单点登录）
   * @param {string} userId 用户ID
   * @param {string} token JWT令牌
   * @param {number} expiresIn 过期时间（秒）
   * @param {boolean} singleLogin 是否启用单点登录
   */
  async saveUserToken(userId, token, expiresIn, singleLogin = true) {
    if (!this.isConnected || !this.client) {
      console.warn('Redis未连接，无法保存用户令牌');
      return false;
    }

    try {
      const userKey = `user:token:${userId}`;
      const tokenKey = `token:user:${token}`;
      
      if (singleLogin) {
        // 单点登录：获取用户之前的令牌并加入黑名单
        const oldToken = await this.client.get(userKey);
        if (oldToken && oldToken !== token) {
          console.log(`用户 ${userId} 有新的登录，旧令牌 ${oldToken} 将被注销`);
          await this.addTokenToBlacklist(oldToken, expiresIn);
        }
      }
      
      // 保存新令牌和用户ID的映射关系
      await this.client.set(userKey, token, { EX: expiresIn });
      await this.client.set(tokenKey, userId, { EX: expiresIn });
      
      return true;
    } catch (err) {
      console.error('保存用户令牌失败:', err);
      return false;
    }
  }

  /**
   * 删除用户令牌
   * @param {string} userId 用户ID
   * @param {string} token JWT令牌 
   */
  async removeUserToken(userId, token) {
    if (!this.isConnected || !this.client) {
      console.warn('Redis未连接，无法删除用户令牌');
      return false;
    }

    try {
      const userKey = `user:token:${userId}`;
      const tokenKey = `token:user:${token}`;
      
      await this.client.del(userKey);
      await this.client.del(tokenKey);
      
      return true;
    } catch (err) {
      console.error('删除用户令牌失败:', err);
      return false;
    }
  }

  /**
   * 强制用户下线
   * @param {string} userId 用户ID
   */
  async forceUserLogout(userId) {
    if (!this.isConnected || !this.client) {
      console.warn('Redis未连接，无法强制用户下线');
      return false;
    }

    try {
      const userKey = `user:token:${userId}`;
      
      // 获取用户当前令牌
      const token = await this.client.get(userKey);
      if (!token) {
        console.log(`用户 ${userId} 没有活跃的登录会话`);
        return true;
      }
      
      // 解析令牌以获取过期时间
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'julingcloud-secret-key');
      const currentTime = Math.floor(Date.now() / 1000);
      const expirationTime = decoded.exp;
      const remainingTime = expirationTime - currentTime;
      
      if (remainingTime > 0) {
        // 将令牌加入黑名单
        await this.addTokenToBlacklist(token, remainingTime);
        console.log(`用户 ${userId} 已被强制下线，令牌已加入黑名单`);
      }
      
      // 删除用户令牌映射
      await this.removeUserToken(userId, token);
      
      return true;
    } catch (err) {
      console.error('强制用户下线失败:', err);
      return false;
    }
  }

  /**
   * 测试 Redis 连接
   * @returns {Promise<boolean>} 连接是否正常
   */
  async testConnection() {
    if (!this.isConnected || !this.client) {
      console.log('Redis 未连接');
      return false;
    }
    
    try {
      // 尝试设置一个测试键
      const testKey = 'test:connection:' + Date.now();
      const testValue = 'test-' + Math.random().toString(36).substring(2, 10);
      
      console.log(`测试 Redis 连接，设置键: ${testKey}, 值: ${testValue}`);
      
      // 设置测试键
      await this.client.set(testKey, testValue, { EX: 10 });
      
      // 读取测试键
      const readValue = await this.client.get(testKey);
      
      console.log(`读取测试键结果: ${readValue}`);
      
      // 删除测试键
      await this.client.del(testKey);
      
      // 检查值是否匹配
      const isMatch = readValue === testValue;
      console.log(`测试结果: ${isMatch ? '连接正常' : '连接异常'}`);
      
      return isMatch;
    } catch (err) {
      console.error('Redis 连接测试失败:', err);
      return false;
    }
  }

  /**
   * 获取Redis客户端实例
   * @returns Redis客户端实例
   */
  getClient() {
    return this.client;
  }

  /**
   * 关闭Redis连接
   */
  async close() {
    if (this.client && this.isConnected) {
      await this.client.quit();
      this.isConnected = false;
    }
  }
}

// 导出单例实例
module.exports = new RedisUtil();
