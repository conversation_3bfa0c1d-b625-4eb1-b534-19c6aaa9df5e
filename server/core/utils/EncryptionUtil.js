const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

/**
 * 加密工具类
 * 提供RSA加密/解密和密钥生成功能
 */
class EncryptionUtil {
  constructor() {
    this.keyPair = null;
    this.keyDir = path.join(__dirname, '../../config/keys');
    
    // 确保密钥目录存在
    if (!fs.existsSync(this.keyDir)) {
      fs.mkdirSync(this.keyDir, { recursive: true });
    }
    
    // 加载或生成密钥对
    this.initializeKeyPair();
  }
  
  /**
   * 初始化RSA密钥对
   */
  initializeKeyPair() {
    const privateKeyPath = path.join(this.keyDir, 'private.key');
    const publicKeyPath = path.join(this.keyDir, 'public.key');
    
    // 检查密钥文件是否存在
    if (fs.existsSync(privateKeyPath) && fs.existsSync(publicKeyPath)) {
      try {
        // 加载已有密钥
        const privateKey = fs.readFileSync(privateKeyPath, 'utf8');
        const publicKey = fs.readFileSync(publicKeyPath, 'utf8');
        
        this.keyPair = {
          privateKey,
          publicKey
        };
        
        console.log('已加载RSA密钥对');
      } catch (err) {
        console.error('加载RSA密钥失败:', err.message);
        this.generateKeyPair();
      }
    } else {
      // 生成新密钥对
      this.generateKeyPair();
    }
  }
  
  /**
   * 生成新的RSA密钥对
   */
  generateKeyPair() {
    try {
      console.log('正在生成新的RSA密钥对...');
      
      // 生成新密钥对
      const { privateKey, publicKey } = crypto.generateKeyPairSync('rsa', {
        modulusLength: 2048,
        publicKeyEncoding: {
          type: 'spki',
          format: 'pem'
        },
        privateKeyEncoding: {
          type: 'pkcs8',
          format: 'pem'
        }
      });
      
      // 保存密钥对
      fs.writeFileSync(path.join(this.keyDir, 'private.key'), privateKey);
      fs.writeFileSync(path.join(this.keyDir, 'public.key'), publicKey);
      
      this.keyPair = {
        privateKey,
        publicKey
      };
      
      console.log('RSA密钥对生成成功');
    } catch (err) {
      console.error('RSA密钥对生成失败:', err.message);
      throw err;
    }
  }
  
  /**
   * 获取公钥
   * @returns {string} 公钥PEM格式字符串
   */
  getPublicKey() {
    return this.keyPair.publicKey;
  }
  
  /**
   * 使用RSA公钥加密数据
   * 对于长数据，先执行对称加密，然后用RSA加密对称密钥
   * @param {string} data - 要加密的明文数据
   * @returns {string} - Base64编码的加密数据
   */
  encrypt(data) {
    try {
      // 对于较短的数据，直接使用RSA加密
      if (data.length <= 100) {
        const buffer = Buffer.from(data, 'utf8');
        const encrypted = crypto.publicEncrypt(
          {
            key: this.keyPair.publicKey,
            padding: crypto.constants.RSA_PKCS1_OAEP_PADDING
          },
          buffer
        );
        
        return encrypted.toString('base64');
      }
      
      // 对于长数据，这里应该实现混合加密方案
      // 但为简化，这里仍使用RSA直接加密
      console.warn('数据长度超过RSA推荐范围，可能导致加密问题');
      const buffer = Buffer.from(data, 'utf8');
      const encrypted = crypto.publicEncrypt(
        {
          key: this.keyPair.publicKey,
          padding: crypto.constants.RSA_PKCS1_OAEP_PADDING
        },
        buffer
      );
      
      return encrypted.toString('base64');
    } catch (err) {
      console.error('加密失败:', err.message);
      throw err;
    }
  }
  
  /**
   * 使用RSA私钥解密数据
   * @param {string} encryptedText - Base64编码的加密数据
   * @returns {string} - 解密后的明文
   */
  decrypt(encryptedText) {
    try {
      // 解析Base64编码，获取二进制数据
      let buffer;
      try {
        buffer = Buffer.from(encryptedText, 'base64');
      } catch (err) {
        console.error('Base64解码失败:', err.message);
        throw new Error('加密数据格式错误，无法解码');
      }
      
      // 检查数据大小
      if (buffer.length > 256) { // 2048位RSA密钥的最大数据长度
        throw new Error(`数据长度(${buffer.length}字节)超过RSA密钥可处理范围`);
      }
      
      // 执行RSA解密
      const decrypted = crypto.privateDecrypt(
        {
          key: this.keyPair.privateKey,
          padding: crypto.constants.RSA_PKCS1_OAEP_PADDING
        },
        buffer
      );
      
      return decrypted.toString('utf8');
    } catch (err) {
      console.error('解密失败:', err.message);
      throw err;
    }
  }
  
  /**
   * 提供AES对称加密方法（备选方案）
   * @param {string} text - 要加密的文本
   * @param {string} secretKey - 密钥
   * @returns {string} - 加密后的文本（Base64编码）
   */
  static aesEncrypt(text, secretKey) {
    try {
      // 确保密钥长度为32字节（256位）
      const key = crypto.createHash('sha256').update(secretKey).digest();
      // 生成16字节的随机IV
      const iv = crypto.randomBytes(16);
      
      const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
      let encrypted = cipher.update(text, 'utf8', 'base64');
      encrypted += cipher.final('base64');
      
      // 将IV附加到加密文本中（以便解密时使用）
      return iv.toString('base64') + ':' + encrypted;
    } catch (err) {
      console.error('AES加密失败:', err.message);
      throw err;
    }
  }
  
  /**
   * 提供AES对称解密方法（备选方案）
   * @param {string} encryptedText - 加密后的文本（Base64编码，带IV）
   * @param {string} secretKey - 密钥
   * @returns {string} - 解密后的明文
   */
  static aesDecrypt(encryptedText, secretKey) {
    try {
      // 分离IV和加密数据
      const [ivBase64, encryptedData] = encryptedText.split(':');
      
      // 确保密钥长度为32字节（256位）
      const key = crypto.createHash('sha256').update(secretKey).digest();
      const iv = Buffer.from(ivBase64, 'base64');
      
      const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
      let decrypted = decipher.update(encryptedData, 'base64', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (err) {
      console.error('AES解密失败:', err.message);
      throw err;
    }
  }
}

module.exports = new EncryptionUtil();
