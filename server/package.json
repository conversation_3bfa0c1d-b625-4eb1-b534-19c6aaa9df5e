{"name": "server", "version": "1.0.0", "description": "聚灵云4.0后端服务", "main": "index.js", "directories": {"lib": "lib"}, "scripts": {"start": "node index.js", "dev": "node prisma/merge-schema.js && npx prisma generate && nodemon index.js", "prisma:merge": "node prisma/merge-schema.js", "prisma:generate": "npx prisma generate", "prisma:deploy": "npm run prisma:merge && npm run prisma:generate", "prisma:migrate:dev": "npx prisma migrate dev", "prisma:migrate:deploy": "npx prisma migrate deploy", "prisma:db:push": "npx prisma db push", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testMatch='**/__tests__/unit/**/*.test.js'"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@alicloud/credentials": "^2.4.3", "@alicloud/ocr-api20210707": "^3.1.3", "@alicloud/openapi-client": "^0.4.14", "@alicloud/pop-core": "^1.8.0", "@alicloud/tea-util": "^1.4.10", "@elastic/elasticsearch": "^8.13.0", "@prisma/client": "^4.16.2", "ali-oss": "^6.22.0", "amqplib": "^0.10.8", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "body-parser": "^1.20.3", "cors": "^2.8.5", "cos-nodejs-sdk-v5": "^2.14.7", "dotenv": "^16.5.0", "express": "^4.21.2", "form-data": "^4.0.3", "iconv-lite": "^0.6.3", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "node-cron": "^4.1.0", "nodejs-snowflake": "^2.0.1", "nodemailer": "^6.10.1", "pg": "^8.16.1", "qiniu": "^7.14.0", "qrcode": "^1.5.4", "redis": "^4.7.0", "slugify": "^1.6.6", "svg-captcha": "^1.4.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tencentcloud-sdk-nodejs": "^4.1.30", "uuid": "^11.1.0", "vue-qr": "^4.0.9", "wechatpay-node-v3": "^2.2.1", "winston": "^3.17.0", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@jest/globals": "^29.7.0", "@types/jest": "^29.5.14", "babel-jest": "^29.7.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "prisma": "^4.16.2"}}