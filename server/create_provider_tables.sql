-- 创建服务商团队管理相关表

-- 创建团队表
CREATE TABLE IF NOT EXISTS provider.team (
    id BIGINT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    leader_id BIGINT NOT NULL,
    monthly_target DECIMAL(15,2),
    quarterly_target DECIMAL(15,2),
    yearly_target DECIMAL(15,2),
    status INTEGER DEFAULT 1,
    created_by BIGIN<PERSON>,
    updated_by BIGINT,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    deleted_at BIGINT
);

-- 创建团队成员关联表
CREATE TABLE IF NOT EXISTS provider.team_member (
    id BIGINT PRIMARY KEY,
    team_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    role VARCHAR(20) DEFAULT 'member',
    monthly_target DECIMAL(15,2),
    quarterly_target DECIMAL(15,2),
    yearly_target DECIMAL(15,2),
    status INTEGER DEFAULT 1,
    join_time BIGINT NOT NULL,
    created_by B<PERSON>IN<PERSON>,
    updated_by BIGIN<PERSON>,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    deleted_at BIGINT,
    CONSTRAINT fk_team_member_team FOREIGN KEY (team_id) REFERENCES provider.team(id),
    CONSTRAINT unique_team_user UNIQUE (team_id, user_id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_team_leader_id ON provider.team(leader_id);
CREATE INDEX IF NOT EXISTS idx_team_status ON provider.team(status);
CREATE INDEX IF NOT EXISTS idx_team_deleted_at ON provider.team(deleted_at);
CREATE INDEX IF NOT EXISTS idx_team_created_by ON provider.team(created_by);

CREATE INDEX IF NOT EXISTS idx_team_member_team_id ON provider.team_member(team_id);
CREATE INDEX IF NOT EXISTS idx_team_member_user_id ON provider.team_member(user_id);
CREATE INDEX IF NOT EXISTS idx_team_member_status ON provider.team_member(status);
CREATE INDEX IF NOT EXISTS idx_team_member_deleted_at ON provider.team_member(deleted_at);

-- 添加表注释
COMMENT ON TABLE provider.team IS '服务商团队信息表，存储团队基本信息';
COMMENT ON COLUMN provider.team.id IS '团队ID，16位雪花算法，系统自动生成';
COMMENT ON COLUMN provider.team.name IS '团队名称，必填';
COMMENT ON COLUMN provider.team.description IS '团队描述';
COMMENT ON COLUMN provider.team.leader_id IS '团队负责人ID，关联系统用户表';
COMMENT ON COLUMN provider.team.monthly_target IS '月度目标金额';
COMMENT ON COLUMN provider.team.quarterly_target IS '季度目标金额';
COMMENT ON COLUMN provider.team.yearly_target IS '年度目标金额';
COMMENT ON COLUMN provider.team.status IS '状态：1-正常，0-禁用';
COMMENT ON COLUMN provider.team.created_by IS '创建者ID，16位';
COMMENT ON COLUMN provider.team.updated_by IS '更新者ID，16位';
COMMENT ON COLUMN provider.team.created_at IS '创建时间戳（毫秒）';
COMMENT ON COLUMN provider.team.updated_at IS '更新时间戳（毫秒）';
COMMENT ON COLUMN provider.team.deleted_at IS '删除时间戳（毫秒）（软删除）';

COMMENT ON TABLE provider.team_member IS '服务商团队成员关联表，存储团队与成员的多对多关系';
COMMENT ON COLUMN provider.team_member.id IS '关联ID，16位雪花算法，系统自动生成';
COMMENT ON COLUMN provider.team_member.team_id IS '团队ID，关联团队表';
COMMENT ON COLUMN provider.team_member.user_id IS '用户ID，关联系统用户表';
COMMENT ON COLUMN provider.team_member.role IS '成员角色：leader-负责人，member-普通成员';
COMMENT ON COLUMN provider.team_member.monthly_target IS '个人月度目标金额';
COMMENT ON COLUMN provider.team_member.quarterly_target IS '个人季度目标金额';
COMMENT ON COLUMN provider.team_member.yearly_target IS '个人年度目标金额';
COMMENT ON COLUMN provider.team_member.status IS '状态：1-正常，0-禁用';
COMMENT ON COLUMN provider.team_member.join_time IS '加入团队时间戳（毫秒）';
COMMENT ON COLUMN provider.team_member.created_by IS '创建者ID，16位';
COMMENT ON COLUMN provider.team_member.updated_by IS '更新者ID，16位';
COMMENT ON COLUMN provider.team_member.created_at IS '创建时间戳（毫秒）';
COMMENT ON COLUMN provider.team_member.updated_at IS '更新时间戳（毫秒）';
COMMENT ON COLUMN provider.team_member.deleted_at IS '删除时间戳（毫秒）（软删除）';
