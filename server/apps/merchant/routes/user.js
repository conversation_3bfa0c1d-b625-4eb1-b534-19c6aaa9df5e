const express = require('express');
const router = express.Router();
const userController = require('../controllers/UserController');

  /**
   * @swagger
   * /api/merchant/users:
   *   post:
   *     tags: [商户用户]
   *     summary: 创建商户用户
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required: [username, password, email, companyName]
   *             properties:
   *               username:
   *                 type: string
   *               password:
   *                 type: string
   *               email:
   *                 type: string
   *               companyName:
   *                 type: string
   *     responses:
   *       201:
   *         description: 用户创建成功
   */
  router.post('/', userController.create);

  /**
   * @swagger
   * /api/merchant/users/{id}:
   *   put:
   *     tags: [商户用户]
   *     summary: 更新商户用户
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: string
   *     requestBody:
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               username:
   *                 type: string
   *               email:
   *                 type: string
   *               companyName:
   *                 type: string
   *     responses:
   *       200:
   *         description: 用户更新成功
   */
  router.put('/:id', userController.update);

  /**
   * @swagger
   * /api/merchant/users/{id}:
   *   delete:
   *     tags: [商户用户]
   *     summary: 删除商户用户
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: string
   *     responses:
   *       204:
   *         description: 用户删除成功
   */
  router.delete('/:id', userController.delete);

  /**
   * @swagger
   * /api/merchant/users/{id}:
   *   get:
   *     tags: [商户用户]
   *     summary: 获取单个商户用户
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: string
   *     responses:
   *       200:
   *         description: 返回用户信息
   */
  router.get('/:id', userController.getById);

  /**
   * @swagger
   * /api/merchant/users:
   *   get:
   *     tags: [商户用户]
   *     summary: 获取商户用户列表
   *     parameters:
   *       - name: page
   *         in: query
   *         schema:
   *           type: integer
   *           default: 1
   *       - name: pageSize
   *         in: query
   *         schema:
   *           type: integer
   *           default: 10
   *     responses:
   *       200:
   *         description: 返回用户列表
   */
  router.get('/', userController.list);

  /**
   * @swagger
   * /api/merchant/users/by-company/{companyName}:
   *   get:
   *     tags: [商户用户]
   *     summary: 通过公司名查找商户用户
   *     parameters:
   *       - name: companyName
   *         in: path
   *         required: true
   *         schema:
   *           type: string
   *     responses:
   *       200:
   *         description: 返回用户信息
   */
  router.get('/by-company/:companyName', userController.findByCompanyName);

module.exports = router;
