class UserController {
  /**
   * @swagger
   * /api/merchant/users:
   *   post:
   *     tags:
   *       - 商户用户管理
   *     summary: 创建商户用户
   *     description: 创建一个新商户用户
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UserCreateRequest'
   *     responses:
   *       201:
   *         description: 创建成功
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/Success'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/User'
   */
  async create(req, res) {
    try {
      // 模拟创建商户用户
      const user = { id: 1, ...req.body };
      res.status(201).json({
        success: true,
        message: '创建商户用户成功',
        data: user
      });
    } catch (err) {
      res.status(400).json({
        success: false,
        message: err.message
      });
    }
  }

  /**
   * @swagger
   * /api/merchant/users/{id}:
   *   put:
   *     tags:
   *       - 商户用户管理
   *     summary: 更新商户用户
   *     description: 更新指定商户用户的信息
   *     parameters:
   *       - name: id
   *         in: path
   *         description: 用户ID
   *         required: true
   *         schema:
   *           type: integer
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UserUpdateRequest'
   *     responses:
   *       200:
   *         description: 更新成功
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/Success'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/User'
   */
  async update(req, res) {
    try {
      // 模拟更新商户用户
      const user = { id: parseInt(req.params.id), ...req.body };
      res.json({
        success: true,
        message: '更新商户用户成功',
        data: user
      });
    } catch (err) {
      res.status(400).json({
        success: false,
        message: err.message
      });
    }
  }

  /**
   * @swagger
   * /api/merchant/users/{id}:
   *   delete:
   *     tags:
   *       - 商户用户管理
   *     summary: 删除商户用户
   *     description: 删除指定的商户用户
   *     parameters:
   *       - name: id
   *         in: path
   *         description: 用户ID
   *         required: true
   *         schema:
   *           type: integer
   *     responses:
   *       200:
   *         description: 删除成功
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Success'
   */
  async delete(req, res) {
    try {
      // 模拟删除商户用户
      res.json({
        success: true,
        message: '删除商户用户成功'
      });
    } catch (err) {
      res.status(400).json({
        success: false,
        message: err.message
      });
    }
  }

  /**
   * @swagger
   * /api/merchant/users/{id}:
   *   get:
   *     tags:
   *       - 商户用户管理
   *     summary: 获取商户用户详情
   *     description: 获取指定商户用户的详细信息
   *     parameters:
   *       - name: id
   *         in: path
   *         description: 用户ID
   *         required: true
   *         schema:
   *           type: integer
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/Success'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/User'
   */
  async getById(req, res) {
    try {
      // 模拟获取商户用户
      const user = { id: parseInt(req.params.id), username: 'merchant1' };
      if (!user) {
        return res.status(404).json({
          success: false,
          message: '商户用户不存在'
        });
      }
      res.json({
        success: true,
        data: user
      });
    } catch (err) {
      res.status(400).json({
        success: false,
        message: err.message
      });
    }
  }

  /**
   * @swagger
   * /api/merchant/users:
   *   get:
   *     tags:
   *       - 商户用户管理
   *     summary: 获取商户用户列表
   *     description: 分页获取商户用户列表
   *     parameters:
   *       - name: page
   *         in: query
   *         description: 页码
   *         schema:
   *           type: integer
   *           default: 1
   *       - name: pageSize
   *         in: query
   *         description: 每页记录数
   *         schema:
   *           type: integer
   *           default: 10
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/UserListResponse'
   */
  async list(req, res) {
    try {
      const { page = 1, pageSize = 10 } = req.query;
      
      // 模拟获取商户用户列表
      const result = {
        list: [{ id: 1, username: 'merchant1' }],
        total: 1,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      };

      res.json({
        success: true,
        data: result
      });
    } catch (err) {
      res.status(400).json({
        success: false,
        message: err.message
      });
    }
  }

  /**
   * @swagger
   * /api/merchant/users/by-company/{companyName}:
   *   get:
   *     tags:
   *       - 商户用户管理
   *     summary: 通过公司名称查找商户用户
   *     description: 根据公司名称查找商户用户
   *     parameters:
   *       - name: companyName
   *         in: path
   *         description: 公司名称
   *         required: true
   *         schema:
   *           type: string
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/Success'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/User'
   */
  async findByCompanyName(req, res) {
    try {
      // 模拟通过公司名称查找商户用户
      const user = { id: 1, username: 'merchant1', companyName: req.params.companyName };
      if (!user) {
        return res.status(404).json({
          success: false,
          message: '商户不存在'
        });
      }
      res.json({
        success: true,
        data: user
      });
    } catch (err) {
      res.status(400).json({
        success: false,
        message: err.message
      });
    }
  }
}

module.exports = new UserController();
