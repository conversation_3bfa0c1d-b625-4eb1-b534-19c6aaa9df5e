/**
 * 新闻分类相关的Swagger模式定义
 */

module.exports = {
  /**
   * @swagger
   * components:
   *   schemas:
   *     NewsCategory:
   *       type: object
   *       properties:
   *         id:
   *           type: string
   *           description: 分类ID

   *         name:
   *           type: string
   *           description: 分类名称
   *         description:
   *           type: string
   *           nullable: true
   *           description: 分类描述

   *         is_enabled:
   *           type: integer
   *           enum: [0, 1]
   *           description: 是否启用：1-启用，0-禁用
   *         created_at:
   *           type: string
   *           description: 创建时间戳（毫秒）
   *         updated_at:
   *           type: string
   *           description: 更新时间戳（毫秒）
   *       required:
   *         - id
   *         - name
   *         - is_enabled
   *         - created_at
   *         - updated_at
   * 
   *     NewsCategoryWithChildren:
   *       allOf:
   *         - $ref: '#/components/schemas/NewsCategory'
   *         - type: object
   *           properties:
   *             children:
   *               type: array
   *               items:
   *                 $ref: '#/components/schemas/NewsCategoryWithChildren'
   *               description: 子分类列表
   * 
   *     NewsCategoryCreateRequest:
   *       type: object
   *       properties:
   *         name:
   *           type: string
   *           description: 分类名称
   *         description:
   *           type: string
   *           nullable: true
   *           description: 分类描述
   *         is_enabled:
   *           type: integer
   *           enum: [0, 1]
   *           description: 是否启用：1-启用，0-禁用
   *           default: 1
   *       required:
   *         - name
   * 
   *     NewsCategoryUpdateRequest:
   *       type: object
   *       properties:
   *         name:
   *           type: string
   *           description: 分类名称
   *         description:
   *           type: string
   *           nullable: true
   *           description: 分类描述
   *         is_enabled:
   *           type: integer
   *           enum: [0, 1]
   *           description: 是否启用：1-启用，0-禁用
   * 
   *     NewsCategoryResponse:
   *       type: object
   *       properties:
   *         code:
   *           type: integer
   *           description: 状态码
   *           example: 200
   *         message:
   *           type: string
   *           description: 响应消息
   *           example: 成功
   *         data:
   *           $ref: '#/components/schemas/NewsCategory'
   * 
   *     NewsCategoryDetailResponse:
   *       type: object
   *       properties:
   *         code:
   *           type: integer
   *           description: 状态码
   *           example: 200
   *         message:
   *           type: string
   *           description: 响应消息
   *           example: 成功
   *         data:
   *           $ref: '#/components/schemas/NewsCategory'
   * 
   *     NewsCategoryListResponse:
   *       type: object
   *       properties:
   *         code:
   *           type: integer
   *           description: 状态码
   *           example: 200
   *         message:
   *           type: string
   *           description: 响应消息
   *           example: 成功
   *         data:
   *           type: object
   *           properties:
   *             list:
   *               type: array
   *               items:
   *                 $ref: '#/components/schemas/NewsCategory'
   *               description: 分类列表数据
   *             pagination:
   *               type: object
   *               properties:
   *                 current:
   *                   type: integer
   *                   description: 当前页码
   *                 pageSize:
   *                   type: integer
   *                   description: 每页数量
   *                 total:
   *                   type: integer
   *                   description: 总记录数
   * 
   *     NewsCategoryTreeResponse:
   *       type: object
   *       properties:
   *         code:
   *           type: integer
   *           description: 状态码
   *           example: 200
   *         message:
   *           type: string
   *           description: 响应消息
   *           example: 成功
   *         data:
   *           type: array
   *           items:
   *             $ref: '#/components/schemas/NewsCategoryWithChildren'
   *           description: 分类树形结构数据
   * 
   *     SuccessResponse:
   *       type: object
   *       properties:
   *         code:
   *           type: integer
   *           description: 状态码
   *           example: 200
   *         message:
   *           type: string
   *           description: 响应消息
   *           example: 成功
   *         data:
   *           type: object
   *           description: 响应数据
   */
};
