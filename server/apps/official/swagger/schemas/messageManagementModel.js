/**
 * 留言管理Swagger模型定义
 */
const { messageModel } = require('../../models/MessageManagementModel');

/**
 * 留言创建请求
 */
const MessageCreateRequest = {
  type: 'object',
  required: ['message_location', 'submitter_name'],
  properties: {
    message_location: {
      type: 'string',
      description: '留言位置',
      example: '官网首页'
    },
    submitter_name: {
      type: 'string',
      description: '留言人姓名',
      example: '张三'
    },
    email: {
      type: 'string',
      format: 'email',
      description: '邮箱',
      example: '<EMAIL>'
    },
    phone: {
      type: 'string',
      description: '电话',
      example: '13800138000'
    },
    message_details: {
      type: 'string',
      description: '留言详情',
      example: '我想了解贵公司的产品服务，请与我联系。'
    },
    attachment_name: {
      type: 'string',
      description: '附件名称',
      example: '需求文档.pdf'
    },
    attachment_url: {
      type: 'string',
      description: '附件地址',
      example: 'https://example.com/uploads/需求文档.pdf'
    }
  }
};

/**
 * 留言更新请求
 */
const MessageUpdateRequest = {
  type: 'object',
  properties: {
    message_location: {
      type: 'string',
      description: '留言位置',
      example: '官网首页'
    },
    submitter_name: {
      type: 'string',
      description: '留言人姓名',
      example: '张三'
    },
    email: {
      type: 'string',
      format: 'email',
      description: '邮箱',
      example: '<EMAIL>'
    },
    phone: {
      type: 'string',
      description: '电话',
      example: '13800138000'
    },
    message_details: {
      type: 'string',
      description: '留言详情',
      example: '我想了解贵公司的产品服务，请与我联系。'
    },
    attachment_name: {
      type: 'string',
      description: '附件名称',
      example: '需求文档.pdf'
    },
    attachment_url: {
      type: 'string',
      description: '附件地址',
      example: 'https://example.com/uploads/需求文档.pdf'
    },
    status: {
      type: 'integer',
      enum: [0, 1],
      description: '状态：0-未处理，1-已处理',
      example: 1
    }
  }
};

/**
 * 留言状态更新请求
 */
const MessageStatusUpdateRequest = {
  type: 'object',
  required: ['status'],
  properties: {
    status: {
      type: 'integer',
      enum: [0, 1],
      description: '状态：0-未处理，1-已处理',
      example: 1
    }
  }
};

/**
 * 留言响应
 */
const MessageResponse = {
  type: 'object',
  properties: {
    success: {
      type: 'boolean',
      description: '请求是否成功',
      example: true
    },
    message: {
      type: 'string',
      description: '响应消息',
      example: '操作成功'
    },
    data: {
      type: 'object',
      properties: messageModel
    }
  }
};

/**
 * 留言详情响应
 */
const MessageDetailResponse = {
  type: 'object',
  properties: {
    success: {
      type: 'boolean',
      description: '请求是否成功',
      example: true
    },
    message: {
      type: 'string',
      description: '响应消息',
      example: '获取成功'
    },
    data: {
      type: 'object',
      properties: messageModel
    }
  }
};

/**
 * 留言列表响应
 */
const MessageListResponse = {
  type: 'object',
  properties: {
    success: {
      type: 'boolean',
      description: '请求是否成功',
      example: true
    },
    message: {
      type: 'string',
      description: '响应消息',
      example: '获取成功'
    },
    data: {
      type: 'object',
      properties: {
        items: {
          type: 'array',
          items: {
            type: 'object',
            properties: messageModel
          }
        },
        pageInfo: {
          type: 'object',
          properties: {
            total: {
              type: 'integer',
              description: '总记录数',
              example: 100
            },
            currentPage: {
              type: 'integer',
              description: '当前页码',
              example: 1
            },
            totalPage: {
              type: 'integer',
              description: '总页数',
              example: 10
            }
          }
        }
      }
    }
  }
};

module.exports = {
  MessageCreateRequest,
  MessageUpdateRequest,
  MessageStatusUpdateRequest,
  MessageResponse,
  MessageDetailResponse,
  MessageListResponse
};
