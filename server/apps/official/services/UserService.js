/**
 * 官方用户服务
 * 负责用户业务逻辑处理
 */

/**
 * 处理BigInt序列化问题
 * @param {Object} data 要处理的数据
 * @returns {Object} 处理后的数据
 */
function handleBigInt(data) {
  if (data === null || data === undefined) return data;
  
  if (typeof data === 'bigint') {
    return data.toString();
  }
  
  if (Array.isArray(data)) {
    return data.map(item => handleBigInt(item));
  }
  
  if (typeof data === 'object' && !(data instanceof Date)) {
    const result = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        result[key] = handleBigInt(data[key]);
      }
    }
    return result;
  }
  
  return data;
}

/**
 * 官方用户服务类
 */
class UserService {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    this.prisma = prisma;
  }
  
  /**
   * 根据用户ID获取用户信息
   * @param {string} userId 用户ID
   * @returns {Promise<Object>} 用户信息
   */
  async getUserById(userId) {
    try {
      // 从数据库中查询用户信息
      // 注意：这里需要根据实际的Prisma模型名称进行调整
      const user = await this.prisma.OfficialUser.findFirst({
        where: {
          id: userId,
          deleted_at: null
        }
      });
      
      if (!user) {
        return {
          code: 404,
          message: '用户不存在',
          data: null
        };
      }
      
      // 处理BigInt类型
      const processedUser = handleBigInt(user);
      
      // 返回用户信息，移除敏感字段
      const { password, ...userInfo } = processedUser;
      
      return {
        code: 200,
        message: '获取用户信息成功',
        data: userInfo
      };
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return {
        code: 500,
        message: '获取用户信息失败',
        data: null
      };
    }
  }
  
  /**
   * 用户登录
   * @param {string} username 用户名
   * @param {string} password 密码
   * @returns {Promise<Object>} 登录结果，包含token和用户信息
   */
  async login(username, password) {
    try {
      // 从数据库中查询用户
      // 注意：这里需要根据实际的Prisma模型名称进行调整
      const user = await this.prisma.OfficialUser.findFirst({
        where: {
          username: username,
          deleted_at: null
        }
      });
      
      if (!user) {
        return {
          code: 401,
          message: '用户名或密码错误',
          data: null
        };
      }
      
      // 验证密码
      // 实际项目中应该使用bcrypt等库进行密码验证
      // const isPasswordValid = await bcrypt.compare(password, user.password);
      const isPasswordValid = password === user.password; // 示例验证，实际应使用加密比较
      
      if (!isPasswordValid) {
        return {
          code: 401,
          message: '用户名或密码错误',
          data: null
        };
      }
      
      // 生成JWT令牌
      // 实际项目中应该使用jwt库生成令牌
      // const token = jwt.sign({ id: user.id, username: user.username }, JWT_SECRET, { expiresIn: '24h' });
      const token = 'mock_token_for_official_user'; // 示例令牌
      
      // 处理BigInt类型
      const processedUser = handleBigInt(user);
      
      // 返回登录结果，移除敏感字段
      const { password: _, ...userInfo } = processedUser;
      
      return {
        code: 200,
        message: '登录成功',
        data: {
          token: token,
          user: userInfo
        }
      };
    } catch (error) {
      console.error('用户登录失败:', error);
      return {
        code: 500,
        message: '登录失败，服务器错误',
        data: null
      };
    }
  }
  
  /**
   * 获取用户列表
   * @param {Object} filters 筛选条件
   * @param {number} page 页码
   * @param {number} pageSize 每页数量
   * @returns {Promise<Object>} 用户列表和分页信息
   */
  async getUserList(filters = {}, page = 1, pageSize = 10) {
    try {
      // 构建查询条件
      const where = {
        deleted_at: null
      };
      
      // 添加筛选条件
      if (filters.username) {
        where.username = {
          contains: filters.username
        };
      }
      
      if (filters.status !== undefined) {
        where.status = parseInt(filters.status);
      }
      
      // 计算分页参数
      const skip = (page - 1) * pageSize;
      
      // 查询总记录数
      const total = await this.prisma.OfficialUser.count({ where });
      
      // 查询用户列表
      const users = await this.prisma.OfficialUser.findMany({
        where,
        select: {
          id: true,
          username: true,
          nickname: true,
          avatar: true,
          status: true,
          created_at: true,
          updated_at: true
        },
        orderBy: {
          created_at: 'desc'
        },
        skip,
        take: pageSize
      });
      
      // 处理BigInt类型
      const processedUsers = handleBigInt(users);
      
      // 计算总页数
      const totalPages = Math.ceil(total / pageSize);
      
      return {
        code: 200,
        message: '获取用户列表成功',
        data: {
          items: processedUsers,
          pageInfo: {
            total,
            currentPage: parseInt(page),
            totalPage: totalPages
          }
        }
      };
    } catch (error) {
      console.error('获取用户列表失败:', error);
      return {
        code: 500,
        message: '获取用户列表失败',
        data: null
      };
    }
  }
}

module.exports = UserService;
