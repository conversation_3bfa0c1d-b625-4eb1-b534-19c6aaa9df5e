const express = require('express');
const router = express.Router();
const userRoutes = require('./user');
const caseRoutes = require('./CaseManagementRoute');
const newsCategoryRoutes = require('./news-category');
const newsArticleRoutes = require('./news-article');
const RecruitmentRoute = require('./RecruitmentRoute');
const MessageManagementRoute = require('./MessageManagementRoute');
const productCategoryRoutes = require('./product-category');
const productRoutes = require('./product');
const companyInfoRoutes = require('./company-info');
const EnterpriseInformationRoute = require('./EnterpriseInformationRoute');

/**
 * @swagger
 * tags:
 *   - name: 官方用户
 *     description: 官方用户管理接口
 */

// 用户路由
router.use('/users', userRoutes);

// 案例路由
router.use('/case-management', caseRoutes);

// 新闻分类路由
router.use('/news-categories', newsCategoryRoutes);

// 新闻文章路由
router.use('/news-articles', newsArticleRoutes);

// 招聘管理
router.use('/recruitment', RecruitmentRoute);

// 留言管理
router.use('/message-management', MessageManagementRoute);

// 产品分类路由
router.use('/product-categories', productCategoryRoutes);

// 产品路由
router.use('/products', productRoutes);

// 公司基础信息路由
router.use('/company-info', companyInfoRoutes);

// 企业信息路由
router.use('/enterprise-information', EnterpriseInformationRoute);

module.exports = router;
