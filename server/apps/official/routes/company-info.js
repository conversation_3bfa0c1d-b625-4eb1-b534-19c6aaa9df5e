/**
 * 公司基础信息路由
 */
const express = require('express');
const mainRouter = express.Router(); // 主路由器对象

// 创建另一个路由器对象用于需要验证的路由
const authRouter = express.Router();
const RouterConfig = require('../../../core/routes/RouterConfig');

// 应用JWT保护到authRouter
RouterConfig.authRoute(authRouter);

// 将这两个路由器对象汇总为一个路由器
const router = express.Router();

/**
 * @swagger
 * /api/v1/official/company-info:
 *   get:
 *     tags:
 *       - 公司信息管理
 *     summary: 获取公司信息列表
 *     description: 分页获取公司信息列表，支持按名称等条件进行筛选
 *     parameters:
 *       - name: page
 *         in: query
 *         description: 页码，默认为1
 *         schema:
 *           type: integer
 *           default: 1
 *       - name: pageSize
 *         in: query
 *         description: 每页数量，默认为10
 *         schema:
 *           type: integer
 *           default: 10
 *       - name: name
 *         in: query
 *         description: 公司名称（模糊搜索）
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 */
mainRouter.get('/', (req, res) => req.controllers.CompanyInfoController.getCompanyInfoList(req, res));

/**
 * @swagger
 * /api/v1/official/company-info/{id}:
 *   get:
 *     tags:
 *       - 公司信息管理
 *     summary: 获取公司信息详情
 *     description: 根据ID获取公司信息详情
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: 公司信息ID
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *       404:
 *         description: 信息不存在
 */
mainRouter.get('/:id', (req, res) => req.controllers.CompanyInfoController.getCompanyInfoById(req, res));


/**
 * @swagger
 * /api/v1/official/company-info:
 *   post:
 *     tags:
 *       - 公司信息管理
 *     summary: 保存公司基础信息
 *     description: 创建或更新公司的基本信息
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SaveCompanyInfoRequest'
 *     responses:
 *       200:
 *         description: 保存成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 */
authRouter.post('/', (req, res) => req.controllers.CompanyInfoController.saveCompanyInfo(req, res));

/**
 * @swagger
 * /api/v1/official/company-info/{id}:
 *   put:
 *     tags:
 *       - 公司信息管理
 *     summary: 更新指定ID的公司信息
 *     description: 根据ID更新公司的基本信息
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: 公司信息ID
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SaveCompanyInfoRequest'
 *     responses:
 *       200:
 *         description: 更新成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *       404:
 *         description: 公司信息不存在
 */
authRouter.put('/:id', (req, res) => req.controllers.CompanyInfoController.updateCompanyInfoById(req, res));
// 将两个路由器合并到主路由器
router.use('/', mainRouter);
router.use('/', authRouter);
module.exports = router;
