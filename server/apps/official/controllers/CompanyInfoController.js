/**
 * 公司基础信息控制器
 * 处理公司基础信息相关的请求
 */
const BaseController = require('../../../core/controllers/BaseController');
const CompanyInfoService = require('../services/CompanyInfoService');

/**
 * 公司基础信息控制器类
 */
class CompanyInfoController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    super();
    this.prisma = prisma;
    this.companyInfoService = new CompanyInfoService(prisma);
  }
  
  /**
   * 从请求中获取用户ID
   * @param {Object} req Express请求对象
   * @returns {string|null} 用户ID或null
   */
  getUserId(req) {
    // 如果请求中有用户信息，则返回用户ID，否则返回null
    return req.user ? req.user.id : null;
  }

  /**
   * 保存公司基础信息
   * @param {Object} req Express请求对象
   * @param {Object} res Express响应对象
   */
  async saveCompanyInfo(req, res) {
    try {
      const userId = this.getUserId(req) || 0; // 获取当前用户ID，如果不存在则默认为0
      
      const data = req.body;
      const result = await this.companyInfoService.saveCompanyInfo(data, userId);
      
      return this.success(res, result, '保存成功');
    } catch (error) {
      return this.fail(res, `保存失败: ${error.message}`);
    }
  }

  /**
   * 获取公司基础信息
   * @param {Object} req Express请求对象
   * @param {Object} res Express响应对象
   */
  async getCompanyInfo(req, res) {
    try {
      const result = await this.companyInfoService.getCompanyInfo();
      
      if (!result) {
        return this.success(res, null, '公司信息未设置');
      }
      
      return this.success(res, result, '获取成功');
    } catch (error) {
      return this.fail(res, `获取失败: ${error.message}`);
    }
  }
  
  /**
   * 获取公司信息列表
   * @param {Object} req Express请求对象
   * @param {Object} res Express响应对象
   */
  async getCompanyInfoList(req, res) {
    try {
      const params = {
        ...req.query,
        page: parseInt(req.query.current || req.query.page || 1, 10),
        pageSize: parseInt(req.query.pageSize || 10, 10)
      };
      
      const result = await this.companyInfoService.getCompanyInfoList(params);
      
      return this.success(res, result, '获取成功');
    } catch (error) {
      return this.fail(res, `获取公司信息列表失败: ${error.message}`);
    }
  }
  
  /**
   * 获取公司信息详情
   * @param {Object} req Express请求对象
   * @param {Object} res Express响应对象
   */
  async getCompanyInfoById(req, res) {
    try {
      const { id } = req.params;
      const result = await this.companyInfoService.getCompanyInfoById(id);
      
      return this.success(res, result, '获取成功');
    } catch (error) {
      return this.fail(res, `获取公司信息详情失败: ${error.message}`);
    }
  }
  
  /**
   * 更新指定ID的公司信息
   * @param {Object} req Express请求对象
   * @param {Object} res Express响应对象
   */
  async updateCompanyInfoById(req, res) {
    try {
      const { id } = req.params;
      const userId = this.getUserId(req) || 0; // 获取当前用户ID，如果不存在则默认为0
      
      const data = req.body;
      const result = await this.companyInfoService.updateCompanyInfoById(id, data, userId);
      
      return this.success(res, result, '更新成功');
    } catch (error) {
      return this.fail(res, `更新公司信息失败: ${error.message}`);
    }
  }
}

module.exports = CompanyInfoController;
