/**
 * 产品控制器
 * 处理产品相关的请求
 */
const BaseController = require('../../../core/controllers/BaseController');
const ProductService = require('../services/ProductService');

/**
 * 产品控制器类
 */
class ProductController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    super();
    this.prisma = prisma;
    this.productService = new ProductService(prisma);
  }
  
  /**
   * 从请求中获取用户ID
   * @param {Object} req Express请求对象
   * @returns {string|null} 用户ID或null
   */
  getUserId(req) {
    // 如果请求中有用户信息，则返回用户ID，否则返回null
    return req.user ? req.user.id : null;
  }

  /**
   * 创建产品
   * @param {Object} req Express请求对象
   * @param {Object} res Express响应对象
   */
  async createProduct(req, res) {
    try {
      const userId = this.getUserId(req) || 0; // 获取当前用户ID，如果不存在则默认为0
      
      const data = req.body;
      const result = await this.productService.createProduct(data, userId);
      
      return this.success(res, result, '创建成功');
    } catch (error) {
      return this.fail(res, `创建失败: ${error.message}`);
    }
  }

  /**
   * 获取产品详情
   * @param {Object} req Express请求对象
   * @param {Object} res Express响应对象
   */
  async getProductById(req, res) {
    try {
      const { id } = req.params;
      const result = await this.productService.getProductById(id);
      
      return this.success(res, result, '获取成功');
    } catch (error) {
      return this.fail(res, `获取失败: ${error.message}`);
    }
  }

  /**
   * 获取产品列表
   * @param {Object} req Express请求对象
   * @param {Object} res Express响应对象
   */
  async getList(req, res) {
    try {
      const params = {
        page: req.query.page,
        pageSize: req.query.pageSize,
        name: req.query.name,
        description: req.query.description,
        category_id: req.query.category_id,
        is_enabled: req.query.is_enabled !== undefined ? parseInt(req.query.is_enabled) : undefined
      };
      
      const result = await this.productService.getProducts(params);
      
      return this.success(res, result, '获取成功');
    } catch (error) {
      return this.fail(res, `获取失败: ${error.message}`);
    }
  }

  /**
   * 更新产品
   * @param {Object} req Express请求对象
   * @param {Object} res Express响应对象
   */
  async updateProduct(req, res) {
    try {
      const { id } = req.params;
      const userId = this.getUserId(req) || 0; // 获取当前用户ID，如果不存在则默认为0
      
      const data = req.body;
      const result = await this.productService.updateProduct(id, data, userId);
      
      return this.success(res, result, '更新成功');
    } catch (error) {
      return this.fail(res, `更新失败: ${error.message}`);
    }
  }

  /**
   * 删除产品
   * @param {Object} req Express请求对象
   * @param {Object} res Express响应对象
   */
  async deleteProduct(req, res) {
    try {
      const { id } = req.params;
      const userId = this.getUserId(req) || 0; // 获取当前用户ID，如果不存在则默认为0
      
      const result = await this.productService.deleteProduct(id, userId);
      
      return this.success(res, result, '删除成功');
    } catch (error) {
      return this.fail(res, `删除失败: ${error.message}`);
    }
  }

  /**
   * 批量删除产品
   * @param {Object} req Express请求对象
   * @param {Object} res Express响应对象
   */
  async batchDeleteProducts(req, res) {
    try {
      const { ids } = req.body;
      const userId = this.getUserId(req) || 0; // 获取当前用户ID，如果不存在则默认为0
      
      const result = await this.productService.batchDeleteProducts(ids, userId);
      
      return this.success(res, result, '批量删除成功');
    } catch (error) {
      return this.fail(res, `批量删除失败: ${error.message}`);
    }
  }

  /**
   * 切换产品状态
   * @param {Object} req Express请求对象
   * @param {Object} res Express响应对象
   */
  async toggleStatus(req, res) {
    try {
      const { id } = req.params;
      const { is_enabled } = req.body;
      const userId = this.getUserId(req) || 0; // 获取当前用户ID，如果不存在则默认为0
      
      const result = await this.productService.toggleProductStatus(id, is_enabled, userId);
      
      return this.success(res, result, '状态更新成功');
    } catch (error) {
      return this.fail(res, `状态更新失败: ${error.message}`);
    }
  }
}

module.exports = ProductController;
