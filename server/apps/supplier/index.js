const path = require('path');
const BaseModule = require('../../core/module/BaseModule');

/**
 * 供应商模块
 */
class SupplierModule extends BaseModule {
  /**
   * 初始化路由
   */
  async initRoutes() {
    // 加载路由
    const routes = require('./routes');
    this.router.use('/', routes);
  }

  /**
   * 初始化 Swagger 文档
   */
  async initSwagger() {
    const swaggerPaths = [
      path.join(__dirname, 'swagger', 'schemas', '*.js'),
      path.join(__dirname, 'routes', '**', '*.js')
    ];
    
    // 注册 Swagger 文档
    this.emit('swagger:register', {
      module: this.config.name,
      paths: swaggerPaths
    });
  }

  /**
   * 初始化事件监听
   */
  async initEventListeners() {
    // 监听用户登录事件
    this.on('user:login', async ({ data }) => {
      console.log(`供应商模块：用户 ${data.username} 登录系统`);
    });
  }
}

module.exports = SupplierModule;
