// Supplier 模块的 Prisma schema
generator client {
  provider = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["supplier"]
}

// Supplier 用户模型
model User {
  id           Int       @id @default(autoincrement())
  username     String    @unique
  password     String
  email        String    @unique
  businessType String    // 供应商类型
  companyName  String    // 公司名称
  status       String    @default("active") // active, inactive, suspended
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  deletedAt    DateTime?

  @@map("users")
  @@schema("supplier")
}
