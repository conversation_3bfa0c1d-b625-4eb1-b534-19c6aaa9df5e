class UserController {
  /**
   * 用户登录
   */
  async login(req, res) {
    try {
      const { username, password } = req.body;
      
      // 模拟用户登录
      if (username === 'supplier' && password === '123456') {
        res.json({
          success: true,
          message: '登录成功',
          data: {
            token: 'mock_token',
            user: {
              id: 1,
              username: 'supplier',
              role: 'supplier'
            }
          }
        });
      } else {
        res.status(400).json({
          success: false,
          message: '用户名或密码错误'
        });
      }
    } catch (err) {
      res.status(400).json({
        success: false,
        message: err.message
      });
    }
  }

  /**
   * @swagger
   * /api/supplier/users:
   *   post:
   *     tags:
   *       - 供应商用户管理
   *     summary: 创建供应商用户
   *     description: 创建一个新供应商用户
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UserCreateRequest'
   *     responses:
   *       201:
   *         description: 创建成功
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/Success'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/User'
   */
  async create(req, res) {
    try {
      // 模拟创建供应商用户
      const user = { id: 1, ...req.body };
      res.status(201).json({
        success: true,
        message: '创建供应商用户成功',
        data: user
      });
    } catch (err) {
      res.status(400).json({
        success: false,
        message: err.message
      });
    }
  }

  /**
   * @swagger
   * /api/supplier/users/{id}:
   *   put:
   *     tags:
   *       - 供应商用户管理
   *     summary: 更新供应商用户
   *     description: 更新指定供应商用户的信息
   *     parameters:
   *       - name: id
   *         in: path
   *         description: 用户ID
   *         required: true
   *         schema:
   *           type: integer
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UserUpdateRequest'
   *     responses:
   *       200:
   *         description: 更新成功
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/Success'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/User'
   */
  async update(req, res) {
    try {
      // 模拟更新供应商用户
      const user = { id: parseInt(req.params.id), ...req.body };
      res.json({
        success: true,
        message: '更新供应商用户成功',
        data: user
      });
    } catch (err) {
      res.status(400).json({
        success: false,
        message: err.message
      });
    }
  }

  /**
   * @swagger
   * /api/supplier/users/{id}:
   *   delete:
   *     tags:
   *       - 供应商用户管理
   *     summary: 删除供应商用户
   *     description: 删除指定的供应商用户
   *     parameters:
   *       - name: id
   *         in: path
   *         description: 用户ID
   *         required: true
   *         schema:
   *           type: integer
   *     responses:
   *       200:
   *         description: 删除成功
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Success'
   */
  async delete(req, res) {
    try {
      // 模拟删除供应商用户
      res.json({
        success: true,
        message: '删除供应商用户成功'
      });
    } catch (err) {
      res.status(400).json({
        success: false,
        message: err.message
      });
    }
  }

  /**
   * @swagger
   * /api/supplier/users/{id}:
   *   get:
   *     tags:
   *       - 供应商用户管理
   *     summary: 获取供应商用户详情
   *     description: 获取指定供应商用户的详细信息
   *     parameters:
   *       - name: id
   *         in: path
   *         description: 用户ID
   *         required: true
   *         schema:
   *           type: integer
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/Success'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/User'
   */
  async getById(req, res) {
    try {
      // 模拟获取供应商用户
      const user = { id: parseInt(req.params.id), username: 'supplier1' };
      if (!user) {
        return res.status(404).json({
          success: false,
          message: '供应商用户不存在'
        });
      }
      res.json({
        success: true,
        data: user
      });
    } catch (err) {
      res.status(400).json({
        success: false,
        message: err.message
      });
    }
  }

  /**
   * @swagger
   * /api/supplier/users:
   *   get:
   *     tags:
   *       - 供应商用户管理
   *     summary: 获取供应商用户列表
   *     description: 分页获取供应商用户列表
   *     parameters:
   *       - name: page
   *         in: query
   *         description: 页码
   *         schema:
   *           type: integer
   *           default: 1
   *       - name: pageSize
   *         in: query
   *         description: 每页记录数
   *         schema:
   *           type: integer
   *           default: 10
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/UserListResponse'
   */
  async list(req, res) {
    try {
      const { page = 1, pageSize = 10 } = req.query;
      
      // 模拟获取供应商用户列表
      const result = {
        list: [{ id: 1, username: 'supplier1' }],
        total: 1,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      };

      res.json({
        success: true,
        data: result
      });
    } catch (err) {
      res.status(400).json({
        success: false,
        message: err.message
      });
    }
  }

  /**
   * @swagger
   * /api/supplier/users/by-business-type/{businessType}:
   *   get:
   *     tags:
   *       - 供应商用户管理
   *     summary: 通过业务类型查找供应商用户
   *     description: 根据业务类型查找供应商用户
   *     parameters:
   *       - name: businessType
   *         in: path
   *         description: 业务类型
   *         required: true
   *         schema:
   *           type: string
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/Success'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       type: object
   *                       properties:
   *                         items:
   *                           type: array
   *                           items:
   *                             $ref: '#/components/schemas/User'
   *                         total:
   *                           type: integer
   */
  async findByBusinessType(req, res) {
    try {
      // 模拟通过业务类型查找供应商用户
      const users = [{ id: 1, username: 'supplier1', businessType: req.params.businessType }];
      res.json({
        success: true,
        data: {
          items: users,
          total: users.length
        }
      });
    } catch (err) {
      res.status(400).json({
        success: false,
        message: err.message
      });
    }
  }
}

module.exports = new UserController();
