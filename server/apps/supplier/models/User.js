const prismaManager = require('../../../core/prisma');

class SupplierUser {
  constructor() {
    this.prisma = prismaManager.getClient('supplier');
  }

  /**
   * 创建用户
   */
  async create(data) {
    return this.prisma.user.create({
      data: {
        ...data,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });
  }

  /**
   * 更新用户
   */
  async update(id, data) {
    return this.prisma.user.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date()
      }
    });
  }

  /**
   * 删除用户
   */
  async delete(id) {
    return this.prisma.user.delete({
      where: { id }
    });
  }

  /**
   * 查找单个用户
   */
  async findById(id) {
    return this.prisma.user.findUnique({
      where: { id }
    });
  }

  /**
   * 查找多个用户
   */
  async findMany(params = {}) {
    const { skip = 0, take = 10, where = {}, orderBy = { createdAt: 'desc' } } = params;
    
    const [total, items] = await Promise.all([
      this.prisma.user.count({ where }),
      this.prisma.user.findMany({
        skip,
        take,
        where,
        orderBy,
        select: {
          id: true,
          username: true,
          email: true,
          businessType: true,
          status: true,
          createdAt: true,
          updatedAt: true
        }
      })
    ]);

    return {
      items,
      total,
      page: Math.floor(skip / take) + 1,
      pageSize: take
    };
  }

  /**
   * 通过业务类型查找用户
   */
  async findByBusinessType(businessType) {
    return this.prisma.user.findMany({
      where: { businessType }
    });
  }
}

module.exports = SupplierUser;
