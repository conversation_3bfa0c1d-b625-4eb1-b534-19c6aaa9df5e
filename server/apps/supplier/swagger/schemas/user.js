/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: 用户ID
 *         username:
 *           type: string
 *           description: 用户名
 *         email:
 *           type: string
 *           description: 邮箱
 *         businessType:
 *           type: string
 *           description: 业务类型
 *       required:
 *         - id
 *         - username
 *         - email
 *         - businessType
 *
 *     UserCreateRequest:
 *       type: object
 *       properties:
 *         username:
 *           type: string
 *           description: 用户名
 *         password:
 *           type: string
 *           description: 密码
 *         email:
 *           type: string
 *           description: 邮箱
 *         businessType:
 *           type: string
 *           description: 业务类型
 *       required:
 *         - username
 *         - password
 *         - email
 *         - businessType
 *
 *     UserUpdateRequest:
 *       type: object
 *       properties:
 *         username:
 *           type: string
 *           description: 用户名
 *         email:
 *           type: string
 *           description: 邮箱
 *         businessType:
 *           type: string
 *           description: 业务类型
 *
 *     UserListResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/Success'
 *         - type: object
 *           properties:
 *             data:
 *               type: object
 *               properties:
 *                 list:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/User'
 *                 total:
 *                   type: integer
 *                   description: 总记录数
 *                 page:
 *                   type: integer
 *                   description: 当前页码
 *                 pageSize:
 *                   type: integer
 *                   description: 每页记录数
 */

module.exports = {};
