const express = require('express');
const router = express.Router();
const userController = require('../controllers/UserController');

/**
 * @swagger
 * /api/supplier/auth/login:
 *   post:
 *     tags: [供应商认证]
 *     summary: 供应商用户登录
 *     description: 供应商用户登录并获取访问令牌
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UserLoginRequest'
 *     responses:
 *       200:
 *         $ref: '#/components/responses/UserLoginResponse'
 *       400:
 *         $ref: '#/components/responses/Error'
 */
router.post('/login', userController.login);

module.exports = router;
