const express = require('express');
const router = express.Router();
const userController = require('../controllers/UserController');

  /**
   * @swagger
   * /api/supplier/users:
   *   post:
   *     tags: [Supplier Users]
   *     summary: 创建供应商用户
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required: [username, password, email, businessType]
   *             properties:
   *               username:
   *                 type: string
   *               password:
   *                 type: string
   *               email:
   *                 type: string
   *               businessType:
   *                 type: string
   *     responses:
   *       201:
   *         description: 用户创建成功
   */
  router.post('/', userController.create);

  /**
   * @swagger
   * /api/supplier/users/{id}:
   *   put:
   *     tags: [Supplier Users]
   *     summary: 更新供应商用户
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: string
   *     requestBody:
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               username:
   *                 type: string
   *               email:
   *                 type: string
   *               businessType:
   *                 type: string
   *     responses:
   *       200:
   *         description: 用户更新成功
   */
  router.put('/:id', userController.update);

  /**
   * @swagger
   * /api/supplier/users/{id}:
   *   delete:
   *     tags: [Supplier Users]
   *     summary: 删除供应商用户
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: string
   *     responses:
   *       204:
   *         description: 用户删除成功
   */
  router.delete('/:id', userController.delete);

  /**
   * @swagger
   * /api/supplier/users/{id}:
   *   get:
   *     tags: [Supplier Users]
   *     summary: 获取单个供应商用户
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: string
   *     responses:
   *       200:
   *         description: 返回用户信息
   */
  router.get('/:id', userController.getById);

  /**
   * @swagger
   * /api/supplier/users:
   *   get:
   *     tags: [Supplier Users]
   *     summary: 获取供应商用户列表
   *     parameters:
   *       - name: page
   *         in: query
   *         schema:
   *           type: integer
   *           default: 1
   *       - name: pageSize
   *         in: query
   *         schema:
   *           type: integer
   *           default: 10
   *     responses:
   *       200:
   *         description: 返回用户列表
   */
  router.get('/', userController.list);

  /**
   * @swagger
   * /api/supplier/users/by-business-type/{businessType}:
   *   get:
   *     tags: [Supplier Users]
   *     summary: 通过业务类型查找供应商用户
   *     parameters:
   *       - name: businessType
   *         in: path
   *         required: true
   *         schema:
   *           type: string
   *     responses:
   *       200:
   *         description: 返回用户列表
   */
  router.get('/by-business-type/:businessType', userController.findByBusinessType);

module.exports = router;
