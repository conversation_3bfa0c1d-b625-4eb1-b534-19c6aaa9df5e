/**
 * 商城商品服务
 * 负责商品业务逻辑处理
 */

const DecimalUtils = require('../../../core/utils/DecimalUtils');

/**
 * 将数据库字段名转换为前端字段名
 * @param {Object} item 数据库记录
 * @returns {Object} 转换后的记录
 */
function transformCategoryFields(item) {
  if (!item) return null;
  
  return {
    id: item.id,
    goodsParentCategoryId: item.goods_parent_category_id,
    name: item.name,
    imageUrl: item.image_url,
    description: item.description,
    metaTitle: item.meta_title,
    metaKeywords: item.meta_keywords,
    metaDescription: item.meta_description,
    sortOrder: item.sort_order || 1,
    isEnabled: item.is_enabled,
    level: item.level || 1,
    children: []
  };
}

/**
 * 处理BigInt序列化问题
 * @param {Object} data 要处理的数据
 * @returns {Object} 处理后的数据
 */
function handleBigInt(data) {
  // 直接使用公共的 DecimalUtils 处理 BigInt
  return DecimalUtils.handleBigInt(data);
}



/**
 * 处理数据中的 BigInt 和 Decimal 类型，转换为前端可用的格式
 * @param {*} data 需要处理的数据
 * @returns {*} 处理后的数据
 */
function processDecimalPrice(data) {
  // 使用公共的 DecimalUtils 处理，支持 Decimal 和 BigInt
  const decimalFields = ['salesPrice', 'marketPrice', 'costPrice', 'price', 'amount'];
  return DecimalUtils.processDataFields(data, decimalFields);
}

/**
 * 商品服务类
 */
class GoodsService {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    this.prisma = prisma;
  }
  
  /**
   * 获取商品列表，支持多种筛选条件
   * 
   * 方法功能说明：
   * 该方法提供商城前台商品列表展示功能，支持多种筛选条件和排序方式。
   * 包括分类筛选、品牌筛选、关键词搜索、价格区间筛选、标签筛选等。
   * 返回的商品数据包含基本信息、价格和主图，适合列表展示。
   * 
   * @param {Object} filters 筛选条件对象
   * @param {number|string} filters.categoryId 分类ID - 按商品分类筛选
   * @param {number|string} filters.brandId 品牌ID - 按商品品牌筛选
   * @param {string} filters.keyword 关键词 - 在商品名称和描述中搜索
   * @param {number|string} filters.minPrice 最低价格 - 价格下限
   * @param {number|string} filters.maxPrice 最高价格 - 价格上限
   * @param {Array<string>} filters.tags 标签数组 - 按商品标签筛选
   * @param {Array<string>} filters.serviceIds 服务ID数组 - 按商品服务筛选
   * @param {string} filters.sortBy 排序字段 - 支持'price'(价格),'sales'(销量),'created_at'(新品)
   * @param {string} filters.sortOrder 排序方式 - 'asc'(升序)/'desc'(降序)
   * @param {number} page 页码 - 默认1，第几页
   * @param {number} pageSize 每页数量 - 默认10，每页显示多少商品
   * @returns {Promise<Object>} 商品列表数据，包含商品数组和分页信息
   */
  async getProducts(filters = {}, page = 1, pageSize = 10) {
    try {
      // 转换参数类型
      page = parseInt(page);
      pageSize = parseInt(pageSize);
      
      // 计算分页偏移量
      const skip = (page - 1) * pageSize;
      
      // 构建基础查询条件
      const whereCondition = {
        status: 1, // 只获取上架的商品
        deleted_at: null // 未删除的商品
      };
      
      // 处理分类ID筛选
      // 优化点：收集指定分类及其所有子分类中的商品
      if (filters.categoryId && filters.categoryId !== 'undefined' && filters.categoryId !== 'null') {
        // 检查是否为有效的数字字符串
        const isValidId = /^\d+$/.test(filters.categoryId);
        
        if (isValidId) {
          try {
            // 将字符串转换为BigInt类型
            const categoryIdBigInt = BigInt(filters.categoryId);
            
            // 获取指定分类及其所有子分类的ID列表
            const categoryIds = await this.getCategoryWithSubcategoriesIds(categoryIdBigInt);
            
            // 使用获取到的所有分类ID进行筛选
            // 这样能确保查询结果中包含指定分类及其所有子分类的商品
            whereCondition.goods_category_associations = {
              some: {
                goods_category_id: {
                  in: categoryIds
                }
              }
            };
          } catch (error) {
            console.error('分类ID处理失败:', error.message);
          }
        }
      }
      
      // 处理品牌ID筛选
      if (filters.brandId && filters.brandId !== 'undefined' && filters.brandId !== 'null') {
        // 检查是否为有效的数字字符串
        const isValidId = /^\d+$/.test(filters.brandId);
        
        if (isValidId) {
          try {
            // 将字符串转换为BigInt类型
            const brandIdBigInt = BigInt(filters.brandId);
            whereCondition.goods_brand_id = brandIdBigInt;
          } catch (error) {
            console.error('品牌ID转换为BigInt失败:', error.message);
          }
        }
      }
      
      // 处理关键词搜索
      if (filters.keyword && typeof filters.keyword === 'string' && filters.keyword.trim() !== '') {
        whereCondition.OR = [
          { name: { contains: filters.keyword.trim() } },
          { description: { contains: filters.keyword.trim() } }
        ];
      }
      
      // 处理价格范围筛选
      if ((filters.minPrice && filters.minPrice !== 'undefined') || 
          (filters.maxPrice && filters.maxPrice !== 'undefined')) {
        
        // 初始化价格条件
        const priceCondition = {};
        
        // 处理最低价格
        if (filters.minPrice && filters.minPrice !== 'undefined') {
          const minPrice = parseFloat(filters.minPrice);
          if (!isNaN(minPrice)) {
            priceCondition.gte = minPrice;
          }
        }
        
        // 处理最高价格
        if (filters.maxPrice && filters.maxPrice !== 'undefined') {
          const maxPrice = parseFloat(filters.maxPrice);
          if (!isNaN(maxPrice)) {
            priceCondition.lte = maxPrice;
          }
        }
        
        // 只有当价格条件存在时才添加到查询条件
        if (Object.keys(priceCondition).length > 0) {
          // 使用Prisma支持的方式进行价格筛选
          whereCondition.goods_skus = {
            some: {
              deleted_at: null,
              is_enabled: 1,
              sales_price: priceCondition
            }
          };
        }
      }
      
      // 处理标签筛选（使用标签ID）
      if (filters.tagIds && Array.isArray(filters.tagIds) && filters.tagIds.length > 0) {
        // 将字符串ID转换为BigInt数组
        const tagIdsBigInt = [];
        for (const tagId of filters.tagIds) {
          if (/^\d+$/.test(tagId)) {
            try {
              tagIdsBigInt.push(BigInt(tagId));
            } catch (error) {
              console.error('标签ID转换为BigInt失败:', error.message);
            }
          }
        }
        
        // 只有当有有效的标签ID时才添加到查询条件
        if (tagIdsBigInt.length > 0) {
          // 首先验证标签ID是否存在
          const existingTags = await this.prisma.GoodsTag.findMany({
            where: {
              id: {
                in: tagIdsBigInt
              },
              deleted_at: null
            },
            select: {
              id: true
            }
          });
          
          // 提取存在的标签ID
          const existingTagIds = existingTags.map(tag => tag.id);
          
          // 只有当有存在的标签ID时才添加到查询条件
          if (existingTagIds.length > 0) {
            whereCondition.goods_tag_associations = {
              some: {
                goods_tag_id: {
                  in: existingTagIds
                }
              }
            };
          } else {
            // 如果没有任何标签ID存在，则返回空结果
            // 这里使用一个不可能满足的条件，确保查询结果为空
            whereCondition.id = BigInt(-1); // 使用一个不可能存在的ID值
          }
        }
      }
      
      // 处理商品服务筛选
      if (filters.serviceIds && Array.isArray(filters.serviceIds) && filters.serviceIds.length > 0) {
        // 将字符串ID转换为BigInt数组
        const serviceIdsBigInt = [];
        for (const serviceId of filters.serviceIds) {
          if (/^\d+$/.test(serviceId)) {
            try {
              serviceIdsBigInt.push(BigInt(serviceId));
            } catch (error) {
              console.error('服务ID转换为BigInt失败:', error.message);
            }
          }
        }
        
        // 只有当有有效的服务ID时才添加到查询条件
        if (serviceIdsBigInt.length > 0) {
          whereCondition.goods_service_associations = {
            some: {
              goods_service_id: {
                in: serviceIdsBigInt
              }
            }
          };
        }
      }
      
      // 处理排序
      const orderBy = [];
      
      // 处理排序字段和方式
      if (filters.sortBy) {
        const sortOrder = (filters.sortOrder && filters.sortOrder.toLowerCase() === 'asc') ? 'asc' : 'desc';
        
        // 根据不同的排序字段添加不同的排序规则
        switch (filters.sortBy) {
          case 'price':
            // 实现价格排序
            // 注意：价格排序会在查询后对结果进行处理
            // 这里不添加orderBy条件，因为我们将在查询后手动排序
            break;
          case 'sales':
            orderBy.push({ total_sales: sortOrder });
            break;
          case 'created_at':
            orderBy.push({ created_at: sortOrder });
            break;
          default:
            // 默认按照排序权重和创建时间排序
            orderBy.push({ sort_order: 'asc' });
            orderBy.push({ created_at: 'desc' });
        }
      }
      
      // 如果没有指定排序，使用默认排序
      if (orderBy.length === 0) {
        orderBy.push({ sort_order: 'asc' });
        orderBy.push({ created_at: 'desc' });
      }
      
      /**
       * 查询商品列表
       * 
       * 查询策略说明：
       * 1. 只获取商品的基本信息，不获取完整的规格和详情，以提高查询效率
       * 2. 只查询状态为上架并且未删除的商品
       * 3. 对于每个商品，只获取一张主图和最低价格的SKU
       * 4. 使用分页查询，避免返回数据量过大
       */
      const products = await this.prisma.GoodsSpu.findMany({
        where: whereCondition,
        select: {
          id: true,                // 商品ID，唯一标识
          name: true,              // 商品名称
          description: true,       // 商品描述
          total_sales: true,       // 总销量，用于销量排序
          goods_skus: {            // 关联的SKU信息
            where: {
              deleted_at: null,     // 未删除的SKU
              is_enabled: 1         // 只获取启用的SKU
            },
            select: {
              id: true,              // SKU ID
              sales_price: true,     // 销售价格
              stock: true,           // 库存数量
              sales_volume: true     // 销售数量
            },
            orderBy: {
              sales_price: 'asc'     // 价格从低到高排序
            }
          },
          goods_images: {          // 商品图片
            where: {
              is_default: true,      // 只获取默认主图
              goods_sku_id: null    // SPU级别的图片，非SKU特有图片
            },
            select: {
              image_url: true        // 只需要图片URL
            },
            take: 1                 // 只取一张主图，用于列表展示
          }
        },
        skip: skip,               // 分页偏移量
        take: pageSize,           // 获取数量
        orderBy: orderBy          // 排序规则
      });
      
      // 获取总记录数，使用相同的查询条件
      const total = await this.prisma.GoodsSpu.count({
        where: whereCondition
      });
      
      /**
       * 格式化商品数据，将数据库记录转换为前端所需的格式
       * 
       * 转换说明：
       * 1. 对于每个商品，提取最低价格和主图
       * 2. 处理BigInt和Decimal类型，转换为前端可用的格式
       * 3. 添加临时字段_rawPrice用于排序，返回前删除
       */
      let formattedProducts = products.map(product => {
        // 处理价格，取最低价格的SKU价格作为商品展示价格
        const price = product.goods_skus.length > 0 ? product.goods_skus[0].sales_price : 0;
        
        // 调试日志：记录原始价格数据
        if (price && typeof price === 'object' && price.s !== undefined) {
          console.log('【调试】原始Decimal价格数据:', {
            productId: product.id,
            productName: product.name,
            rawPrice: price,
            priceStructure: {
              s: price.s,
              e: price.e,
              d: price.d,
              constructor: price.constructor?.name,
              methods: Object.getOwnPropertyNames(Object.getPrototypeOf(price)).filter(name => typeof price[name] === 'function')
            }
          });
        }
        
        // 处理主图，取默认主图作为商品展示图片
        const imageUrl = product.goods_images.length > 0 ? product.goods_images[0].image_url : '';
        
        // 使用公共工具处理价格数据
        const processedPrice = DecimalUtils.convertToString(price);
        
        // 调试日志：记录处理后的价格数据
        if (price && typeof price === 'object' && price.s !== undefined) {
          console.log('【调试】处理后的价格数据:', {
            productId: product.id,
            originalPrice: price,
            processedPrice: processedPrice,
            priceType: typeof processedPrice,
            isValidNumber: !isNaN(parseFloat(processedPrice))
          });
        }
        
        // 处理SKU数据，只保留id、库存、销售价和销售数量
        const skuData = product.goods_skus.map(sku => ({
          id: DecimalUtils.handleBigInt(sku.id),
          salesPrice: DecimalUtils.convertToString(sku.sales_price),
          stock: sku.stock || 0,
          salesVolume: sku.sales_volume || 0
        }));
        
        // 返回前端需要的商品数据格式
        return {
          id: DecimalUtils.handleBigInt(product.id),       // 处理BigInt类型的ID，转为字符串
          name: product.name,                 // 商品名称
          description: product.description || '', // 商品描述，确保非空
          imageUrl: imageUrl,                 // 商品主图链接
          totalSales: product.total_sales,    // 总销量
          price: processedPrice,              // 字符串格式的价格，用于展示
          skus: skuData,                      // 关联的SKU ID和库存信息
          _rawPrice: DecimalUtils.convertToNumber(price)   // 数值类型价格，仅用于排序
        };
      });
      
      // 如果是按价格排序，在这里处理
      if (filters.sortBy === 'price') {
        const sortOrder = (filters.sortOrder && filters.sortOrder.toLowerCase() === 'asc') ? 'asc' : 'desc';
        
        formattedProducts.sort((a, b) => {
          if (sortOrder === 'asc') {
            return a._rawPrice - b._rawPrice; // 升序（从低到高）
          } else {
            return b._rawPrice - a._rawPrice; // 降序（从高到低）
          }
        });
      }
      
      // 移除临时排序字段
      formattedProducts = formattedProducts.map(({ _rawPrice, ...rest }) => rest);
      
      // 使用BaseController的响应格式
      return {
        code: 200,
        message: '获取商品列表成功',
        data: {
          items: formattedProducts,
          pageInfo: {
            total: total,
            currentPage: page,
            totalPage: Math.ceil(total / pageSize)
          }
        }
      };
    } catch (error) {
      console.error('获取商品列表失败:', error);
      return {
        code: 500,
        message: '获取商品列表失败',
        data: null
      };
    }
  }
  
  // 旧方法getProductsByCategoryId已移除，统一使用getProducts方法
  
  /**
   * 获取指定分类及其所有子分类的ID集合
   * 
   * 方法功能说明：
   * 1. 递归查询指定分类的所有子分类，包含多级子分类
   * 2. 返回包含原始分类ID和所有子分类ID的数组
   * 3. 主要用于商品列表查询时将指定分类及其所有子分类中的商品全部查询出来
   * 
   * @param {BigInt} categoryId 分类ID
   * @returns {Promise<Array<BigInt>>} 分类ID数组，包含指定分类及其所有子分类的ID
   */
  async getCategoryWithSubcategoriesIds(categoryId) {
    try {
      // 存储所有分类ID的集合，先把当前分类ID添加进去
      const categoryIds = [categoryId];
      
      // 递归获取子分类ID的函数
      const collectChildCategories = async (parentId) => {
        // 查询所有直接子分类，只查含启用状态且未删除的分类
        const childCategories = await this.prisma.GoodsCategory.findMany({
          where: {
            goods_parent_category_id: parentId, // 父分类ID字段
            is_enabled: 1,                    // 启用状态
            deleted_at: null                  // 未删除
          },
          select: {
            id: true                         // 只需要获取分类ID
          }
        });
        
        // 遍历处理每个直接子分类
        for (const childCategory of childCategories) {
          // 将子分类ID添加到结果集合
          categoryIds.push(childCategory.id);
          // 递归处理该子分类的所有子分类，实现多级递归查询
          await collectChildCategories(childCategory.id);
        }
      };
      
      // 开始递归搜集所有子分类
      await collectChildCategories(categoryId);
      
      return categoryIds;
    } catch (error) {
      console.error('获取分类及子分类ID失败:', error.message);
      // 如果出错，至少返回当前分类ID，确保查询不会失败
      return [categoryId];
    }
  }
  
  /**
   * 获取商品分类树形结构
   * @returns {Promise<Object>} 分类树形结构数据
   */
  async getCategoryTree() {
    try {
      // 获取所有未删除且启用的分类
      const allCategories = await this.prisma.GoodsCategory.findMany({
        where: {
          deleted_at: null,
          is_enabled: 1 // 只获取启用的分类
        },
        orderBy: [
          { level: 'asc' },
          { sort_order: 'asc' },
          { created_at: 'desc' }
        ]
      });
      
      // 处理BigInt并转换字段名
      const processedCategories = allCategories.map(item => {
        return transformCategoryFields(handleBigInt(item));
      });
      
      // 构建树形结构
      const categoryMap = {};
      const rootCategories = [];
      
      // 先将所有分类放入Map中，方便查找
      processedCategories.forEach(category => {
        categoryMap[category.id] = category;
      });
      
      // 构建树形结构
      processedCategories.forEach(category => {
        // 如果有父分类，则添加到父分类的children中
        if (category.goodsParentCategoryId) {
          const parent = categoryMap[category.goodsParentCategoryId];
          if (parent) {
            parent.children.push(category);
          } else {
            // 如果找不到父分类，则作为根分类
            rootCategories.push(category);
          }
        } else {
          // 没有父分类，则为根分类
          rootCategories.push(category);
        }
      });
      
      return {
        code: 200,
        message: '获取商品分类树形结构成功',
        data: rootCategories
      };
    } catch (error) {
      console.error('获取商品分类树形结构失败:', error);
      return {
        code: 500,
        message: '获取商品分类树形结构失败',
        data: null
      };
    }
  }
  
  /**
   * 根据商品分类ID获取分类基本信息
   * @param {string|number} categoryId 分类ID
   * @returns {Promise<Object>} 分类信息
   */
  async getCategoryById(categoryId) {
    try {
      // 将categoryId转换为BigInt类型，避免类型不匹配错误
      // 对于雪花ID，使用BigInt类型可以避免精度丢失
      const categoryIdBigInt = BigInt(categoryId);
      
      // 查询分类信息
      const category = await this.prisma.GoodsCategory.findFirst({
        where: {
          id: categoryIdBigInt, // 使用BigInt类型的ID进行查询
          deleted_at: null,
          is_enabled: 1 // 只获取启用的分类
        }
      });
      
      if (!category) {
        // 如果找不到分类，返回错误信息
        return {
          code: 404,
          message: '分类不存在',
          data: null
        };
      }
      
      // 转换字段名称
      const formattedCategory = transformCategoryFields(handleBigInt(category));
      
      return {
        code: 200,
        message: '获取商品分类信息成功',
        data: formattedCategory
      };
    } catch (error) {
      console.error('获取商品分类信息失败:', error);
      return {
        code: 500,
        message: '获取商品分类信息失败',
        data: null
      };
    }
  }
  
  /**
   * 获取商品标签列表，支持分页和名称模糊搜索
   * @param {Object} query 查询参数
   * @param {string} query.name 标签名称，用于模糊搜索
   * @param {number} query.page 页码，默认1
   * @param {number} query.pageSize 每页记录数，默认10
   * @returns {Promise<Object>} 分页后的标签列表和分页信息
   */
  async getAllTags(query = {}) {
    try {
      const { name, page = 1, pageSize = 10 } = query;
      
      // 构建查询条件，只获取非禁用标签
      const where = {
        deleted_at: null,
        is_enabled: true // 只获取启用的标签
      };
      
      // 如果有标签名称查询参数，添加模糊查询条件
      if (name) {
        where.name = {
          contains: name
        };
      }
      
      // 计算总记录数
      const total = await this.prisma.GoodsTag.count({ where });
      
      // 计算总页数
      const totalPage = Math.ceil(total / pageSize);
      
      // 计算分页偏移量
      const skip = (page - 1) * pageSize;
      
      // 查询当前页的标签数据，按排序字段排序
      const tags = await this.prisma.GoodsTag.findMany({
        where,
        orderBy: [
          { sort_order: 'asc' },
          { created_at: 'desc' }
        ],
        skip,
        take: pageSize
      });
      
      // 处理BigInt并转换字段名
      const formattedTags = tags.map(tag => {
        return {
          id: handleBigInt(tag.id),
          name: tag.name,
          slug: tag.slug,
          imageUrl: tag.image_url,
          description: tag.description,
          tagType: tag.tag_type,
          sortOrder: tag.sort_order
        };
      });
      
      return {
        code: 200,
        message: '获取商品标签列表成功',
        data: {
          items: formattedTags,
          pageInfo: {
            total,
            currentPage: page,
            totalPage
          }
        }
      };
    } catch (error) {
      console.error('获取商品标签列表失败:', error);
      return {
        code: 500,
        message: '获取商品标签列表失败',
        data: null
      };
    }
  }
  
  /**
   * 根据SPU ID获取商品详情
   * @param {number} spuId 商品SPU ID
   * @returns {Promise<Object>} 商品详情数据
   */
  async getProductDetail(spuId) {
    try {
      // 转换参数类型为BigInt，避免雪花ID精度丢失
      spuId = BigInt(spuId);
      
      // 查询商品SPU信息，包含关联的SKU、图片、服务、品牌、标签等信息
      // 但不包含分类关联，分类信息将单独查询
      const product = await this.prisma.GoodsSpu.findFirst({
        where: {
          id: spuId,
          deleted_at: null,
          status: 1 // 只获取上架的商品
        },
        include: {
          // 包含SKU信息
          goods_skus: {
            where: {
              deleted_at: null,
              is_enabled: 1 // 只获取启用的SKU
            },
            orderBy: {
              sales_price: 'asc' // 按价格升序排序
            },
            include: {
              // 包含SKU的图片
              goods_images: {
                orderBy: {
                  sort_order: 'asc'
                }
              },
              // 包含SKU的规格值
              goods_sku_specification_values: {
                include: {
                  goods_specification_value: {
                    include: {
                      goods_specification_name: true
                    }
                  }
                }
              }
            }
          },
          // 包含商品图片
          goods_images: {
            where: {
              goods_sku_id: null // 只获取SPU级别的图片
            },
            orderBy: [
              { is_default: 'desc' }, // 默认图片排在前面
              { sort_order: 'asc' }
            ]
          },
          // 包含商品视频
          goods_videos: {
            orderBy: {
              sort_order: 'asc'
            }
          },
          // 包含商品品牌
          goods_brands: true,
          // 包含商品分类关联
          goods_category_associations: {
            select: {
              goods_category_id: true,
              is_primary: true
            }
          },
          // 包含商品服务关联
          goods_service_associations: {
            include: {
              goods_service: true
            }
          },
          // 包含商品标签关联
          goods_tag_associations: {
            include: {
              goods_tags: true
            }
          },
          // 包含商品属性值
          goods_attribute_values: {
            include: {
              goods_attribute_items: true
            }
          }
        }
      });
      
      if (!product) {
        return {
          code: 404,
          message: '商品不存在或已下架',
          data: null
        };
      }
      
      // 处理BigInt和Decimal类型，并转换数据结构
      let processedProduct = handleBigInt(product);
      // 处理Decimal类型的价格数据
      processedProduct = processDecimalPrice(processedProduct);
      
      // 构建返回的商品详情数据结构
      const productDetail = {
        // 基本信息
        id: processedProduct.id,
        name: processedProduct.name,
        subtitle: processedProduct.subtitle,
        description: processedProduct.description,
        spuCode: processedProduct.spu_code,
        slug: processedProduct.slug,
        status: processedProduct.status,
        sortOrder: processedProduct.sort_order,
        totalSales: processedProduct.total_sales,
        totalStock: processedProduct.total_stock,
        isVirtual: processedProduct.is_virtual,
        isShippingRequired: processedProduct.is_shipping_required,
        isFreeShipping: processedProduct.is_free_shipping,
        publishedAt: processedProduct.published_at,
        createdAt: processedProduct.created_at,
        updatedAt: processedProduct.updated_at,
        
        // SEO信息
        metaTitle: processedProduct.meta_title,
        metaKeywords: processedProduct.meta_keywords,
        metaDescription: processedProduct.meta_description,
        
        /**
         * 配送信息 - 商品可配送的区域和配送时间
         * 
         * deliveryArea数据结构说明：
         * 1. 返回的是一个JSON对象，存储商品可配送的区域信息
         * 2. 可能的格式为：
         *    - 全国配送：{"type":"nationwide"}
         *    - 指定省市：{"type":"selected","regions":[{"province":"北京市","cities":["全市"]},{"province":"上海市"}]}
         *    - 除外区域：{"type":"excluded","regions":[{"province":"西藏","cities":["全区"]}]}
         * 3. 如果JSON解析失败，返回原始字符串
         * 
         * deliveryTime说明：
         * 1. 表示预计配送时间的文字说明，如"付款后48小时内发货"
         * 
         * 前端使用说明：
         * 1. 显示商品可配送区域信息，如"全国配送"或"仅限北京、上海配送"
         * 2. 在用户选择收货地址时，验证地址是否在配送范围内
         * 3. 显示预计配送时间，提升用户体验
         */
        deliveryArea: processedProduct.delivery_area ? (() => {
          try {
            return JSON.parse(processedProduct.delivery_area);
          } catch (error) {
            console.warn('解析配送区域JSON失败:', error.message);
            return processedProduct.delivery_area; // 如果解析失败，返回原始字符串
          }
        })() : null,
        deliveryTime: processedProduct.delivery_time,
        
        // 商品规格类型：1-单规格，2-多规格
        skuType: processedProduct.goods_skus?.length > 1 ? 2 : 1,
        
        // 品牌信息
        brand: processedProduct.goods_brands ? {
          id: processedProduct.goods_brands.id,
          name: processedProduct.goods_brands.name,
          logoUrl: processedProduct.goods_brands.logo_url,
          description: processedProduct.goods_brands.description
        } : null,
        
        // 分类信息
        categories: await (async () => {
          // 如果没有分类关联数据，返回空数组
          if (!processedProduct.goods_category_associations || 
              !Array.isArray(processedProduct.goods_category_associations) ||
              processedProduct.goods_category_associations.length === 0) {
            return [];
          }
          
          // 获取分类ID列表 - 保持BigInt类型
          const categoryIds = processedProduct.goods_category_associations
            .filter(association => association && association.goods_category_id)
            // 不要转换为字符串，保持BigInt类型
            .map(association => BigInt(association.goods_category_id));
          
          if (categoryIds.length === 0) {
            return [];
          }
          
          // 查询分类详情
          const categories = await this.prisma.GoodsCategory.findMany({
            where: {
              id: { in: categoryIds },
              deleted_at: null
            }
          });
          
          // 处理BigInt类型
          const processedCategories = handleBigInt(categories);
          
          // 转换字段名称
          return processedCategories.map(category => ({
            id: category.id,
            name: category.name,
            imageUrl: category.image_url,
            description: category.description
          }));
        })(),
        
        // 服务信息
        services: processedProduct.goods_service_associations ? 
          (Array.isArray(processedProduct.goods_service_associations) ? 
            processedProduct.goods_service_associations
              .filter(association => association && association.goods_service)
              .map(association => ({
                id: association.goods_service.id,
                name: association.goods_service.name
              })) : 
            []) : 
          [],
        
        // 标签信息
        tags: processedProduct.goods_tag_associations ? 
          (Array.isArray(processedProduct.goods_tag_associations) ? 
            processedProduct.goods_tag_associations
              .filter(association => association && association.goods_tags)
              .map(association => ({
                id: association.goods_tags.id,
                name: association.goods_tags.name
              })) : 
            []) : 
          [],
        
        // 属性信息
        attributes: processedProduct.goods_attribute_values ? 
          (Array.isArray(processedProduct.goods_attribute_values) ? 
            processedProduct.goods_attribute_values
              .filter(attributeValue => attributeValue && attributeValue.goods_attribute_items)
              .map(attributeValue => ({
                id: attributeValue.goods_attribute_items.id,
                name: attributeValue.goods_attribute_items.name,
                value: attributeValue.value
              })) : 
            []) : 
          [],
        
        // 图片信息
        images: processedProduct.goods_images ? 
          (Array.isArray(processedProduct.goods_images) ? 
            processedProduct.goods_images.map(image => image.image_url) : 
            []) : 
          [],
        
        // 视频信息
        videos: processedProduct.goods_videos ? 
          (Array.isArray(processedProduct.goods_videos) ? 
            processedProduct.goods_videos.map(video => video.video_url) : 
            []) : 
          [],
        
        /**
         * 规格选项分组信息 - 用于前端构建商品规格选择器
         * 
         * 数据结构说明：
         * 1. 返回数组，其中每个元素代表一类规格（如"颜色"、"尺寸"等）
         * 2. 每个规格分组包含名称和可选值列表：{name: '颜色', options: [{id: '1001', name: '红色', image: 'url'}, ...]}
         * 3. 规格值包含ID、名称和可选的图片（如颜色样式图）
         * 
         * 前端使用说明：
         * 1. 用于生成商品规格选择区，每个分组生成一行选项
         * 2. 对于单规格商品，返回空数组，不需要展示规格选择器
         * 3. 用户选择不同规格组合时，需要结合skuSpecMatrix查找对应的SKU
         * 4. 规格选项可能包含图片（如颜色、样式图等），可以展示为图片样式
         * 
         * 示例：
         * [
         *   {
         *     name: '颜色',
         *     options: [
         *       {id: '10001', name: '红色', image: 'http://example.com/red.jpg'},
         *       {id: '10002', name: '蓝色', image: 'http://example.com/blue.jpg'}
         *     ]
         *   },
         *   {
         *     name: '尺寸',
         *     options: [
         *       {id: '10003', name: 'S码', image: null},
         *       {id: '10004', name: 'M码', image: null}
         *     ]
         *   }
         * ]
         */
        specGroups: (() => {
          // 如果是单规格商品，不需要规格分组
          if (!processedProduct.goods_skus || !Array.isArray(processedProduct.goods_skus) || processedProduct.goods_skus.length <= 1) {
            return [];
          }
          
          // 收集所有规格名称和规格值
          const specMap = new Map();
          
          processedProduct.goods_skus.forEach(sku => {
            if (sku.goods_sku_specification_values && Array.isArray(sku.goods_sku_specification_values)) {
              sku.goods_sku_specification_values
                .filter(specValue => specValue && specValue.goods_specification_value && specValue.goods_specification_value.goods_specification_name)
                .forEach(specValue => {
                  const specName = specValue.goods_specification_value.goods_specification_name.name;
                  const valueId = specValue.goods_specification_value.id;
                  const valueName = specValue.goods_specification_value.value;
                  const valueImage = specValue.goods_specification_value.image_url || null;
                  
                  if (!specMap.has(specName)) {
                    specMap.set(specName, new Map());
                  }
                  
                  if (!specMap.get(specName).has(valueId)) {
                    specMap.get(specName).set(valueId, {
                      id: valueId,
                      name: valueName,
                      image: valueImage
                    });
                  }
                });
            }
          });
          
          // 转换Map为数组结构
          const specGroups = [];
          specMap.forEach((values, name) => {
            const specOptions = Array.from(values.values());
            specGroups.push({
              name: name,
              options: specOptions
            });
          });
          
          return specGroups;
        })(),
        
        /**
         * SKU商品列表 - 包含所有可销售SKU的详细信息
         * 
         * 数据结构说明：
         * 1. 返回数组，其中每个元素是一个可销售的SKU商品
         * 2. 对于单规格商品，数组只有一个元素
         * 3. 对于多规格商品，数组包含多个元素，每个都对应不同的规格组合
         * 4. 每个SKU包含基本信息、价格、库存、规格值等字段
         * 
         * 前端使用说明：
         * 1. 对于单规格商品，直接使用数组第一个元素的信息
         * 2. 对于多规格商品，结合specGroups和skuSpecMatrix显示当前选中规格对应的SKU
         * 3. skuList中的specs和specIds字段可以用于显示已选规格的文本说明
         * 4. 在添加到购物车时，需要传递对应的skuId
         * 
         * 示例：
         * [
         *   {
         *     id: '100001',
         *     skuCode: 'SKU001',
         *     skuName: '红色 XL码',
         *     salesPrice: '99.00',
         *     stock: 100,
         *     specs: {'颜色': '红色', '尺寸': 'XL码'},
         *     specIds: {'颜色': '10001', '尺寸': '10005'},
         *     image: 'http://example.com/red-xl.jpg'
         *   },
         *   ...
         * ]
         */
        skuList: processedProduct.goods_skus ? 
          (Array.isArray(processedProduct.goods_skus) ? 
            processedProduct.goods_skus.map(sku => {
              // 获取SKU的默认图片（如果有）
              let defaultImage = null;
              if (sku.goods_images && Array.isArray(sku.goods_images) && sku.goods_images.length > 0) {
                defaultImage = sku.goods_images[0].image_url;
              }
              
              // 处理规格值
              const specs = {};
              const specIds = {}; // 新增：记录规格值ID
              if (sku.goods_sku_specification_values && Array.isArray(sku.goods_sku_specification_values)) {
                sku.goods_sku_specification_values
                  .filter(specValue => specValue && specValue.goods_specification_value && specValue.goods_specification_value.goods_specification_name)
                  .forEach(specValue => {
                    const specName = specValue.goods_specification_value.goods_specification_name.name;
                    const valueName = specValue.goods_specification_value.value;
                    const valueId = specValue.goods_specification_value.id;
                    specs[specName] = valueName;
                    specIds[specName] = valueId; // 记录规格值ID
                  });
              }
              
              // 生成SKU名称（使用规格值组合）
              let skuName = '';
              if (Object.keys(specs).length > 0) {
                skuName = Object.values(specs).join(' ');
              } else {
                skuName = sku.sku_code || '';
              }
              
              return {
                id: sku.id,
                skuCode: sku.sku_code,
                skuName: skuName,
                salesPrice: DecimalUtils.convertToString(sku.sales_price || '0'),
                marketPrice: DecimalUtils.convertToString(sku.market_price || '0'),
                costPrice: DecimalUtils.convertToString(sku.cost_price || '0'),
                stock: sku.stock,
                salesVolume: sku.sales_volume,
                unit: sku.unit || '',
                weight: sku.weight,
                volume: sku.volume,
                barcode: sku.barcode,
                isEnabled: sku.is_enabled,
                specs: specs,
                specIds: specIds, // 新增：规格值ID映射
                image: defaultImage
              };
            }) : 
            []) : 
          [],
          
        /**
         * SKU规格矩阵 - 用于前端快速查找用户选择的规格组合对应的SKU
         * 
         * 数据结构说明：
         * 1. 返回一个对象，其中键名是多个规格值ID排序后以下划线连接的字符串，如"10001_10002_10003"
         * 2. 每个键对应的值是一个对象，包含该规格组合对应SKU的基本信息（ID、库存、价格、图片）
         * 
         * 前端使用说明：
         * 1. 用户选择不同规格组合时，收集所有选中规格的ID值
         * 2. 将这些规格值ID排序并用下划线连接，得到一个匹配键，如：[10002, 10001, 10003].sort().join('_') => '10001_10002_10003'
         * 3. 用这个匹配键从矩阵中查找对应的SKU信息：matrix['10001_10002_10003']
         * 4. 如果查找结果为空，表示该规格组合没有对应的SKU，可以禁用该组合
         * 5. 如果有结果，则可以显示对应的价格、库存信息，并更新商品图片
         * 
         * 示例：
         * {
         *   '10001_10002': { skuId: '123', stock: 10, price: '99.00', image: 'http://example.com/img.jpg' },
         *   '10001_10003': { skuId: '124', stock: 5, price: '199.00', image: 'http://example.com/img2.jpg' }
         * }
         */
        skuSpecMatrix: (() => {
          if (!processedProduct.goods_skus || !Array.isArray(processedProduct.goods_skus)) {
            return {};
          }
          
          const matrix = {};
          
          processedProduct.goods_skus.forEach(sku => {
            if (!sku.goods_sku_specification_values || !Array.isArray(sku.goods_sku_specification_values)) {
              return;
            }
            
            // 收集该SKU的所有规格值ID
            const specValueIds = [];
            sku.goods_sku_specification_values
              .filter(specValue => specValue && specValue.goods_specification_value)
              .forEach(specValue => {
                specValueIds.push(specValue.goods_specification_value.id.toString());
              });
            
            // 如果有规格值，则添加到矩阵中
            if (specValueIds.length > 0) {
              // 对规格值ID排序，确保相同组合的规格值能够匹配（不受用户选择顺序影响）
              const key = specValueIds.sort().join('_');
              matrix[key] = {
                skuId: sku.id,         // SKU ID，用于添加到购物车等操作
                stock: sku.stock,      // 库存量，用于判断是否可购买
                price: DecimalUtils.convertToString(sku.sales_price || '0'),  // 销售价格，用于显示当前选中规格组合的价格
                image: sku.goods_images && Array.isArray(sku.goods_images) && sku.goods_images.length > 0
                  ? sku.goods_images[0].image_url  // SKU特有图片，用于切换到当前规格组合的商品图片
                  : null
              };
            }
          });
          
          return matrix;
        })()
      };
      
      return {
        code: 200,
        message: '获取商品详情成功',
        data: productDetail
      };
    } catch (error) {
      console.error('获取商品详情失败:', error);
      return {
        code: 500,
        message: '获取商品详情失败',
        data: null
      };
    }
  }

  /**
   * 获取商品品牌列表（仅id和名称）
   * @param {number} page 页码，默认1
   * @param {number} pageSize 每页数量，默认10
   * @returns {Promise<Object>} 品牌列表数据和分页信息
   */
  async getAllBrands(page = 1, pageSize = 10) {
    try {
      // 计算跳过的记录数
      const skip = (page - 1) * pageSize;
      
      // 查询总记录数
      const total = await this.prisma.GoodsBrand.count({
        where: {
          deleted_at: null
        }
      });
      
      // 从数据库中查询未删除的品牌，只选择id和name字段
      // 按名称和创建时间排序，并进行分页
      const brands = await this.prisma.GoodsBrand.findMany({
        where: {
          deleted_at: null
        },
        select: {
          id: true,
          name: true
        },
        orderBy: [
          { name: 'asc' },      // 按名称字母升序
          { created_at: 'desc' } // 然后按创建时间降序（最新的先显示）
        ],
        skip,           // 跳过前面的记录
        take: pageSize  // 获取当前页的记录数
      });
      
      // 处理BigInt类型
      const processedBrands = handleBigInt(brands);
      
      // 计算总页数
      const totalPages = Math.ceil(total / pageSize);
      
      // 返回数据，使用与商品列表一致的响应结构
      return {
        code: 200,
        message: '获取品牌列表成功',
        data: {
          items: processedBrands,
          pageInfo: {
            total: total,
            currentPage: parseInt(page),
            totalPage: totalPages
          }
        }
      };
    } catch (error) {
      console.error('获取品牌列表失败:', error);
      return {
        code: 500,
        message: '获取品牌列表失败',
        data: null
      };
    }
  }

  /**
   * 获取商品服务列表（仅id和名称）
   * @param {number} page 页码，默认1
   * @param {number} pageSize 每页数量，默认10
   * @returns {Promise<Object>} 服务列表数据和分页信息
   */
  async getAllServices(page = 1, pageSize = 10) {
    try {
      // 计算跳过的记录数
      const skip = (page - 1) * pageSize;
      
      // 查询总记录数
      const total = await this.prisma.GoodsService.count({
        where: {
          deleted_at: null
        }
      });
      
      // 从数据库中查询未删除的商品服务，只选择id和name字段
      const services = await this.prisma.GoodsService.findMany({
        where: {
          deleted_at: null
        },
        select: {
          id: true,
          name: true
        },
        orderBy: [
          { name: 'asc' },      // 按名称字母升序
          { created_at: 'desc' } // 然后按创建时间降序（最新的先显示）
        ],
        skip,           // 跳过前面的记录
        take: pageSize  // 获取当前页的记录数
      });
      
      // 处理BigInt类型
      const processedServices = handleBigInt(services);
      
      // 计算总页数
      const totalPages = Math.ceil(total / pageSize);
      
      // 返回数据，使用与商品列表一致的响应结构
      return {
        code: 200,
        message: '获取商品服务列表成功',
        data: {
          items: processedServices,
          pageInfo: {
            total: total,
            currentPage: parseInt(page),
            totalPage: totalPages
          }
        }
      };
    } catch (error) {
      console.error('获取商品服务列表失败:', error);
      return {
        code: 500,
        message: '获取商品服务列表失败',
        data: null
      };
    }
  }
  
  /**
   * 获取指定数量的带图片的商品列表
   * @param {number} count 需要获取的商品数量
   * @param {boolean} random 是否随机获取
   * @returns {Promise<Object>} 商品列表数据
   */
  async getProductsWithImages(count = 6, random = false) {
    try {
      // 构建查询条件
      const queryOptions = {
        where: {
          deleted_at: null,
          status: 1, // 已上架商品
          goods_images: {
            some: {} // 确保有关联的图片
          }
        },
        select: {
          id: true,
          name: true,
          total_sales: true, // 添加销量字段
          total_stock: true, // 添加库存字段
          goods_images: {
            take: 1,
            select: {
              image_url: true
            }
          },
          goods_skus: {
            take: 1, // 注意：这里只获取第一个 SKU 来获取价格
                   // 对于多 SKU 商品，这可能不是最理想的做法
                   // 更理想的做法是计算所有 SKU 的价格区间或默认 SKU 的价格
            select: {
              sales_price: true,  // 销售价格
              market_price: true  // 市场价格
            }
          }
        },
        take: count
      };
      
      // 根据是否需要随机获取来设置排序
      if (random) {
        // Prisma 不直接支持随机排序，使用以下方法模拟
        // 1. 先获取所有符合条件的商品总数
        const totalProducts = await this.prisma.GoodsSpu.count({
          where: queryOptions.where
        });
        
        // 2. 计算跳过多少条记录（随机偏移）
        let skip = 0;
        if (totalProducts > count) {
          skip = Math.floor(Math.random() * (totalProducts - count));
        }
        
        queryOptions.skip = skip;
      } else {
        // 非随机时使用时间降序
        queryOptions.orderBy = {
          created_at: 'desc'
        };
      }
      
      // 查询数据库
      const products = await this.prisma.GoodsSpu.findMany(queryOptions);
      
      // 处理数据格式
      const processedProducts = products.map(product => ({
        id: handleBigInt(product.id),
        name: product.name,
        totalSales: product.total_sales || 0, // 销量
        totalStock: product.total_stock || 0, // 库存
        // 注意：下面只返回第一个 SKU 的价格信息
        // 对于多规格商品，可能会造成价格误解
        // 后续可优化为返回价格区间或默认规格价格
        salePrice: product.goods_skus[0]?.sales_price || 0, // 销售价格（首个SKU）
        marketPrice: product.goods_skus[0]?.market_price || 0, // 市场价格（首个SKU）
        imageUrl: product.goods_images[0]?.image_url || ''
      }));
      
      return {
        code: 200,
        message: '获取商品列表成功',
        data: processedProducts
      };
    } catch (error) {
      console.error('获取带图片商品列表失败:', error);
      return {
        code: 500,
        message: '获取商品列表失败',
        data: null
      };
    }
  }

  /**
   * 获取指定数量的带图片的商品品牌列表
   * @param {number} count 需要获取的品牌数量
   * @param {boolean} random 是否随机获取
   * @returns {Promise<Object>} 品牌列表数据
   */
  async getBrandsWithImages(count = 6, random = false) {
    try {
      // 构建查询条件
      const queryOptions = {
        where: {
          deleted_at: null,
          logo_url: {
            not: null,
          }
        },
        select: {
          id: true,
          name: true,
          logo_url: true
        },
        take: count
      };
      
      // 根据是否需要随机获取来设置排序
      if (random) {
        // Prisma 不直接支持随机排序，使用以下方法模拟
        // 1. 先获取所有符合条件的品牌总数
        const totalBrands = await this.prisma.GoodsBrand.count({
          where: queryOptions.where
        });
        
        // 2. 计算跳过多少条记录（随机偏移）
        let skip = 0;
        if (totalBrands > count) {
          skip = Math.floor(Math.random() * (totalBrands - count));
        }
        
        queryOptions.skip = skip;
      } else {
        // 非随机时使用时间降序
        queryOptions.orderBy = [
          { created_at: 'desc' }  // 按创建时间降序排序
        ];
      }
      
      // 查询数据库
      const brands = await this.prisma.GoodsBrand.findMany(queryOptions);
      
      // 处理数据格式
      const processedBrands = brands.map(brand => ({
        id: handleBigInt(brand.id),
        name: brand.name,
        imageUrl: brand.logo_url
      }));
      
      return {
        code: 200,
        message: '获取品牌列表成功',
        data: processedBrands
      };
    } catch (error) {
      console.error('获取带图片品牌列表失败:', error);
      return {
        code: 500,
        message: '获取品牌列表失败',
        data: null
      };
    }
  }
}

module.exports = GoodsService;
