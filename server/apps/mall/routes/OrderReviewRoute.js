/**
 * 订单评价路由
 */
const express = require('express');
const router = express.Router();
const OrderReviewController = require('../controllers/OrderReviewController');
const authMiddleware = require('../../../core/middleware/AuthMiddleware');

/**
 * 订单评价路由模块
 * @param {Object} prisma - Prisma客户端实例
 * @returns {Object} - Express路由对象
 */
module.exports = (prisma) => {
  // 实例化控制器
  const orderReviewController = new OrderReviewController(prisma);

  /**
   * @swagger
   * /api/v1/mall/review/reviewable-items/{orderId}:
   *   get:
   *     summary: 获取订单可评价的商品列表
   *     tags: [Mall-OrderReview]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: orderId
   *         required: true
   *         schema:
   *           type: string
   *         description: 订单ID
   *         example: "1750147298192384000"
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "获取可评价商品列表成功"
   *                 data:
   *                   type: object
   *                   properties:
   *                     order:
   *                       type: object
   *                       properties:
   *                         id:
   *                           type: string
   *                         orderSn:
   *                           type: string
   *                         createdAt:
   *                           type: string
   *                         totalAmount:
   *                           type: number
   *                     items:
   *                       type: array
   *                       items:
   *                         type: object
   *                         properties:
   *                           id:
   *                             type: string
   *                           productName:
   *                             type: string
   *                           productImage:
   *                             type: string
   *                           unitPrice:
   *                             type: number
   *                           quantity:
   *                             type: integer
   *                           isEvaluated:
   *                             type: boolean
   *       400:
   *         description: 请求参数错误
   *       401:
   *         description: 用户未登录
   *       404:
   *         description: 订单不存在
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/reviewable-items/:orderId', authMiddleware, orderReviewController.getReviewableItems.bind(orderReviewController));

  /**
   * @swagger
   * /api/v1/mall/review/submit:
   *   post:
   *     summary: 提交商品评价
   *     tags: [Mall-OrderReview]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - orderId
   *               - orderItemId
   *             properties:
   *               orderId:
   *                 type: string
   *                 description: 订单ID
   *                 example: "1750147298192384000"
   *               orderItemId:
   *                 type: string
   *                 description: 订单商品ID
   *                 example: "1750147298192384001"
   *               qualityRating:
   *                 type: integer
   *                 minimum: 1
   *                 maximum: 5
   *                 default: 5
   *                 description: 商品质量评分
   *               serviceRating:
   *                 type: integer
   *                 minimum: 1
   *                 maximum: 5
   *                 default: 5
   *                 description: 服务态度评分
   *               logisticsRating:
   *                 type: integer
   *                 minimum: 1
   *                 maximum: 5
   *                 default: 5
   *                 description: 物流速度评分
   *               reviewContent:
   *                 type: string
   *                 maxLength: 500
   *                 description: 评价内容
   *                 example: "商品质量很好，物流很快"
   *               isAnonymous:
   *                 type: boolean
   *                 default: false
   *                 description: 是否匿名评价
   *               images:
   *                 type: array
   *                 maxItems: 5
   *                 items:
   *                   type: object
   *                   properties:
   *                     fileUrl:
   *                       type: string
   *                       description: 图片URL
   *                     filePath:
   *                       type: string
   *                       description: 图片路径
   *                     originalName:
   *                       type: string
   *                       description: 原始文件名
   *                     fileSize:
   *                       type: string
   *                       description: 文件大小
   *                     extension:
   *                       type: string
   *                       description: 文件扩展名
   *     responses:
   *       200:
   *         description: 评价提交成功
   *       400:
   *         description: 请求参数错误
   *       401:
   *         description: 用户未登录
   *       500:
   *         description: 服务器内部错误
   */
  router.post('/submit', authMiddleware, orderReviewController.submitReview.bind(orderReviewController));

  /**
   * @swagger
   * /api/v1/mall/review/submit-batch:
   *   post:
   *     summary: 批量提交商品评价
   *     tags: [Mall-OrderReview]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - orderId
   *               - reviews
   *             properties:
   *               orderId:
   *                 type: string
   *                 description: 订单ID
   *               reviews:
   *                 type: array
   *                 minItems: 1
   *                 items:
   *                   type: object
   *                   required:
   *                     - orderItemId
   *                   properties:
   *                     orderItemId:
   *                       type: string
   *                     qualityRating:
   *                       type: integer
   *                       minimum: 1
   *                       maximum: 5
   *                       default: 5
   *                     serviceRating:
   *                       type: integer
   *                       minimum: 1
   *                       maximum: 5
   *                       default: 5
   *                     logisticsRating:
   *                       type: integer
   *                       minimum: 1
   *                       maximum: 5
   *                       default: 5
   *                     reviewContent:
   *                       type: string
   *                       maxLength: 500
   *                     isAnonymous:
   *                       type: boolean
   *                       default: false
   *                     images:
   *                       type: array
   *                       maxItems: 5
   *                       items:
   *                         type: object
   *     responses:
   *       200:
   *         description: 批量评价提交成功
   *       400:
   *         description: 请求参数错误
   *       401:
   *         description: 用户未登录
   *       500:
   *         description: 服务器内部错误
   */
  router.post('/submit-batch', authMiddleware, orderReviewController.submitBatchReviews.bind(orderReviewController));

  /**
   * @swagger
   * /api/v1/mall/review/product/{goodsSpuId}:
   *   get:
   *     summary: 获取商品评价列表（用于商品详情页）
   *     tags: [Mall-OrderReview]
   *     parameters:
   *       - in: path
   *         name: goodsSpuId
   *         required: true
   *         schema:
   *           type: string
   *         description: 商品SPU ID
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: pageSize
   *         schema:
   *           type: integer
   *           default: 10
   *         description: 每页数量
   *       - in: query
   *         name: rating
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 5
   *         description: 评分筛选
   *       - in: query
   *         name: hasImages
   *         schema:
   *           type: string
   *           enum: [true, false]
   *         description: 是否有图片
   *       - in: query
   *         name: sortBy
   *         schema:
   *           type: string
   *           default: created_at
   *         description: 排序字段
   *       - in: query
   *         name: sortOrder
   *         schema:
   *           type: string
   *           enum: [asc, desc]
   *           default: desc
   *         description: 排序方向
   *     responses:
   *       200:
   *         description: 获取成功
   *       400:
   *         description: 请求参数错误
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/product/:goodsSpuId', orderReviewController.getProductReviews.bind(orderReviewController));

  /**
   * @swagger
   * /api/v1/mall/review/product/{goodsSpuId}/stats:
   *   get:
   *     summary: 获取商品评价统计
   *     tags: [Mall-OrderReview]
   *     parameters:
   *       - in: path
   *         name: goodsSpuId
   *         required: true
   *         schema:
   *           type: string
   *         description: 商品SPU ID
   *     responses:
   *       200:
   *         description: 获取成功
   *       400:
   *         description: 请求参数错误
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/product/:goodsSpuId/stats', orderReviewController.getProductReviewStats.bind(orderReviewController));

  /**
   * @swagger
   * /api/v1/mall/review/user/reviews:
   *   get:
   *     summary: 获取用户的评价历史
   *     tags: [Mall-OrderReview]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: pageSize
   *         schema:
   *           type: integer
   *           default: 10
   *         description: 每页数量
   *       - in: query
   *         name: sortBy
   *         schema:
   *           type: string
   *           default: created_at
   *         description: 排序字段
   *       - in: query
   *         name: sortOrder
   *         schema:
   *           type: string
   *           enum: [asc, desc]
   *           default: desc
   *         description: 排序方向
   *     responses:
   *       200:
   *         description: 获取成功
   *       401:
   *         description: 用户未登录
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/user/reviews', authMiddleware, orderReviewController.getUserReviews.bind(orderReviewController));

  return router;
};
