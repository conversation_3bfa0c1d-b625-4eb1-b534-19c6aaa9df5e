const BaseController = require('../../../core/controllers/BaseController');
const OrderService = require('../services/OrderService');

/**
 * 商城订单控制器
 */
class OrderController extends BaseController {
  constructor(prisma) {
    super();
    this.prisma = prisma;
    this.orderService = new OrderService(prisma);
  }

  /**
   * 查询用户自己的订单
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async queryOwnOrders(req, res) {
    try {
      // 从认证中间件获取用户ID
      const userId = req.user.id; // 直接从解码后的用户信息中获取ID
      console.log('查询订单用户ID:', userId);

      // 获取查询参数
      const { 
        page = 1, 
        pageSize = 10, 
        orderStatus, 
        id, // 使用id替代orderSn作为订单编号,
        sortField = 'created_at',
        sortOrder = 'desc'
      } = req.body;

      console.log('查询订单参数:', { 
        page, 
        pageSize, 
        orderStatus, 
        id, // 使用id替代orderSn作为订单编号,
        sortField,
        sortOrder
      });

      // 验证参数
      if (page < 1 || pageSize < 1 || pageSize > 50) {
        return this.fail(res, '分页参数无效', 400);
      }

      // 查询订单
      const result = await this.orderService.queryUserOrders(userId, {
        page,
        pageSize,
        orderStatus: orderStatus !== undefined ? parseInt(orderStatus) : undefined,
        id, // 使用id替代orderSn作为订单编号,
        sortField,
        sortOrder
      });

      // 处理订单数据，转换为新的响应格式
      const formattedOrders = result.list.map(order => {
        // 根据订单状态生成文字说明
        const orderStatusMap = {
          0: '待付款',
          1: '待发货',
          2: '待收货',
          3: '已完成',
          4: '已取消',
          5: '已关闭'
        };
        
        const paymentStatusMap = {
          0: '未支付',
          1: '已支付',
          2: '支付失败',
          3: '已退款'
        };
        
        const shippingStatusMap = {
          0: '未发货',
          1: '已发货',
          2: '已签收',
          3: '退货中',
          4: '退货完成'
        };
        
        const orderTypeMap = {
          0: '普通订单',
          1: '商城订单',
          2: '卡券订单'
        };
        
        const orderSourceMap = {
          0: '系统创建',
          1: '后台创建',
          2: '商城下单'
        };
        
        // 处理订单项
        const orderItems = order.order_items ? order.order_items.map(item => ({
          id: item.id ? item.id.toString() : '',
          productId: item.goods_spu_id ? item.goods_spu_id.toString() : '',
          orderId: order.id ? order.id.toString() : '',
          productName: item.product_name || '',
          productImage: item.product_image || '/uploads/products/default.jpg',
          unitPrice: item.unit_price ? item.unit_price.toString() : '0',
          quantity: item.quantity || 0,
          totalPrice: item.unit_price && item.quantity ? (parseFloat(item.unit_price) * item.quantity).toFixed(2) : '0.00',
          spuNameSnapshot: item.product_name || '',
          skuSpecifications: item.product_attr_values || '',
          thirdPartyProductCode: item.third_party_product_code || ''
        })) : [];
        
        // 处理配送信息
        const shipping = order.order_shipping_info ? {
          id: order.order_shipping_info.id ? order.order_shipping_info.id.toString() : '',
          recipientName: order.order_shipping_info.recipient_name || '',
          recipientPhone: order.order_shipping_info.recipient_phone || '',
          // 区域 ID
          regionProvinceId: order.order_shipping_info.region_province_id || 0,
          regionCityId: order.order_shipping_info.region_city_id || 0,
          regionDistrictId: order.order_shipping_info.region_district_id || 0,
          // 区域路径名称
          regionPathName: order.order_shipping_info.region_path_name || '',
          streetAddress: order.order_shipping_info.street_address || '',
          postalCode: order.order_shipping_info.postal_code || '100000',
          shippingMethod: order.order_shipping_info.shipping_method || 1,
          shippingMethodText: '快递物流',
          shippingCompanyName: order.order_shipping_info.shipping_company_name || '',
          trackingNumber: order.order_shipping_info.tracking_number || ''
        } : null;
        
        // 返回格式化后的订单数据
        return {
          id: order.id ? order.id.toString() : '',
          // orderSn字段已移除，使用id作为订单编号
          userId: order.user_id.toString(),
          thirdPartySubEntityId: order.third_party_sub_entity_id ? order.third_party_sub_entity_id.toString() : null,
          thirdPartyOrderSn: order.third_party_order_sn || '',
          orderStatus: order.order_status || 0,
          paymentStatus: order.payment_status || 0,
          shippingStatus: order.shipping_status || 0,
          totalProductAmount: order.total_product_amount ? order.total_product_amount.toString() : '0',
          shippingFee: order.shipping_fee ? order.shipping_fee.toString() : '0',
          discountAmount: order.discount_amount ? order.discount_amount.toString() : '0',
          taxAmount: order.tax_amount ? order.tax_amount.toString() : '0',
          totalAmount: order.total_amount ? order.total_amount.toString() : '0',
          paidAmount: order.paid_amount ? order.paid_amount.toString() : '0',
          paymentMethod: order.payment_method || '',
          paymentSn: order.payment_sn || '',
          shippingMethod: order.shipping_method || '',
          orderSource: order.order_source || 0,
          orderType: order.order_type || 0,
          remark: order.remark || '',
          adminRemark: order.admin_remark || '',
          createdAt: order.created_at ? order.created_at.toString() : '',
          updatedAt: order.updated_at ? order.updated_at.toString() : '',
          paidAt: order.paid_at ? order.paid_at.toString() : null,
          shippedAt: order.shipped_at ? order.shipped_at.toString() : null,
          completedAt: order.completed_at ? order.completed_at.toString() : null,
          cancelledAt: order.cancelled_at ? order.cancelled_at.toString() : null,
          cancelReason: order.cancel_reason || '',
          deletedAt: order.deleted_at ? order.deleted_at.toString() : null,
          orderStatusText: orderStatusMap[order.order_status] || '未知状态',
          paymentStatusText: paymentStatusMap[order.payment_status] || '未知状态',
          shippingStatusText: shippingStatusMap[order.shipping_status] || '未知状态',
          orderTypeText: orderTypeMap[order.order_type] || '普通订单',
          orderSourceText: orderSourceMap[order.order_source] || '系统创建',
          items: orderItems,
          shipping: shipping
        };
      });

      // 返回新的响应格式
      return this.success(res, {
        items: formattedOrders,
        pageInfo: {
          total: result.total,
          currentPage: result.page,
          totalPage: result.totalPages
        }
      }, '获取订单列表成功');
    } catch (error) {
      console.error('查询订单失败:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 查询订单详情
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getOrderDetail(req, res) {
    try {
      // 从认证中间件获取用户ID
      const userId = req.user.id; // 直接从解码后的用户信息中获取ID
      
      // 获取订单ID
      const { orderId } = req.params;
      
      if (!orderId) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      // 查询订单详情
      const order = await this.orderService.getOrderDetail(BigInt(orderId), userId);
      
      if (!order) {
        return this.fail(res, '订单不存在或已被删除', 404);
      }

      // 格式化订单数据
      const formattedOrder = {
        ...order,
        created_at: order.created_at,
        updated_at: order.updated_at,
        paid_at: order.paid_at,
        shipped_at: order.shipped_at,
        completed_at: order.completed_at,
        cancelled_at: order.cancelled_at,
        // 格式化金额为字符串，避免前端精度问题
        total_product_amount: order.total_product_amount.toString(),
        shipping_fee: order.shipping_fee.toString(),
        discount_amount: order.discount_amount.toString(),
        tax_amount: order.tax_amount.toString(),
        total_amount: order.total_amount.toString(),
        paid_amount: order.paid_amount.toString()
      };

      return this.success(res, formattedOrder, '查询订单详情成功');
    } catch (error) {
      console.error('查询订单详情失败:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 获取订单详情
   * @param {Object} req - 请求对象
      const order = await this.orderService.getOrderDetail(orderId, userId);
      
      if (!order) {
        return this.fail(res, '订单不存在或已被删除', 404);
      }
        recipientPhone: order.order_shipping_info.recipient_phone || '',
        // 区域 ID
        regionProvinceId: order.order_shipping_info.region_province_id || 0,
        regionCityId: order.order_shipping_info.region_city_id || 0,
        regionDistrictId: order.order_shipping_info.region_district_id || 0,
        // 区域路径名称
        regionPathName: order.order_shipping_info.region_path_name || '',
        streetAddress: order.order_shipping_info.street_address || '',
        postalCode: order.order_shipping_info.postal_code || '100000',
        shippingMethod: order.order_shipping_info.shipping_method || 1,
        shippingMethodText: '快递物流',
        shippingCompanyName: order.order_shipping_info.shipping_company_name || '',
        trackingNumber: order.order_shipping_info.tracking_number || ''
      } : null;

      // 格式化订单时间
      const formatTime = (timestamp) => {
        if (!timestamp) return '';
        
        try {
          // 如果 timestamp 是 BigInt 类型，先转换为字符串
          const timestampStr = typeof timestamp === 'bigint' ? timestamp.toString() : timestamp;
          
          // 尝试直接创建 Date 对象
          let date = new Date(timestampStr);
          
          // 检查日期是否有效
          if (isNaN(date.getTime())) {
            // 如果是数据库返回的时间戳字符串，可能需要额外处理
            console.log('日期格式化失败，尝试其他方法，原始值：', timestampStr);
            
            // 如果是数字字符串，可能是毫秒时间戳，尝试转换
            if (/^\d+$/.test(timestampStr)) {
              const num = Number(timestampStr);
              // 判断是秒还是毫秒
              date = new Date(num.toString().length > 10 ? num : num * 1000);
            } else {
              // 如果是其他格式，返回原始字符串
              return timestampStr;
            }
          }
          
          // 再次检查日期是否有效
          if (isNaN(date.getTime())) {
            console.log('日期仍然无效，返回原始值');
            return timestampStr;
          }
          
          return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
        } catch (error) {
          console.error('格式化时间出错:', error);
          return timestamp ? timestamp.toString() : '';
        }
      };

      // 构建订单详情响应
      const orderDetail = {
        id: order.id ? order.id.toString() : '',
        // orderSn字段已移除，使用id作为订单编号
        userId: order.user_id.toString(),
        status: order.order_status || 0,
        paymentStatus: order.payment_status || 0,
        shippingStatus: order.shipping_status || 0,
        totalAmount: order.total_amount ? Number(order.total_amount.toString()) : 0,
        shippingFee: order.shipping_fee ? Number(order.shipping_fee.toString()) : 0,
        discountAmount: order.discount_amount ? Number(order.discount_amount.toString()) : 0,
        paymentMethod: order.payment_method || 0,
        paymentTime: formatTime(order.payment_time),
        createTime: formatTime(order.created_at),
        updateTime: formatTime(order.updated_at),
        items: items,
        address: shipping,
        logistics: order.shipping_status > 0 ? {
          company: shipping ? shipping.shippingCompanyName : '',
          trackingNumber: shipping ? shipping.trackingNumber : '',
          deliveryTime: formatTime(order.delivery_time),
          receivedTime: formatTime(order.received_time)
        } : null,
        remark: order.remark || ''
      };

      // 返回成功响应
      this.success(res, orderDetail, '获取订单详情成功');
    } catch (error) {
      // 记录错误日志
      console.error('获取订单详情失败', error);

      // 返回通用错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 查询用户不同订单状态的数量
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getOrderStatusCounts(req, res) {
    try {
      // 从认证中间件获取用户ID
      const userId = req.user.id;
      console.log('查询订单状态数量用户ID:', userId);

      // 调用服务查询订单状态数量
      const result = await this.orderService.queryOrderStatusCounts(userId);

      // 返回成功响应
      this.success(res, result.data, '查询订单状态数量成功');
    } catch (error) {
      // 记录错误日志
      console.error('查询订单状态数量失败', error);

      // 返回通用错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 取消订单
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async cancelOrder(req, res) {
    try {
      // 从认证中间件获取用户ID
      const userId = req.user.id;
      console.log('取消订单用户ID:', userId);

      // 获取订单ID
      const orderId = req.params.id;
      if (!orderId) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      console.log('取消订单:', orderId);

      // 取消原因，可以从请求体中获取
      const cancelReason = req.body.cancelReason || '用户取消';

      // 构建操作者信息
      const operator = {
        id: userId,
        name: req.user.username || req.user.nickname || '用户',
        role: 'user',
        ip: req.ip || '127.0.0.1',
        platform: 'mall'
      };

      // 调用服务取消订单
      const result = await this.orderService.cancelOrder(orderId, userId, cancelReason, operator);

      // 返回成功响应，使用指定的数据结构
      this.success(res, result.data, '订单取消成功');
    } catch (error) {
      // 记录错误日志
      console.error('取消订单失败', error);

      // 根据错误类型返回不同的响应
      if (error.message.includes('不存在')) {
        return this.fail(res, error.message, 404);
      }
      if (error.message.includes('状态') || error.message.includes('不允许') || error.message.includes('无法取消')) {
        return this.fail(res, error.message, 400);
      }

      // 返回通用错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }
}

module.exports = OrderController;
