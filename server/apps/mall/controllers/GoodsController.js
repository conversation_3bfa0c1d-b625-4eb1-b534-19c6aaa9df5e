/**
 * 商城商品控制器
 * 负责处理商品相关的HTTP请求
 */
const BaseController = require('../../../core/controllers/BaseController');
const GoodsService = require('../services/GoodsService');

/**
 * 商品控制器类
 */
class GoodsController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    super();
    this.prisma = prisma;
    this.goodsService = new GoodsService(prisma);
  }
  
  /**
   * 获取商品列表，支持多种筛选条件
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getProducts(req, res) {
    try {
      // 从查询参数中获取所有可能的筛选条件
      const { 
        page = 1, 
        pageSize = 10,
        categoryId,
        brandId,
        keyword,
        minPrice,
        maxPrice,
        sortBy = 'created_at',
        sortOrder = 'desc',
        tags,
        serviceIds
      } = req.query;
      
      // 构建筛选条件对象
      
      // 构建筛选条件对象
      const filters = {
        categoryId,
        brandId,
        keyword,
        minPrice,
        maxPrice,
        tagIds: tags ? tags.split(',') : undefined, // 改为tagIds，使用标签ID而非名称
        serviceIds: serviceIds ? serviceIds.split(',') : undefined,
        sortBy,
        sortOrder
      };
      
      // 调用服务层方法获取商品列表
      const result = await this.goodsService.getProducts(filters, page, pageSize);
      
      if (result.code === 200) {
        // 使用successList方法返回列表数据
        return this.successList(
          res, 
          result.data.items, 
          result.data.pageInfo.total, 
          result.data.pageInfo.currentPage, 
          pageSize, 
          result.message
        );
      } else {
        return this.fail(res, result.message, null, result.code);
      }
    } catch (error) {
      console.error('获取商品列表失败:', error);
      return this.fail(res, '获取商品列表失败', null, 500);
    }
  }
  
  // 旧方法getProductsByCategoryId已移除，统一使用getProducts方法
  
  /**
   * 获取商品分类详情
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getCategoryById(req, res) {
    try {
      const { categoryId } = req.params;
      
      if (!categoryId) {
        return this.fail(res, '分类ID不能为空', null, 400);
      }
      
      const result = await this.goodsService.getCategoryById(categoryId);
      
      if (result.code === 200) {
        return this.success(res, result.data, result.message);
      } else {
        return this.fail(res, result.message, null, result.code);
      }
    } catch (error) {
      console.error('获取商品分类详情失败:', error);
      return this.fail(res, '获取商品分类详情失败', null, 500);
    }
  }
  
  /**
   * 获取商品分类树形结构
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getCategoryTree(req, res) {
    try {
      const result = await this.goodsService.getCategoryTree();
      return this.success(res, result.data, result.message);
    } catch (error) {
      console.error('获取商品分类树形结构失败:', error);
      return this.fail(res, '获取商品分类树形结构失败', 500);
    }
  }
  
  /**
   * 获取商品详情
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getProductDetail(req, res) {
    try {
      const { spuId } = req.params;

      if (!spuId) {
        return this.fail(res, '商品ID不能为空', null, 400);
      }

      const result = await this.goodsService.getProductDetail(spuId);

      if (result.code === 200) {
        // 如果用户已登录，添加浏览记录
        if (req.user && req.user.id) {
          try {
            // 异步添加浏览记录，不影响商品详情的返回
            this.addBrowseHistoryAsync(req.user.id, spuId);
          } catch (browseError) {
            // 浏览记录添加失败不影响商品详情的正常返回
            console.warn('添加浏览记录失败:', browseError);
          }
        }

        return this.success(res, result.data, result.message);
      } else if (result.code === 404) {
        return this.fail(res, result.message, null, 404);
      } else {
        return this.fail(res, result.message, null, result.code);
      }
    } catch (error) {
      console.error('获取商品详情失败:', error);
      return this.fail(res, '获取商品详情失败', null, 500);
    }
  }

  /**
   * 异步添加浏览记录
   * @param {BigInt} userId 用户ID
   * @param {String} goodsSpuId 商品SPU ID
   */
  async addBrowseHistoryAsync(userId, goodsSpuId) {
    try {
      // 导入浏览记录服务
      const BrowseHistoryService = require('../services/BrowseHistoryService');
      const browseHistoryService = new BrowseHistoryService(this.prisma);

      // 添加浏览记录
      await browseHistoryService.addBrowseHistory({
        userId: BigInt(userId),
        goodsSpuId: BigInt(goodsSpuId),
        goodsSkuId: null // 商品详情页面暂时不记录具体的SKU
      });

      console.log(`用户 ${userId} 浏览商品 ${goodsSpuId} 的记录已添加`);
    } catch (error) {
      console.error('异步添加浏览记录失败:', error);
    }
  }
  
  /**
   * 获取所有商品标签
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getAllTags(req, res) {
    try {
      const { name, page = 1, pageSize = 10 } = req.query;
      const result = await this.goodsService.getAllTags({ name, page: parseInt(page), pageSize: parseInt(pageSize) });
      
      if (result.code === 200) {
        return this.success(res, result.data, result.message);
      } else {
        return this.fail(res, result.message, null, result.code);
      }
    } catch (error) {
      console.error('获取商品标签列表失败:', error);
      return this.fail(res, '获取商品标签列表失败', null, 500);
    }
  }
  
  /**
   * 获取商品品牌列表（仅id和名称）
   * @param {Object} req 请求对象
   * @param {number} req.query.page 页码，默认1
   * @param {number} req.query.pageSize 每页数量，默认10
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getAllBrands(req, res) {
    try {
      // 获取分页参数，设置默认值
      const { page = 1, pageSize = 10 } = req.query;
      
      // 转换为数字并进行安全限制
      const currentPage = Math.max(1, parseInt(page, 10) || 1);
      const limit = Math.min(Math.max(1, parseInt(pageSize, 10) || 10), 100); // 每页最多100条
      
      const result = await this.goodsService.getAllBrands(currentPage, limit);
      
      if (result.code === 200) {
        return this.success(res, result.data, result.message);
      } else {
        return this.fail(res, result.message, null, result.code);
      }
    } catch (error) {
      console.error('获取品牌列表失败:', error);
      return this.fail(res, '获取品牌列表失败', null, 500);
    }
  }

  /**
   * 获取商品服务列表（仅id和名称）
   * @param {Object} req 请求对象
   * @param {number} req.query.page 页码，默认1
   * @param {number} req.query.pageSize 每页数量，默认10
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getAllServices(req, res) {
    try {
      // 获取分页参数，设置默认值
      const { page = 1, pageSize = 10 } = req.query;
      
      // 转换为数字并进行安全限制
      const currentPage = Math.max(1, parseInt(page, 10) || 1);
      const limit = Math.min(Math.max(1, parseInt(pageSize, 10) || 10), 100); // 每页最多100条
      
      const result = await this.goodsService.getAllServices(currentPage, limit);
      
      if (result.code === 200) {
        return this.success(res, result.data, result.message);
      } else {
        return this.fail(res, result.message, null, result.code);
      }
    } catch (error) {
      console.error('获取商品服务列表失败:', error);
      return this.fail(res, '获取商品服务列表失败', null, 500);
    }
  }
  
  /**
   * 获取指定数量的带图片的商品列表
   * @param {Object} req 请求对象
   * @param {number} req.query.count 需要获取的商品数量，默认6
   * @param {boolean} req.query.random 是否随机获取，默认false
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getProductsWithImages(req, res) {
    try {
      const { count = 6, random = false } = req.query;
      const limit = Math.min(Math.max(1, parseInt(count, 10) || 6), 100); // 限制最多100个
      
      // 将random字符串转换为布尔值
      const isRandom = random === 'true' || random === '1' || random === true;
      
      const result = await this.goodsService.getProductsWithImages(limit, isRandom);
      
      if (result.code === 200) {
        return this.success(res, result.data, result.message);
      } else {
        return this.fail(res, result.message, null, result.code);
      }
    } catch (error) {
      console.error('获取商品列表失败:', error);
      return this.fail(res, '获取商品列表失败', null, 500);
    }
  }

  /**
   * 获取指定数量的带图片的商品品牌列表
   * @param {Object} req 请求对象
   * @param {number} req.query.count 需要获取的品牌数量，默认6
   * @param {boolean} req.query.random 是否随机获取，默认false
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getBrandsWithImages(req, res) {
    try {
      const { count = 6, random = false } = req.query;
      const limit = Math.min(Math.max(1, parseInt(count, 10) || 6), 100); // 限制最多100个
      
      // 将random字符串转换为布尔值
      const isRandom = random === 'true' || random === '1' || random === true;
      
      const result = await this.goodsService.getBrandsWithImages(limit, isRandom);
      
      if (result.code === 200) {
        return this.success(res, result.data, result.message);
      } else {
        return this.fail(res, result.message, null, result.code);
      }
    } catch (error) {
      console.error('获取品牌列表失败:', error);
      return this.fail(res, '获取品牌列表失败', null, 500);
    }
  }
}

module.exports = GoodsController;
