/**
 * 商城用户收货地址控制器
 * 处理用户收货地址相关的请求
 */
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

/**
 * 处理包含 BigInt 的数据，将 BigInt 转换为字符串
 * @param {Object|Array} data - 需要处理的数据
 * @returns {Object|Array} - 处理后的数据
 */
function processBigIntData(data) {
  if (data === null || data === undefined) {
    return data;
  }
  
  // 如果是数组，递归处理每个元素
  if (Array.isArray(data)) {
    return data.map(item => processBigIntData(item));
  }
  
  // 如果是对象，递归处理每个属性
  if (typeof data === 'object') {
    const result = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        result[key] = processBigIntData(data[key]);
      }
    }
    return result;
  }
  
  // 如果是 BigInt，转换为字符串
  if (typeof data === 'bigint') {
    return data.toString();
  }
  
  // 其他类型直接返回
  return data;
}

class UserAddressController {
  /**
   * 获取用户所有收货地址
   * @param {Object} req - Express 请求对象
   * @param {Object} res - Express 响应对象
   */
  async getAddresses(req, res) {
    try {
      const userId = req.user.id;
      
      // 查询用户的所有未删除的收货地址，按是否默认地址和创建时间排序
      const addresses = await prisma.$queryRaw`
        SELECT id, name, phone, province, city, district, address, postcode, is_default
        FROM base.mall_user_address
        WHERE user_id = ${userId}::bigint AND deleted_at IS NULL
        ORDER BY is_default DESC, created_at DESC
      `;
      
      // 处理 BigInt 数据
      const processedAddresses = processBigIntData(addresses);
      
      return res.status(200).json({
        code: 200,
        message: '获取收货地址成功',
        data: processedAddresses
      });
    } catch (error) {
      console.error('获取收货地址失败:', error);
      return res.status(500).json({
        code: 500,
        message: '获取收货地址失败，请稍后再试',
        data: null
      });
    }
  }
  
  /**
   * 获取用户默认收货地址
   * @param {Object} req - Express 请求对象
   * @param {Object} res - Express 响应对象
   */
  async getDefaultAddress(req, res) {
    try {
      const userId = req.user.id;
      
      // 查询用户的默认收货地址
      const defaultAddress = await prisma.$queryRaw`
        SELECT id, name, phone, province, city, district, address, postcode, is_default
        FROM base.mall_user_address
        WHERE user_id = ${userId}::bigint AND deleted_at IS NULL AND is_default = true
        LIMIT 1
      `;
      
      if (defaultAddress && defaultAddress.length > 0) {
        // 处理 BigInt 数据
        const processedAddress = processBigIntData(defaultAddress[0]);
        
        return res.status(200).json({
          code: 200,
          message: '获取默认收货地址成功',
          data: processedAddress
        });
      } else {
        return res.status(200).json({
          code: 200,
          message: '用户没有设置默认收货地址',
          data: null
        });
      }
    } catch (error) {
      console.error('获取默认收货地址失败:', error);
      return res.status(500).json({
        code: 500,
        message: '获取默认收货地址失败，请稍后再试',
        data: null
      });
    }
  }
  
  /**
   * 获取收货地址详情
   * @param {Object} req - Express 请求对象
   * @param {Object} res - Express 响应对象
   */
  async getAddressById(req, res) {
    try {
      const userId = req.user.id;
      const addressId = BigInt(req.params.id);
      
      // 查询特定的收货地址
      const address = await prisma.$queryRaw`
        SELECT id, name, phone, province, city, district, address, postcode, is_default
        FROM base.mall_user_address
        WHERE id = ${addressId} AND user_id = ${userId}::bigint AND deleted_at IS NULL
      `;
      
      if (address && address.length > 0) {
        // 处理 BigInt 数据
        const processedAddress = processBigIntData(address[0]);
        
        return res.status(200).json({
          code: 200,
          message: '获取收货地址详情成功',
          data: processedAddress
        });
      } else {
        return res.status(404).json({
          code: 404,
          message: '收货地址不存在或已被删除',
          data: null
        });
      }
    } catch (error) {
      console.error('获取收货地址详情失败:', error);
      return res.status(500).json({
        code: 500,
        message: '获取收货地址详情失败，请稍后再试',
        data: null
      });
    }
  }
  
  /**
   * 创建新的收货地址
   * @param {Object} req - Express 请求对象
   * @param {Object} res - Express 响应对象
   */
  async createAddress(req, res) {
    try {
      const userId = req.user.id;
      const { name, phone, province, city, district, address, postcode, isDefault } = req.body;
      
      // 验证必填字段
      if (!name || !phone || !province || !city || !district || !address) {
        return res.status(400).json({
          code: 400,
          message: '请填写完整的收货地址信息',
          data: null
        });
      }
      
      // 验证手机号格式
      if (!/^1[3-9]\d{9}$/.test(phone)) {
        return res.status(400).json({
          code: 400,
          message: '请输入正确的手机号码',
          data: null
        });
      }
      
      // 生成地址ID
      const addressId = await generateSnowflakeId();
      const now = BigInt(Date.now());
      
      // 如果设置为默认地址，则将用户其他地址设为非默认
      if (isDefault) {
        await prisma.$executeRaw`
          UPDATE base.mall_user_address
          SET is_default = false, updated_at = ${now}
          WHERE user_id = ${userId}::bigint AND deleted_at IS NULL
        `;
      }
      
      // 如果是用户的第一个地址，则自动设为默认地址
      const addressCount = await prisma.$queryRaw`
        SELECT COUNT(*) as count FROM base.mall_user_address
        WHERE user_id = ${userId}::bigint AND deleted_at IS NULL
      `;
      
      const shouldBeDefault = isDefault || (addressCount[0].count === 0);
      
      // 创建新地址
      await prisma.$executeRaw`
        INSERT INTO base.mall_user_address (
          id, user_id, name, phone, province, city, district, address, 
          postcode, is_default, created_at, updated_at
        ) VALUES (
          ${addressId}, ${userId}::bigint, ${name}, ${phone}, ${province}, ${city}, 
          ${district}, ${address}, ${postcode || ''}, ${shouldBeDefault}, ${now}, ${now}
        )
      `;
      
      // 查询新创建的地址
      const newAddress = await prisma.$queryRaw`
        SELECT id, name, phone, province, city, district, address, postcode, is_default
        FROM base.mall_user_address
        WHERE id = ${addressId}
      `;
      
      // 处理 BigInt 数据
      const processedAddress = processBigIntData(newAddress[0]);
      
      return res.status(200).json({
        code: 200,
        message: '添加收货地址成功',
        data: processedAddress
      });
    } catch (error) {
      console.error('添加收货地址失败:', error);
      return res.status(500).json({
        code: 500,
        message: '添加收货地址失败，请稍后再试',
        data: null
      });
    }
  }
  
  /**
   * 更新收货地址
   * @param {Object} req - Express 请求对象
   * @param {Object} res - Express 响应对象
   */
  async updateAddress(req, res) {
    try {
      const userId = req.user.id;
      const addressId = BigInt(req.params.id);
      const { name, phone, province, city, district, address, postcode, isDefault } = req.body;
      
      // 验证必填字段
      if (!name || !phone || !province || !city || !district || !address) {
        return res.status(400).json({
          code: 400,
          message: '请填写完整的收货地址信息',
          data: null
        });
      }
      
      // 验证手机号格式
      if (!/^1[3-9]\d{9}$/.test(phone)) {
        return res.status(400).json({
          code: 400,
          message: '请输入正确的手机号码',
          data: null
        });
      }
      
      // 检查地址是否存在且属于当前用户
      const existingAddress = await prisma.$queryRaw`
        SELECT id FROM base.mall_user_address
        WHERE id = ${addressId} AND user_id = ${userId}::bigint AND deleted_at IS NULL
      `;
      
      if (!existingAddress || existingAddress.length === 0) {
        return res.status(404).json({
          code: 404,
          message: '收货地址不存在或已被删除',
          data: null
        });
      }
      
      const now = BigInt(Date.now());
      
      // 如果设置为默认地址，则将用户其他地址设为非默认
      if (isDefault) {
        await prisma.$executeRaw`
          UPDATE base.mall_user_address
          SET is_default = false, updated_at = ${now}
          WHERE user_id = ${userId}::bigint AND deleted_at IS NULL
        `;
      }
      
      // 更新地址
      await prisma.$executeRaw`
        UPDATE base.mall_user_address
        SET 
          name = ${name},
          phone = ${phone},
          province = ${province},
          city = ${city},
          district = ${district},
          address = ${address},
          postcode = ${postcode || ''},
          is_default = ${isDefault},
          updated_at = ${now}
        WHERE id = ${addressId} AND user_id = ${userId}::bigint
      `;
      
      // 查询更新后的地址
      const updatedAddress = await prisma.$queryRaw`
        SELECT id, name, phone, province, city, district, address, postcode, is_default
        FROM base.mall_user_address
        WHERE id = ${addressId}
      `;
      
      // 处理 BigInt 数据
      const processedAddress = processBigIntData(updatedAddress[0]);
      
      return res.status(200).json({
        code: 200,
        message: '更新收货地址成功',
        data: processedAddress
      });
    } catch (error) {
      console.error('更新收货地址失败:', error);
      return res.status(500).json({
        code: 500,
        message: '更新收货地址失败，请稍后再试',
        data: null
      });
    }
  }
  
  /**
   * 设置默认收货地址
   * @param {Object} req - Express 请求对象
   * @param {Object} res - Express 响应对象
   */
  async setDefaultAddress(req, res) {
    try {
      const userId = req.user.id;
      const addressId = BigInt(req.params.id);
      
      // 检查地址是否存在且属于当前用户
      const existingAddress = await prisma.$queryRaw`
        SELECT id FROM base.mall_user_address
        WHERE id = ${addressId} AND user_id = ${userId}::bigint AND deleted_at IS NULL
      `;
      
      if (!existingAddress || existingAddress.length === 0) {
        return res.status(404).json({
          code: 404,
          message: '收货地址不存在或已被删除',
          data: null
        });
      }
      
      const now = BigInt(Date.now());
      
      // 将用户所有地址设为非默认
      await prisma.$executeRaw`
        UPDATE base.mall_user_address
        SET is_default = false, updated_at = ${now}
        WHERE user_id = ${userId}::bigint AND deleted_at IS NULL
      `;
      
      // 将指定地址设为默认
      await prisma.$executeRaw`
        UPDATE base.mall_user_address
        SET is_default = true, updated_at = ${now}
        WHERE id = ${addressId} AND user_id = ${userId}::bigint
      `;
      
      return res.status(200).json({
        code: 200,
        message: '设置默认地址成功',
        data: null
      });
    } catch (error) {
      console.error('设置默认地址失败:', error);
      return res.status(500).json({
        code: 500,
        message: '设置默认地址失败，请稍后再试',
        data: null
      });
    }
  }
  
  /**
   * 删除收货地址
   * @param {Object} req - Express 请求对象
   * @param {Object} res - Express 响应对象
   */
  async deleteAddress(req, res) {
    try {
      const userId = req.user.id;
      const addressId = BigInt(req.params.id);
      
      // 检查地址是否存在且属于当前用户
      const existingAddress = await prisma.$queryRaw`
        SELECT id, is_default FROM base.mall_user_address
        WHERE id = ${addressId} AND user_id = ${userId}::bigint AND deleted_at IS NULL
      `;
      
      if (!existingAddress || existingAddress.length === 0) {
        return res.status(404).json({
          code: 404,
          message: '收货地址不存在或已被删除',
          data: null
        });
      }
      
      const now = BigInt(Date.now());
      const isDefault = existingAddress[0].is_default;
      
      // 软删除地址
      await prisma.$executeRaw`
        UPDATE base.mall_user_address
        SET deleted_at = ${now}, is_default = false
        WHERE id = ${addressId} AND user_id = ${userId}::bigint
      `;
      
      // 如果删除的是默认地址，则将用户的第一个地址设为默认（如果有的话）
      if (isDefault) {
        const remainingAddresses = await prisma.$queryRaw`
          SELECT id FROM base.mall_user_address
          WHERE user_id = ${userId}::bigint AND deleted_at IS NULL
          ORDER BY created_at ASC
          LIMIT 1
        `;
        
        if (remainingAddresses && remainingAddresses.length > 0) {
          await prisma.$executeRaw`
            UPDATE base.mall_user_address
            SET is_default = true, updated_at = ${now}
            WHERE id = ${remainingAddresses[0].id}
          `;
        }
      }
      
      return res.status(200).json({
        code: 200,
        message: '删除收货地址成功',
        data: null
      });
    } catch (error) {
      console.error('删除收货地址失败:', error);
      return res.status(500).json({
        code: 500,
        message: '删除收货地址失败，请稍后再试',
        data: null
      });
    }
  }
}

module.exports = new UserAddressController();
