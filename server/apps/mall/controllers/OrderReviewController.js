const BaseController = require('../../../core/controllers/BaseController');
const OrderReviewService = require('../services/OrderReviewService');
const Joi = require('joi');

/**
 * 订单评价控制器
 */
class OrderReviewController extends BaseController {
  constructor(prisma) {
    super();
    this.prisma = prisma;
    this.orderReviewService = new OrderReviewService(prisma);
  }

  /**
   * 获取订单可评价的商品列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getReviewableItems(req, res) {
    try {
      const { orderId } = req.params;
      const userId = req.user.id;

      if (!orderId) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      const result = await this.orderReviewService.getReviewableItems(orderId, userId);
      return this.success(res, result, '获取可评价商品列表成功');
    } catch (error) {
      console.error('获取可评价商品列表失败:', error);
      return this.fail(res, error.message || '获取可评价商品列表失败', 500);
    }
  }

  /**
   * 提交商品评价
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async submitReview(req, res) {
    try {
      // 验证请求参数
      const schema = Joi.object({
        orderId: Joi.string().required().messages({
          'any.required': '订单ID不能为空',
          'string.empty': '订单ID不能为空'
        }),
        orderItemId: Joi.string().required().messages({
          'any.required': '订单商品ID不能为空',
          'string.empty': '订单商品ID不能为空'
        }),
        qualityRating: Joi.number().integer().min(1).max(5).default(5).messages({
          'number.base': '商品质量评分必须是数字',
          'number.integer': '商品质量评分必须是整数',
          'number.min': '商品质量评分最低1分',
          'number.max': '商品质量评分最高5分'
        }),
        serviceRating: Joi.number().integer().min(1).max(5).default(5).messages({
          'number.base': '服务态度评分必须是数字',
          'number.integer': '服务态度评分必须是整数',
          'number.min': '服务态度评分最低1分',
          'number.max': '服务态度评分最高5分'
        }),
        logisticsRating: Joi.number().integer().min(1).max(5).default(5).messages({
          'number.base': '物流速度评分必须是数字',
          'number.integer': '物流速度评分必须是整数',
          'number.min': '物流速度评分最低1分',
          'number.max': '物流速度评分最高5分'
        }),
        reviewContent: Joi.string().allow('').max(500).messages({
          'string.max': '评价内容不能超过500字'
        }),
        isAnonymous: Joi.boolean().default(false).messages({
          'boolean.base': '匿名评价标识必须是布尔值'
        }),
        images: Joi.array().items(
          Joi.object({
            fileUrl: Joi.string().required(),
            filePath: Joi.string().required(),
            originalName: Joi.string().required(),
            fileSize: Joi.string().allow(''),
            extension: Joi.string().allow('')
          })
        ).max(5).default([]).messages({
          'array.max': '最多只能上传5张图片'
        })
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        return this.fail(res, error.details[0].message, 400);
      }

      const userId = req.user.id;
      const reviewData = { ...value, userId };

      const result = await this.orderReviewService.submitReview(reviewData);
      return this.success(res, result, '评价提交成功');
    } catch (error) {
      console.error('提交评价失败:', error);
      return this.fail(res, error.message || '提交评价失败', 500);
    }
  }

  /**
   * 批量提交商品评价
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async submitBatchReviews(req, res) {
    try {
      // 验证请求参数
      const schema = Joi.object({
        orderId: Joi.string().required().messages({
          'any.required': '订单ID不能为空',
          'string.empty': '订单ID不能为空'
        }),
        reviews: Joi.array().items(
          Joi.object({
            orderItemId: Joi.string().required(),
            qualityRating: Joi.number().integer().min(1).max(5).default(5),
            serviceRating: Joi.number().integer().min(1).max(5).default(5),
            logisticsRating: Joi.number().integer().min(1).max(5).default(5),
            reviewContent: Joi.string().allow('').max(500),
            isAnonymous: Joi.boolean().default(false),
            images: Joi.array().items(
              Joi.object({
                fileUrl: Joi.string().required(),
                filePath: Joi.string().required(),
                originalName: Joi.string().required(),
                fileSize: Joi.string().allow(''),
                extension: Joi.string().allow('')
              })
            ).max(5).default([])
          })
        ).min(1).required().messages({
          'array.min': '至少需要评价一个商品',
          'any.required': '评价数据不能为空'
        })
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        return this.fail(res, error.details[0].message, 400);
      }

      const userId = req.user.id;
      const { orderId, reviews } = value;

      const results = [];
      const errors = [];

      // 逐个提交评价
      for (let i = 0; i < reviews.length; i++) {
        try {
          const reviewData = {
            orderId,
            userId,
            ...reviews[i]
          };
          const result = await this.orderReviewService.submitReview(reviewData);
          results.push({
            orderItemId: reviews[i].orderItemId,
            success: true,
            reviewId: result.reviewId
          });
        } catch (error) {
          errors.push({
            orderItemId: reviews[i].orderItemId,
            success: false,
            error: error.message
          });
        }
      }

      return this.success(res, {
        successCount: results.length,
        errorCount: errors.length,
        results,
        errors
      }, `批量评价完成，成功${results.length}个，失败${errors.length}个`);
    } catch (error) {
      console.error('批量提交评价失败:', error);
      return this.fail(res, error.message || '批量提交评价失败', 500);
    }
  }

  /**
   * 获取商品评价列表（用于商品详情页）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getProductReviews(req, res) {
    try {
      const { goodsSpuId } = req.params;
      const {
        page = 1,
        pageSize = 10,
        rating,
        hasImages,
        sortBy = 'created_at',
        sortOrder = 'desc'
      } = req.query;

      if (!goodsSpuId) {
        return this.fail(res, '商品ID不能为空', 400);
      }

      // 验证分页参数
      if (page < 1 || pageSize < 1 || pageSize > 50) {
        return this.fail(res, '分页参数无效', 400);
      }

      const options = {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        rating: rating ? parseInt(rating) : null,
        hasImages: hasImages === 'true' ? true : (hasImages === 'false' ? false : null),
        sortBy,
        sortOrder
      };

      const result = await this.orderReviewService.getProductReviews(goodsSpuId, options);
      return this.success(res, result, '获取商品评价列表成功');
    } catch (error) {
      console.error('获取商品评价列表失败:', error);
      return this.fail(res, error.message || '获取商品评价列表失败', 500);
    }
  }

  /**
   * 获取商品评价统计
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getProductReviewStats(req, res) {
    try {
      const { goodsSpuId } = req.params;

      if (!goodsSpuId) {
        return this.fail(res, '商品ID不能为空', 400);
      }

      const result = await this.orderReviewService.getProductReviewStats(goodsSpuId);
      return this.success(res, result, '获取商品评价统计成功');
    } catch (error) {
      console.error('获取商品评价统计失败:', error);
      return this.fail(res, error.message || '获取商品评价统计失败', 500);
    }
  }

  /**
   * 获取用户的评价历史
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getUserReviews(req, res) {
    try {
      const userId = req.user.id;
      const {
        page = 1,
        pageSize = 10,
        sortBy = 'created_at',
        sortOrder = 'desc'
      } = req.query;

      // 验证分页参数
      if (page < 1 || pageSize < 1 || pageSize > 50) {
        return this.fail(res, '分页参数无效', 400);
      }

      const where = {
        user_id: BigInt(userId),
        deleted_at: null
      };

      // 查询总数
      const total = await this.prisma.order_item_reviews.count({ where });

      // 查询评价列表
      const reviews = await this.prisma.order_item_reviews.findMany({
        where,
        include: {
          order_review_images: {
            where: {
              status: 1,
              deleted_at: null
            },
            orderBy: {
              sort_order: 'asc'
            }
          },
          orders: {
            select: {
              id: true
            }
          }
        },
        orderBy: {
          [sortBy]: sortOrder
        },
        skip: (parseInt(page) - 1) * parseInt(pageSize),
        take: parseInt(pageSize)
      });

      // 格式化数据
      const formattedReviews = reviews.map(review => ({
        id: review.id,
        orderId: review.order_id,
        orderSn: review.orders.id.toString(), // 使用订单ID作为订单编号
        productName: review.product_name_snapshot,
        productImage: review.product_image_snapshot,
        skuSpecifications: review.sku_specifications_snapshot,
        qualityRating: review.quality_rating,
        serviceRating: review.service_rating,
        logisticsRating: review.logistics_rating,
        overallRating: review.overall_rating,
        reviewContent: review.review_content,
        isAnonymous: review.is_anonymous,
        images: review.order_review_images.map(img => ({
          id: img.id,
          url: img.image_url,
          originalName: img.original_name
        })),
        createdAt: review.created_at,
        adminReply: review.admin_reply,
        adminReplyAt: review.admin_reply_at
      }));

      const result = {
        total,
        list: formattedReviews,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(total / parseInt(pageSize))
      };

      return this.success(res, result, '获取用户评价历史成功');
    } catch (error) {
      console.error('获取用户评价历史失败:', error);
      return this.fail(res, error.message || '获取用户评价历史失败', 500);
    }
  }
}

module.exports = OrderReviewController;
