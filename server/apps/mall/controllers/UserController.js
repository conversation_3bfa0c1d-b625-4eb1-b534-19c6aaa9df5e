const BaseController = require('../../../core/controllers/BaseController');
const UserService = require('../services/UserService');
const VerificationCodeService = require('../../../apps/master/system/integration/services/VerificationCodeService');
const bcrypt = require('bcryptjs'); // 添加 bcrypt 导入
const redisUtil = require('../../../core/utils/RedisUtil'); // 添加 Redis 工具类导入

/**
 * 商城前端用户控制器
 */
class UserController extends BaseController {
  constructor(prisma) {
    super();
    this.prisma = prisma; // 添加这一行，保存prisma实例
    this.userService = new UserService(prisma);
    this.verificationCodeService = new VerificationCodeService(prisma);
  }

  /**
   * 获取验证码
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getCaptcha(req, res) {
    try {
      const { phone } = req.query;
      
      console.log('收到获取验证码请求，手机号:', phone);
      
      // 验证手机号
      if (!phone) {
        return this.fail(res, '手机号不能为空', 400);
      }
      
      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(phone)) {
        return this.fail(res, '手机号格式不正确', 400);
      }
      
      // 检查Redis连接状态
      const redisUtil = require('../../../core/utils/RedisUtil');
      console.log('Redis连接状态:', redisUtil.isConnected ? '已连接' : '未连接');
      
      // 使用验证码服务生成和发送验证码
      try {
        // 使用验证码服务生成验证码
        console.log('开始生成验证码...');
        const result = await this.verificationCodeService.getCode(phone, 'register');
        console.log('验证码生成成功:', result.code);
        
        // 在生产环境中，不应该返回验证码
        // 这里为了方便开发和测试，返回验证码
        if (process.env.NODE_ENV !== 'production') {
          return this.success(res, { captcha: result.code }, '获取验证码成功');
        } else {
          return this.success(res, {}, '验证码已发送到您的手机');
        }
      } catch (error) {
        console.error('生成或发送验证码失败:', error);
        return this.fail(res, '获取验证码失败: ' + error.message, 500);
      }
    } catch (error) {
      console.error('获取验证码过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 用户注册
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async register(req, res) {
    try {
      // 验证必填字段
      const requiredFields = ['username', 'password', 'phone', 'captcha'];
      if (!this.validateFields(req.body, requiredFields)) {
        return this.fail(res, '用户名、密码、手机号和验证码不能为空', 400);
      }

      const { username, phone, captcha } = req.body;

      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(phone)) {
        return this.fail(res, '手机号格式不正确', 400);
      }

      // 验证验证码
      try {
        console.log('开始验证验证码，手机号:', phone, '验证码:', captcha);
        
        // 检查Redis连接状态
        const redisUtil = require('../../../core/utils/RedisUtil');
        console.log('Redis连接状态:', redisUtil.isConnected ? '已连接' : '未连接');
        
        // 直接从 Redis 中获取验证码进行比较
        const codeKey = `verification_code:register:${phone}`;
        console.log('验证码键:', codeKey);
        
        const savedCode = await redisUtil.getClient().get(codeKey);
        console.log('Redis中存储的验证码:', savedCode || '无');
        
        if (savedCode) {
          console.log('验证码比较:');
          console.log(`- 输入验证码: ${captcha}, 类型: ${typeof captcha}, 长度: ${captcha.length}`);
          console.log(`- 存储验证码: ${savedCode}, 类型: ${typeof savedCode}, 长度: ${savedCode.length}`);
          
          // 直接比较
          const directMatch = captcha === savedCode;
          console.log(`- 直接比较结果: ${directMatch ? '匹配' : '不匹配'}`);
          
          // 规范化后比较
          const normalizedCaptcha = String(captcha).trim();
          const normalizedSavedCode = String(savedCode).trim();
          const normalizedMatch = normalizedCaptcha === normalizedSavedCode;
          
          console.log(`- 规范化后的输入验证码: ${normalizedCaptcha}`);
          console.log(`- 规范化后的存储验证码: ${normalizedSavedCode}`);
          console.log(`- 规范化后比较结果: ${normalizedMatch ? '匹配' : '不匹配'}`);
          
          if (normalizedMatch) {
            console.log('验证码直接比较成功，继续注册流程');
            // 验证成功后删除验证码
            await redisUtil.getClient().del(codeKey);
            console.log(`验证成功，已删除Redis键: ${codeKey}`);
            // 继续注册流程
          } else {
            // 如果直接比较失败，返回错误
            console.log('验证码不匹配，返回错误');
            return this.fail(res, '验证码不正确', 400);
          }
        } else {
          console.log('Redis中未找到验证码');
          
          // 开发环境下的特殊处理
          if (process.env.NODE_ENV !== 'production') {
            // 在开发环境中，如果Redis连接失败，可以使用特定的测试验证码
            if (captcha === '123456' || captcha === '888888') {
              console.log('开发环境下，使用测试验证码通过验证');
              // 继续注册流程
            } else {
              console.log('验证码不存在且不是测试验证码，返回错误');
              return this.fail(res, '验证码已过期或不存在', 400);
            }
          } else {
            // 生产环境下，严格验证
            console.log('生产环境下，验证码不存在，返回错误');
            return this.fail(res, '验证码已过期或不存在', 400);
          }
        }
        
        console.log('验证码验证通过，继续注册流程');
      } catch (error) {
        console.error('验证码验证失败:', error);
        return this.fail(res, '验证码不正确', 400);
      }

      // 检查用户名是否已存在
      const existUser = await this.userService.findByUsername(username);
      console.log('检查用户名是否存在:', username, '结果:', existUser ? '已存在' : '不存在');
      if (existUser) {
        return this.fail(res, '用户名已存在', 400);
      }

      // 检查手机号是否已被注册
      const existPhone = await this.userService.findByPhone(phone);
      console.log('检查手机号是否存在:', phone, '结果:', existPhone ? '已存在' : '不存在');
      if (existPhone) {
        return this.fail(res, '该手机号已被注册', 400);
      }

      // 创建用户
      const now = Date.now();
      const userData = {
        ...req.body,
        created_at: now,
        updated_at: now
      };

      const user = await this.userService.create(userData);
      // 移除密码字段
      delete user.password;
      
      return this.success(res, user, '注册成功');
    } catch (error) {
      console.error('用户注册失败，详细错误:', error);
      
      // 检查是否是唯一约束冲突错误
      if (error.code === 'P2002') {
        const field = error.meta?.target?.[0] || '未知字段';
        return this.fail(res, `字段 ${field} 已存在，请使用其他值`, 400);
      }
      
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 测试手机号查询（仅用于调试）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async testPhoneQuery(req, res) {
    try {
      const { phone } = req.query;
      
      if (!phone) {
        return this.fail(res, '请提供手机号参数', 400);
      }
      
      // 尝试多种方式查询
      const result = {
        phone: phone,
        methods: {}
      };
      
      // 方法1：使用 Prisma 模型
      try {
        const user1 = await this.prisma.masterMallUser.findFirst({
          where: {
            phone: phone,
            deleted_at: null
          }
        });
        result.methods.prismaModel = {
          success: !!user1,
          user: user1 ? {
            id: user1.id,
            username: user1.username,
            phone: user1.phone
          } : null
        };
      } catch (error) {
        result.methods.prismaModel = {
          success: false,
          error: error.message
        };
      }
      
      // 方法2：使用原始 SQL 查询
      try {
        const users2 = await this.prisma.$queryRaw`
          SELECT id, username, phone FROM "base"."mall_user" 
          WHERE "phone" = ${phone} 
          AND "deleted_at" IS NULL
        `;
        result.methods.rawSql = {
          success: users2 && users2.length > 0,
          users: users2
        };
      } catch (error) {
        result.methods.rawSql = {
          success: false,
          error: error.message
        };
      }
      
      // 方法3：使用 OR 条件查询
      try {
        const users3 = await this.prisma.$queryRaw`
          SELECT id, username, phone FROM "base"."mall_user" 
          WHERE ("username" = ${phone} OR "phone" = ${phone})
          AND "deleted_at" IS NULL
        `;
        result.methods.orCondition = {
          success: users3 && users3.length > 0,
          users: users3
        };
      } catch (error) {
        result.methods.orCondition = {
          success: false,
          error: error.message
        };
      }
      
      // 返回所有结果
      return this.success(res, result, '测试完成');
    } catch (error) {
      return this.fail(res, '测试失败: ' + error.message, 500);
    }
  }

  /**
   * 测试登录过程（仅用于调试）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async testLogin(req, res) {
    try {
      const { username, password } = req.body;
      
      if (!username || !password) {
        return this.fail(res, '用户名和密码不能为空', 400);
      }
      
      const result = {
        input: {
          username,
          password: '******' // 不显示实际密码
        },
        steps: {}
      };
      
      // 步骤1：查找用户
      try {
        const users = await this.prisma.$queryRaw`
          SELECT id, username, phone, password FROM "base"."mall_user" 
          WHERE ("username" = ${username} OR "phone" = ${username})
          AND "deleted_at" IS NULL
        `;
        
        console.log('查询结果:', users ? users.length : 0, '条记录');
        
        if (!users || users.length === 0) {
          console.log('未找到用户');
          return this.fail(res, '用户名/手机号或密码错误', 400);
        }
        
        user = users[0];
        console.log('找到用户:', {
          id: user.id,
          username: user.username,
          phone: user.phone,
          status: user.status,
          passwordHash: user.password ? user.password.substring(0, 10) + '...' : null
        });
      } catch (queryError) {
        console.error('查询用户失败:', queryError);
        return this.fail(res, '登录失败，请稍后再试', 500);
      }

      // 验证密码
      console.log('开始验证密码...');
      try {
        const isMatch = await bcrypt.compare(password, user.password);
        
        result.steps.verifyPassword = {
          success: true,
          isMatch: isMatch
        };
        
        // 如果密码匹配，登录成功
        if (isMatch) {
          result.loginSuccess = true;
          result.message = '登录成功';
        } else {
          result.loginSuccess = false;
          result.message = '密码不正确';
        }
      } catch (passwordError) {
        result.steps.verifyPassword = {
          success: false,
          error: passwordError.message
        };
        
        result.loginSuccess = false;
        result.message = '密码验证失败: ' + passwordError.message;
      }
      
      return this.success(res, result, '测试完成');
    } catch (error) {
      return this.fail(res, '测试失败: ' + error.message, 500);
    }
  }

  /**
   * 测试密码验证（仅用于调试）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async testPassword(req, res) {
    try {
      const { phone, password } = req.query;
      
      if (!phone || !password) {
        return this.fail(res, '请提供手机号和密码参数', 400);
      }
      
      console.log('测试密码验证:', { phone, password: '******' });
      
      // 查询用户
      const users = await this.prisma.$queryRaw`
        SELECT id, username, phone, password, status FROM "base"."mall_user" 
        WHERE "phone" = ${phone}
        AND "deleted_at" IS NULL
      `;
      
      if (!users || users.length === 0) {
        return this.fail(res, '未找到该手机号用户', 404);
      }
      
      const user = users[0];
      console.log('找到用户:', {
        id: user.id,
        username: user.username,
        phone: user.phone,
        passwordHash: user.password ? user.password.substring(0, 10) + '...' : null
      });
      
      // 验证密码
      let isMatch = false;
      try {
        isMatch = await bcrypt.compare(password, user.password);
        console.log('密码验证结果:', isMatch ? '密码正确' : '密码错误');
      } catch (error) {
        console.error('密码验证出错:', error);
        return this.fail(res, '密码验证失败: ' + error.message, 500);
      }
      
      // 返回结果
      return this.success(res, {
        user: {
          id: user.id,
          username: user.username,
          phone: user.phone,
          status: user.status
        },
        passwordMatch: isMatch
      }, isMatch ? '密码正确' : '密码错误');
    } catch (error) {
      console.error('测试密码验证失败:', error);
      return this.fail(res, '测试失败: ' + error.message, 500);
    }
  }

  /**
   * 测试 Redis 连接（仅用于调试）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async testRedis(req, res) {
    try {
      console.log('开始测试 Redis 连接...');
      
      const redisUtil = require('../../../core/utils/RedisUtil');
      console.log('Redis连接状态:', redisUtil.isConnected ? '已连接' : '未连接');
      
      // 测试 Redis 连接
      const testResult = await redisUtil.testConnection();
      console.log('Redis连接测试结果:', testResult ? '成功' : '失败');
      
      // 如果 Redis 未连接，尝试重新初始化
      if (!redisUtil.isConnected) {
        console.log('尝试重新初始化 Redis 连接...');
        await redisUtil.init();
        console.log('重新初始化后的连接状态:', redisUtil.isConnected ? '已连接' : '未连接');
      }
      
      // 返回测试结果
      return this.success(res, {
        connected: redisUtil.isConnected,
        testResult: testResult
      }, testResult ? 'Redis 连接正常' : 'Redis 连接异常');
    } catch (error) {
      console.error('测试 Redis 连接失败:', error);
      return this.fail(res, '测试 Redis 连接失败: ' + error.message, 500);
    }
  }

  /**
   * 用户登录
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async login(req, res) {
    console.log('=== 开始处理登录请求 ===');
    try {
      const { username, password } = req.body;
      
      console.log('登录请求参数:', { 
        username, 
        passwordLength: password ? password.length : 0,
        headers: req.headers,
        ip: req.ip || req.headers['x-forwarded-for'] || 'unknown'
      });
      
      // 验证必填字段
      if (!username || !password) {
        console.log('用户名或密码为空');
        return this.fail(res, '用户名和密码不能为空', 400);
      }

      // 直接查询数据库
      console.log('开始查询用户信息...');
      let user = null;
      
      try {
        const users = await this.prisma.$queryRaw`
          SELECT * FROM "base"."mall_user" 
          WHERE ("username" = ${username} OR "phone" = ${username})
          AND "deleted_at" IS NULL
        `;
        
        console.log('查询结果:', users ? users.length : 0, '条记录');
        
        if (!users || users.length === 0) {
          console.log('未找到用户');
          return this.fail(res, '用户名/手机号或密码错误', 400);
        }
        
        user = users[0];
        console.log('找到用户:', {
          id: user.id,
          username: user.username,
          phone: user.phone,
          status: user.status,
          passwordHash: user.password ? user.password.substring(0, 10) + '...' : null
        });
      } catch (queryError) {
        console.error('查询用户失败:', queryError);
        return this.fail(res, '登录失败，请稍后再试', 500);
      }

      // 验证密码
      console.log('开始验证密码...');
      try {
        const isMatch = await bcrypt.compare(password, user.password);
        console.log('密码验证结果:', isMatch ? '密码正确' : '密码错误');
        
        if (!isMatch) {
          return this.fail(res, '用户名/手机号或密码错误', 400);
        }
      } catch (passwordError) {
        console.error('密码验证失败:', passwordError);
        return this.fail(res, '登录失败，请稍后再试', 500);
      }

      // 检查用户状态
      if (user.status !== 1) {
        console.log('用户状态异常:', user.status);
        return this.fail(res, '账号已被禁用', 403);
      }

      // 更新登录信息
      console.log('更新登录信息...');
      const now = Date.now();
      const loginIp = req.ip || req.headers['x-forwarded-for'] || '';
      await this.userService.updateLoginInfo(user.id, {
        last_login_ip: loginIp,
        last_login_time: now,
        login_count: (user.login_count || 0) + 1,
        updated_at: now
      });

      // 生成简单的会话标识
      const sessionId = `${user.id}_${now}_${Math.random().toString(36).substring(2, 15)}`;

      // 移除密码字段
      delete user.password;
      
      return this.success(res, { user, token: sessionId }, '登录成功');
    } catch (error) {
      console.error('登录过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 用户退出登录
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async logout(req, res) {
    console.log('=== 开始处理退出登录请求 ===');
    try {
      // 获取认证令牌
      const authHeader = req.headers.authorization;
      if (!authHeader) {
        console.log('未提供认证令牌');
        return this.fail(res, '未登录状态', 400);
      }
      
      // 从Authorization头中提取令牌
      // 格式为: "Bearer <token>"
      const token = authHeader.split(' ')[1];
      if (!token) {
        console.log('认证令牌格式错误');
        return this.fail(res, '认证令牌格式错误', 400);
      }
      
      // 获取用户信息
      const userId = req.user ? req.user.id : null;
      console.log('退出登录用户ID:', userId);
      
      // 将令牌加入黑名单
      try {
        // 如果是JWT令牌，计算剩余有效期
        let expiresIn = 24 * 60 * 60; // 默认24小时
        
        if (userId) {
          // 如果有用户ID，则删除用户令牌映射
          await redisUtil.removeUserToken(userId, token);
          console.log('已删除用户令牌映射');
        }
        
        // 将令牌加入黑名单
        const result = await redisUtil.addTokenToBlacklist(token, expiresIn);
        console.log('令牌加入黑名单结果:', result ? '成功' : '失败');
        
        return this.success(res, {}, '退出登录成功');
      } catch (error) {
        console.error('令牌处理失败:', error);
        // 即使令牌处理失败，也返回退出成功
        return this.success(res, {}, '退出登录成功');
      }
    } catch (error) {
      console.error('退出登录过程中出错:', error);
      return this.fail(res, '退出登录失败，请稍后再试', 500);
    }
  }

  /**
   * 修改密码
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async changePassword(req, res) {
    console.log('=== 开始处理修改密码请求 ===');
    try {
      // 验证用户是否已登录
      if (!req.user || !req.user.id) {
        console.log('用户未登录');
        return this.fail(res, '请先登录后再操作', 401);
      }

      const userId = req.user.id;
      console.log('修改密码用户ID:', userId);

      // 验证请求参数
      const { oldPassword, newPassword } = req.body;
      if (!oldPassword || !newPassword) {
        console.log('缺少必要参数');
        return this.fail(res, '原密码和新密码不能为空', 400);
      }

      // 验证新密码格式
      if (newPassword.length < 6) {
        console.log('新密码长度不足');
        return this.fail(res, '新密码长度不能少于6位', 400);
      }

      // 查询用户信息
      console.log('查询用户信息...');
      let user = null;
      
      try {
        const users = await this.prisma.$queryRaw`
          SELECT * FROM "base"."mall_user" 
          WHERE "id" = ${userId}
          AND "deleted_at" IS NULL
        `;
        
        if (!users || users.length === 0) {
          console.log('未找到用户');
          return this.fail(res, '用户不存在或已被删除', 400);
        }
        
        user = users[0];
        console.log('找到用户:', {
          id: user.id,
          username: user.username,
          status: user.status
        });
      } catch (queryError) {
        console.error('查询用户失败:', queryError);
        return this.fail(res, '修改密码失败，请稍后再试', 500);
      }

      // 验证原密码
      console.log('验证原密码...');
      try {
        const isMatch = await bcrypt.compare(oldPassword, user.password);
        console.log('原密码验证结果:', isMatch ? '密码正确' : '密码错误');
        
        if (!isMatch) {
          return this.fail(res, '原密码错误', 400);
        }
      } catch (passwordError) {
        console.error('密码验证失败:', passwordError);
        return this.fail(res, '修改密码失败，请稍后再试', 500);
      }

      // 加密新密码
      console.log('加密新密码...');
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(newPassword, salt);

      // 更新密码
      console.log('更新密码...');
      try {
        await this.prisma.$executeRaw`
          UPDATE "base"."mall_user"
          SET "password" = ${hashedPassword}, "updated_at" = ${Date.now()}
          WHERE "id" = ${userId}
        `;
        console.log('密码更新成功');
      } catch (updateError) {
        console.error('更新密码失败:', updateError);
        return this.fail(res, '修改密码失败，请稍后再试', 500);
      }

      // 强制用户重新登录（可选）
      // 获取当前令牌
      const authHeader = req.headers.authorization;
      if (authHeader) {
        const token = authHeader.split(' ')[1];
        if (token) {
          // 将当前令牌加入黑名单
          await redisUtil.addTokenToBlacklist(token, 24 * 60 * 60);
          console.log('已将当前令牌加入黑名单');
        }
      }

      return this.success(res, {}, '密码修改成功，请重新登录');
    } catch (error) {
      console.error('修改密码过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 修改头像
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateAvatar(req, res) {
    console.log('=== 开始处理修改头像请求 ===');
    try {
      // 验证用户是否已登录
      if (!req.user || !req.user.id) {
        console.log('用户未登录');
        return this.fail(res, '请先登录后再操作', 401);
      }

      const userId = req.user.id;
      console.log('修改头像用户ID:', userId);

      // 验证请求参数
      const { avatarUrl } = req.body;
      if (!avatarUrl) {
        console.log('缺少头像URL参数');
        return this.fail(res, '头像URL不能为空', 400);
      }

      // 更新用户头像
      console.log('更新用户头像...');
      try {
        await this.prisma.$executeRaw`
          UPDATE "base"."mall_user"
          SET "avatar" = ${avatarUrl}, "updated_at" = ${Date.now()}
          WHERE "id" = ${userId}
        `;
        console.log('头像更新成功');
      } catch (updateError) {
        console.error('更新头像失败:', updateError);
        return this.fail(res, '修改头像失败，请稍后再试', 500);
      }

      // 查询更新后的用户信息
      console.log('查询更新后的用户信息...');
      let user = null;
      try {
        const users = await this.prisma.$queryRaw`
          SELECT id, username, nickname, phone, email, avatar, status, last_login_time, last_login_ip, login_count, created_at, updated_at
          FROM "base"."mall_user" 
          WHERE "id" = ${userId}
          AND "deleted_at" IS NULL
        `;
        
        if (!users || users.length === 0) {
          console.log('未找到用户');
          return this.fail(res, '用户不存在或已被删除', 400);
        }
        
        user = users[0];
      } catch (queryError) {
        console.error('查询用户失败:', queryError);
        // 即使查询失败，也返回成功，因为头像已经更新
        return this.success(res, {}, '头像修改成功');
      }

      return this.success(res, { user }, '头像修改成功');
    } catch (error) {
      console.error('修改头像过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 上传头像
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async uploadAvatar(req, res) {
    console.log('=== 开始处理上传头像请求 ===');
    try {
      // 验证用户是否已登录
      if (!req.user || !req.user.id) {
        console.log('用户未登录');
        return this.fail(res, '请先登录后再操作', 401);
      }

      const userId = req.user.id;
      console.log('上传头像用户ID:', userId);

      // 验证请求中是否包含文件
      if (!req.file) {
        console.log('未检测到上传文件');
        return this.fail(res, '未检测到上传文件', 400);
      }

      const file = req.file;
      console.log('上传的文件:', {
        originalname: file.originalname,
        mimetype: file.mimetype,
        size: file.size
      });

      // 验证文件类型
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.mimetype)) {
        console.log('文件类型不支持:', file.mimetype);
        return this.fail(res, '只支持JPG、PNG、GIF和WEBP格式的图片', 400);
      }

      // 验证文件大小（限制为2MB）
      const maxSize = 2 * 1024 * 1024; // 2MB
      if (file.size > maxSize) {
        console.log('文件大小超过限制:', file.size);
        return this.fail(res, '图片大小不能超过2MB', 400);
      }

      // 使用系统上传服务上传文件
      const UploadController = require('../../../master/system/integration/upload/controllers/UploadController');
      const uploadController = new UploadController(this.prisma);
      
      // 修改请求对象，添加必要的参数
      req.body.dir = 'avatars';
      req.body.module = 'mall';
      req.body.bizType = 'user_avatar';
      req.body.bizId = userId;
      req.body.isPublic = 1;
      
      // 调用上传控制器的上传方法
      // 由于我们不能直接获取上传控制器的返回值，所以需要拦截响应
      const originalJson = res.json;
      let uploadResult = null;
      
      res.json = function(data) {
        uploadResult = data;
        // 恢复原始json方法
        res.json = originalJson;
        return res;
      };
      
      await uploadController.uploadFile(req, res);
      
      // 检查上传结果
      if (!uploadResult || !uploadResult.success) {
        console.log('上传失败:', uploadResult ? uploadResult.message : '未知错误');
        return this.fail(res, uploadResult ? uploadResult.message : '上传头像失败', 500);
      }
      
      const fileUrl = uploadResult.data.fileUrl;
      console.log('文件上传成功，URL:', fileUrl);
      
      // 更新用户头像
      console.log('更新用户头像...');
      try {
        await this.prisma.$executeRaw`
          UPDATE "base"."mall_user"
          SET "avatar" = ${fileUrl}, "updated_at" = ${Date.now()}
          WHERE "id" = ${userId}
        `;
        console.log('头像更新成功');
      } catch (updateError) {
        console.error('更新头像失败:', updateError);
        return this.fail(res, '修改头像失败，请稍后再试', 500);
      }

      // 查询更新后的用户信息
      console.log('查询更新后的用户信息...');
      let user = null;
      try {
        const users = await this.prisma.$queryRaw`
          SELECT id, username, nickname, phone, email, avatar, status, last_login_time, last_login_ip, login_count, created_at, updated_at
          FROM "base"."mall_user" 
          WHERE "id" = ${userId}
          AND "deleted_at" IS NULL
        `;
        
        if (!users || users.length === 0) {
          console.log('未找到用户');
          return this.fail(res, '用户不存在或已被删除', 400);
        }
        
        user = users[0];
      } catch (queryError) {
        console.error('查询用户失败:', queryError);
        // 即使查询失败，也返回成功，因为头像已经更新
        return this.success(res, { fileUrl }, '头像上传成功');
      }

      return this.success(res, { user, fileUrl }, '头像上传成功');
    } catch (error) {
      console.error('上传头像过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 修改昵称
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateNickname(req, res) {
    console.log('=== 开始处理修改昵称请求 ===');
    try {
      // 验证用户是否已登录
      if (!req.user || !req.user.id) {
        console.log('用户未登录');
        return this.fail(res, '请先登录后再操作', 401);
      }

      const userId = req.user.id;
      console.log('修改昵称用户ID:', userId);

      // 验证请求参数
      const { nickname } = req.body;
      if (!nickname) {
        console.log('缺少昵称参数');
        return this.fail(res, '昵称不能为空', 400);
      }

      // 验证昵称长度
      if (nickname.length > 20) {
        console.log('昵称长度超过限制');
        return this.fail(res, '昵称长度不能超过20个字符', 400);
      }

      // 更新用户昵称
      console.log('更新用户昵称...');
      try {
        await this.prisma.$executeRaw`
          UPDATE "base"."mall_user"
          SET "nickname" = ${nickname}, "updated_at" = ${Date.now()}
          WHERE "id" = ${userId}
        `;
        console.log('昵称更新成功');
      } catch (updateError) {
        console.error('更新昵称失败:', updateError);
        return this.fail(res, '修改昵称失败，请稍后再试', 500);
      }

      // 查询更新后的用户信息
      console.log('查询更新后的用户信息...');
      let user = null;
      try {
        const users = await this.prisma.$queryRaw`
          SELECT id, username, nickname, phone, email, avatar, status, last_login_time, last_login_ip, login_count, created_at, updated_at
          FROM "base"."mall_user" 
          WHERE "id" = ${userId}
          AND "deleted_at" IS NULL
        `;
        
        if (!users || users.length === 0) {
          console.log('未找到用户');
          return this.fail(res, '用户不存在或已被删除', 400);
        }
        
        user = users[0];
      } catch (queryError) {
        console.error('查询用户失败:', queryError);
        // 即使查询失败，也返回成功，因为昵称已经更新
        return this.success(res, {}, '昵称修改成功');
      }

      return this.success(res, { user }, '昵称修改成功');
    } catch (error) {
      console.error('修改昵称过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 发送手机号修改验证码
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async sendPhoneChangeCaptcha(req, res) {
    console.log('=== 开始处理发送手机号修改验证码请求 ===');
    try {
      // 验证用户是否已登录
      if (!req.user || !req.user.id) {
        console.log('用户未登录');
        return this.fail(res, '请先登录后再操作', 401);
      }

      const userId = req.user.id;
      console.log('发送验证码用户ID:', userId);

      // 验证请求参数
      const { newPhone } = req.query;
      if (!newPhone) {
        console.log('缺少新手机号参数');
        return this.fail(res, '新手机号不能为空', 400);
      }

      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(newPhone)) {
        console.log('手机号格式不正确:', newPhone);
        return this.fail(res, '手机号格式不正确', 400);
      }

      // 检查新手机号是否已被使用
      try {
        const existingUsers = await this.prisma.$queryRaw`
          SELECT id FROM "base"."mall_user" 
          WHERE "phone" = ${newPhone}
          AND "deleted_at" IS NULL
          AND "id" != ${userId}
        `;
        
        if (existingUsers && existingUsers.length > 0) {
          console.log('手机号已被使用:', newPhone);
          return this.fail(res, '该手机号已被其他账号使用', 400);
        }
      } catch (queryError) {
        console.error('查询手机号是否存在失败:', queryError);
        return this.fail(res, '验证手机号失败，请稍后再试', 500);
      }

      // 检查Redis连接状态
      const redisUtil = require('../../../core/utils/RedisUtil');
      console.log('Redis连接状态:', redisUtil.isConnected ? '已连接' : '未连接');
      
      // 使用验证码服务生成和发送验证码
      try {
        // 使用验证码服务生成验证码
        console.log('开始生成验证码...');
        const result = await this.verificationCodeService.getCode(newPhone, 'change_phone');
        console.log('验证码生成成功:', result.code);
        
        // 在生产环境中，不应该返回验证码
        // 这里为了方便开发和测试，返回验证码
        if (process.env.NODE_ENV !== 'production') {
          return this.success(res, { captcha: result.code }, '获取验证码成功');
        } else {
          return this.success(res, {}, '验证码已发送到您的手机');
        }
      } catch (error) {
        console.error('生成或发送验证码失败:', error);
        return this.fail(res, '获取验证码失败: ' + error.message, 500);
      }
    } catch (error) {
      console.error('发送手机号修改验证码过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 修改手机号
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updatePhone(req, res) {
    console.log('=== 开始处理修改手机号请求 ===');
    try {
      // 验证用户是否已登录
      if (!req.user || !req.user.id) {
        console.log('用户未登录');
        return this.fail(res, '请先登录后再操作', 401);
      }

      const userId = req.user.id;
      console.log('修改手机号用户ID:', userId);

      // 验证请求参数
      const { newPhone, captcha } = req.body;
      if (!newPhone || !captcha) {
        console.log('缺少必要参数');
        return this.fail(res, '新手机号和验证码不能为空', 400);
      }

      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(newPhone)) {
        console.log('手机号格式不正确:', newPhone);
        return this.fail(res, '手机号格式不正确', 400);
      }

      // 检查新手机号是否已被使用
      try {
        const existingUsers = await this.prisma.$queryRaw`
          SELECT id FROM "base"."mall_user" 
          WHERE "phone" = ${newPhone}
          AND "deleted_at" IS NULL
          AND "id" != ${userId}
        `;
        
        if (existingUsers && existingUsers.length > 0) {
          console.log('手机号已被使用:', newPhone);
          return this.fail(res, '该手机号已被其他账号使用', 400);
        }
      } catch (queryError) {
        console.error('查询手机号是否存在失败:', queryError);
        return this.fail(res, '验证手机号失败，请稍后再试', 500);
      }

      // 验证验证码
      try {
        console.log('开始验证验证码，手机号:', newPhone, '验证码:', captcha);
        
        // 检查Redis连接状态
        const redisUtil = require('../../../core/utils/RedisUtil');
        console.log('Redis连接状态:', redisUtil.isConnected ? '已连接' : '未连接');
        
        // 直接从 Redis 中获取验证码进行比较
        const codeKey = `verification_code:change_phone:${newPhone}`;
        console.log('验证码键:', codeKey);
        
        const savedCode = await redisUtil.getClient().get(codeKey);
        console.log('Redis中存储的验证码:', savedCode || '无');
        
        if (!savedCode) {
          console.log('验证码不存在或已过期');
          return this.fail(res, '验证码不存在或已过期，请重新获取', 400);
        }
        
        if (savedCode !== captcha) {
          console.log('验证码不匹配');
          return this.fail(res, '验证码错误', 400);
        }
        
        console.log('验证码验证成功');
      } catch (error) {
        console.error('验证验证码失败:', error);
        return this.fail(res, '验证验证码失败: ' + error.message, 500);
      }

      // 更新用户手机号
      console.log('更新用户手机号...');
      try {
        await this.prisma.$executeRaw`
          UPDATE "base"."mall_user"
          SET "phone" = ${newPhone}, "updated_at" = ${Date.now()}
          WHERE "id" = ${userId}
        `;
        console.log('手机号更新成功');
      } catch (updateError) {
        console.error('更新手机号失败:', updateError);
        return this.fail(res, '修改手机号失败，请稍后再试', 500);
      }

      // 查询更新后的用户信息
      console.log('查询更新后的用户信息...');
      let user = null;
      try {
        const users = await this.prisma.$queryRaw`
          SELECT id, username, nickname, phone, email, avatar, status, last_login_time, last_login_ip, login_count, created_at, updated_at
          FROM "base"."mall_user" 
          WHERE "id" = ${userId}
          AND "deleted_at" IS NULL
        `;
        
        if (!users || users.length === 0) {
          console.log('未找到用户');
          return this.fail(res, '用户不存在或已被删除', 400);
        }
        
        user = users[0];
      } catch (queryError) {
        console.error('查询用户失败:', queryError);
        // 即使查询失败，也返回成功，因为手机号已经更新
        return this.success(res, {}, '手机号修改成功');
      }

      return this.success(res, { user }, '手机号修改成功');
    } catch (error) {
      console.error('修改手机号过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 获取用户余额
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getBalance(req, res) {
    console.log('=== 开始处理获取用户余额请求 ===');
    try {
      // 验证用户是否已登录
      if (!req.user || !req.user.id) {
        console.log('用户未登录');
        return this.fail(res, '请先登录后再操作', 401);
      }

      const userId = req.user.id;
      console.log('获取余额用户ID:', userId);

      // 查询用户余额信息
      console.log('查询用户余额信息...');
      let balance = 0;

      try {
        // 从用户表中查询余额字段，如果没有余额字段则返回0
        const users = await this.prisma.$queryRaw`
          SELECT id, username, balance
          FROM "base"."mall_user"
          WHERE "id" = ${userId}
          AND "deleted_at" IS NULL
        `;

        if (!users || users.length === 0) {
          console.log('未找到用户');
          return this.fail(res, '用户不存在或已被删除', 404);
        }

        const user = users[0];
        // 如果用户表中有余额字段，使用该值，否则默认为0
        balance = user.balance ? parseFloat(user.balance) : 0;

        console.log('用户余额:', balance);
      } catch (queryError) {
        console.error('查询用户余额失败:', queryError);
        // 如果查询失败，可能是因为余额字段不存在，返回默认值0
        balance = 0;
        console.log('查询余额失败，使用默认值0');
      }

      return this.success(res, { balance }, '获取用户余额成功');
    } catch (error) {
      console.error('获取用户余额过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }
}

module.exports = UserController;
