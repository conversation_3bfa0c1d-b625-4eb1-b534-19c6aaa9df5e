const BaseController = require('../../../../../core/controllers/BaseController');
const ChannelManagementService = require('../services/ChannelManagementService');
const ChannelManagementDto = require('../dto/ChannelManagementDto');
const { prisma } = require('../../../../../core/database/prisma');

/**
 * 渠道管理控制器
 * 处理渠道管理相关HTTP请求
 */
class ChannelManagementController extends BaseController {
  /**
   * 构造函数
   */
  constructor() {
    super(prisma);
  }
  /**
   * 获取渠道列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getChannelList(req, res) {
    try {
      // 验证查询参数
      const { error, value } = ChannelManagementDto.validateQuery(req.query);
      if (error) {
        return this.fail(res, '参数验证失败', { errors: error.details.map(detail => detail.message) }, 400);
      }

      // 调用服务获取渠道列表
      const result = await ChannelManagementService.getChannelList(value);
      
      // 使用successList方法返回带有success字段的响应格式
      return this.successList(
        res,
        result.data.list,
        result.data.pagination.total,
        result.data.pagination.page,
        result.data.pagination.pageSize,
        '获取渠道列表成功',
        200
      );
    } catch (error) {
      console.error('获取渠道列表失败:', error);
      return this.fail(res, '服务器内部错误', { error: error.message }, 500);
    }
  }

  /**
   * 获取渠道详情
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getChannelById(req, res) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return this.fail(res, '渠道ID不能为空', null, 400);
      }

      // 调用服务获取渠道详情
      const result = await ChannelManagementService.getChannelById(id);
      
      // 使用基础控制器的方法处理响应，避免 BigInt 序列化问题
      return this.success(res, result.data, '获取渠道详情成功', 200);
    } catch (error) {
      console.error('获取渠道详情失败:', error);
      return this.fail(res, '服务器内部错误', { error: error.message }, 500);
    }
  }

  /**
   * 创建渠道
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async createChannel(req, res) {
    try {
      // 验证请求体
      const { error, value } = ChannelManagementDto.validateCreate(req.body);
      if (error) {
        return this.fail(res, '参数验证失败', { errors: error.details.map(detail => detail.message) }, 400);
      }

      // 获取用户ID
      const userId = req.user?.id || null;

      // 调用服务创建渠道
      const result = await ChannelManagementService.createChannel(value, userId);
      
      // 使用基础控制器的方法处理响应，避免 BigInt 序列化问题
      return this.success(res, result.data, '创建渠道成功', 200);
    } catch (error) {
      console.error('创建渠道失败:', error);
      return this.fail(res, '服务器内部错误', { error: error.message }, 500);
    }
  }

  /**
   * 更新渠道
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateChannel(req, res) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return this.fail(res, '渠道ID不能为空', null, 400);
      }

      // 验证请求体
      const { error, value } = ChannelManagementDto.validateUpdate(req.body);
      if (error) {
        return this.fail(res, '参数验证失败', { errors: error.details.map(detail => detail.message) }, 400);
      }

      // 获取用户ID
      const userId = req.user?.id || null;

      // 调用服务更新渠道
      const result = await ChannelManagementService.updateChannel(id, value, userId);
      
      // 使用基础控制器的方法处理响应，避免 BigInt 序列化问题
      return this.success(res, result.data, '更新渠道成功', 200);
    } catch (error) {
      console.error('更新渠道失败:', error);
      return this.fail(res, '服务器内部错误', { error: error.message }, 500);
    }
  }

  /**
   * 删除渠道
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async deleteChannel(req, res) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return this.fail(res, '渠道ID不能为空', null, 400);
      }

      // 获取用户ID
      const userId = req.user?.id || null;

      // 调用服务删除渠道
      const result = await ChannelManagementService.deleteChannel(id, userId);
      
      // 检查服务层返回的状态码
      if (result.code !== 200) {
        return this.fail(res, result.message, null, result.code);
      }
      
      // 使用基础控制器的方法处理响应，避免 BigInt 序列化问题
      return this.success(res, result.data, '删除渠道成功', 200);
    } catch (error) {
      console.error('删除渠道失败:', error);
      return this.fail(res, '服务器内部错误', { error: error.message }, 500);
    }
  }
}

module.exports = ChannelManagementController;
