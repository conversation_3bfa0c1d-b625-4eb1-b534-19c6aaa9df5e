const ChannelModel = require('../models/ChannelModel');
const PlatformModel = require('../../platform/models/PlatformModel');

/**
 * 渠道管理服务
 * 处理渠道管理相关业务逻辑
 */
class ChannelService {
  /**
   * 获取渠道列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 渠道列表及分页信息
   */
  async getChannelList(params) {
    try {
      const result = await ChannelModel.getChannelList(params);
      
      // 转换字段名为驼峰命名
      const list = result.list.map(item => ({
        id: item.id,
        name: item.name,
        iconUrl: item.icon_url,
        isBuiltIn: item.is_built_in,
        createdAt: item.created_at,
        updatedAt: item.updated_at
      }));

      return {
        code: 200,
        message: '获取渠道列表成功',
        data: {
          list,
          pagination: result.pagination
        }
      };
    } catch (error) {
      console.error('获取渠道列表失败:', error);
      return {
        code: 500,
        message: '获取渠道列表失败',
        error: error.message
      };
    }
  }

  /**
   * 获取渠道详情
   * @param {string} id - 渠道ID
   * @returns {Promise<Object>} - 渠道详情
   */
  async getChannelById(id) {
    try {
      const channel = await ChannelModel.getChannelById(id);
      
      if (!channel) {
        return {
          code: 404,
          message: '渠道不存在'
        };
      }

      // 转换字段名为驼峰命名
      const data = {
        id: channel.id,
        name: channel.name,
        iconUrl: channel.icon_url,
        isBuiltIn: channel.is_built_in,
        createdAt: channel.created_at,
        updatedAt: channel.updated_at
      };

      return {
        code: 200,
        message: '获取渠道详情成功',
        data
      };
    } catch (error) {
      console.error('获取渠道详情失败:', error);
      return {
        code: 500,
        message: '获取渠道详情失败',
        error: error.message
      };
    }
  }

  /**
   * 创建渠道
   * @param {Object} data - 渠道数据
   * @param {BigInt} userId - 创建者ID
   * @returns {Promise<Object>} - 创建结果
   */
  async createChannel(data, userId) {
    try {
      // 检查渠道名称是否已存在
      const nameExists = await ChannelModel.checkNameExists(data.name);
      if (nameExists) {
        return {
          code: 400,
          message: '渠道名称已存在'
        };
      }

      const channel = await ChannelModel.createChannel(data, userId);
      
      // 转换字段名为驼峰命名
      const responseData = {
        id: channel.id,
        name: channel.name,
        iconUrl: channel.icon_url,
        isBuiltIn: channel.is_built_in,
        createdAt: channel.created_at
      };

      return {
        code: 201,
        message: '创建渠道成功',
        data: responseData
      };
    } catch (error) {
      console.error('创建渠道失败:', error);
      return {
        code: 500,
        message: '创建渠道失败',
        error: error.message
      };
    }
  }

  /**
   * 更新渠道
   * @param {string} id - 渠道ID
   * @param {Object} data - 更新数据
   * @param {BigInt} userId - 更新者ID
   * @returns {Promise<Object>} - 更新结果
   */
  async updateChannel(id, data, userId) {
    try {
      // 检查渠道是否存在
      const channel = await ChannelModel.getChannelById(id);
      if (!channel) {
        return {
          code: 404,
          message: '渠道不存在'
        };
      }

      // 如果更新名称，检查名称是否已存在
      if (data.name && data.name !== channel.name) {
        const nameExists = await ChannelModel.checkNameExists(data.name, id);
        if (nameExists) {
          return {
            code: 400,
            message: '渠道名称已存在'
          };
        }
      }

      const updatedChannel = await ChannelModel.updateChannel(id, data, userId);
      
      // 转换字段名为驼峰命名
      const responseData = {
        id: updatedChannel.id,
        name: updatedChannel.name,
        iconUrl: updatedChannel.icon_url,
        isBuiltIn: updatedChannel.is_built_in,
        updatedAt: updatedChannel.updated_at
      };

      return {
        code: 200,
        message: '更新渠道成功',
        data: responseData
      };
    } catch (error) {
      console.error('更新渠道失败:', error);
      return {
        code: 500,
        message: '更新渠道失败',
        error: error.message
      };
    }
  }

  /**
   * 删除渠道
   * @param {string} id - 渠道ID
   * @param {BigInt} userId - 删除者ID
   * @returns {Promise<Object>} - 删除结果
   */
  async deleteChannel(id, userId) {
    try {
      // 检查渠道是否存在
      const channel = await ChannelModel.getChannelById(id);
      if (!channel) {
        return {
          code: 404,
          message: '渠道不存在'
        };
      }

      // 检查是否为内置渠道
      if (channel.is_built_in === 1) {
        return {
          code: 403,
          message: '内置渠道不允许删除'
        };
      }
      
      // 检查渠道是否被平台使用
      const isInUse = await PlatformModel.checkChannelInUse(id);
      if (isInUse) {
        // 获取使用该渠道的平台列表
        const platforms = await PlatformModel.getPlatformsByChannelId(id);
        const platformNames = platforms.map(p => p.name).join('、');
        
        return {
          code: 403,
          message: `该渠道已被以下平台使用，不允许删除：${platformNames}`,
          data: {
            platforms
          }
        };
      }

      await ChannelModel.deleteChannel(id, userId);

      return {
        code: 200,
        message: '删除渠道成功'
      };
    } catch (error) {
      console.error('删除渠道失败:', error);
      return {
        code: 500,
        message: '删除渠道失败',
        error: error.message
      };
    }
  }
}

module.exports = new ChannelService();
