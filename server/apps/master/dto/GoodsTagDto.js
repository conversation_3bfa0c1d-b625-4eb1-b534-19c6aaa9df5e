const { z } = require('zod');

/**
 * 商品标签DTO验证模式
 */
const GoodsTagDto = z.object({
  // 标签名称
  name: z.string()
    .min(1, { message: '标签名称不能为空' })
    .max(100, { message: '标签名称不能超过100个字符' }),
  
  // 标签图片
  image_url: z.string()
    .optional()
    .nullable(),
  
  // 标签说明
  description: z.string()
    .optional()
    .nullable(),
  
  // 排序
  sort_order: z.number()
    .int()
    .default(0),
  
  // 是否启用
  is_enabled: z.boolean()
    .default(true)
});

/**
 * 商品标签更新DTO验证模式
 */
const GoodsTagUpdateDto = GoodsTagDto.partial();

module.exports = {
  GoodsTagDto,
  GoodsTagUpdateDto
};
