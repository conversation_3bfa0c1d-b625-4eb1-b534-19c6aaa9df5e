const express = require('express');
const MallTagGroupController = require('../controllers/MallTagGroupController');
const authMiddleware = require('../../../../../core/middleware/AuthMiddleware');
const { prisma } = require('../../../../../core/database/prisma');

// 创建控制器实例 - 使用单例模式获取prisma客户端
// 获取base schema的prisma客户端
const router = express.Router();
const tagGroupController = new MallTagGroupController(prisma);

/**
 * @swagger
 * /api/v1/master/mall/user-tag-group:
 *   post:
 *     summary: 创建商城会员标签组
 *     tags: [商城会员标签组]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tag_group_name
 *             properties:
 *               tag_group_name:
 *                 type: string
 *                 description: 标签组名称
 *               tag_group_sort:
 *                 type: integer
 *                 description: 排序值，数字越小越靠前
 *                 default: 0
 *               tag_group_buildin:
 *                 type: integer
 *                 description: 是否系统内置：1-是，0-否
 *                 default: 0
 *               tag_group_enable:
 *                 type: integer
 *                 description: 是否启用：1-启用，0-禁用
 *                 default: 1
 *               subsite_id:
 *                 type: string
 *                 description: 子站点ID，为空表示适用于所有子站点
 *               status:
 *                 type: integer
 *                 description: 状态：1-正常，0-禁用
 *                 default: 1
 *               remark:
 *                 type: string
 *                 description: 备注信息
 *     responses:
 *       200:
 *         description: 创建成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.post('/', authMiddleware, tagGroupController.create.bind(tagGroupController));

/**
 * @swagger
 * /api/v1/master/mall/user-tag-group/{id}:
 *   put:
 *     summary: 更新商城会员标签组
 *     tags: [商城会员标签组]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 标签组ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               tag_group_name:
 *                 type: string
 *                 description: 标签组名称
 *               tag_group_sort:
 *                 type: integer
 *                 description: 排序值，数字越小越靠前
 *               tag_group_buildin:
 *                 type: integer
 *                 description: 是否系统内置：1-是，0-否
 *               tag_group_enable:
 *                 type: integer
 *                 description: 是否启用：1-启用，0-禁用
 *               subsite_id:
 *                 type: string
 *                 description: 子站点ID，为空表示适用于所有子站点
 *               status:
 *                 type: integer
 *                 description: 状态：1-正常，0-禁用
 *               remark:
 *                 type: string
 *                 description: 备注信息
 *     responses:
 *       200:
 *         description: 更新成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.put('/:id', authMiddleware, tagGroupController.update.bind(tagGroupController));

/**
 * @swagger
 * /api/v1/master/mall/user-tag-group/{id}:
 *   delete:
 *     summary: 删除商城会员标签组
 *     tags: [商城会员标签组]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 标签组ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.delete('/:id', authMiddleware, tagGroupController.delete.bind(tagGroupController));

/**
 * @swagger
 * /api/v1/master/mall/user-tag-group/{id}:
 *   get:
 *     summary: 获取商城会员标签组详情
 *     tags: [商城会员标签组]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 标签组ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.get('/:id', authMiddleware, tagGroupController.getDetail.bind(tagGroupController));

/**
 * @swagger
 * /api/v1/master/mall/user-tag-group:
 *   get:
 *     summary: 获取商城会员标签组列表
 *     tags: [商城会员标签组]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: id
 *         schema:
 *           type: string
 *         description: 标签组ID
 *       - in: query
 *         name: tag_group_name
 *         schema:
 *           type: string
 *         description: 标签组名称
 *       - in: query
 *         name: tag_group_buildin
 *         schema:
 *           type: integer
 *         description: 是否系统内置：1-是，0-否
 *       - in: query
 *         name: tag_group_enable
 *         schema:
 *           type: integer
 *         description: 是否启用：1-启用，0-禁用
 *       - in: query
 *         name: subsite_id
 *         schema:
 *           type: string
 *         description: 子站点ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *         description: 状态：1-正常，0-禁用
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 关键词搜索
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *         default: 1
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *         description: 每页条数
 *         default: 10
 *       - in: query
 *         name: sortField
 *         schema:
 *           type: string
 *         description: 排序字段
 *         default: tag_group_sort
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *         description: 排序方向
 *         default: asc
 *     responses:
 *       200:
 *         description: 获取成功
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
router.get('/', authMiddleware, tagGroupController.getList.bind(tagGroupController));

/**
 * @swagger
 * /api/v1/master/mall/user-tag-group/options/enabled:
 *   get:
 *     summary: 获取所有启用的标签组（用于下拉选择）
 *     tags: [商城会员标签组]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: subsite_id
 *         schema:
 *           type: string
 *         description: 子站点ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       500:
 *         description: 服务器错误
 */
router.get('/options/enabled', authMiddleware, tagGroupController.getAllEnabled.bind(tagGroupController));

module.exports = router;
