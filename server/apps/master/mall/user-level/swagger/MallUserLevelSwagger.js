/**
 * 商城会员等级管理 Swagger 模型定义
 */

module.exports = {
  schemas: {
    // 商城会员等级基础模型
    MallUserLevel: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          description: '等级ID，16位雪花算法'
        },
        level_name: {
          type: 'string',
          description: '等级名称，唯一'
        },
        experience_value: {
          type: 'string',
          description: '升级所需经验值'
        },
        total_consumption: {
          type: 'number',
          format: 'float',
          description: '升级所需累计消费金额（元）'
        },
        level_icon: {
          type: 'string',
          description: '等级图标URL地址'
        },
        discount_rate: {
          type: 'number',
          format: 'float',
          description: '折扣率（百分比），例如：95表示95折'
        },
        is_system: {
          type: 'integer',
          enum: [0, 1],
          description: '是否系统内置：1-是，0-否'
        },
        status: {
          type: 'integer',
          enum: [0, 1],
          description: '状态：1-启用，0-禁用'
        },
        created_at: {
          type: 'string',
          description: '创建时间戳（毫秒）'
        },
        updated_at: {
          type: 'string',
          description: '更新时间戳（毫秒）'
        },
        remark: {
          type: 'string',
          description: '备注信息'
        }
      }
    },
    
    // 创建商城会员等级请求
    MallUserLevelCreateRequest: {
      type: 'object',
      required: ['levelName'],
      properties: {
        levelName: {
          type: 'string',
          description: '等级名称，必填，唯一'
        },
        experienceValue: {
          type: 'integer',
          description: '升级所需经验值',
          default: 0
        },
        totalConsumption: {
          type: 'number',
          format: 'float',
          description: '升级所需累计消费金额（元）',
          default: 0
        },
        levelIcon: {
          type: 'string',
          description: '等级图标URL地址'
        },
        discountRate: {
          type: 'number',
          format: 'float',
          description: '折扣率（百分比），例如：95表示95折',
          default: 100
        },
        isSystem: {
          type: 'integer',
          enum: [0, 1],
          description: '是否系统内置：1-是，0-否',
          default: 0
        },
        status: {
          type: 'integer',
          enum: [0, 1],
          description: '状态：1-启用，0-禁用',
          default: 1
        },
        remark: {
          type: 'string',
          description: '备注信息'
        }
      }
    },
    
    // 更新商城会员等级请求
    MallUserLevelUpdateRequest: {
      type: 'object',
      properties: {
        levelName: {
          type: 'string',
          description: '等级名称，唯一'
        },
        experienceValue: {
          type: 'integer',
          description: '升级所需经验值'
        },
        totalConsumption: {
          type: 'number',
          format: 'float',
          description: '升级所需累计消费金额（元）'
        },
        levelIcon: {
          type: 'string',
          description: '等级图标URL地址'
        },
        discountRate: {
          type: 'number',
          format: 'float',
          description: '折扣率（百分比），例如：95表示95折'
        },
        isSystem: {
          type: 'integer',
          enum: [0, 1],
          description: '是否系统内置：1-是，0-否'
        },
        status: {
          type: 'integer',
          enum: [0, 1],
          description: '状态：1-启用，0-禁用'
        },
        remark: {
          type: 'string',
          description: '备注信息'
        }
      }
    }
  },
  
  responses: {
    // 商城会员等级响应
    MallUserLevelResponse: {
      type: 'object',
      properties: {
        code: {
          type: 'integer',
          example: 200
        },
        message: {
          type: 'string',
          example: '操作成功'
        },
        data: {
          $ref: '#/components/schemas/MallUserLevel'
        }
      }
    },
    
    // 商城会员等级列表响应
    MallUserLevelListResponse: {
      type: 'object',
      properties: {
        code: {
          type: 'integer',
          example: 200
        },
        message: {
          type: 'string',
          example: '获取商城会员等级列表成功'
        },
        data: {
          type: 'object',
          properties: {
            list: {
              type: 'array',
              items: {
                $ref: '#/components/schemas/MallUserLevel'
              }
            },
            total: {
              type: 'integer',
              description: '总记录数'
            },
            page: {
              type: 'integer',
              description: '当前页码'
            },
            pageSize: {
              type: 'integer',
              description: '每页记录数'
            }
          }
        }
      }
    }
  }
};
