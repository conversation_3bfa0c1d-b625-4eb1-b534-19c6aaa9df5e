const bcrypt = require('bcryptjs');
const { generateSnowflakeId } = require('../../../../../shared/utils/snowflake');
const prismaManager = require('../../../../../core/prisma');

/**
 * 商城用户服务类
 * 处理商城用户相关的业务逻辑
 */
class MallUserService {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    // 使用传入的prisma客户端，如果没有则使用单例模式获取
    this.prisma = prisma || prismaManager.getClient('base');
    
    // 调试信息：检查 prisma 客户端是否正确初始化
    console.log('Prisma 客户端初始化状态:', !!this.prisma);
    console.log('Prisma 客户端可用模型:', Object.keys(this.prisma).filter(key => !key.startsWith('$')));
  }

  /**
   * 获取商城用户列表
   * @param {Object} params 查询参数，包含分页和筛选条件
   * @returns {Object} 包含用户列表、总数和分页信息的对象
   */
  async list(params = {}) {
    let { page = 1, pageSize = 10, ...filters } = params;
    page = parseInt(page, 10) || 1;
    pageSize = parseInt(pageSize, 10) || 10;
    const skip = (page - 1) * pageSize;

    try {
      // 确保 prisma 实例存在
      if (!this.prisma) {
        throw new Error('数据库连接未初始化');
      }

      // 确保 MasterMallUser 模型存在
      if (!this.prisma.MasterMallUser) {
        throw new Error('商城用户模型不存在，请检查 Prisma 模型定义');
      }

      // 构建查询条件
      const where = { deleted_at: null };
      const allowKeys = ['id', 'status', 'realname_auth_status'];
      
      // 精确匹配字段
      for (const key of allowKeys) {
        if (filters[key] !== undefined && filters[key] !== '') {
          where[key] = filters[key];
        }
      }
      
      // 模糊查询字段
      const fuzzyKeys = ['username', 'nickname', 'email', 'phone'];
      for (const key of fuzzyKeys) {
        if (filters[key] !== undefined && filters[key] !== '') {
          where[key] = { contains: filters[key] };
        }
      }
      
      // 关键字模糊搜索
      if (filters.keyword && filters.keyword.trim() !== '') {
        const keyword = filters.keyword.trim();
        where.OR = [
          { username: { contains: keyword } },
          { nickname: { contains: keyword } },
          { email: { contains: keyword } },
          { phone: { contains: keyword } }
        ];
      }

      // 执行查询
      let total = 0;
      let users = [];

      try {
        // 分别执行查询，避免一个查询失败影响另一个
        total = await this.prisma.MasterMallUser.count({ where });
      } catch (countError) {
        console.error('查询用户总数失败:', countError);
        throw new Error(`查询用户总数失败: ${countError.message}`);
      }

      try {
        users = await this.prisma.MasterMallUser.findMany({
          where,
          skip,
          take: pageSize,
          select: {
            id: true,
            username: true,
            nickname: true,
            email: true,
            phone: true,
            avatar: true,
            status: true,
            last_login_ip: true,
            last_login_time: true,
            login_count: true,
            created_at: true,
            updated_at: true,
            remark: true,
            realname_auth_status: true
          },
          orderBy: {
            created_at: 'desc'
          }
        });
      } catch (findError) {
        console.error('查询用户列表失败:', findError);
        throw new Error(`查询用户列表失败: ${findError.message}`);
      }

      return {
        list: users,
        total,
        page,
        pageSize
      };
    } catch (error) {
      console.error('获取商城用户列表失败:', error);
      throw error; // 向上层抛出错误，让控制器处理
    }
  }

  /**
   * 创建商城用户
   * @param {Object} userData 用户数据
   * @returns {Object} 创建的用户对象
   */
  async create(userData) {
    // 检查用户名是否已存在
    const existingUser = await this.prisma.MasterMallUser.findFirst({
      where: { 
        username: userData.username,
        deleted_at: null
      }
    });

    if (existingUser) {
      throw new Error('用户名已存在');
    }

    // 密码加密
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(userData.password, salt);
    
    // 生成雪花ID
    const id = generateSnowflakeId();
    
    // 获取当前时间戳
    const now = BigInt(Date.now());
    
    // 创建用户
    const newUser = await this.prisma.MasterMallUser.create({
      data: {
        id,
        username: userData.username,
        password: hashedPassword,
        nickname: userData.nickname || null,
        avatar: userData.avatar || null,
        phone: userData.phone || null,
        email: userData.email || null,
        status: userData.status || 1,
        login_count: 0,
        created_at: now,
        updated_at: now,
        created_by: userData.created_by ? BigInt(userData.created_by) : null,
        updated_by: userData.created_by ? BigInt(userData.created_by) : null,
        remark: userData.remark || null
      }
    });

    // 返回创建的用户（不包含密码）
    const { password, ...userWithoutPassword } = newUser;
    return userWithoutPassword;
  }

  /**
   * 更新商城用户
   * @param {string} id 用户ID
   * @param {Object} userData 用户数据
   * @returns {Object} 更新后的用户对象
   */
  async update(id, userData) {
    // 检查用户是否存在
    const existingUser = await this.prisma.MasterMallUser.findUnique({
      where: { id: BigInt(id) }
    });

    if (!existingUser) {
      throw new Error('用户不存在');
    }

    // 如果要更新用户名，检查新用户名是否已被使用
    if (userData.username && userData.username !== existingUser.username) {
      const userWithSameUsername = await this.prisma.MasterMallUser.findFirst({
        where: { 
          username: userData.username,
          deleted_at: null,
          id: { not: BigInt(id) }
        }
      });

      if (userWithSameUsername) {
        throw new Error('用户名已存在');
      }
    }

    // 准备更新数据
    const updateData = { ...userData };
    
    // 删除id字段，避免在data中包含id字段
    delete updateData.id;
    
    // 如果有密码更新，需要加密
    if (updateData.password) {
      const salt = await bcrypt.genSalt(10);
      updateData.password = await bcrypt.hash(updateData.password, salt);
    }
    
    // 处理BigInt类型字段
    if (updateData.updated_by) {
      updateData.updated_by = BigInt(updateData.updated_by);
    }
    
    // 更新时间戳
    updateData.updated_at = BigInt(Date.now());
    
    // 更新用户
    const updatedUser = await this.prisma.MasterMallUser.update({
      where: { id: BigInt(id) },
      data: updateData
    });

    // 返回更新后的用户（不包含密码）
    const { password, ...userWithoutPassword } = updatedUser;
    return userWithoutPassword;
  }

  /**
   * 删除商城用户（软删除）
   * @param {string} id 用户ID
   * @param {string} updatedBy 操作人ID
   * @returns {Object} 删除结果
   */
  async delete(id, updatedBy) {
    // 检查用户是否存在
    const existingUser = await this.prisma.MasterMallUser.findUnique({
      where: { id: BigInt(id) }
    });

    if (!existingUser) {
      throw new Error('用户不存在');
    }

    // 软删除用户
    const now = BigInt(Date.now());
    await this.prisma.MasterMallUser.update({
      where: { id: BigInt(id) },
      data: {
        deleted_at: now,
        updated_at: now,
        updated_by: updatedBy ? BigInt(updatedBy) : null
      }
    });

    return { success: true };
  }

  /**
   * 根据ID获取商城用户详情
   * @param {string} id 用户ID
   * @returns {Object} 用户详情
   */
  async getById(id) {
    const user = await this.prisma.MasterMallUser.findFirst({
      where: { 
        id: BigInt(id),
        deleted_at: null
      }
    });

    if (!user) {
      throw new Error('用户不存在');
    }

    // 返回用户信息（不包含密码）
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }
  
  /**
   * 获取用户实名认证信息
   * @param {string} userId 用户ID
   * @returns {Object|null} 实名认证信息，如果不存在则返回null
   */
  async getRealnameAuth(userId) {
    try {
      // 查询实名认证信息
      const authInfo = await this.prisma.MallUserRealnameAuth.findFirst({
        where: {
          user_id: BigInt(userId),
          deleted_at: null
        }
      });
      
      return authInfo;
    } catch (error) {
      console.error('获取实名认证信息失败:', error);
      throw error;
    }
  }
}

module.exports = MallUserService;
