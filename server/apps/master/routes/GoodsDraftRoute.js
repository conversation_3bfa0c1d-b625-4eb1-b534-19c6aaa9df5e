const express = require('express');
const router = express.Router();
const { prisma } = require('../../../core/database/prisma');
const GoodsDraftController = require('../controllers/GoodsDraftController');
const RouterConfig = require('../../../core/routes/RouterConfig');

// 创建控制器实例
const controller = new GoodsDraftController(prisma);

// 创建受JWT保护的路由
const protectedRouter = RouterConfig.authRoute(router);

/**
 * @swagger
 * /api/master/goods-draft/latest:
 *   get:
 *     summary: 获取最新草稿
 *     description: 获取当前用户最新的一条草稿数据
 *     responses:
 *       200:
 *         description: 成功获取最新草稿
 *       401:
 *         description: 用户未登录
 */
protectedRouter.get('/latest', controller.getLatestDraft.bind(controller));

/**
 * @swagger
 * /api/master/goods-draft:
 *   post:
 *     summary: 保存草稿
 *     description: 保存当前用户的商品草稿
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               content:
 *                 type: string
 *                 description: 草稿内容（JSON格式，存储商品表单数据）
 *     responses:
 *       201:
 *         description: 草稿保存成功
 *       400:
 *         description: 数据验证失败
 *       401:
 *         description: 用户未登录
 */
protectedRouter.post('/', controller.createDraft.bind(controller));

/**
 * @swagger
 * /api/master/goods-draft/{id}:
 *   delete:
 *     summary: 删除草稿
 *     description: 根据草稿ID删除指定的草稿
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: 草稿ID
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 草稿删除成功
 *       400:
 *         description: 参数错误
 *       401:
 *         description: 用户未登录
 *       404:
 *         description: 草稿不存在或无权限删除
 */
protectedRouter.delete('/:id', controller.deleteDraft.bind(controller));

module.exports = router;
