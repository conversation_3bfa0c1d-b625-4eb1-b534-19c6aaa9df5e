/**
 * 商品分类路由
 */
const express = require('express');
const router = express.Router();
const { prisma } = require('../../../core/database/prisma');
const GoodsCategoryController = require('../controllers/GoodsCategoryController');
const RouterConfig = require('../../../core/routes/RouterConfig');

// 创建控制器实例
const controller = new GoodsCategoryController(prisma);

// 创建受JWT保护的路由
const protectedRouter = RouterConfig.authRoute(router);

/**
 * @swagger
 * /api/master/goods-category:
 *   get:
 *     tags:
 *       - 商品分类管理
 *     summary: 获取所有商品分类
 *     description: 获取系统中所有的商品分类列表，支持按名称模糊搜索
 *     parameters:
 *       - name: name
 *         in: query
 *         required: false
 *         description: 分类名称（模糊搜索）
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 成功获取分类列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   description: 请求是否成功
 *                   example: true
 *                 data:
 *                   type: array
 *                   description: 分类数据（当有name参数时为扁平结构，无name参数时为树形结构）
 */
protectedRouter.get('/', controller.getAll.bind(controller));

/**
 * @swagger
 * /api/master/goods-category/{id}:
 *   get:
 *     tags:
 *       - 商品分类管理
 *     summary: 获取指定ID的商品分类
 *     description: 根据ID获取商品分类详情
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: 分类ID
 *         schema:
 *           type: integer
 *           format: int64
 *     responses:
 *       200:
 *         description: 成功获取分类详情
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   description: 请求是否成功
 *                   example: true
 *                 data:
 *                   type: object
 *                   description: 分类详情数据
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 分类不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.get('/:id', controller.getById.bind(controller));

/**
 * @swagger
 * /api/master/goods-category:
 *   post:
 *     tags:
 *       - 商品分类管理
 *     summary: 创建商品分类
 *     description: 创建新的商品分类
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/GoodsCategoryRequest'
 *     responses:
 *       200:
 *         description: 成功创建商品分类
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/GoodsCategoryResponse'
 */
protectedRouter.post('/', controller.create.bind(controller));

/**
 * @swagger
 * /api/master/goods-category/{id}:
 *   put:
 *     tags:
 *       - 商品分类管理
 *     summary: 更新商品分类
 *     description: 根据ID更新商品分类信息
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 商品分类ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/GoodsCategoryRequest'
 *     responses:
 *       200:
 *         description: 成功更新商品分类
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/GoodsCategoryResponse'
 */
protectedRouter.put('/:id', controller.update.bind(controller));

/**
 * @swagger
 * /api/master/goods-category/{id}:
 *   delete:
 *     tags:
 *       - 商品分类管理
 *     summary: 删除商品分类
 *     description: 根据ID删除商品分类
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 商品分类ID
 *     responses:
 *       200:
 *         description: 成功删除商品分类
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 */
protectedRouter.delete('/:id', controller.delete.bind(controller));

/**
 * @swagger
 * /api/master/goods-category/{id}/attribute-template:
 *   get:
 *     tags:
 *       - 商品分类管理
 *     summary: 获取商品分类属性模板
 *     description: 根据分类ID获取商品属性模板和参数
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 商品分类ID
 *     responses:
 *       200:
 *         description: 成功获取商品属性模板
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       sortOrder:
 *                         type: integer
 *                       attributes:
 *                         type: array
 *                         items:
 *                           type: object
 */
protectedRouter.get('/:id/attribute-template', controller.getCategoryAttributeTemplate.bind(controller));

module.exports = router;
