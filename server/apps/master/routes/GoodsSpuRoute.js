/**
 * 商品SPU路由
 */
const express = require('express');
const router = express.Router();
const GoodsSpuController = require('../controllers/GoodsSpuController');
const { prisma } = require('../../../core/database/prisma');
const RouterConfig = require('../../../core/routes/RouterConfig');
const controller = new GoodsSpuController(prisma);

// 创建受JWT保护的路由
const protectedRouter = RouterConfig.authRoute(router);

/**
 * @swagger
 * /api/master/goods-spu:
 *   post:
 *     summary: 添加商品
 *     description: 添加新商品(SPU和SKU)
 *     tags: [商品管理]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - basicInfo
 *               - media
 *               - attributes
 *               - skuInfo
 *               - details
 *               - settings
 *             properties:
 *               basicInfo:
 *                 type: object
 *                 required:
 *                   - productName
 *                   - categoryId
 *                   - brandId
 *                   - freightTemplateId
 *                 properties:
 *                   productName:
 *                     type: string
 *                     description: 商品名称
 *                   categoryId:
 *                     type: integer
 *                     description: 分类ID
 *                   brandId:
 *                     type: integer
 *                     description: 品牌ID
 *                   subtitle:
 *                     type: string
 *                     description: 商品副标题
 *                   unit:
 *                     type: string
 *                     description: 计量单位
 *                   freightTemplateId:
 *                     type: integer
 *                     description: 运费模板ID
 *               media:
 *                 type: object
 *                 properties:
 *                   images:
 *                     type: array
 *                     description: 商品图片列表
 *                     items:
 *                       type: object
 *                       required:
 *                         - url
 *                       properties:
 *                         url:
 *                           type: string
 *                           description: 图片URL
 *                         isDefault:
 *                           type: boolean
 *                           description: 是否默认图片
 *                         sort:
 *                           type: integer
 *                           description: 排序
 *                   videos:
 *                     type: array
 *                     description: 商品视频列表
 *                     items:
 *                       type: object
 *                       required:
 *                         - url
 *                       properties:
 *                         url:
 *                           type: string
 *                           description: 视频URL
 *                         coverUrl:
 *                           type: string
 *                           description: 封面图URL
 *                         sort:
 *                           type: integer
 *                           description: 排序
 *               attributes:
 *                 type: object
 *                 properties:
 *                   tags:
 *                     type: array
 *                     description: 商品标签ID列表
 *                     items:
 *                       type: integer
 *                   services:
 *                     type: array
 *                     description: 商品服务ID列表
 *                     items:
 *                       type: integer
 *               skuInfo:
 *                 type: object
 *                 required:
 *                   - skuType
 *                   - skuList
 *                 properties:
 *                   skuType:
 *                     type: string
 *                     enum: [single, multi]
 *                     description: SKU类型(单规格/多规格)
 *                   specDefinitions:
 *                     type: array
 *                     description: 规格定义(多规格时)
 *                     items:
 *                       type: object
 *                       required:
 *                         - name
 *                         - values
 *                       properties:
 *                         name:
 *                           type: string
 *                           description: 规格名称
 *                         values:
 *                           type: array
 *                           description: 规格值列表
 *                           items:
 *                             type: string
 *                   skuList:
 *                     type: array
 *                     description: SKU列表
 *                     items:
 *                       type: object
 *                       required:
 *                         - salesPrice
 *                         - stock
 *                         - skuCode
 *                       properties:
 *                         specs:
 *                           type: object
 *                           description: 规格值组合(多规格时)
 *                         image:
 *                           type: object
 *                           properties:
 *                             url:
 *                               type: string
 *                               description: 图片URL
 *                         salesPrice:
 *                           type: number
 *                           description: 销售价
 *                         stock:
 *                           type: integer
 *                           description: 库存
 *                         skuCode:
 *                           type: string
 *                           description: SKU编码
 *                         unit:
 *                           type: string
 *                           description: 单位
 *                         weight:
 *                           type: number
 *                           description: 重量(kg)
 *                         volume:
 *                           type: number
 *                           description: 体积(m³)
 *                         costPrice:
 *                           type: number
 *                           description: 成本价
 *                         marketPrice:
 *                           type: number
 *                           description: 市场价
 *                         lowStockThreshold:
 *                           type: integer
 *                           description: 库存预警阈值
 *                         barcode:
 *                           type: string
 *                           description: 条形码
 *                         expirationDate:
 *                           type: string
 *                           format: date
 *                           description: 过期时间
 *               details:
 *                 type: object
 *                 properties:
 *                   detailHtml:
 *                     type: string
 *                     description: 商品详情HTML
 *                   detailMobile:
 *                     type: string
 *                     description: 移动端商品详情
 *               settings:
 *                 type: object
 *                 properties:
 *                   seoTitle:
 *                     type: string
 *                     description: SEO标题
 *                   seoKeywords:
 *                     type: string
 *                     description: SEO关键词
 *                   seoDescription:
 *                     type: string
 *                     description: SEO描述
 *     responses:
 *       201:
 *         description: 商品添加成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 201
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: 商品添加成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     spuId:
 *                       type: integer
 *                       description: 创建的商品SPU ID
 *                     slug:
 *                       type: string
 *                       description: 商品slug
 *       400:
 *         description: 请求数据验证失败
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 400
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: 请求数据验证失败
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       field:
 *                         type: string
 *                         description: 错误字段
 *                       message:
 *                         type: string
 *                         description: 错误信息
 *       500:
 *         description: 服务器错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 500
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: 添加商品失败
 *                 error:
 *                   type: string
 *                   description: 错误详情
 */
protectedRouter.post('/', controller.addProduct.bind(controller));

/**
 * @swagger
 * /api/master/goods-spu/{id}:
 *   put:
 *     summary: 编辑商品
 *     description: 编辑商品信息(SPU和SKU)
 *     tags: [商品管理]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: 商品ID
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               basicInfo:
 *                 type: object
 *                 properties:
 *                   productName:
 *                     type: string
 *                     description: 商品名称
 *                   categoryId:
 *                     type: integer
 *                     description: 分类ID
 *                   brandId:
 *                     type: integer
 *                     description: 品牌ID
 *                   subtitle:
 *                     type: string
 *                     description: 商品副标题
 *                   unit:
 *                     type: string
 *                     description: 计量单位
 *                   freightTemplateId:
 *                     type: integer
 *                     description: 运费模板ID
 *               media:
 *                 type: object
 *                 properties:
 *                   spuImages:
 *                     type: array
 *                     description: 商品图片列表
 *                     items:
 *                       type: object
 *                       properties:
 *                         url:
 *                           type: string
 *                           description: 图片URL
 *                         isDefault:
 *                           type: boolean
 *                           description: 是否默认图片
 *                         sortOrder:
 *                           type: integer
 *                           description: 排序
 *                   video:
 *                     type: object
 *                     properties:
 *                       url:
 *                         type: string
 *                         description: 视频URL
 *                       coverUrl:
 *                         type: string
 *                         description: 封面图URL
 *                       sortOrder:
 *                         type: integer
 *                         description: 排序
 *               associations:
 *                 type: object
 *                 properties:
 *                   tagIds:
 *                     type: array
 *                     description: 商品标签ID列表
 *                     items:
 *                       type: integer
 *                   serviceIds:
 *                     type: array
 *                     description: 商品服务ID列表
 *                     items:
 *                       type: integer
 *               attributes:
 *                 type: object
 *                 properties:
 *                   attributeValues:
 *                     type: array
 *                     description: 商品属性值
 *                     items:
 *                       type: object
 *                       properties:
 *                         attributeItemId:
 *                           type: integer
 *                           description: 属性项ID
 *                         value:
 *                           type: string
 *                           description: 属性值
 *                   qualificationInfo:
 *                     type: object
 *                     description: 资质信息
 *               skuInfo:
 *                 type: object
 *                 properties:
 *                   skuType:
 *                     type: string
 *                     enum: [single, multi]
 *                     description: 单规格或多规格
 *                   specDefinitions:
 *                     type: array
 *                     description: 规格定义
 *                     items:
 *                       type: object
 *                       properties:
 *                         name:
 *                           type: string
 *                           description: 规格名称
 *                         values:
 *                           type: array
 *                           description: 规格值列表
 *                           items:
 *                             type: string
 *                   skuList:
 *                     type: array
 *                     description: SKU列表
 *                     items:
 *                       type: object
 *                       properties:
 *                         skuCode:
 *                           type: string
 *                           description: SKU编码
 *                         specs:
 *                           type: object
 *                           description: 规格值组合(多规格时)
 *                         salesPrice:
 *                           type: number
 *                           description: 销售价
 *                         marketPrice:
 *                           type: number
 *                           description: 市场价
 *                         costPrice:
 *                           type: number
 *                           description: 成本价
 *                         stock:
 *                           type: integer
 *                           description: 库存
 *                         weight:
 *                           type: number
 *                           description: 重量
 *                         volume:
 *                           type: number
 *                           description: 体积
 *                         barcode:
 *                           type: string
 *                           description: 条形码
 *                         image:
 *                           type: object
 *                           properties:
 *                             url:
 *                               type: string
 *                               description: 图片URL
 *               details:
 *                 type: object
 *                 properties:
 *                   description:
 *                     type: string
 *                     description: 商品详情
 *               settings:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: string
 *                     enum: [on_shelf, off_shelf]
 *                     description: 商品状态
 *                   sortOrder:
 *                     type: integer
 *                     description: 排序
 *                   isVirtual:
 *                     type: boolean
 *                     description: 是否虚拟商品
 *                   metaTitle:
 *                     type: string
 *                     description: SEO标题
 *                   metaKeywords:
 *                     type: string
 *                     description: SEO关键词
 *                   metaDescription:
 *                     type: string
 *                     description: SEO描述
 *     responses:
 *       200:
 *         description: 商品更新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: 商品更新成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     spuId:
 *                       type: string
 *                       example: "1"
 *                     slug:
 *                       type: string
 *                       example: "test-product"
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 400
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: 请求数据验证失败
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       field:
 *                         type: string
 *                         example: "basicInfo.productName"
 *                       message:
 *                         type: string
 *                         example: "商品名称不能为空"
 *       404:
 *         description: 商品不存在
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 404
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "商品不存在"
 *       500:
 *         description: 服务器错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 500
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "更新商品失败"
 *                 error:
 *                   type: string
 *                   description: 错误详情
 */
protectedRouter.put('/:id', controller.updateGoodsSpu.bind(controller));

/**
 * @swagger
 * /api/master/goods-spu:
 *   get:
 *     summary: 获取商品列表
 *     description: 获取商品列表，支持分页、筛选和排序
 *     tags: [商品管理]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: 每页数量
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 搜索关键词（商品名称、副标题、商品编码）
 *       - in: query
 *         name: categoryId
 *         schema:
 *           type: integer
 *         description: 分类ID
 *       - in: query
 *         name: brandId
 *         schema:
 *           type: integer
 *         description: 品牌ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [on_shelf, off_shelf]
 *         description: 商品状态（on_shelf-上架，off_shelf-下架）
 *       - in: query
 *         name: startTime
 *         schema:
 *           type: string
 *           format: date-time
 *         description: 开始时间
 *       - in: query
 *         name: endTime
 *         schema:
 *           type: string
 *           format: date-time
 *         description: 结束时间
 *       - in: query
 *         name: sortField
 *         schema:
 *           type: string
 *           enum: [id, created_at, sales, stock, price]
 *           default: created_at
 *         description: 排序字段
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: 排序方式（asc-升序，desc-降序）
 *     responses:
 *       200:
 *         description: 成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: 获取商品列表成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         type: object
 *                       description: 商品列表
 *                     total:
 *                       type: integer
 *                       description: 总记录数
 *                     page:
 *                       type: integer
 *                       description: 当前页码
 *                     pageSize:
 *                       type: integer
 *                       description: 每页记录数
 *                     totalPages:
 *                       type: integer
 *                       description: 总页数
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 服务器错误
 */
protectedRouter.get('/', controller.getProductList.bind(controller));

/**
 * @swagger
 * /api/master/goods-spu/{id}:
 *   get:
 *     summary: 获取商品详情
 *     description: 根据ID获取商品详细信息
 *     tags: [商品管理]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 商品SPU ID
 *     responses:
 *       200:
 *         description: 获取商品详情成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: 获取商品详情成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     basicInfo:
 *                       type: object
 *                       description: 商品基本信息
 *                     mediaInfo:
 *                       type: object
 *                       description: 商品媒体信息
 *                     relatedInfo:
 *                       type: object
 *                       description: 商品关联信息
 *                     skuInfo:
 *                       type: object
 *                       description: 商品SKU信息
 *                     detailInfo:
 *                       type: object
 *                       description: 商品详情信息
 *                     otherSettings:
 *                       type: object
 *                       description: 其他设置
 *       404:
 *         description: 商品不存在
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 404
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: 商品不存在
 *       500:
 *         description: 服务器错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 500
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: 获取商品详情失败
 *                 error:
 *                   type: string
 *                   description: 错误详情
 */
protectedRouter.get('/:id', controller.getProductDetail.bind(controller));

/**
 * @swagger
 * /api/master/goods-spu/{id}/soft:
 *   delete:
 *     summary: 软删除商品
 *     description: 将商品移至回收站（设置deleted_at字段）
 *     tags: [商品管理]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 商品SPU ID
 *     responses:
 *       200:
 *         description: 软删除商品成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: 商品已移至回收站
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       description: 被软删除的商品ID
 *       400:
 *         description: 参数错误
 *       404:
 *         description: 商品不存在
 *       500:
 *         description: 服务器错误
 */
protectedRouter.delete('/:id/soft', controller.softDeleteProduct.bind(controller));

/**
 * @swagger
 * /api/master/goods-spu/{id}/restore:
 *   post:
 *     summary: 恢复已删除的商品
 *     description: 从回收站恢复商品（清除deleted_at字段）
 *     tags: [商品管理]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 商品SPU ID
 *     responses:
 *       200:
 *         description: 恢复商品成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: 商品已成功恢复
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       description: 被恢复的商品ID
 *       400:
 *         description: 参数错误
 *       404:
 *         description: 商品不存在
 *       500:
 *         description: 服务器错误
 */
protectedRouter.post('/:id/restore', controller.restoreProduct.bind(controller));

/**
 * @swagger
 * /api/master/goods-spu/{id}/permanent:
 *   delete:
 *     summary: 永久删除商品
 *     description: 永久删除商品（物理删除，无法恢复）
 *     tags: [商品管理]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 商品SPU ID
 *     responses:
 *       200:
 *         description: 永久删除商品成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: 商品已永久删除
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       description: 被删除的商品ID
 *       400:
 *         description: 参数错误
 *       404:
 *         description: 商品不存在
 *       500:
 *         description: 服务器错误
 */
protectedRouter.delete('/:id/permanent', controller.permanentDeleteProduct.bind(controller));

module.exports = router;
