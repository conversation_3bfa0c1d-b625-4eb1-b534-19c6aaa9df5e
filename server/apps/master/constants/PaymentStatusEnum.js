/**
 * 支付状态枚举
 * 定义订单支付的各种状态
 */

const PaymentStatusEnum = {
  /**
   * 未支付
   */
  UNPAID: 0,
  
  /**
   * 已支付
   */
  PAID: 1,
  
  /**
   * 部分支付
   */
  PARTIAL_PAID: 2,
  
  /**
   * 退款中
   */
  REFUNDING: 3,
  
  /**
   * 已退款
   */
  REFUNDED: 4,
  
  /**
   * 根据状态码获取状态名称
   * @param {number} statusCode - 状态码
   * @returns {string} - 状态名称
   */
  getStatusName(statusCode) {
    switch (parseInt(statusCode)) {
      case this.UNPAID:
        return '未支付';
      case this.PAID:
        return '已支付';
      case this.PARTIAL_PAID:
        return '部分支付';
      case this.REFUNDING:
        return '退款中';
      case this.REFUNDED:
        return '已退款';
      default:
        return '未知状态';
    }
  }
};

module.exports = PaymentStatusEnum; 