model Region {
  id             Int     @id
  citycode       Int?    @default(0) @map("citycode")
  cityname       String? @default("") @map("cityname") @db.VarChar(32)
  cityfullname   String? @default("") @map("cityfullname") @db.VarChar(500)
  citycodeprefix Int?    @default(0) @map("citycodeprefix")
  level          Int?    @default(0) @map("level") @db.SmallInt
  parent_id      Int?    @default(0) @map("parent_id")
  out_field      Int?    @default(0) @map("out_field") @db.SmallInt

  @@map("region")
  @@schema("base")
  @@index([citycode], name: "idx_region_citycode")
  @@index([level], name: "idx_region_level")
  @@index([parent_id], name: "idx_region_parent_id")
}
