-- 创建schema
CREATE SCHEMA IF NOT EXISTS base;

-- 订单主表
CREATE TABLE base.orders (
  id BIGSERIAL PRIMARY KEY,
  order_sn VARCHAR(64) UNIQUE NOT NULL,
  user_id BIGINT NOT NULL,
  third_party_sub_entity_id BIGINT,
  third_party_order_sn VARCHAR(128),
  order_status INT NOT NULL DEFAULT 0,
  payment_status INT NOT NULL DEFAULT 0,
  shipping_status INT NOT NULL DEFAULT 0,
  total_product_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
  shipping_fee DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
  discount_amount DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
  tax_amount DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
  total_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
  paid_amount DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
  payment_method VARCHAR(50),
  payment_sn VARCHAR(128),
  shipping_method VARCHAR(50),
  order_source VARCHAR(50),
  order_type INT NOT NULL DEFAULT 0,
  remark TEXT,
  admin_remark TEXT,
  created_at BIGINT NOT NULL DEFAULT (EXTRACT(epoch FROM now()) * 1000)::BIGINT,
  updated_at BIGINT NOT NULL DEFAULT (EXTRACT(epoch FROM now()) * 1000)::BIGINT,
  paid_at BIGINT,
  shipped_at BIGINT,
  completed_at BIGINT,
  cancelled_at BIGINT,
  cancel_reason TEXT,
  deleted_at BIGINT
);

-- 添加列注释
COMMENT ON COLUMN base.orders.order_sn IS '订单唯一编号 (我们系统的, UI: 订单编号)';
COMMENT ON COLUMN base.orders.user_id IS '下单用户ID (逻辑关联用户表)';
COMMENT ON COLUMN base.orders.third_party_sub_entity_id IS '关联的第三方子实体ID (逻辑关联 third_party_sub_entities.id), NULL表示内部订单';
COMMENT ON COLUMN base.orders.third_party_order_sn IS '第三方平台的原始订单号 (UI: 订单号?)';
COMMENT ON COLUMN base.orders.order_status IS '订单状态 (0:待付款, 1:待发货, 2:已发货, 3:已完成, 4:已取消, 5:已关闭) (UI: 订单状态)';
COMMENT ON COLUMN base.orders.payment_status IS '支付状态 (0:未支付, 1:已支付, 2:部分支付, 3:已退款, 4:部分退款)';
COMMENT ON COLUMN base.orders.shipping_status IS '发货状态 (0:未发货, 1:部分发货, 2:已发货, 3:已签收)';
COMMENT ON COLUMN base.orders.total_product_amount IS '商品总金额 (UI: 商品金额)';
COMMENT ON COLUMN base.orders.shipping_fee IS '运费金额 (UI: 运费金额)';
COMMENT ON COLUMN base.orders.discount_amount IS '订单级优惠金额 (UI: 优惠金额)';
COMMENT ON COLUMN base.orders.tax_amount IS '税费金额';
COMMENT ON COLUMN base.orders.total_amount IS '订单应付总额';
COMMENT ON COLUMN base.orders.paid_amount IS '实际支付金额 (UI: 支付金额)';
COMMENT ON COLUMN base.orders.payment_method IS '支付方式代码 (UI: 支付方式)';
COMMENT ON COLUMN base.orders.payment_sn IS '支付流水号';
COMMENT ON COLUMN base.orders.shipping_method IS '配送方式代码 (UI: 配送方式)';
COMMENT ON COLUMN base.orders.order_source IS '订单来源 (我们系统定义的, UI: 订单来源)';
COMMENT ON COLUMN base.orders.order_type IS '订单类型 (0:商城订单, 1:平台订单, ...) (UI: 订单类型)';
COMMENT ON COLUMN base.orders.remark IS '用户下单时填写的备注 (UI: 订单备注)';
COMMENT ON COLUMN base.orders.admin_remark IS '管理员添加的备注';
COMMENT ON COLUMN base.orders.created_at IS '下单时间 (时间戳 ms, UI: 下单时间)';
COMMENT ON COLUMN base.orders.updated_at IS '最后更新时间 (时间戳 ms)';
COMMENT ON COLUMN base.orders.paid_at IS '支付完成时间 (时间戳 ms, UI: 支付时间)';
COMMENT ON COLUMN base.orders.shipped_at IS '发货时间 (时间戳 ms)';
COMMENT ON COLUMN base.orders.completed_at IS '订单完成时间 (时间戳 ms)';
COMMENT ON COLUMN base.orders.cancelled_at IS '订单取消时间 (时间戳 ms)';
COMMENT ON COLUMN base.orders.cancel_reason IS '订单取消原因';
COMMENT ON COLUMN base.orders.deleted_at IS '软删除标记 (时间戳 ms)';

-- 创建索引
CREATE INDEX idx_orders_user_id ON base.orders(user_id);
CREATE INDEX idx_orders_order_status ON base.orders(order_status);
CREATE INDEX idx_orders_created_at ON base.orders(created_at);
CREATE INDEX idx_orders_deleted_at ON base.orders(deleted_at);
CREATE INDEX idx_orders_third_party_sub_entity_id ON base.orders(third_party_sub_entity_id);
CREATE INDEX idx_orders_third_party_order_sn ON base.orders(third_party_order_sn);
CREATE INDEX idx_orders_order_type ON base.orders(order_type);

-- 订单商品表
CREATE TABLE base.order_items (
  id BIGSERIAL PRIMARY KEY,
  order_id BIGINT NOT NULL,
  goods_spu_id BIGINT,
  goods_sku_id BIGINT NOT NULL,
  spu_code_snapshot VARCHAR(100),
  spu_name_snapshot TEXT,
  product_name TEXT NOT NULL,
  sku_code VARCHAR(100),
  sku_specifications JSONB,
  product_image TEXT,
  unit_price DECIMAL(12, 2) NOT NULL,
  market_price_snapshot DECIMAL(12, 2),
  cost_price_snapshot DECIMAL(12, 2),
  weight_snapshot DECIMAL(10, 3),
  volume_snapshot DECIMAL(10, 6),
  third_party_spu_id VARCHAR(255),
  third_party_sku_id VARCHAR(255),
  third_party_product_code VARCHAR(255),
  third_party_item_snapshot JSONB,
  quantity INT NOT NULL DEFAULT 1,
  total_price DECIMAL(12, 2) NOT NULL,
  item_paid_amount DECIMAL(12, 2) NOT NULL,
  created_at BIGINT NOT NULL DEFAULT (EXTRACT(epoch FROM now()) * 1000)::BIGINT,
  updated_at BIGINT NOT NULL DEFAULT (EXTRACT(epoch FROM now()) * 1000)::BIGINT
);

-- 添加列注释
COMMENT ON COLUMN base.order_items.order_id IS '关联的订单ID (逻辑关联 orders.id)';
COMMENT ON COLUMN base.order_items.goods_spu_id IS '关联的内部商品SPU ID (goods_spus.id, 下单时关联)';
COMMENT ON COLUMN base.order_items.goods_sku_id IS '关联的内部商品SKU ID (goods_skus.id, 下单时关联)';
COMMENT ON COLUMN base.order_items.spu_code_snapshot IS 'SPU编码快照 (冗余)';
COMMENT ON COLUMN base.order_items.spu_name_snapshot IS 'SPU名称快照 (冗余, 可考虑合并到product_name或单独存储)';
COMMENT ON COLUMN base.order_items.product_name IS '商品名称 (快照)';
COMMENT ON COLUMN base.order_items.sku_code IS 'SKU编码快照 (UI: 系统SKU)';
COMMENT ON COLUMN base.order_items.sku_specifications IS 'SKU规格描述快照 (JSON格式, UI: 商品规格)';
COMMENT ON COLUMN base.order_items.product_image IS '商品图片URL快照 (通常是SKU图片或SPU主图, UI: 商品图片)';
COMMENT ON COLUMN base.order_items.unit_price IS '商品销售单价快照 (UI: 单价)';
COMMENT ON COLUMN base.order_items.market_price_snapshot IS '市场价/划线价快照 (可选)';
COMMENT ON COLUMN base.order_items.cost_price_snapshot IS '成本价快照 (可选, 用于利润计算)';
COMMENT ON COLUMN base.order_items.weight_snapshot IS '商品重量快照 (单位: kg)';
COMMENT ON COLUMN base.order_items.volume_snapshot IS '商品体积快照 (单位: m³)';
COMMENT ON COLUMN base.order_items.third_party_spu_id IS '第三方平台的SPU ID或商品ID (下单时关联)';
COMMENT ON COLUMN base.order_items.third_party_sku_id IS '第三方平台的SKU ID或规格ID (下单时关联)';
COMMENT ON COLUMN base.order_items.third_party_product_code IS '第三方平台的商品编码/货号 (下单时关联)';
COMMENT ON COLUMN base.order_items.third_party_item_snapshot IS '第三方商品完整信息快照 (JSON格式, 包含名称、规格、图片URL、价格等从第三方获取的关键信息)';
COMMENT ON COLUMN base.order_items.quantity IS '购买数量 (UI: 购买数量)';
COMMENT ON COLUMN base.order_items.total_price IS '商品行总价 (quantity * unit_price)';
COMMENT ON COLUMN base.order_items.item_paid_amount IS '单品实付金额 (考虑了可能的单品调整后的金额, 通常等于total_price)';
COMMENT ON COLUMN base.order_items.created_at IS '记录创建时间 (时间戳 ms)';
COMMENT ON COLUMN base.order_items.updated_at IS '记录最后更新时间 (时间戳 ms)';

-- 创建索引
CREATE INDEX idx_order_items_order_id ON base.order_items(order_id);
CREATE INDEX idx_order_items_goods_sku_id ON base.order_items(goods_sku_id);

-- 订单收货信息表
CREATE TABLE base.order_shipping_info (
  id BIGSERIAL PRIMARY KEY,
  order_id BIGINT NOT NULL UNIQUE,
  recipient_name VARCHAR(100) NOT NULL,
  recipient_phone VARCHAR(20) NOT NULL,
  region_province_id INT,
  region_city_id INT,
  region_district_id INT,
  region_path_name TEXT,
  street_address TEXT NOT NULL,
  postal_code VARCHAR(20),
  shipping_company_code VARCHAR(50),
  shipping_company_name VARCHAR(100),
  tracking_number VARCHAR(100),
  created_at BIGINT NOT NULL DEFAULT (EXTRACT(epoch FROM now()) * 1000)::BIGINT,
  updated_at BIGINT NOT NULL DEFAULT (EXTRACT(epoch FROM now()) * 1000)::BIGINT,
  CONSTRAINT fk_order_shipping_info_order_id FOREIGN KEY (order_id) REFERENCES base.orders(id)
);

-- 添加列注释
COMMENT ON COLUMN base.order_shipping_info.order_id IS '关联的订单ID (逻辑关联 orders.id), 设为UNIQUE表示一个订单只有一个收货信息';
COMMENT ON COLUMN base.order_shipping_info.recipient_name IS '收货人姓名 (UI: 收货人)';
COMMENT ON COLUMN base.order_shipping_info.recipient_phone IS '收货人电话 (UI: 联系电话)';
COMMENT ON COLUMN base.order_shipping_info.region_province_id IS '省份区域ID (逻辑关联 region.id)';
COMMENT ON COLUMN base.order_shipping_info.region_city_id IS '城市区域ID (逻辑关联 region.id)';
COMMENT ON COLUMN base.order_shipping_info.region_district_id IS '区/县区域ID (逻辑关联 region.id)';
COMMENT ON COLUMN base.order_shipping_info.region_path_name IS '省市区完整名称 (冗余, UI: 收货地址 前缀)';
COMMENT ON COLUMN base.order_shipping_info.street_address IS '详细街道地址 (UI: 收货地址 后缀)';
COMMENT ON COLUMN base.order_shipping_info.postal_code IS '邮政编码';
COMMENT ON COLUMN base.order_shipping_info.shipping_company_code IS '物流公司编码 (如: sf, yto)';
COMMENT ON COLUMN base.order_shipping_info.shipping_company_name IS '物流公司名称 (如: 顺丰快递, UI: 物流公司)';
COMMENT ON COLUMN base.order_shipping_info.tracking_number IS '物流单号 (UI: 快递单号)';
COMMENT ON COLUMN base.order_shipping_info.created_at IS '记录创建时间 (时间戳 ms)';
COMMENT ON COLUMN base.order_shipping_info.updated_at IS '记录最后更新时间 (时间戳 ms)';

-- 创建索引
CREATE INDEX idx_order_shipping_info_recipient_phone ON base.order_shipping_info(recipient_phone);
CREATE INDEX idx_order_shipping_info_tracking_number ON base.order_shipping_info(tracking_number);

-- 订单日志表
CREATE TABLE base.order_logs (
  id BIGSERIAL PRIMARY KEY,
  order_id BIGINT NOT NULL,
  operator_id BIGINT,
  operator_type SMALLINT NOT NULL,
  operator_name VARCHAR(100),
  action VARCHAR(255) NOT NULL,
  details TEXT,
  created_at BIGINT NOT NULL DEFAULT (EXTRACT(epoch FROM now()) * 1000)::BIGINT,
  CONSTRAINT fk_order_logs_order_id FOREIGN KEY (order_id) REFERENCES base.orders(id)
);

-- 添加列注释
COMMENT ON COLUMN base.order_logs.order_id IS '关联的订单ID (逻辑关联 orders.id)';
COMMENT ON COLUMN base.order_logs.operator_id IS '操作人ID (逻辑关联 system_user.id 或 用户表), NULL表示系统';
COMMENT ON COLUMN base.order_logs.operator_type IS '操作人类型 (0: 系统, 1: 管理员/后台用户, 2: 客户/买家)';
COMMENT ON COLUMN base.order_logs.operator_name IS '操作人名称 (冗余, UI: 操作人)';
COMMENT ON COLUMN base.order_logs.action IS '操作动作描述 (如: 创建订单, 支付成功, 发货, UI: 日志节点标题)';
COMMENT ON COLUMN base.order_logs.details IS '操作详情/备注 (如: 状态变更前后)';
COMMENT ON COLUMN base.order_logs.created_at IS '操作时间 (时间戳 ms, UI: 日志节点时间)';

-- 创建索引
CREATE INDEX idx_order_logs_order_id ON base.order_logs(order_id);
CREATE INDEX idx_order_logs_operator_id ON base.order_logs(operator_id);

-- 第三方顶级平台主数据表
CREATE TABLE base.third_party_platforms (
  id BIGSERIAL PRIMARY KEY,
  platform_code VARCHAR(50) UNIQUE NOT NULL,
  platform_name VARCHAR(100) NOT NULL,
  description TEXT,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at BIGINT NOT NULL DEFAULT (EXTRACT(epoch FROM now()) * 1000)::BIGINT,
  updated_at BIGINT NOT NULL DEFAULT (EXTRACT(epoch FROM now()) * 1000)::BIGINT,
  deleted_at BIGINT
);

-- 添加列注释
COMMENT ON COLUMN base.third_party_platforms.platform_code IS '顶级平台代码 (如: JD_HUICAI, ZHENGCAI, TAOBAO)';
COMMENT ON COLUMN base.third_party_platforms.platform_name IS '顶级平台名称 (如: 京东慧采, 政府采购, 淘宝)';
COMMENT ON COLUMN base.third_party_platforms.description IS '描述信息';
COMMENT ON COLUMN base.third_party_platforms.is_active IS '是否启用';
COMMENT ON COLUMN base.third_party_platforms.created_at IS '创建时间 (时间戳 ms)';
COMMENT ON COLUMN base.third_party_platforms.updated_at IS '更新时间 (时间戳 ms)';
COMMENT ON COLUMN base.third_party_platforms.deleted_at IS '软删除标记 (时间戳 ms)';

-- 创建索引
CREATE INDEX idx_third_party_platforms_is_active ON base.third_party_platforms(is_active);
CREATE INDEX idx_third_party_platforms_deleted_at ON base.third_party_platforms(deleted_at);

-- 第三方子实体主数据表 (店铺/子平台等)
CREATE TABLE base.third_party_sub_entities (
  id BIGSERIAL PRIMARY KEY,
  platform_id BIGINT NOT NULL,
  sub_entity_identifier VARCHAR(100) NOT NULL,
  sub_entity_name VARCHAR(255) NOT NULL,
  sub_entity_type VARCHAR(50) NOT NULL,
  description TEXT,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at BIGINT NOT NULL DEFAULT (EXTRACT(epoch FROM now()) * 1000)::BIGINT,
  updated_at BIGINT NOT NULL DEFAULT (EXTRACT(epoch FROM now()) * 1000)::BIGINT,
  deleted_at BIGINT,
  CONSTRAINT fk_third_party_sub_entities_platform_id FOREIGN KEY (platform_id) REFERENCES base.third_party_platforms(id) ON DELETE CASCADE,
  CONSTRAINT third_party_sub_entities_platform_identifier_key UNIQUE (platform_id, sub_entity_identifier)
);

-- 添加列注释
COMMENT ON COLUMN base.third_party_sub_entities.platform_id IS '关联的顶级平台ID (外键, 指向 third_party_platforms.id)';
COMMENT ON COLUMN base.third_party_sub_entities.sub_entity_identifier IS '子实体唯一标识符 (来自第三方, 如: 店铺ID, 子平台代码)';
COMMENT ON COLUMN base.third_party_sub_entities.sub_entity_name IS '子实体名称 (如: XXX旗舰店, XX省政采平台)';
COMMENT ON COLUMN base.third_party_sub_entities.sub_entity_type IS '子实体类型 (如: STORE=店铺, SUB_PLATFORM=子平台, CHANNEL=渠道)';
COMMENT ON COLUMN base.third_party_sub_entities.description IS '描述信息';
COMMENT ON COLUMN base.third_party_sub_entities.is_active IS '是否启用';
COMMENT ON COLUMN base.third_party_sub_entities.created_at IS '创建时间 (时间戳 ms)';
COMMENT ON COLUMN base.third_party_sub_entities.updated_at IS '更新时间 (时间戳 ms)';
COMMENT ON COLUMN base.third_party_sub_entities.deleted_at IS '软删除标记 (时间戳 ms)';

-- 创建索引
CREATE INDEX idx_third_party_sub_entities_platform_id ON base.third_party_sub_entities(platform_id);
CREATE INDEX idx_third_party_sub_entities_identifier ON base.third_party_sub_entities(sub_entity_identifier);
CREATE INDEX idx_third_party_sub_entities_is_active ON base.third_party_sub_entities(is_active);
CREATE INDEX idx_third_party_sub_entities_deleted_at ON base.third_party_sub_entities(deleted_at);


-- =============================================
-- Table: order_salespeople (Many-to-Many Join Table)
-- =============================================
DROP TABLE IF EXISTS "base"."order_salespeople";
CREATE TABLE "base"."order_salespeople" (
  "id" int8 NOT NULL DEFAULT nextval('"base".order_salespeople_id_seq'::regclass),
  "order_id" int8 NOT NULL,
  "salesperson_id" int8 NOT NULL,
  "created_at" int8 NOT NULL DEFAULT (EXTRACT(epoch FROM now()) * (1000)::numeric)
);
ALTER TABLE "base"."order_salespeople" OWNER TO "postgres"; -- Adjust owner if needed
COMMENT ON TABLE "base"."order_salespeople" IS '订单与业务员关联表 (中间表)';
COMMENT ON COLUMN "base"."order_salespeople"."id" IS '关联主键ID';
COMMENT ON COLUMN "base"."order_salespeople"."order_id" IS '关联的订单ID (逻辑关联 orders.id)';
COMMENT ON COLUMN "base"."order_salespeople"."salesperson_id" IS '关联的业务员ID (逻辑关联 system_user.id)';
COMMENT ON COLUMN "base"."order_salespeople"."created_at" IS '分配时间 (时间戳 ms)';
ALTER TABLE "base"."order_salespeople" ADD CONSTRAINT "order_salespeople_pkey" PRIMARY KEY ("id");
ALTER TABLE "base"."order_salespeople" ADD CONSTRAINT "order_salesperson_unique_assignment" UNIQUE ("order_id", "salesperson_id");
CREATE INDEX "idx_order_salesperson_order_id" ON "base"."order_salespeople" USING btree ("order_id");
CREATE INDEX "idx_order_salesperson_salesperson_id" ON "base"."order_salespeople" USING btree ("salesperson_id");