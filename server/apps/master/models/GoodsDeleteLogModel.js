/**
 * 商品删除日志模型
 */
const prismaManager = require('../../../core/prisma');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

class GoodsDeleteLogModel {
  /**
   * 构造函数
   */
  constructor(prismaInstance) {
    this.prisma = prismaInstance || prismaManager.getClient('base');
  }

  /**
   * 确保ID是BigInt类型
   * @param {string|number|BigInt} id - 需要转换的ID
   * @returns {BigInt} - 转换后的BigInt类型ID
   */
  ensureBigInt(id) {
    if (typeof id === 'bigint') return id;
    return BigInt(id);
  }

  /**
   * 创建商品删除日志
   * @param {Object} logData - 日志数据
   * @param {string|number|BigInt} logData.goodsSpuId - 商品SPU ID
   * @param {Object} logData.goodsSpuData - 删除前的商品SPU数据
   * @param {Array} logData.goodsSkusData - 删除前的商品SKU数据
   * @param {string} logData.reason - 删除原因
   * @param {string|number|BigInt} logData.createdBy - 操作人ID
   * @param {string} logData.ipAddress - 操作IP地址
   * @param {Object} tx - 事务对象（可选）
   * @returns {Promise<Object>} - 创建的日志记录
   */
  async createDeleteLog(logData, tx) {
    try {
      const prismaClient = tx || this.prisma;
      
      // 将对象数据转换为JSON字符串
      const goodsSpuDataJson = JSON.stringify(logData.goodsSpuData);
      const goodsSkusDataJson = logData.goodsSkusData ? JSON.stringify(logData.goodsSkusData) : null;
      
      // 创建日志记录
      const log = await prismaClient.goodsDeleteLog.create({
        data: {
          goods_spu_id: this.ensureBigInt(logData.goodsSpuId),
          goods_spu_data: goodsSpuDataJson,
          goods_skus_data: goodsSkusDataJson,
          reason: logData.reason,
          created_by: logData.createdBy ? this.ensureBigInt(logData.createdBy) : null,
          ip_address: logData.ipAddress
        }
      });
      
      return log;
    } catch (error) {
      console.error('创建商品删除日志失败:', error);
      throw error;
    }
  }

  /**
   * 获取商品删除日志列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页数量
   * @param {string|number|BigInt} params.goodsSpuId - 商品SPU ID（可选）
   * @param {string|number|BigInt} params.createdBy - 操作人ID（可选）
   * @returns {Promise<Object>} - 日志列表和总数
   */
  async getDeleteLogs(params) {
    try {
      const { page = 1, pageSize = 10, goodsSpuId, createdBy } = params;
      const skip = (page - 1) * pageSize;
      
      // 构建查询条件
      const where = {};
      if (goodsSpuId) {
        where.goods_spu_id = this.ensureBigInt(goodsSpuId);
      }
      if (createdBy) {
        where.created_by = this.ensureBigInt(createdBy);
      }
      
      // 查询日志列表
      const [logs, total] = await Promise.all([
        this.prisma.goodsDeleteLog.findMany({
          where,
          skip,
          take: pageSize,
          orderBy: {
            created_at: 'desc'
          }
        }),
        this.prisma.goodsDeleteLog.count({ where })
      ]);
      
      // 处理日志数据
      const processedLogs = logs.map(log => {
        // 将JSON字符串转换回对象
        const goodsSpuData = log.goods_spu_data ? JSON.parse(log.goods_spu_data) : null;
        const goodsSkusData = log.goods_skus_data ? JSON.parse(log.goods_skus_data) : null;
        
        return {
          ...log,
          goods_spu_data: goodsSpuData,
          goods_skus_data: goodsSkusData
        };
      });
      
      return {
        logs: processedLogs,
        total,
        page,
        pageSize
      };
    } catch (error) {
      console.error('获取商品删除日志列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取商品删除日志详情
   * @param {string|number|BigInt} id - 日志ID
   * @returns {Promise<Object>} - 日志详情
   */
  async getDeleteLogById(id) {
    try {
      const logId = this.ensureBigInt(id);
      
      const log = await this.prisma.goodsDeleteLog.findUnique({
        where: {
          id: logId
        }
      });
      
      if (!log) {
        return null;
      }
      
      // 将JSON字符串转换回对象
      const goodsSpuData = log.goods_spu_data ? JSON.parse(log.goods_spu_data) : null;
      const goodsSkusData = log.goods_skus_data ? JSON.parse(log.goods_skus_data) : null;
      
      return {
        ...log,
        goods_spu_data: goodsSpuData,
        goods_skus_data: goodsSkusData
      };
    } catch (error) {
      console.error('获取商品删除日志详情失败:', error);
      throw error;
    }
  }
}

module.exports = GoodsDeleteLogModel;
