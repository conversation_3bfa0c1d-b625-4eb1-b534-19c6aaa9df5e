const prismaManager = require('../../../core/prisma');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

/**
 * 将 snake_case 格式的字符串转换为 camelCase 格式
 * @param {string} str snake_case 格式的字符串
 * @returns {string} camelCase 格式的字符串
 */
function snakeToCamel(str) {
  return str.replace(/(_\w)/g, match => match[1].toUpperCase());
}

/**
 * 将对象的键从 snake_case 格式转换为 camelCase 格式
 * @param {Object} obj 要转换的对象
 * @returns {Object} 转换后的对象
 */
function convertKeysToCamelCase(obj) {
  if (obj === null || obj === undefined) return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(item => convertKeysToCamelCase(item));
  }
  
  if (typeof obj === 'object' && !(obj instanceof Date)) {
    const result = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        const camelKey = snakeToCamel(key);
        result[camelKey] = convertKeysToCamelCase(obj[key]);
      }
    }
    return result;
  }
  
  return obj;
}

/**
 * 处理BigInt序列化问题
 * @param {Object} data 要处理的数据
 * @returns {Object} 处理后的数据
 */
function handleBigInt(data) {
  if (data === null || data === undefined) return data;
  
  if (typeof data === 'bigint') {
    // 对于id字段，返回字符串形式，避免精度丢失
    return data.toString();
  }
  
  if (Array.isArray(data)) {
    return data.map(item => handleBigInt(item));
  }
  
  if (typeof data === 'object' && !(data instanceof Date)) {
    const result = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        // 如果是id相关字段且值为bigint，返回字符串
        if ((key === 'id' || key.endsWith('_id')) && typeof data[key] === 'bigint') {
          result[key] = data[key].toString();
        } else {
          result[key] = handleBigInt(data[key]);
        }
      }
    }
    return result;
  }
  
  return data;
}

class GoodsBrandModel {
  // 私有实例
  static #instance;
  
  /**
   * 获取GoodsBrandModel的单例实例
   * @returns {GoodsBrandModel} GoodsBrandModel实例
   */
  static getInstance() {
    if (!this.#instance) {
      this.#instance = new GoodsBrandModel();
    }
    return this.#instance;
  }

  /**
   * 构造函数 - 私有
   */
  constructor() {
    this.prisma = prismaManager.getClient('base');
  }

  /**
   * 获取所有商品品牌
   * @param {Object} options 查询选项
   * @param {string} options.name 品牌名称（模糊搜索）
   * @param {string} options.description 品牌描述（模糊搜索）
   * @param {string} options.sortField 排序字段
   * @param {string} options.sortOrder 排序方式（asc/desc）
   * @param {string} options.startTime 创建时间范围开始
   * @param {string} options.endTime 创建时间范围结束
   * @param {number} options.page 页码
   * @param {number} options.pageSize 每页数量
   * @returns {Promise<Object>} 返回品牌列表和总数
   */
  async getAllGoodsBrands(options = {}) {
    try {
      // 构建查询条件
      const whereCondition = {
        deleted_at: null // 只获取未删除的品牌
      };
      
      // 如果提供了名称搜索参数，添加模糊搜索条件
      if (options.name) {
        whereCondition.name = {
          contains: options.name,
          mode: 'insensitive' // 不区分大小写
        };
      }
      
      // 如果提供了描述搜索参数，添加模糊搜索条件
      if (options.description) {
        whereCondition.description = {
          contains: options.description,
          mode: 'insensitive' // 不区分大小写
        };
      }
      
      // 如果提供了创建时间范围，添加时间范围条件
      if (options.startTime || options.endTime) {
        whereCondition.created_at = {};
        
        if (options.startTime) {
          // 直接使用毫秒级时间戳，并作为BigInt处理
          const startTimeMs = BigInt(options.startTime);
          whereCondition.created_at.gte = startTimeMs;
        }
        
        if (options.endTime) {
          // 直接使用毫秒级时间戳，并作为BigInt处理
          const endTimeMs = BigInt(options.endTime);
          whereCondition.created_at.lte = endTimeMs;
        }
      }
      
      // 分页参数
      const page = options.page || 1;
      const pageSize = options.pageSize || 10;
      const skip = (page - 1) * pageSize;
      
      // 排序参数
      const sortField = options.sortField || 'id';
      const sortOrder = options.sortOrder || 'asc';
      
      // 构建排序对象
      const orderBy = {};
      // 将驼峰命名转换为下划线命名
      const dbSortField = sortField.replace(/([A-Z])/g, '_$1').toLowerCase();
      orderBy[dbSortField] = sortOrder.toLowerCase();
      
      // 从数据库获取品牌和总数
      const [brands, total] = await Promise.all([
        this.prisma.goodsBrand.findMany({
          where: whereCondition,
          orderBy,
          skip,
          take: pageSize
        }),
        this.prisma.goodsBrand.count({
          where: whereCondition
        })
      ]);
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(brands);
      const processedBrands = convertKeysToCamelCase(processedData);
      
      return {
        data: processedBrands,
        total: Number(total)
      };
    } catch (error) {
      console.error('获取商品品牌失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取品牌
   * @param {number} id - 品牌ID
   * @returns {Promise<Object|null>} 返回品牌对象或null
   */
  async getGoodsBrandById(id) {
    try {
      
      // 使用Prisma客户端查询
      const brand = await this.prisma.goodsBrand.findUnique({
        where: { 
          id: BigInt(id)
        }
      });
      
      if (!brand) return null;
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(brand);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error(`获取ID为${id}的品牌失败:`, error);
      console.error('错误堆栈:', error.stack);
      return null;
    }
  }

  /**
   * 添加商品品牌
   * @param {Object} brandData 品牌数据
   * @param {BigInt|null} userId 用户ID
   * @returns {Promise<Object>} 返回新创建的品牌
   */
  async addGoodsBrand(brandData, userId) {
    try {
      // 生成雪花ID
      const brandId = generateSnowflakeId();
      
      // 创建品牌
      const newBrand = await this.prisma.goodsBrand.create({
        data: {
          id: brandId,
          name: brandData.name,
          logo_url: brandData.logo_url || null,
          description: brandData.description || null,
          created_by: userId ? BigInt(userId) : null,
          updated_by: userId ? BigInt(userId) : null
        }
      });
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(newBrand);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error('添加品牌失败:', error);
      throw error;
    }
  }

  /**
   * 更新商品品牌
   * @param {number} id 品牌ID
   * @param {Object} brandData 品牌数据
   * @param {BigInt|null} userId 用户ID
   * @returns {Promise<Object>} 返回更新后的品牌
   */
  async updateGoodsBrand(id, brandData, userId) {
    try {
      const brandId = BigInt(id);
      
      // 首先检查品牌是否存在
      const existingBrand = await this.prisma.goodsBrand.findUnique({
        where: { id: brandId }
      });
      
      if (!existingBrand) {
        throw new Error(`ID为${id}的品牌不存在`);
      }
      
      // 更新品牌
      const updatedBrand = await this.prisma.goodsBrand.update({
        where: { id: brandId },
        data: {
          name: brandData.name,
          logo_url: brandData.logo_url,
          description: brandData.description,
          updated_at: BigInt(Date.now()), // 使用 BigInt 类型存储当前时间戳
          updated_by: userId ? BigInt(userId) : null
        }
      });
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(updatedBrand);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error(`更新ID为${id}的品牌失败:`, error);
      throw error;
    }
  }

  /**
   * 删除商品品牌
   * @param {number} id 品牌ID
   * @returns {Promise<Object>} 返回删除结果
   */
  async deleteGoodsBrand(id) {
    try {
      const brandId = BigInt(id);
      
      // 检查是否有关联的商品
      const relatedProducts = await this.prisma.goodsSpu.findMany({
        where: {
          goods_brand_id: brandId,
          deleted_at: null
        }
      });
      
      if (relatedProducts.length > 0) {
        throw new Error('该品牌下有关联商品，无法删除');
      }
      
      // 软删除品牌
      const deletedBrand = await this.prisma.goodsBrand.update({
        where: { id: brandId },
        data: { deleted_at: BigInt(Date.now()) } // 使用 BigInt 类型存储当前时间戳
      });
      
      // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
      const processedData = handleBigInt(deletedBrand);
      return convertKeysToCamelCase(processedData);
    } catch (error) {
      console.error(`删除ID为${id}的品牌失败:`, error);
      throw error;
    }
  }
}

module.exports = GoodsBrandModel;
