const prismaManager = require('../../../core/prisma');

/**
 * 将 snake_case 格式的字符串转换为 camelCase 格式
 * @param {string} str snake_case 格式的字符串
 * @returns {string} camelCase 格式的字符串
 */
function snakeToCamel(str) {
  return str.replace(/(_\w)/g, match => match[1].toUpperCase());
}

/**
 * 将对象的键从 snake_case 格式转换为 camelCase 格式
 * @param {Object} obj 要转换的对象
 * @returns {Object} 转换后的对象
 */
function convertKeysToCamelCase(obj) {
  if (obj === null || obj === undefined) return obj;
  
  if (Array.isArray(obj)) {
    return obj.map(item => convertKeysToCamelCase(item));
  }
  
  if (typeof obj === 'object' && !(obj instanceof Date)) {
    const result = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        const camelKey = snakeToCamel(key);
        result[camelKey] = convertKeysToCamelCase(obj[key]);
      }
    }
    return result;
  }
  
  return obj;
}

/**
 * 处理BigInt序列化问题
 * @param {Object} data 要处理的数据
 * @returns {Object} 处理后的数据
 */
function handleBigInt(data) {
  if (data === null || data === undefined) return data;
  
  if (typeof data === 'bigint') {
    return Number(data);
  }
  
  if (Array.isArray(data)) {
    return data.map(item => handleBigInt(item));
  }
  
  if (typeof data === 'object' && !(data instanceof Date)) {
    const result = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        result[key] = handleBigInt(data[key]);
      }
    }
    return result;
  }
  
  return data;
}

/**
 * 商品图片模型
 */
class GoodsImageModel {
  constructor() {
    this.prisma = prismaManager.getClient('base');
  }

  /**
   * 根据SPU ID获取商品图片
   * @param {string|number} spuId 商品SPU ID
   * @returns {Promise<Array>} 商品图片列表
   */
  async getImagesBySpuId(spuId) {
    try {
      const images = await this.prisma.goodsImage.findMany({
        where: {
          goods_spu_id: BigInt(spuId),
          goods_sku_id: null
        },
        orderBy: {
          sort_order: 'asc'
        }
      });
      
      return convertKeysToCamelCase(handleBigInt(images));
    } catch (error) {
      console.error(`获取商品SPU(ID:${spuId})的图片失败:`, error);
      throw error;
    }
  }

  /**
   * 根据SKU ID获取商品图片
   * @param {string|number} skuId 商品SKU ID
   * @returns {Promise<Array>} 商品图片列表
   */
  async getImagesBySkuId(skuId) {
    try {
      const images = await this.prisma.goodsImage.findMany({
        where: {
          goods_sku_id: BigInt(skuId)
        },
        orderBy: {
          sort_order: 'asc'
        }
      });
      
      return convertKeysToCamelCase(handleBigInt(images));
    } catch (error) {
      console.error(`获取商品SKU(ID:${skuId})的图片失败:`, error);
      throw error;
    }
  }

  /**
   * 添加商品图片
   * @param {Object} imageData 图片数据
   * @returns {Promise<Object>} 添加的图片
   */
  async addImage(imageData) {
    try {
      const image = await this.prisma.goodsImage.create({
        data: {
          goods_spu_id: BigInt(imageData.goodsSpuId),
          goods_sku_id: imageData.goodsSkuId ? BigInt(imageData.goodsSkuId) : null,
          image_url: imageData.imageUrl,
          alt_text: imageData.altText || null,
          sort_order: imageData.sortOrder || 0,
          is_default: imageData.isDefault || false
        }
      });
      
      return convertKeysToCamelCase(handleBigInt(image));
    } catch (error) {
      console.error('添加商品图片失败:', error);
      throw error;
    }
  }

  /**
   * 更新商品图片
   * @param {string|number} id 图片ID
   * @param {Object} imageData 图片数据
   * @returns {Promise<Object>} 更新后的图片
   */
  async updateImage(id, imageData) {
    try {
      const image = await this.prisma.goodsImage.update({
        where: {
          id: BigInt(id)
        },
        data: {
          image_url: imageData.imageUrl,
          alt_text: imageData.altText,
          sort_order: imageData.sortOrder,
          is_default: imageData.isDefault
        }
      });
      
      return convertKeysToCamelCase(handleBigInt(image));
    } catch (error) {
      console.error(`更新商品图片(ID:${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 删除商品图片
   * @param {string|number} id 图片ID
   * @returns {Promise<void>}
   */
  async deleteImage(id) {
    try {
      await this.prisma.goodsImage.delete({
        where: {
          id: BigInt(id)
        }
      });
    } catch (error) {
      console.error(`删除商品图片(ID:${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 设置商品默认图片
   * @param {string|number} spuId 商品SPU ID
   * @param {string|number} imageId 图片ID
   * @returns {Promise<void>}
   */
  async setDefaultImage(spuId, imageId) {
    try {
      // 先将所有图片设为非默认
      await this.prisma.goodsImage.updateMany({
        where: {
          goods_spu_id: BigInt(spuId)
        },
        data: {
          is_default: false
        }
      });
      
      // 将指定图片设为默认
      await this.prisma.goodsImage.update({
        where: {
          id: BigInt(imageId)
        },
        data: {
          is_default: true
        }
      });
    } catch (error) {
      console.error(`设置商品默认图片失败:`, error);
      throw error;
    }
  }
}

module.exports = new GoodsImageModel();
