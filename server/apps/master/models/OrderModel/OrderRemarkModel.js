/**
 * 订单备注模型
 */
const { prisma } = require('../../../../core/database/prisma');
const { recursiveSnakeToCamel } = require('../../../../shared/utils/format');

/**
 * 处理 BigInt 类型的数据，将其转换为字符串
 * @param {*} data - 需要处理的数据
 * @returns {*} - 处理后的数据
 */
const handleBigInt = (data) => {
  if (data === null || data === undefined) return data;
  if (typeof data === 'bigint') return data.toString();
  if (Array.isArray(data)) return data.map(item => handleBigInt(item));
  if (typeof data === 'object') {
    const newData = {};
    for (const key in data) {
      newData[key] = handleBigInt(data[key]);
    }
    return newData;
  }
  return data;
};

class OrderRemarkModel {
  /**
   * 构造函数
   */
  constructor() {
    this.prisma = prisma;
  }

  /**
   * 确保ID是BigInt类型
   * @param {string|number|BigInt} id - 需要转换的ID
   * @returns {BigInt} - 转换后的BigInt类型ID
   */
  ensureBigInt(id) {
    if (typeof id === 'bigint') return id;
    return BigInt(id);
  }

  /**
   * 获取订单备注列表
   * @param {string|number|BigInt} orderId - 订单ID
   * @returns {Promise<Array>} - 订单备注列表
   */
  async getOrderRemarks(orderId) {
    try {
      const orderIdBigInt = this.ensureBigInt(orderId);
      
      // 1. 首先获取备注列表
      const remarks = await this.prisma.order_remarks.findMany({
        where: {
          order_id: orderIdBigInt,
          deleted_at: null
        },
        orderBy: {
          created_at: 'desc'
        }
      });

      // 2. 收集所有的创建者和更新者ID
      const userIds = new Set();
      remarks.forEach(remark => {
        if (remark.created_by) userIds.add(remark.created_by.toString());
        if (remark.updated_by) userIds.add(remark.updated_by.toString());
      });
      
      // 3. 一次性查询所有相关用户
      const users = {};
      if (userIds.size > 0) {
        const userIdsArray = Array.from(userIds).map(id => BigInt(id));
        const userRecords = await this.prisma.BaseSystemUser.findMany({
          where: {
            id: {
              in: userIdsArray
            }
          },
          select: {
            id: true,
            username: true
          }
        });
        
        // 将用户记录转换为映射对象，供快速查找
        userRecords.forEach(user => {
          users[user.id.toString()] = user.username;
        });
      }
      
      // 4. 处理 BigInt
      const processedRemarks = handleBigInt(remarks);
      
      // 5. 先转换为驼峰格式
      const camelCaseRemarks = recursiveSnakeToCamel(processedRemarks);
      
      // 6. 添加用户名字段
      const formattedRemarks = camelCaseRemarks.map(remark => {
        return {
          ...remark,
          createdByName: remark.createdBy ? users[remark.createdBy] || null : null,
          updatedByName: remark.updatedBy ? users[remark.updatedBy] || null : null
        };
      });

      return formattedRemarks;
    } catch (error) {
      console.error('获取订单备注列表失败:', error);
      throw error;
    }
  }


}

module.exports = new OrderRemarkModel();
