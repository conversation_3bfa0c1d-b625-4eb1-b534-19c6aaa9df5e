/**
 * 订单模型
 */
const { prisma } = require("../../../../core/database/prisma");
const { generateSnowflakeId } = require("../../../../shared/utils/snowflake");
const PaymentMethodEnum = require("../../constants/PaymentMethodEnum");
const OrderStatusEnum = require("../../constants/OrderStatusEnum");
const PaymentStatusEnum = require("../../constants/PaymentStatusEnum");
const ShippingStatusEnum = require("../../constants/ShippingStatusEnum");
const OrderFollowerModel = require("./OrderFollowerModel");

/**
 * 处理 BigInt 类型的数据，将其转换为字符串
 * @param {*} data - 需要处理的数据
 * @returns {*} - 处理后的数据
 */
const handleBigInt = (data) => {
  if (data === null || data === undefined) return data;
  if (typeof data === "bigint") return data.toString();
  if (Array.isArray(data)) return data.map((item) => handleBigInt(item));
  if (typeof data === "object") {
    const newData = {};
    for (const key in data) {
      newData[key] = handleBigInt(data[key]);
    }
    return newData;
  }
  return data;
};

/**
 * 将下划线命名法转换为驼峰命名法
 * @param {*} data - 需要处理的数据
 * @returns {*} - 处理后的数据
 */
const convertKeysToCamelCase = (data) => {
  if (data === null || data === undefined) return data;
  if (typeof data !== "object" || data instanceof Date) return data;

  if (Array.isArray(data)) {
    return data.map((item) => convertKeysToCamelCase(item));
  }

  const newData = {};
  for (const key in data) {
    const camelCaseKey = key.replace(/_([a-z])/g, (_, letter) =>
      letter.toUpperCase()
    );
    newData[camelCaseKey] = convertKeysToCamelCase(data[key]);
  }
  return newData;
};

class OrderModel {
  /**
   * 构造函数
   */
  constructor() {
    this.prisma = prisma;
  }

  /**
   * 确保ID是BigInt类型
   * @param {string|number|BigInt} id - 需要转换的ID
   * @returns {BigInt} - 转换后的BigInt类型ID
   */
  ensureBigInt(id) {
    if (typeof id === "bigint") return id;
    return BigInt(id);
  }

  /**
   * 获取订单列表
   * @param {Object} filters - 订单主表过滤条件
   * @param {number} page - 页码
   * @param {number} pageSize - 每页数量
   * @param {Object} orderBy - 排序条件
   * @param {Object} shippingFilters - 收货信息过滤条件
   * @param {Object} itemFilters - 订单项过滤条件
   * @returns {Promise<Array>} - 订单列表
   */
  async getOrders(
    filters = {},
    page = 1,
    pageSize = 10,
    orderBy = { created_at: "desc" },
    shippingFilters = {},
    itemFilters = {}
  ) {
    try {
      const skip = (page - 1) * pageSize;
      const take = pageSize;

      // 从过滤条件中删除分页和排序参数，防止它们被错误地放入 where 条件
      const {
        page: _,
        pageSize: __,
        sortField,
        sortOrder,
        ...validFilters
      } = filters;

      // 构建查询条件
      const where = {
        deleted_at: null, // 只获取未删除的订单
        ...validFilters,
      };

      // 添加额外的过滤条件处理
      if (validFilters.orderSn) {
        where.id = validFilters.orderSn; // 使用id替代order_sn作为订单编号
        delete where.orderSn; // 删除原始属性，避免重复
      }

      if (validFilters.userId) {
        where.user_id = BigInt(validFilters.userId);
        delete where.userId; // 删除原始属性，避免重复
      }

      if (validFilters.orderStatus !== undefined) {
        where.order_status = parseInt(validFilters.orderStatus);
        delete where.orderStatus; // 删除原始属性，避免重复
      }

      if (validFilters.paymentStatus !== undefined) {
        where.payment_status = parseInt(validFilters.paymentStatus);
        delete where.paymentStatus; // 删除原始属性，避免重复
      }

      if (validFilters.shippingStatus !== undefined) {
        where.shipping_status = parseInt(validFilters.shippingStatus);
        delete where.shippingStatus; // 删除原始属性，避免重复
      }

      if (validFilters.startDate && validFilters.endDate) {
        where.created_at = {
          gte: BigInt(new Date(validFilters.startDate).getTime()),
          lte: BigInt(new Date(validFilters.endDate).getTime()),
        };
        delete where.startDate;
        delete where.endDate;
      }

      // 构建收货信息的过滤条件
      let shippingWhere = {};
      if (Object.keys(shippingFilters).length > 0) {
        shippingWhere = shippingFilters;
      }

      // 构建订单项的过滤条件
      let itemsWhere = {};
      if (Object.keys(itemFilters).length > 0) {
        itemsWhere = itemFilters;
      }

      // 查询符合商品名称等条件的订单ID
      let orderIdsWithMatchedItems = [];
      if (Object.keys(itemFilters).length > 0) {
        const matchedItems = await this.prisma.order_items.findMany({
          where: itemsWhere,
          select: {
            order_id: true,
          },
        });
        orderIdsWithMatchedItems = matchedItems.map((item) => item.order_id);
        if (orderIdsWithMatchedItems.length > 0) {
          where.id = { in: orderIdsWithMatchedItems };
        } else {
          // 如果没有匹配的订单项，返回空数组
          return [];
        }
      }

      // 收货信息筛选
      if (Object.keys(shippingFilters).length > 0) {
        // 构建具有关联查询条件
        where.order_shipping_info = shippingWhere;
      }

      // 查询订单总数
      const total = await this.prisma.orders.count({ where });

      // 查询订单列表 - 包含收货信息和包裹信息
      const orders = await this.prisma.orders.findMany({
        where,
        skip,
        take,
        orderBy,
        select: {
          id: true,
          user_id: true,
          channel_id: true,
          platform_id: true,
          store_id: true,
          order_status: true,
          payment_status: true,
          shipping_status: true,
          invoice_status: true, // 添加开票状态字段
          payment_method_id: true,
          total_amount: true, // 使用正确的字段名 total_amount 而不是 order_amount
          total_product_amount: true, // 添加商品总金额字段
          paid_amount: true,
          discount_amount: true,
          shipping_fee: true,
          tax_amount: true,
          third_party_platform_id: true,
          third_party_order_sn: true,
          order_type: true,
          order_source: true,
          remark: true,
          payment_method: true, // 添加支付方式字段
          created_at: true,
          updated_at: true,
          deleted_at: true,
          order_shipping_info: true,
          order_packages: {
            select: {
              id: true,
              package_sn: true,
              shipping_method: true,
              shipping_status: true,
              shipping_company_code: true,
              shipping_company_name: true,
              tracking_number: true,
              shipped_at: true,
            },
          },
        },
      });

      // 继续处理订单数据

      // 创建OrderFollowerModel实例，用于获取跟单员信息
      const orderFollowerModel = new OrderFollowerModel();

      // 单独查询每个订单的订单项和跟单员信息
      const ordersWithItems = await Promise.all(
        orders.map(async (order) => {
          // 查询订单项
          const orderItems = await this.prisma.order_items.findMany({
            where: {
              order_id: order.id,
            },
            // 只选择列表页面需要的字段，减少返回数据量
            select: {
              id: true,
              order_id: true,
              product_name: true,
              product_image: true,
              unit_price: true,
              quantity: true,
              total_price: true,
              spu_name_snapshot: true,
              sku_specifications: true,
              third_party_product_code: true,
              // 新增字段：商品SPU ID、商品SKU ID、第三方平台SPU ID、第三方平台SKU ID
              goods_spu_id: true,
              goods_sku_id: true,
              third_party_spu_id: true,
              third_party_sku_id: true,
            },
          });

          // 查询订单的跟单员信息
          const orderFollowers = await orderFollowerModel.getOrderFollowers(
            order.id
          );

          // 处理包裹信息，转换配送方式为可读文本
          const processedPackages = order.order_packages
            ? order.order_packages.map((pkg) => ({
                id: String(pkg.id),
                packageSn: pkg.package_sn,
                shippingMethod: pkg.shipping_method,
                shippingMethodText: this.getShippingMethodText(
                  pkg.shipping_method
                ),
                shippingStatus: pkg.shipping_status,
                shippingStatusText: this.getPackageShippingStatusText(
                  pkg.shipping_status
                ),
                shippingCompanyCode: pkg.shipping_company_code,
                shippingCompanyName: pkg.shipping_company_name,
                trackingNumber: pkg.tracking_number,
                shippedAt: pkg.shipped_at ? String(pkg.shipped_at) : null,
              }))
            : [];

          return {
            ...order,
            orderItems,
            orderFollowers,
            packages: processedPackages,
          };
        })
      );

      // 获取所有渠道信息
      const channels = await this.prisma.channel.findMany({
        where: {
          deleted_at: null,
        },
        select: {
          id: true,
          name: true,
          icon_url: true,
        },
      });

      // 获取所有用到的平台ID和店铺ID
      const platformIds = [
        ...new Set(
          ordersWithItems.map((order) => order.platform_id).filter(Boolean)
        ),
      ];
      const storeIds = [
        ...new Set(
          ordersWithItems.map((order) => order.store_id).filter(Boolean)
        ),
      ];

      // 使用原生SQL查询平台信息
      let platforms = [];
      if (platformIds.length > 0) {
        try {
          // 使用$queryRawUnsafe来避免参数化查询的问题
          const platformQuery = `
            SELECT id, name, code FROM base.platform
            WHERE id IN (${platformIds.map((id) => `'${id}'`).join(",")})
            AND deleted_at IS NULL
          `;

          platforms = await this.prisma.$queryRawUnsafe(platformQuery);
        } catch (error) {
          console.error(`平台查询失败:`, error.message);
        }
      }

      // 使用原生SQL查询店铺信息
      let stores = [];
      if (storeIds.length > 0) {
        try {
          const storeQuery = `
            SELECT id, name, code FROM base.store
            WHERE id IN (${storeIds.map((id) => `'${id}'`).join(",")})
            AND deleted_at IS NULL
          `;

          stores = await this.prisma.$queryRawUnsafe(storeQuery);
        } catch (error) {
          console.error(`店铺查询失败:`, error.message);
        }
      }

      // 处理返回数据格式
      const formattedOrders = ordersWithItems.map((order) => {
        try {
          // 处理BigInt序列化问题
          const processedOrder = handleBigInt(order);

          // 将字段名转换为 camelCase 格式
          const camelCaseOrder = convertKeysToCamelCase(processedOrder);

          // 关联渠道信息
          let channel = null;

          // 1. 先尝试使用channel_id关联渠道表
          if (order.channel_id) {
            const channelId = order.channel_id;

            // 尝试精确匹配
            channel = channels.find((c) => c.id === channelId);

            // 如果没有精确匹配到，尝试将字符串转换为数字再匹配
            if (!channel && !isNaN(Number(channelId))) {
              channel = channels.find(
                (c) => c.id === Number(channelId).toString()
              );
            }
          }

          // 2. 如果没有匹配到，尝试使用order_source匹配
          if (!channel && order.order_source) {
            const orderSource = order.order_source;

            // 尝试精确匹配
            channel = channels.find((c) => c.id === orderSource?.toString());

            // 如果没有精确匹配到，尝试将字符串转换为数字再匹配
            if (!channel && !isNaN(Number(orderSource))) {
              channel = channels.find(
                (c) => c.id === Number(orderSource).toString()
              );
            }
          }

          // 3. 如果还是没有匹配到，尝试使用默认渠道
          if (!channel && channels.length > 0) {
            // 尝试找到内置渠道作为默认渠道
            channel = channels.find((c) => c.is_built_in === 1);

            // 如果没有内置渠道，则使用第一个渠道
            if (!channel) {
              channel = channels[0];
            }
          }

          // 添加渠道信息
          camelCaseOrder.channelId = channel?.id || null;
          camelCaseOrder.channelName = channel?.name || null;
          camelCaseOrder.channelIconUrl = channel?.icon_url || null;

          // 添加平台信息（平铺结构）
          if (order.platform_id) {
            const platform = platforms.find((p) => p.id === order.platform_id);
            camelCaseOrder.platformName = platform ? platform.name : null;
            console.log(
              `[订单列表-调试] 订单${order.id}的平台信息: ID=${order.platform_id}, Name=${camelCaseOrder.platformName}`
            );
          } else {
            camelCaseOrder.platformName = null;
          }

          // 添加店铺信息（平铺结构）
          if (order.store_id) {
            const store = stores.find((s) => s.id === order.store_id);
            camelCaseOrder.storeName = store ? store.name : null;
            console.log(
              `[订单列表-调试] 订单${order.id}的店铺信息: ID=${order.store_id}, Name=${camelCaseOrder.storeName}`
            );
          } else {
            camelCaseOrder.storeName = null;
          }

          // 添加状态文本
          camelCaseOrder.orderStatusText = this.getOrderStatusText(
            camelCaseOrder.orderStatus
          );
          camelCaseOrder.paymentStatusText = this.getPaymentStatusText(
            camelCaseOrder.paymentStatus
          );
          camelCaseOrder.shippingStatusText = this.getShippingStatusText(
            camelCaseOrder.shippingStatus
          );
          camelCaseOrder.orderTypeText = this.getOrderTypeText(
            camelCaseOrder.orderType
          );
          camelCaseOrder.orderSourceText = this.getOrderSourceText(
            camelCaseOrder.orderSource
          );

          // 添加支付方式文本
          if (camelCaseOrder.paymentMethodId) {
            camelCaseOrder.paymentMethodText = PaymentMethodEnum.getName(
              parseInt(camelCaseOrder.paymentMethodId)
            );
          } else {
            // 兼容原有数据，如果没有paymentMethodId，则使用默认值
            camelCaseOrder.paymentMethodText = "未设置";
          }

          // 移除payment_method字段，使用paymentMethodText代替
          delete camelCaseOrder.paymentMethod;

          // 处理订单项 - 简化数据
          if (camelCaseOrder.orderItems) {
            camelCaseOrder.items = camelCaseOrder.orderItems.map((item) =>
              convertKeysToCamelCase(handleBigInt(item))
            );
            delete camelCaseOrder.orderItems;
          } else {
            camelCaseOrder.items = [];
          }

          // 处理配送信息 - 返回完整的收货人和地址信息
          if (camelCaseOrder.orderShippingInfo) {
            const basicShippingInfo = {
              id: camelCaseOrder.orderShippingInfo.id,
              shipping_company:
                camelCaseOrder.orderShippingInfo.shipping_company_name,
              tracking_number: camelCaseOrder.orderShippingInfo.tracking_number,
              shipping_status: camelCaseOrder.orderShippingInfo.shipping_status,
              // 添加收货人信息
              recipient_name: camelCaseOrder.orderShippingInfo.recipient_name,
              recipient_phone: camelCaseOrder.orderShippingInfo.recipient_phone,
              // 添加收货地址信息
              region_path_name:
                camelCaseOrder.orderShippingInfo.region_path_name,
              street_address: camelCaseOrder.orderShippingInfo.street_address,
              postal_code: camelCaseOrder.orderShippingInfo.postal_code,
              shipping_method: camelCaseOrder.orderShippingInfo.shipping_method,
            };
            camelCaseOrder.shipping = convertKeysToCamelCase(
              handleBigInt(basicShippingInfo)
            );
            delete camelCaseOrder.orderShippingInfo;
          }

          // 移除订单日志，只在订单详情中显示
          if (camelCaseOrder.orderLogs) {
            delete camelCaseOrder.orderLogs;
          }

          // 处理跟单员信息
          if (
            camelCaseOrder.orderFollowers &&
            camelCaseOrder.orderFollowers.length > 0
          ) {
            // 将跟单员信息格式化为简化的形式，只包含 ID 和用户名
            camelCaseOrder.followers = camelCaseOrder.orderFollowers.map(
              (follower) => ({
                id: follower.followerId,
                name: follower.followerName || "",
              })
            );
            delete camelCaseOrder.orderFollowers;
          } else {
            camelCaseOrder.followers = [];
          }

          // 处理包裹信息
          if (camelCaseOrder.packages && camelCaseOrder.packages.length > 0) {
            // 将包裹信息转换为驼峰命名格式
            camelCaseOrder.packages = camelCaseOrder.packages.map((pkg) =>
              convertKeysToCamelCase(handleBigInt(pkg))
            );
          } else {
            camelCaseOrder.packages = [];
          }

          // 处理第三方平台信息
          if (camelCaseOrder.thirdPartyPlatform) {
            camelCaseOrder.platformInfo = {
              id: camelCaseOrder.thirdPartyPlatform.id,
              platformCode: camelCaseOrder.thirdPartyPlatform.platformCode,
              platformName: camelCaseOrder.thirdPartyPlatform.platformName,
              description: camelCaseOrder.thirdPartyPlatform.description,
            };
            delete camelCaseOrder.thirdPartyPlatform;
          }

          // 处理第三方子实体信息
          if (camelCaseOrder.thirdPartySubEntity) {
            camelCaseOrder.subEntityInfo = {
              id: camelCaseOrder.thirdPartySubEntity.id,
              platformId: camelCaseOrder.thirdPartySubEntity.platformId,
              subEntityIdentifier:
                camelCaseOrder.thirdPartySubEntity.subEntityIdentifier,
              subEntityName: camelCaseOrder.thirdPartySubEntity.subEntityName,
              subEntityType: camelCaseOrder.thirdPartySubEntity.subEntityType,
              description: camelCaseOrder.thirdPartySubEntity.description,
            };
            delete camelCaseOrder.thirdPartySubEntity;
          }

          return camelCaseOrder;
        } catch (err) {
          console.error("处理订单数据错误:", err);
          // 返回原始数据，确保不会因为处理错误而导致数据丢失
          return order;
        }
      });

      return formattedOrders;
    } catch (error) {
      console.error("获取订单列表失败:", error);
      throw error;
    }
  }

  /**
   * 统计订单数量
   * @param {Object} filters - 过滤条件
   * @returns {Promise<number>} - 订单数量
   */
  async countOrders(filters = {}) {
    try {
      // 构建查询条件，去除可能的分页和排序参数
      const { page, pageSize, sortField, sortOrder, ...validFilters } = filters;

      const where = {
        deleted_at: null, // 只统计未删除的订单
        ...validFilters,
      };

      // 查询订单数量
      const count = await this.prisma.orders.count({ where });
      return count;
    } catch (error) {
      console.error("统计订单数量失败:", error);
      throw error;
    }
  }

  /**
   * 获取订单状态文本
   * @param {number} status - 订单状态码
   * @returns {string} - 订单状态文本
   */
  getOrderStatusText(status) {
    return OrderStatusEnum.getStatusName(status);
  }

  /**
   * 获取支付状态文本
   * @param {number} status - 支付状态码
   * @returns {string} - 支付状态文本
   */
  getPaymentStatusText(status) {
    return PaymentStatusEnum.getStatusName(status);
  }

  /**
   * 获取发货状态文本
   * @param {number} status - 发货状态码
   * @returns {string} - 发货状态文本
   */
  getShippingStatusText(status) {
    return ShippingStatusEnum.getStatusName(status);
  }

  /**
   * 获取订单类型文本
   * @param {string} type - 订单类型代码
   * @returns {string} - 订单类型文本
   */
  getOrderTypeText(type) {
    const typeMap = {
      normal: "普通订单",
      wholesale: "批发订单",
      gift: "赠品订单",
      sample: "样品订单",
      replacement: "补发订单",
    };
    return typeMap[type] || "未知类型";
  }

  /**
   * 获取订单来源文本
   * @param {string} source - 订单来源代码
   * @returns {string} - 订单来源文本
   */
  getOrderSourceText(source) {
    const sourceMap = {
      system: "系统",
      web: "网站",
      app: "APP",
      wechat: "微信",
      third_party: "第三方",
      offline: "线下",
    };
    return sourceMap[source] || "未知";
  }

  /**
   * 获取操作类型文本
   * @param {string} operationType - 操作类型代码
   * @returns {string} - 操作类型文本
   */
  getOperationTypeText(operationType) {
    const typeMap = {
      create: "创建订单",
      update: "更新订单",
      status_change: "状态变更",
      payment: "支付操作",
      shipping: "物流操作",
      cancel: "取消订单",
      refund: "退款操作",
      remark: "备注修改",
      admin_edit: "管理员编辑",
    };
    return typeMap[operationType] || "其他操作";
  }

  /**
   * 获取配送方式文本
   * @param {number} method - 配送方式代码
   * @returns {string} - 配送方式文本
   */
  getShippingMethodText(method) {
    const methodMap = {
      1: "快递物流",
      2: "自定义物流",
      3: "商家自送",
      4: "线下自取",
      5: "无需物流",
    };
    return methodMap[method] || "未知配送方式";
  }

  /**
   * 获取包裹发货状态文本
   * @param {number} status - 包裹状态代码
   * @returns {string} - 包裹状态文本
   */
  getPackageShippingStatusText(status) {
    const statusMap = {
      0: "待发货",
      1: "已发货",
      2: "已收货",
    };
    return statusMap[status] || "未知状态";
  }

  /**
   * 根据ID获取订单详情
   * @param {string|number|BigInt} id - 订单ID
   * @returns {Promise<Object>} - 订单详情
   */
  async getOrderById(id) {
    try {
      const orderId = this.ensureBigInt(id);

      const order = await this.prisma.orders.findFirst({
        where: {
          id: orderId,
          deleted_at: null, // 只获取未删除的订单
        },
        include: {
          // 使用select显式指定需要返回的order_shipping_info字段
          // 这样即使表结构变化也不会出错
          order_shipping_info: {
            select: {
              id: true,
              order_id: true,
              recipient_name: true,
              recipient_phone: true,
              region_province_id: true,
              region_city_id: true,
              region_district_id: true,
              region_path_name: true,
              street_address: true,
              postal_code: true,
              created_at: true,
              updated_at: true,
            },
          },
          order_items: true,
          payment_records: true,
          // 添加channel关联，获取渠道名称和图标
          channel: {
            select: {
              id: true,
              name: true,
              icon_url: true,
            },
          },
          // 不再包含订单日志
        },
      });

      if (!order) {
        return null;
      }

      // 获取订单项
      const orderItems = await this.prisma.order_items.findMany({
        where: {
          order_id: orderId,
        },
        select: {
          id: true,
          order_id: true,
          product_name: true,
          product_image: true,
          unit_price: true,
          quantity: true,
          total_price: true,
          sku_code: true,
          sku_specifications: true, // 使用正确的字段名
          item_paid_amount: true, // 使用正确的字段名
        },
      });

      // 获取订单业务员
      const orderSalespeople = await this.prisma.order_salespeople.findMany({
        where: {
          order_id: orderId,
        },
      });

      // 处理BigInt序列化问题
      const processedOrder = handleBigInt(order);
      const processedItems = orderItems.map((item) => handleBigInt(item));
      const processedSalespeople = orderSalespeople.map((sp) =>
        handleBigInt(sp)
      );

      // 将字段名转换为 camelCase 格式
      const camelCaseOrder = convertKeysToCamelCase(processedOrder);
      const camelCaseItems = processedItems.map((item) =>
        convertKeysToCamelCase(item)
      );
      const camelCaseSalespeople = processedSalespeople.map((sp) =>
        convertKeysToCamelCase(sp)
      );

      // 添加状态文本
      camelCaseOrder.orderStatusText = this.getOrderStatusText(
        camelCaseOrder.orderStatus
      );
      camelCaseOrder.paymentStatusText = this.getPaymentStatusText(
        camelCaseOrder.paymentStatus
      );
      camelCaseOrder.shippingStatusText = this.getShippingStatusText(
        camelCaseOrder.shippingStatus
      );
      camelCaseOrder.orderTypeText = this.getOrderTypeText(
        camelCaseOrder.orderType
      );
      camelCaseOrder.orderSourceText = this.getOrderSourceText(
        camelCaseOrder.orderSource
      );

      // 添加支付方式文本
      if (camelCaseOrder.paymentMethodId) {
        camelCaseOrder.paymentMethodText = PaymentMethodEnum.getName(
          parseInt(camelCaseOrder.paymentMethodId)
        );
      } else {
        // 兼容原有数据，如果没有paymentMethodId，则使用默认值
        camelCaseOrder.paymentMethodText = "未设置";
      }

      // 移除payment_method字段，使用paymentMethodText代替
      delete camelCaseOrder.paymentMethod;

      // 处理订单项
      camelCaseOrder.items = camelCaseItems;

      // 处理配送信息
      if (camelCaseOrder.orderShippingInfo) {
        camelCaseOrder.shipping = camelCaseOrder.orderShippingInfo;
        delete camelCaseOrder.orderShippingInfo;
      }

      // 不再处理订单日志
      if (camelCaseOrder.orderLogs) {
        delete camelCaseOrder.orderLogs;
      }

      // 处理销售人员信息
      camelCaseOrder.salespeople = camelCaseSalespeople;

      // 查询平台信息（平铺结构）
      if (camelCaseOrder.platformId) {
        try {
          const platformQuery = `
            SELECT id, name, code FROM base.platform
            WHERE id = '${camelCaseOrder.platformId}' AND deleted_at IS NULL
            LIMIT 1
          `;

          const platformResult = await this.prisma.$queryRawUnsafe(
            platformQuery
          );

          if (platformResult && platformResult.length > 0) {
            camelCaseOrder.platformName = platformResult[0].name;
          } else {
            camelCaseOrder.platformName = null;
          }
        } catch (error) {
          console.error(`平台查询失败:`, error.message);
          camelCaseOrder.platformName = null;
        }
      } else {
        camelCaseOrder.platformName = null;
      }

      // 查询店铺信息（平铺结构）
      if (camelCaseOrder.storeId) {
        try {
          const storeQuery = `
            SELECT id, name, code FROM base.store
            WHERE id = '${camelCaseOrder.storeId}' AND deleted_at IS NULL
            LIMIT 1
          `;

          const storeResult = await this.prisma.$queryRawUnsafe(storeQuery);

          if (storeResult && storeResult.length > 0) {
            camelCaseOrder.storeName = storeResult[0].name;
          } else {
            camelCaseOrder.storeName = null;
          }
        } catch (error) {
          console.error(`店铺查询失败:`, error.message);
          camelCaseOrder.storeName = null;
        }
      } else {
        camelCaseOrder.storeName = null;
      }

      // 处理第三方平台信息（保留原有逻辑）
      if (camelCaseOrder.thirdPartyPlatform) {
        camelCaseOrder.thirdPartyPlatformInfo = {
          id: camelCaseOrder.thirdPartyPlatform.id,
          platformCode: camelCaseOrder.thirdPartyPlatform.platformCode,
          platformName: camelCaseOrder.thirdPartyPlatform.platformName,
          description: camelCaseOrder.thirdPartyPlatform.description,
        };
        delete camelCaseOrder.thirdPartyPlatform;
      }

      // 第三方子实体信息已移除

      return camelCaseOrder;
    } catch (error) {
      console.error("获取订单详情失败:", error);
      throw error;
    }
  }

  /**
   * 创建订单
   * @param {Object} orderData - 订单数据
   * @returns {Promise<Object>} - 创建的订单
   */
  async createOrder(orderData) {
    try {
      // 使用事务确保数据一致性
      return await this.prisma.$transaction(async (tx) => {
        // 使用传入的订单ID，而不是重新生成
        const orderId = orderData.id || generateSnowflakeId();

        // 不再需要单独生成订单编号，使用id作为订单编号



        // 计算最终的状态值
        const finalOrderStatus = orderData.orderStatus !== undefined
          ? (typeof orderData.orderStatus === 'string'
              ? (orderData.orderStatus === 'pending' ? 0
                 : orderData.orderStatus === 'processing' ? 1
                 : orderData.orderStatus === 'shipped' ? 2
                 : orderData.orderStatus === 'completed' ? 3
                 : orderData.orderStatus === 'cancelled' ? 4
                 : parseInt(orderData.orderStatus) || 0)
              : parseInt(orderData.orderStatus) || 0)
          : 0;

        const finalPaymentStatus = orderData.paymentStatus !== undefined
          ? (typeof orderData.paymentStatus === 'string'
              ? (orderData.paymentStatus === 'unpaid' ? 0
                 : orderData.paymentStatus === 'paid' ? 1
                 : orderData.paymentStatus === 'partial_paid' ? 2
                 : orderData.paymentStatus === 'refunding' ? 3
                 : orderData.paymentStatus === 'refunded' ? 4
                 : parseInt(orderData.paymentStatus) || 0)
              : parseInt(orderData.paymentStatus) || 0)
          : 0;



        // 创建订单
        const newOrder = await tx.orders.create({
          data: {
            id: BigInt(orderId),
            user_id: orderData.customerId
              ? BigInt(orderData.customerId)
              : BigInt(0),
            // 使用字符串类型存储第三方订单号
            third_party_order_sn: orderData.thirdPartyOrderSn || null,
            channel: orderData.channelId
              ? {
                  connect: { id: orderData.channelId },
                }
              : undefined, // 使用关系连接渠道
            // 添加平台ID和店铺ID字段
            platform_id: orderData.platformId || null,
            store_id: orderData.storeId || null,
            order_status: finalOrderStatus,
            payment_status: finalPaymentStatus,
            shipping_status:
              typeof orderData.shippingStatus === "number"
                ? orderData.shippingStatus
                : orderData.shippingStatus === "unshipped"
                ? 0
                : orderData.shippingStatus === "shipped"
                ? 1
                : 0,
            total_product_amount: orderData.totalAmount || 0,
            shipping_fee: orderData.shippingFee || 0,
            discount_amount: orderData.discountAmount || 0,
            tax_amount: orderData.taxAmount || 0,
            // 计算订单应付总额 = 商品总金额 + 运费 + 税费 - 优惠金额
            total_amount:
              orderData.payableAmount ||
              (orderData.totalAmount || 0) +
                (orderData.shippingFee || 0) +
                (orderData.taxAmount || 0) -
                (orderData.discountAmount || 0),
            paid_amount: orderData.paidAmount || 0,
            paid_at: orderData.paymentTime || null,
            payment_method: orderData.paymentMethod || null, // 支付方式名称
            payment_method_id: orderData.payment_method_id || null, // 支付方式ID，使用现有的payment_method_id字段
            payment_sn: orderData.transactionId || null,
            shipping_method: null,
            order_source:
              typeof orderData.orderSource === "string"
                ? orderData.orderSource === "system"
                  ? 0
                  : orderData.orderSource === "offline"
                  ? 1
                  : 2
                : orderData.orderSource !== undefined
                ? parseInt(orderData.orderSource)
                : 0,
            order_type:
              orderData.orderType !== undefined
                ? Number.isNaN(parseInt(orderData.orderType))
                  ? (() => {
                      throw new Error("订单类型必须是有效的整数");
                    })()
                  : parseInt(orderData.orderType)
                : (() => {
                    throw new Error("订单类型不能为空");
                  })(),
            remark: orderData.remark || null,
            admin_remark: null,
            created_at: BigInt(Date.now()),
            updated_at: BigInt(Date.now()),
          },
        });

        // 创建订单项
        if (orderData.items && orderData.items.length > 0) {
          const orderItemsData = orderData.items.map((item) => ({
            order_id: orderId,
            goods_spu_id: item.goodsSpuId ? BigInt(item.goodsSpuId) : null,
            goods_sku_id: item.goodsSkuId ? BigInt(item.goodsSkuId) : BigInt(0),
            spu_name_snapshot: "",
            product_name: item.productName || "",
            sku_specifications: item.skuSpecifications
              ? JSON.stringify(item.skuSpecifications)
              : null,
            product_image: item.productImage || null,
            unit_price: item.unitPrice || 0,
            quantity: item.quantity || 1,
            total_price: item.unitPrice * item.quantity || 0,
            item_paid_amount: 0,
            created_at: BigInt(Date.now()),
            updated_at: BigInt(Date.now()),
          }));

          await tx.order_items.createMany({
            data: orderItemsData,
          });
        }

        // 创建订单配送信息
        if (orderData.shippingInfo) {

          await tx.order_shipping_info.create({
            data: {
              order_id: orderId,
              // 直接使用客户信息作为收货人信息
              recipient_name: orderData.customerName || "",
              recipient_phone: orderData.customerPhone || "",
              // 不再使用区域ID
              region_province_id: null,
              region_city_id: null,
              region_district_id: null,
              region_path_name: "",
              // 将完整地址存储在street_address字段中
              street_address: orderData.shippingInfo.address || "",
              postal_code: orderData.shippingInfo.postalCode || "",
            },
          });
        }

        // 创建订单业务员关联
        if (orderData.salespeople && orderData.salespeople.length > 0) {
          const salespeopleData = orderData.salespeople.map(
            (salespersonId) => ({
              order_id: orderId,
              salesperson_id: BigInt(salespersonId),
            })
          );

          await tx.order_salespeople.createMany({
            data: salespeopleData,
          });
        }

        // 创建订单日志
        await tx.order_logs.create({
          data: {
            order_id: orderId,
            operator_id: orderData.operatorId
              ? BigInt(orderData.operatorId)
              : null,
            operator_type: orderData.operatorType || 1,
            operator_name: orderData.operatorName,
            action: "创建订单",
            details: "订单创建成功",
          },
        });

        // 处理BigInt序列化问题，再将字段名转换为 camelCase 格式
        const processedData = handleBigInt(newOrder);
        const camelCaseData = convertKeysToCamelCase(processedData);

        return camelCaseData;
      });
    } catch (error) {
      console.error("创建订单失败:", error);
      throw error;
    }
  }

  /**
   * 更新订单发货状态
   * @param {string|number|BigInt} id - 订单ID
   * @param {number} shippingStatus - 发货状态（0-未发货，1-已发货，2-已收货）
   * @returns {Promise<Object>} - 更新后的订单
   */
  async updateShippingStatus(id, shippingStatus) {
    try {
      const orderId = this.ensureBigInt(id);

      // 更新发货状态
      const updateData = {
        shipping_status: shippingStatus,
        updated_at: BigInt(Date.now()),
      };

      // 如果发货状态变为已发货，设置发货时间
      if (shippingStatus === 1) {
        updateData.shipped_at = BigInt(Date.now());
      }

      const updatedOrder = await this.prisma.orders.update({
        where: {
          id: orderId,
        },
        data: updateData,
      });

      return handleBigInt(updatedOrder);
    } catch (error) {
      console.error(`更新订单(ID: ${id})发货状态失败:`, error);
      throw error;
    }
  }

  /**
   * 生成订单编号方法已移除
   * 现在使用订单ID作为唯一标识符
   */

  /**
   * 更新订单状态
   * @param {string|number|BigInt} id - 订单ID
   * @param {number} status - 订单状态
   * @param {string} [message] - 状态变更消息
   * @returns {Promise<Object>} - 更新后的订单
   */
  async updateOrderStatus(id, status, message, operator) {
    try {
      const orderId = this.ensureBigInt(id);

      // 准备更新数据
      const updateData = {
        order_status: status,
        updated_at: BigInt(Date.now()),
      };

      // 如果订单状态变为已完成，设置完成时间
      if (status === 3) {
        updateData.completed_at = BigInt(Date.now());
      }

      // 如果订单状态变为已取消，设置取消时间
      if (status === 4) {
        updateData.cancelled_at = BigInt(Date.now());
      }

      // 注意：数据库中没有updated_by字段，如需记录操作人，请使用订单日志表

      // 更新订单
      const updatedOrder = await this.prisma.orders.update({
        where: {
          id: orderId,
        },
        data: updateData,
      });

      return handleBigInt(updatedOrder);
    } catch (error) {
      console.error(`更新订单(ID: ${id})状态失败:`, error);
      throw error;
    }
  }

  /**
   * 更新订单管理员备注
   * @param {string|number|BigInt} id - 订单ID
   * @param {string} remark - 管理员备注
   * @returns {Promise<Object>} - 更新后的订单
   */
  async updateOrderRemark(id, remark) {
    try {
      // 验证订单ID
      const orderId = BigInt(id);

      const updateData = {
        admin_remark: remark,
        updated_at: BigInt(Date.now()),
      };

      const updatedOrder = await this.prisma.orders.update({
        where: {
          id: orderId,
        },
        data: updateData,
      });

      return handleBigInt(updatedOrder);
    } catch (error) {
      console.error("更新订单备注失败:", error);
      throw error;
    }
  }

  /**
   * 更新订单跟单员
   * @param {string|number} id - 订单ID
   * @param {string|number} followerId - 跟单员ID
   * @returns {Promise<Object>} - 更新后的订单对象
   */
  async updateOrderFollower(id, followerId) {
    try {
      // 验证订单ID
      const orderId = BigInt(id);
      // 将跟单员ID转换为BigInt
      const followerIdBigInt = followerId ? BigInt(followerId) : null;

      const updateData = {
        follower_id: followerIdBigInt,
        updated_at: BigInt(Date.now()),
      };

      const updatedOrder = await this.prisma.orders.update({
        where: {
          id: orderId,
        },
        data: updateData,
      });

      return handleBigInt(updatedOrder);
    } catch (error) {
      console.error("更新订单跟单员失败:", error);
      throw error;
    }
  }

  /**
   * 通用订单更新方法
   * @param {string|number|BigInt} id - 订单ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} - 更新后的订单对象
   */
  async updateOrder(id, updateData) {
    try {
      // 验证订单ID
      const orderId = this.ensureBigInt(id);

      // 过滤掉不存在的字段
      const { updated_by, ...validUpdateData } = updateData;

      // 添加更新时间
      const finalUpdateData = {
        ...validUpdateData,
        updated_at: BigInt(Date.now())
      };

      const updatedOrder = await this.prisma.orders.update({
        where: {
          id: orderId
        },
        data: finalUpdateData
      });

      return handleBigInt(updatedOrder);
    } catch (error) {
      console.error(`更新订单(ID: ${id})失败:`, error);
      throw error;
    }
  }
}

module.exports = OrderModel;
