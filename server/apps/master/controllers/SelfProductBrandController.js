/**
 * 产品品牌控制器
 */
const BaseController = require('../../../core/controllers/BaseController');
const SelfProductBrandDto = require('../dto/SelfProductBrandDto');
const SelfProductBrandModel = require('../models/SelfProductBrandModel');

class SelfProductBrandController extends BaseController {
  /**
   * 构造函数
   */
  constructor() {
    super();
  }
  /**
   * 获取产品品牌列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getSelfProductBrandList(req, res) {
    try {
      // 参数验证
      const params = await this.validate(req.query, SelfProductBrandDto.getListValidate());
      
      // 获取列表数据
      const { rows, total } = await SelfProductBrandModel.getSelfProductBrandList(params);
      
      // 返回成功响应
      return this.success(res, {
        rows,
        total,
        page: parseInt(params.page),
        limit: parseInt(params.limit)
      });
    } catch (error) {
      console.error('获取产品品牌列表失败:', error);
      return this.error(res, error.message || '获取产品品牌列表失败');
    }
  }

  /**
   * 获取产品品牌详情
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getSelfProductBrandDetail(req, res) {
    try {
      // 参数验证
      const params = await this.validate(req.params, SelfProductBrandDto.getDetailValidate());
      
      // 获取详情数据
      const detail = await SelfProductBrandModel.getSelfProductBrandDetail(params.id);
      
      if (!detail) {
        return this.notFound(res, '产品品牌不存在');
      }
      
      // 返回成功响应
      return this.success(res, detail);
    } catch (error) {
      console.error('获取产品品牌详情失败:', error);
      return this.error(res, error.message || '获取产品品牌详情失败');
    }
  }

  /**
   * 创建产品品牌
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async createSelfProductBrand(req, res) {
    try {
      // 参数验证
      const data = await this.validate(req.body, SelfProductBrandDto.createValidate());
      
      // 检查品牌代码是否已存在
      const existing = await SelfProductBrandModel.getSelfProductBrandByCode(data.brand_code);
      if (existing) {
        return this.badRequest(res, '该品牌代码已存在');
      }
      
      // 添加创建人和更新人信息
      const userId = req.user?.id || 0; // 获取当前登录用户ID，如果没有则使用0
      data.created_by = userId;
      data.updated_by = userId;
      
      // 创建产品品牌
      const result = await SelfProductBrandModel.createSelfProductBrand(data);
      
      // 返回成功响应
      return this.created(res, result);
    } catch (error) {
      console.error('创建产品品牌失败:', error);
      return this.error(res, error.message || '创建产品品牌失败');
    }
  }

  /**
   * 更新产品品牌
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async updateSelfProductBrand(req, res) {
    try {
      // 参数验证
      const params = await this.validate(req.params, SelfProductBrandDto.getDetailValidate());
      const data = await this.validate(req.body, SelfProductBrandDto.updateValidate());
      
      // 检查品牌是否存在
      const exists = await SelfProductBrandModel.getSelfProductBrandDetail(params.id);
      if (!exists) {
        return this.notFound(res, '产品品牌不存在');
      }
      
      // 如果更新了品牌代码，检查是否与其他记录冲突
      if (data.brand_code && data.brand_code !== exists.brand_code) {
        const existing = await SelfProductBrandModel.getSelfProductBrandByCode(data.brand_code);
        if (existing && existing.id !== params.id) {
          return this.badRequest(res, '该品牌代码已存在');
        }
      }
      
      // 添加更新人信息
      const userId = req.user?.id || 0; // 获取当前登录用户ID，如果没有则使用0
      data.updated_by = userId;
      
      // 更新产品品牌
      const result = await SelfProductBrandModel.updateSelfProductBrand(params.id, data);
      
      // 返回成功响应
      return this.success(res, result);
    } catch (error) {
      console.error('更新产品品牌失败:', error);
      return this.error(res, error.message || '更新产品品牌失败');
    }
  }

  /**
   * 删除产品品牌
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async deleteSelfProductBrand(req, res) {
    try {
      // 参数验证
      const params = await this.validate(req.params, SelfProductBrandDto.deleteValidate());
      
      // 检查品牌是否存在
      const exists = await SelfProductBrandModel.getSelfProductBrandDetail(params.id);
      if (!exists) {
        return this.notFound(res, '产品品牌不存在');
      }
      
      // 删除产品品牌
      await SelfProductBrandModel.deleteSelfProductBrand(params.id);
      
      // 返回成功响应
      return this.success(res, { message: '删除产品品牌成功' });
    } catch (error) {
      console.error('删除产品品牌失败:', error);
      return this.error(res, error.message || '删除产品品牌失败');
    }
  }
}

module.exports = SelfProductBrandController;
