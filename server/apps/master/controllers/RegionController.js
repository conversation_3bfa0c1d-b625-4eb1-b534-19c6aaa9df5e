/**
 * 区域控制器
 * 处理区域相关的API请求
 */
const regionService = require('../services/RegionService');
const RegionDto = require('../dto/RegionDto');
const redisUtil = require('../../../core/utils/RedisUtil');

class RegionController {
  /**
   * 获取区域树结构
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getRegionTree(req, res) {
    try {
      // 获取查询参数
      const parentId = parseInt(req.query.parentId || 0);
      const excludeStreet = req.query.excludeStreet === 'true';
      // 新增参数：是否只获取省市级数据（过滤区/县级）
      const onlyProvinceCity = req.query.onlyProvinceCity === 'true';
      
      // 构建缓存键（包含新参数）
      const cacheKey = `region:tree:${parentId}:${excludeStreet}:${onlyProvinceCity}`;
      
      // 检查Redis连接状态
      if (redisUtil.isConnected) {
        // 尝试从缓存获取数据
        const cachedData = await redisUtil.getClient().get(cacheKey);
        
        if (cachedData) {

          return res.json({
            code: 200,
            message: '获取区域树成功',
            data: JSON.parse(cachedData)
          });
        }
      }
      
      // 缓存未命中或Redis未连接，从数据库获取数据
      const regionTree = await regionService.getRegionTree(parentId, excludeStreet, onlyProvinceCity);
      
      // 使用简化格式化方法，直接返回前端所需的格式
      const formattedData = RegionDto.formatRegionTreeSimple(regionTree);
      
      // 如果Redis已连接，将数据存入缓存，设置半小时过期时间(1800秒)
      if (redisUtil.isConnected) {
        await redisUtil.getClient().set(cacheKey, JSON.stringify(formattedData), { EX: 1800 });

      }
      
      res.json({
        code: 200,
        message: '获取区域树成功',
        data: formattedData
      });
    } catch (error) {
      console.error('获取区域树失败:', error);
      
      res.status(200).json({
        code: 500,
        message: '获取区域树失败',
        error: error.message || '服务器内部错误'
      });
    }
  }
}

module.exports = new RegionController();
