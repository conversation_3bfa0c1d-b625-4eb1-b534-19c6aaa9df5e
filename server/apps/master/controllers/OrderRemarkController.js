/**
 * 订单备注控制器
 * 负责处理订单备注相关的请求和响应
 */
const BaseController = require('../../../core/controllers/BaseController');
const OrderRemarkModel = require('../models/OrderModel/OrderRemarkModel');

class OrderRemarkController extends BaseController {
  /**
   * 构造函数
   */
  constructor() {
    super();
    this.orderRemarkModel = OrderRemarkModel;
  }

  /**
   * 获取订单备注列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @returns {Promise<void>}
   */
  async getOrderRemarks(req, res) {
    try {
      // 获取订单ID
      const { orderId } = req.params;

      // 验证请求参数
      if (!orderId) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      // 获取订单备注列表
      const remarks = await this.orderRemarkModel.getOrderRemarks(orderId);

      // 返回成功响应
      return this.success(res, remarks, '获取订单备注列表成功');
    } catch (error) {
      // 记录错误日志
      console.error('获取订单备注列表失败:', error);

      // 返回错误响应
      return this.fail(res, `获取订单备注列表失败: ${error.message}`, 500);
    }
  }
}

module.exports = new OrderRemarkController();
