/**
 * 订单包裹服务
 */
const OrderPackageModel = require('../models/OrderPackageModel');
const { createOrderPackageSchema, updateOrderPackageSchema, shipOrderPackageSchema, getOrderPackagesSchema } = require('../dto/OrderPackageDto');

class OrderPackageService {
  /**
   * 创建包裹
   * @param {Object} data - 包裹数据
   * @returns {Promise<Object>} - 创建的包裹
   */
  async createPackage(data) {
    try {
      // 验证数据
      const { error, value } = createOrderPackageSchema.validate(data);
      if (error) {
        throw new Error(`数据验证失败: ${error.message}`);
      }
      
      // 提取包裹数据和包裹项
      const { items, ...packageData } = value;
      
      // 创建包裹和包裹项
      const result = await OrderPackageModel.createPackageWithItems(packageData, items);
      
      return result;
    } catch (error) {
      console.error('创建包裹服务失败:', error);
      throw error;
    }
  }

  /**
   * 获取包裹列表
   * @param {Object} query - 查询参数
   * @returns {Promise<Object>} - 包裹列表和总数
   */
  async getPackages(query) {
    try {
      // 验证查询参数
      const { error, value } = getOrderPackagesSchema.validate(query);
      if (error) {
        throw new Error(`查询参数验证失败: ${error.message}`);
      }
      
      const { page, pageSize, ...filters } = value;
      
      // 获取包裹列表
      const result = await OrderPackageModel.getPackages(filters, page, pageSize);
      
      return result;
    } catch (error) {
      console.error('获取包裹列表服务失败:', error);
      throw error;
    }
  }

  /**
   * 获取包裹详情
   * @param {Number} id - 包裹ID
   * @returns {Promise<Object>} - 包裹详情
   */
  async getPackageById(id) {
    try {
      const packageInfo = await OrderPackageModel.getPackageById(id);
      
      return packageInfo;
    } catch (error) {
      console.error('获取包裹详情服务失败:', error);
      throw error;
    }
  }

  /**
   * 更新包裹信息
   * @param {Number} id - 包裹ID
   * @param {Object} data - 更新的数据
   * @returns {Promise<Object>} - 更新后的包裹
   */
  async updatePackage(id, data) {
    try {
      // 验证数据
      const { error, value } = updateOrderPackageSchema.validate(data);
      if (error) {
        throw new Error(`数据验证失败: ${error.message}`);
      }
      
      // 检查包裹是否存在
      await OrderPackageModel.getPackageById(id);
      
      // 更新包裹
      const result = await OrderPackageModel.updatePackage(id, value);
      
      return result;
    } catch (error) {
      console.error('更新包裹服务失败:', error);
      throw error;
    }
  }

  /**
   * 包裹发货
   * @param {Number} id - 包裹ID
   * @param {Object} shippingData - 物流信息
   * @returns {Promise<Object>} - 更新后的包裹
   */
  async shipPackage(id, shippingData) {
    try {
      // 验证数据
      const { error, value } = shipOrderPackageSchema.validate(shippingData);
      if (error) {
        throw new Error(`物流信息验证失败: ${error.message}`);
      }
      
      // 发货
      const result = await OrderPackageModel.shipPackage(id, value);
      
      return result;
    } catch (error) {
      console.error('包裹发货服务失败:', error);
      throw error;
    }
  }

  /**
   * 根据订单ID获取包裹列表
   * @param {Number} orderId - 订单ID
   * @returns {Promise<Array>} - 包裹列表
   */
  async getPackagesByOrderId(orderId) {
    try {
      if (!orderId || isNaN(parseInt(orderId))) {
        throw new Error('订单ID无效');
      }
      
      const result = await OrderPackageModel.getPackagesByOrderId(parseInt(orderId));
      
      return result;
    } catch (error) {
      console.error('获取订单包裹服务失败:', error);
      throw error;
    }
  }

  /**
   * 批量创建多个包裹（统一发货接口）
   * @param {Number} orderId - 订单ID
   * @param {Array} packagesData - 包裹数据数组
   * @returns {Promise<Array>} - 创建的包裹数组
   */
  async createMultiplePackages(orderId, packagesData) {
    try {
      // 验证订单ID
      if (!orderId || isNaN(parseInt(orderId))) {
        throw new Error('订单ID无效');
      }
      
      // 验证包裹数组
      if (!Array.isArray(packagesData) || packagesData.length === 0) {
        throw new Error('包裹数据无效，必须提供至少一个包裹');
      }
      
      // 简化的包裹验证逻辑
      for (const pkg of packagesData) {
        // 验证包裹商品项
        if (!Array.isArray(pkg.items) || pkg.items.length === 0) {
          throw new Error('包裹必须包含至少一个商品项');
        }
        
        // 验证每个商品项
        for (const item of pkg.items) {
          if (!item.orderItemId) {
            throw new Error('商品项缺少有效的orderItemId');
          }
          // 允许orderItemId是字符串或数字类型
          if (typeof item.orderItemId !== 'number' && typeof item.orderItemId !== 'string') {
            throw new Error('商品项的orderItemId类型无效，必须是数字或字符串');
          }
          
          if (!item.quantity || typeof item.quantity !== 'number' || item.quantity <= 0) {
            throw new Error('商品项数量无效，必须为正数');
          }
        }
        
        // 验证配送方式（如果有提供）
        if (pkg.shippingMethod && (pkg.shippingMethod < 1 || pkg.shippingMethod > 5)) {
          throw new Error('配送方式无效，必须是1-5之间的数字');
        }
        
        // 验证物流信息，只有快递物流方式才需要物流信息
        const shippingMethod = pkg.shippingMethod || 1; // 默认为快递物流
        if (shippingMethod === 1) { // 只验证快递物流方式
          if (!pkg.shippingCompanyCode) {
            throw new Error('请提供物流公司编码');
          }
          if (!pkg.shippingCompanyName) {
            throw new Error('请提供物流公司名称');
          }
          if (!pkg.trackingNumber) {
            throw new Error('请提供物流单号');
          }
        }
      }
      
      // 调用模型层创建包裹
      // 确保订单ID是字符串格式以避免精度丢失
      const orderIdStr = String(orderId);
      
      const result = await OrderPackageModel.createMultiplePackages(orderIdStr, packagesData);
      
      return result;
    } catch (error) {
      console.error('发货服务失败:', error);
      throw error;
    }
  }

  /**
   * 确认收货
   * @param {Number} id - 包裹ID
   * @returns {Promise<Object>} - 更新后的包裹
   */
  async confirmReceive(id) {
    try {
      const result = await OrderPackageModel.confirmReceive(id);
      
      return result;
    } catch (error) {
      console.error('确认收货服务失败:', error);
      throw error;
    }
  }
}

module.exports = new OrderPackageService();
