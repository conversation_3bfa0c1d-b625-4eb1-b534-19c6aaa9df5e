/**
 * 系统配置管理服务
 * 负责处理系统配置的增删改查
 */
class ConfigManagementService {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    this.prisma = prisma;
    this.configModel = prisma.baseSystemConfig;
  }

  /**
   * 创建系统配置
   * @param {Object} data 配置数据
   * @returns {Promise<Object>} 创建的配置对象
   */
  async create(data) {
    console.log('服务层接收到的数据:', JSON.stringify(data, (key, value) => 
      typeof value === 'bigint' ? value.toString() : value));
    
    // 如果设置了默认配置，则将该类型下的其他配置设为非默认
    if (data.is_system === 1) {
      await this.configModel.updateMany({
        where: {
          config_type: data.config_type,
          is_system: 1,
          deleted_at: null
        },
        data: {
          is_system: 0
        }
      });
    }

    const now = BigInt(Date.now());
    
    // 处理创建时间和更新时间
    const createData = {
      ...data,
      created_at: now,
      updated_at: now
    };
    
    console.log('准备创建的数据:', JSON.stringify(createData, (key, value) => 
      typeof value === 'bigint' ? value.toString() : value));
    
    // 确保created_by字段为BigInt类型
    if (createData.created_by && typeof createData.created_by === 'string') {
      createData.created_by = BigInt(createData.created_by);
    }
    
    // 直接指定各个字段，避免数据扩展中可能丢失字段
    const prismaData = {
      config_type: createData.config_type,
      config_key: createData.config_key,
      config_value: createData.config_value,
      name: createData.name,
      sort: createData.sort || 0,
      is_system: createData.is_system || 0,
      remark: createData.remark || '',
      created_at: createData.created_at,
      updated_at: createData.updated_at,
      created_by: createData.created_by
    };
    
    console.log('最终传递给prisma的数据:', JSON.stringify(prismaData, (key, value) => 
      typeof value === 'bigint' ? value.toString() : value));
    
    // 先检查是否存在相同config_type和config_key的记录
    const existingConfig = await this.configModel.findFirst({
      where: {
        config_type: prismaData.config_type,
        config_key: prismaData.config_key,
        deleted_at: null
      }
    });
    
    let result;
    
    if (existingConfig) {
      // 如果存在，则更新
      console.log(`找到已存在的配置记录(id: ${existingConfig.id})，执行更新操作`);
      result = await this.configModel.update({
        where: {
          id: existingConfig.id
        },
        data: {
          config_value: prismaData.config_value,
          name: prismaData.name,
          sort: prismaData.sort,
          is_system: prismaData.is_system,
          remark: prismaData.remark,
          updated_at: prismaData.updated_at
        },
        select: {
          id: true,
          config_type: true,
          config_key: true,
          config_value: true,
          name: true,
          sort: true,
          is_system: true,
          remark: true,
          created_at: true,
          updated_at: true,
          created_by: true,
          updated_by: true
        }
      });
    } else {
      // 如果不存在，则创建
      console.log('未找到已存在的配置记录，执行创建操作');
      result = await this.configModel.create({
        data: prismaData,
        select: {
          id: true,
          config_type: true,
          config_key: true,
          config_value: true,
          name: true,
          sort: true,
          is_system: true,
          remark: true,
          created_at: true,
          updated_at: true,
          created_by: true,
          updated_by: true
        }
      });
    }
    
    // 将BigInt转换为字符串，避免序列化问题
    const processedResult = this.processBigIntFields(result);
    console.log('处理后的结果:', JSON.stringify(processedResult));
    
    return processedResult;
  }
  
  /**
   * 处理BigInt字段，将其转换为字符串
   * @param {Object|Array} data 需要处理的数据
   * @returns {Object|Array} 处理后的数据
   */
  processBigIntFields(data) {
    if (!data) return data;
    
    if (Array.isArray(data)) {
      return data.map(item => this.processBigIntFields(item));
    }
    
    const result = { ...data };
    
    // 处理常见的BigInt字段
    const bigIntFields = ['id', 'created_at', 'updated_at', 'deleted_at', 'created_by', 'updated_by'];
    
    for (const field of bigIntFields) {
      if (result[field] !== undefined && result[field] !== null && typeof result[field] === 'bigint') {
        result[field] = result[field].toString();
      }
    }
    
    return result;
  }

  /**
   * 批量创建系统配置
   * @param {Array} configsData 配置数据数组
   * @returns {Promise<Array>} 创建的配置对象数组
   */
  async batchCreate(configsData) {
    console.log('服务层接收到的批量数据:', JSON.stringify(configsData, (key, value) => 
      typeof value === 'bigint' ? value.toString() : value));
    
    const results = [];
    const now = BigInt(Date.now());
    
    // 对每个配置项使用单独的事务进行处理
    for (const data of configsData) {
      try {
        // 使用单独的事务处理每个配置项
        const config = await this.prisma.$transaction(async (prisma) => {
          // 先检查是否存在相同config_type和config_key的记录
          const existingConfig = await prisma.baseSystemConfig.findFirst({
            where: {
              config_type: data.config_type,
              config_key: data.config_key,
              deleted_at: null
            }
          });
          
          // 如果设置了系统内置，则将该类型下的其他配置设为非系统内置
          if (data.is_system === 1) {
            await prisma.baseSystemConfig.updateMany({
              where: {
                config_type: data.config_type,
                is_system: 1,
                deleted_at: null
              },
              data: {
                is_system: 0
              }
            });
          }
          
          // 处理创建时间和更新时间
          const updateData = {
            ...data,
            updated_at: now
          };
          
          let result;
          
          if (existingConfig) {
            // 如果存在，则更新
            console.log(`找到已存在的配置记录(id: ${existingConfig.id})，执行更新操作`);
            result = await prisma.baseSystemConfig.update({
              where: {
                id: existingConfig.id
              },
              data: {
                config_value: updateData.config_value,
                name: updateData.name,
                sort: updateData.sort || 0,
                is_system: updateData.is_system || 0,
                remark: updateData.remark || '',
                updated_at: updateData.updated_at
              }
            });
          } else {
            // 如果不存在，则创建
            console.log('未找到已存在的配置记录，执行创建操作');
            // 明确指定需要的字段，避免使用展开运算符直接传入可能包含 id 字段的数据
            const createData = {
              config_type: data.config_type,
              config_key: data.config_key,
              config_value: data.config_value,
              name: data.name,
              sort: data.sort || 0,
              is_system: data.is_system || 0,
              remark: data.remark || '',
              created_at: now,
              updated_at: now,
              created_by: data.created_by
            };
            
            result = await prisma.baseSystemConfig.create({
              data: createData
            });
          }
          
          return result;
        });
        
        // 处理BigInt字段，避免序列化问题
        const processedConfig = this.processBigIntFields(config);
        
        results.push({
          success: true,
          config_type: data.config_type,
          config_key: data.config_key,
          data: processedConfig
        });
      } catch (error) {
        console.error('处理单个配置失败:', error.message);
        results.push({
          success: false,
          config_type: data.config_type,
          config_key: data.config_key,
          error: error.message
        });
      }
    }
    
    return results;
  }

  /**
   * 批量更新系统配置
   * @param {Array} configsData 配置数据数组
   * @returns {Promise<Array>} 更新结果数组
   */
  async batchUpdate(configsData) {
    console.log('服务层接收到的批量更新数据:', JSON.stringify(configsData, (key, value) => 
      typeof value === 'bigint' ? value.toString() : value));
    
    const results = [];
    const now = BigInt(Date.now());
    
    // 使用事务批量更新
    await this.prisma.$transaction(async (prisma) => {
      for (const data of configsData) {
        try {
          const id = data.id;
          delete data.id; // 移除ID，避免更新时出错
          
          // 检查配置是否存在
          const existingConfig = await prisma.baseSystemConfig.findFirst({
            where: {
              id: BigInt(id),
              deleted_at: null
            }
          });
          
          if (!existingConfig) {
            results.push({
              success: false,
              id: id.toString(),
              error: '配置不存在'
            });
            continue;
          }
          
          // 如果设置了系统内置，则将该类型下的其他配置设为非系统内置
          if (data.is_system === 1) {
            await prisma.baseSystemConfig.updateMany({
              where: {
                config_type: data.config_type || existingConfig.config_type,
                is_system: 1,
                id: { not: BigInt(id) },
                deleted_at: null
              },
              data: {
                is_system: 0
              }
            });
          }
          
          // 处理更新时间
          const updateData = {
            ...data,
            updated_at: now
          };
          
          // 更新配置
          const config = await prisma.baseSystemConfig.update({
            where: {
              id: BigInt(id)
            },
            data: updateData
          });
          
          // 处理BigInt字段，避免序列化问题
          const processedConfig = this.processBigIntFields(config);
          
          results.push({
            success: true,
            id: id.toString(),
            data: processedConfig
          });
        } catch (error) {
          console.error('批量更新单个配置失败:', error.message);
          results.push({
            success: false,
            id: data.id ? data.id.toString() : 'unknown',
            error: error.message
          });
        }
      }
    });
    
    return results;
  }

  /**
   * 更新系统配置
   * @param {number} id 配置ID
   * @param {Object} data 配置数据
   * @returns {Promise<Object>} 更新后的配置对象
   */
  async update(id, data) {
    // 如果设置了默认配置，则将该类型下的其他配置设为非默认
    if (data.is_system === 1) {
      await this.configModel.updateMany({
        where: {
          config_type: data.config_type,
          id: { not: BigInt(id) },
          is_system: 1,
          deleted_at: null
        },
        data: {
          is_system: 0
        }
      });
    }

    // 处理更新数据中的类型转换
    const updateData = { ...data };
    if (updateData.updated_by && typeof updateData.updated_by === 'string') {
      updateData.updated_by = BigInt(updateData.updated_by);
    }
    
    // 直接指定各个字段，避免数据扩展中可能丢失字段
    const prismaData = {
      config_type: updateData.config_type,
      config_key: updateData.config_key,
      config_value: updateData.config_value,
      name: updateData.name,
      sort: updateData.sort || 0,
      is_system: updateData.is_system || 0,
      remark: updateData.remark || '',
      updated_at: BigInt(Date.now()),
      updated_by: updateData.updated_by
    };
    
    console.log('最终传递给prisma的数据:', JSON.stringify(prismaData, (key, value) => 
      typeof value === 'bigint' ? value.toString() : value));
    
    // 更新配置
    const updatedConfig = await this.configModel.update({
      where: { id: BigInt(id) },
      data: prismaData,
      select: {
        id: true,
        config_type: true,
        config_key: true,
        config_value: true,
        name: true,
        sort: true,
        is_system: true,
        remark: true,
        created_at: true,
        updated_at: true,
        created_by: true,
        updated_by: true
      }
    });

    return updatedConfig;
  }

  /**
   * 删除系统配置
   * @param {number} id 配置ID
   * @returns {Promise<Object>} 删除结果
   */
  async delete(id) {
    // 检查是否是系统内置配置
    const config = await this.configModel.findFirst({
      where: {
        id: BigInt(id),
        deleted_at: null
      },
      select: {
        id: true,
        config_type: true,
        config_key: true,
        is_system: true
      }
    });

    if (!config) {
      throw new Error('配置不存在或已被删除');
    }

    // 检查是否是系统内置配置
    if (config.is_system === 1) {
      throw new Error('系统内置配置不能删除');
    }

    // 软删除配置
    const deletedConfig = await this.configModel.update({
      where: { id: BigInt(id) },
      data: {
        deleted_at: BigInt(Date.now())
      },
      select: {
        id: true,
        config_type: true,
        config_key: true,
        config_value: true,
        name: true,
        sort: true,
        is_system: true,
        remark: true,
        created_at: true,
        updated_at: true,
        deleted_at: true,
        created_by: true,
        updated_by: true
      }
    });

    return deletedConfig;
  }

  /**
   * 根据ID获取配置
   * @param {number} id 配置ID
   * @returns {Promise<Object>} 配置详情
   */
  async getById(id) {
    const config = await this.configModel.findFirst({
      where: {
        id: BigInt(id),
        deleted_at: null
      },
      select: {
        id: true,
        config_type: true,
        config_key: true,
        config_value: true,
        name: true,
        sort: true,
        is_system: true,
        remark: true,
        created_at: true,
        updated_at: true,
        created_by: true,
        updated_by: true
      }
    });

    if (!config) {
      throw new Error('配置不存在或已被删除');
    }

    return config;
  }

  /**
   * 获取配置列表
   * @param {Object} params 查询参数
   * @returns {Promise<Object>} 配置列表和分页信息
   */
  async list(params) {
    const { keyword = '', config_type = '', page = 1, page_size = 10 } = params;

    // 构建查询条件
    const where = {
      deleted_at: null
    };

    // 添加关键词搜索
    if (keyword) {
      where.OR = [
        { name: { contains: keyword } },
        { config_key: { contains: keyword } }
      ];
    }

    // 添加配置类型筛选
    if (config_type) {
      where.config_type = config_type;
    }

    // 查询总数
    const total = await this.configModel.count({ where });

    // 查询列表
    const list = await this.configModel.findMany({
      where,
      select: {
        id: true,
        config_type: true,
        config_key: true,
        config_value: true,
        name: true,
        sort: true,
        is_system: true,
        remark: true,
        created_at: true,
        updated_at: true,
        created_by: true,
        updated_by: true
      },
      skip: (page - 1) * page_size,
      take: page_size,
      orderBy: [
        { sort: 'asc' },
        { created_at: 'desc' }
      ]
    });

    return {
      list,
      pagination: {
        total,
        page,
        pageSize: page_size
      }
    };
  }

  /**
   * 根据配置类型获取配置列表
   * @param {string} configType 配置类型
   * @returns {Promise<Array>} 配置列表
   */
  async getByType(configType) {
    console.log(`服务层接收到的配置类型: ${configType}`);
    
    const configs = await this.configModel.findMany({
      where: {
        config_type: configType,
        deleted_at: null
      },
      select: {
        id: true,
        config_type: true,
        config_key: true,
        config_value: true,
        name: true,
        sort: true,
        is_system: true,
        remark: true,
        created_at: true,
        updated_at: true,
        created_by: true,
        updated_by: true
      },
      orderBy: {
        sort: 'asc'
      }
    });
    
    console.log(`查询到${configs.length}条配置记录`);
    
    // 处理BigInt字段，避免序列化问题
    return configs.map(config => this.processBigIntFields(config));
  }

  /**
   * 获取指定类型和键名的配置
   * @param {string} configType 配置类型
   * @param {string} configKey 配置键名
   * @returns {Promise<Object>} 配置对象
   */
  async getConfig(configType, configKey) {
    const config = await this.configModel.findFirst({
      where: {
        config_type: configType,
        config_key: configKey,
        deleted_at: null
      },
      select: {
        id: true,
        config_type: true,
        config_key: true,
        config_value: true,
        name: true,
        sort: true,
        is_system: true,
        remark: true,
        created_at: true,
        updated_at: true,
        created_by: true,
        updated_by: true
      }
    });

    if (!config) {
      throw new Error(`配置不存在，类型: ${configType}，键名: ${configKey}`);
    }

    return config;
  }

  /**
   * 获取指定类型的默认配置
   * @param {string} configType 配置类型
   * @returns {Promise<Object>} 默认配置对象
   */
  async getDefaultConfig(configType) {
    const config = await this.configModel.findFirst({
      where: {
        config_type: configType,
        is_system: 1, // 默认配置
        deleted_at: null
      },
      select: {
        id: true,
        config_type: true,
        config_key: true,
        config_value: true,
        name: true,
        sort: true,
        is_system: true,
        remark: true,
        created_at: true,
        updated_at: true,
        created_by: true,
        updated_by: true
      }
    });

    if (!config) {
      throw new Error(`未找到类型 ${configType} 的默认配置`);
    }

    return config;
  }
}

module.exports = ConfigManagementService;
