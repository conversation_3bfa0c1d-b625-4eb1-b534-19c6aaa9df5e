/**
 * 配置服务
 * 提供统一的配置获取方式，支持缓存
 */
const { prisma } = require('../../../../../core/database/prisma');
const { generateSnowflakeId } = require('../../../../../shared/utils/snowflake');

class ConfigurationService {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prismaInstance) {
    this.prisma = prismaInstance || prisma;
    this.configModel = this.prisma.baseSystemConfig;
    this.configCache = {};
    this.cacheExpiry = {};
    this.cacheDuration = 1 * 60 * 1000; // 缓存有效期1分钟
  }

  /**
   * 获取所有配置类型
   * @returns {Promise<Array<string>>} 配置类型列表
   */
  async getConfigTypes() {
    try {
      // 从数据库获取所有不同的配置类型
      const result = await this.configModel.findMany({
        where: {
          deleted_at: null
        },
        select: {
          config_type: true
        },
        distinct: ['config_type']
      });
      
      // 提取配置类型列表
      return result.map(item => item.config_type);
    } catch (error) {
      console.error('获取配置类型失败', error);
      throw new Error(`获取配置类型失败: ${error.message}`);
    }
  }

  /**
   * 获取指定类型的所有配置（带缓存）
   * @param {string} configType 配置类型
   * @param {boolean} forceRefresh 是否强制刷新缓存
   * @returns {Promise<Array>} 配置列表
   */
  async getConfigsByType(configType, forceRefresh = false) {
    try {
      const now = Date.now();
      console.log(configType);
      // 从数据库获取配置
      const configs = await this.configModel.findMany({
        where: {
          config_type: configType,
          deleted_at: null
        },
        select: {
          id: true,
          config_key: true,
          config_value: true,
          name: true,
          sort: true,
          is_system: true,
          is_default: true,
          remark: true
        },
        orderBy: {
          sort: 'asc'
        }
      });
      
      // 处理BigInt字段
      const processedConfigs = configs.map(config => ({
        ...config,
        id: config.id.toString()
      }));
      
      return processedConfigs;
    } catch (error) {
      console.error(`获取配置失败，类型: ${configType}`, error);
      throw new Error(`获取配置失败: ${error.message}`);
    }
  }

  /**
   * 获取指定类型和键的配置
   * @param {string} configType 配置类型
   * @param {string} configKey 配置键
   * @returns {Promise<Object|null>} 配置对象，如果不存在则返回null
   */
  async getConfig(configType, configKey) {
    try {
      // 获取配置列表
      const configs = await this.getConfigsByType(configType);
      
      // 查找指定的配置
      const config = configs.find(item => item.config_key === configKey);
      if (!config) {
        return null;
      }
      
      return config;
    } catch (error) {
      console.error(`获取配置失败，类型: ${configType}, 键: ${configKey}`, error);
      throw new Error(`获取配置失败: ${error.message}`);
    }
  }

  /**
   * 获取指定类型的配置对象（自动解析JSON）
   * @param {string} configType 配置类型
   * @param {string} configKey 配置键（可选）
   * @returns {Promise<Object|Array>} 配置对象
   */
  async getConfigObject(configType, configKey = null) {
    try {
      console.log(`获取配置对象，类型: ${configType}, 键: ${configKey || '所有'}`);
      
      // 获取配置列表
      const configs = await this.getConfigsByType(configType);
      
      // 如果指定了configKey，则返回单个配置对象
      if (configKey) {
        const config = configs.find(item => item.config_key === configKey);
        if (!config) {
          return null;
        }
        
        // 尝试解析JSON
        try {
          return JSON.parse(config.config_value);
        } catch (e) {
          // 如果不是有效的JSON，则返回原始值
          return config.config_value;
        }
      }
      
      // 返回所有配置的对象
      const result = {};
      for (const config of configs) {
        try {
          result[config.config_key] = JSON.parse(config.config_value);
        } catch (e) {
          result[config.config_key] = config.config_value;
        }
      }
      return result;
    } catch (error) {
      console.error(`获取配置对象失败，类型: ${configType}, 键: ${configKey || '所有'}`, error);
      throw new Error(`获取配置对象失败: ${error.message}`);
    }
  }

  /**
   * 获取指定类型和键的配置对象（自动解析JSON）
   * @param {string} configType 配置类型
   * @param {string} configKey 配置键
   * @returns {Promise<Object|null>} 配置对象，如果不存在则返回null
   */
  async getConfigObjectByKey(configType, configKey) {
    try {
      console.log(`获取配置对象，类型: ${configType}, 键: ${configKey}`);
      
      // 获取配置
      const config = await this.getConfig(configType, configKey);
      if (!config) {
        return null;
      }
      
      // 尝试解析JSON
      try {
        return JSON.parse(config.config_value);
      } catch (e) {
        // 如果不是有效的JSON，则返回原始值
        return config.config_value;
      }
    } catch (error) {
      console.error(`获取配置对象失败，类型: ${configType}, 键: ${configKey}`, error);
      throw new Error(`获取配置对象失败: ${error.message}`);
    }
  }

  /**
   * 获取配置值
   * @param {string} configType 配置类型
   * @param {string} configKey 配置键
   * @returns {Promise<string|Object>} 配置值
   */
  async getConfigValue(configType, configKey) {
    try {
      // 获取配置列表
      const configs = await this.getConfigsByType(configType);
      
      // 查找指定的配置
      const config = configs.find(item => item.config_key === configKey);
      if (!config) {
        return null;
      }
      
      return config.config_value;
    } catch (error) {
      console.error(`获取配置值失败，类型: ${configType}, 键: ${configKey}`, error);
      throw new Error(`获取配置值失败: ${error.message}`);
    }
  }

  /**
   * 清除缓存
   * @param {string} configType 配置类型（可选，不指定则清除所有缓存）
   */
  clearCache(configType = null) {
    if (configType) {
      delete this.configCache[configType];
      delete this.cacheExpiry[configType];
      console.log(`已清除配置缓存，类型: ${configType}`);
    } else {
      this.configCache = {};
      this.cacheExpiry = {};
      console.log('已清除所有配置缓存');
    }
  }

  /**
   * 更新配置
   * @param {string} configType 配置类型
   * @param {string} configKey 配置键
   * @param {string|Object} configValue 配置值
   * @param {Object} options 其他选项
   * @returns {Promise<Object>} 更新后的配置
   */
  async updateConfig(configType, configKey, configValue, options = {}) {
    try {
      const { name, sort = 0, remark = '', isDefault, userId } = options;
      
      // 如果configValue是对象，则转换为JSON字符串
      const valueToSave = typeof configValue === 'object' 
        ? JSON.stringify(configValue) 
        : configValue.toString();
      
      // 查找现有配置
      const existingConfig = await this.configModel.findFirst({
        where: {
          config_type: configType,
          config_key: configKey,
          deleted_at: null
        }
      });
      
      let result;
      
      // 当前时间戳（毫秒）
      const now = BigInt(Date.now());
      
      // 如果需要设置默认配置，则先将同类型下的其他配置设置为非默认
      if (isDefault) {
        await this.configModel.updateMany({
          where: {
            config_type: configType,
            is_default: 1,
            deleted_at: null
          },
          data: {
            is_default: 0,
            updated_at: now,
            ...(userId && { updated_by: BigInt(userId) })
          }
        });
      }
      
      if (existingConfig) {
        // 更新现有配置
        result = await this.configModel.update({
          where: {
            id: existingConfig.id
          },
          data: {
            config_value: valueToSave,
            ...(name && { name }),
            ...(sort !== undefined && { sort }),
            ...(remark && { remark }),
            ...(isDefault !== undefined && { is_default: isDefault ? 1 : 0 }),
            updated_at: now,
            ...(userId && { updated_by: BigInt(userId) })
          }
        });
      } else {
        // 创建新配置
        result = await this.configModel.create({
          data: {
            id: generateSnowflakeId(),
            config_type: configType,
            config_key: configKey,
            config_value: valueToSave,
            name: name || configKey,
            sort: sort || 0,
            remark: remark || '',
            is_default: isDefault ? 1 : 0,
            is_system: 0,
            created_at: now,
            updated_at: now,
            ...(userId && { created_by: BigInt(userId), updated_by: BigInt(userId) })
          }
        });
      }
      
      // 清除缓存
      this.clearCache(configType);
      
      // 处理BigInt字段
      return {
        ...result,
        id: result.id.toString(),
        created_at: result.created_at ? result.created_at.toString() : null,
        updated_at: result.updated_at ? result.updated_at.toString() : null,
        created_by: result.created_by ? result.created_by.toString() : null,
        updated_by: result.updated_by ? result.updated_by.toString() : null
      };
    } catch (error) {
      console.error(`更新配置失败，类型: ${configType}, 键: ${configKey}`, error);
      throw new Error(`更新配置失败: ${error.message}`);
    }
  }

  /**
   * 设置默认配置
   * @param {string} configType 配置类型
   * @param {string} configKey 配置键
   * @returns {Promise<Object>} 设置为默认的配置
   */
  async setDefaultConfig(configType, configKey) {
    try {
      // 查找现有配置
      const existingConfig = await this.configModel.findFirst({
        where: {
          config_type: configType,
          config_key: configKey,
          deleted_at: null
        }
      });
      
      if (!existingConfig) {
        throw new Error(`配置不存在: ${configType}.${configKey}`);
      }
      
      // 当前时间戳（毫秒）
      const now = BigInt(Date.now());
      
      // 将所有同类型配置设为非默认
      await this.configModel.updateMany({
        where: {
          config_type: configType,
          deleted_at: null
        },
        data: {
          is_default: 0, // 非默认
          updated_at: now
        }
      });
      
      // 将指定配置设为默认
      const result = await this.configModel.update({
        where: {
          id: existingConfig.id
        },
        data: {
          is_default: 1, // 默认
          updated_at: now
        }
      });
      
      // 清除缓存
      this.clearCache(configType);
      
      // 处理BigInt字段
      return {
        ...result,
        id: result.id.toString(),
        created_at: result.created_at ? result.created_at.toString() : null,
        updated_at: result.updated_at ? result.updated_at.toString() : null
      };
    } catch (error) {
      console.error(`设置默认配置失败，类型: ${configType}, 键: ${configKey}`, error);
      throw new Error(`设置默认配置失败: ${error.message}`);
    }
  }

  /**
   * 获取默认配置
   * @param {string} configType 配置类型
   * @returns {Promise<Object>} 默认配置
   */
  async getDefaultConfig(configType) {
    try {
      // 获取配置列表
      const configs = await this.getConfigsByType(configType);
      
      // 查找设置为默认的配置
      const defaultConfig = configs.find(item => item.is_default === 1);
      
      // 如果没有默认配置，则返回第一个配置
      return defaultConfig || (configs.length > 0 ? configs[0] : null);
    } catch (error) {
      console.error(`获取默认配置失败，类型: ${configType}`, error);
      throw new Error(`获取默认配置失败: ${error.message}`);
    }
  }

  /**
   * 删除配置
   * @param {string} configType 配置类型
   * @param {string} configKey 配置键
   * @returns {Promise<boolean>} 是否删除成功
   */
  async deleteConfig(configType, configKey) {
    try {
      // 查找现有配置
      const existingConfig = await this.configModel.findFirst({
        where: {
          config_type: configType,
          config_key: configKey,
          deleted_at: null
        }
      });
      
      if (!existingConfig) {
        return false;
      }
      
      // 软删除配置
      await this.configModel.update({
        where: {
          id: existingConfig.id
        },
        data: {
          deleted_at: new Date()
        }
      });
      
      // 清除缓存
      this.clearCache(configType);
      
      return true;
    } catch (error) {
      console.error(`删除配置失败，类型: ${configType}, 键: ${configKey}`, error);
      throw new Error(`删除配置失败: ${error.message}`);
    }
  }
}

module.exports = ConfigurationService;
