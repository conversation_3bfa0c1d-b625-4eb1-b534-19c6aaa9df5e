{"openapi": "3.0.0", "info": {"title": "短信服务 API", "description": "短信服务多平台集成 API", "version": "1.0.0"}, "servers": [{"url": "/api/v1", "description": "API 服务器"}], "paths": {"/master/system/integration/sms/send": {"post": {"summary": "发送短信", "description": "发送短信到指定手机号码", "tags": ["系统管理/集成服务/短信服务"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"phoneNumber": {"type": "string", "description": "手机号码", "example": "13800138000"}, "templateCode": {"type": "string", "description": "模板编码", "example": "SMS_123456789"}, "templateParams": {"type": "object", "description": "模板参数", "example": {"code": "123456"}}, "platform": {"type": "string", "description": "短信平台类型（可选，不指定则使用默认平台）", "example": "<PERSON><PERSON><PERSON>"}}, "required": ["phoneNumber", "templateCode"]}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "短信发送成功"}, "data": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "发送成功"}, "requestId": {"type": "string", "example": "F655A8D5-B967-440B-8683-C474D1B1A8"}}}}}}}}, "400": {"description": "参数错误"}, "500": {"description": "服务器错误"}}}}, "/master/system/integration/sms/batch-send": {"post": {"summary": "批量发送短信", "description": "批量发送短信到多个手机号码", "tags": ["系统管理/集成服务/短信服务"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"phoneNumbers": {"type": "array", "items": {"type": "string"}, "description": "手机号码列表", "example": ["13800138000", "13900139000"]}, "templateCode": {"type": "string", "description": "模板编码", "example": "SMS_123456789"}, "templateParams": {"type": "object", "description": "模板参数", "example": {"content": "系统通知内容"}}, "platform": {"type": "string", "description": "短信平台类型（可选，不指定则使用默认平台）", "example": "<PERSON><PERSON><PERSON>"}}, "required": ["phoneNumbers", "templateCode"]}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "短信批量发送成功"}, "data": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "发送成功"}, "requestId": {"type": "string", "example": "F655A8D5-B967-440B-8683-C474D1B1A8"}}}}}}}}, "400": {"description": "参数错误"}, "500": {"description": "服务器错误"}}}}, "/master/system/integration/sms/test-connection": {"post": {"summary": "测试短信平台连接", "description": "测试指定短信平台的连接是否正常", "tags": ["系统管理/集成服务/短信服务"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"platform": {"type": "string", "description": "短信平台类型", "example": "<PERSON><PERSON><PERSON>"}}, "required": ["platform"]}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "连接测试成功"}, "data": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "连接成功"}}}}}}}}, "400": {"description": "参数错误"}, "500": {"description": "服务器错误"}}}}, "/master/system/integration/sms/logs": {"get": {"summary": "获取短信发送记录", "description": "获取指定平台的短信发送记录", "tags": ["系统管理/集成服务/短信服务"], "security": [{"bearerAuth": []}], "parameters": [{"name": "platform", "in": "query", "required": true, "schema": {"type": "string"}, "description": "短信平台类型"}, {"name": "phoneNumber", "in": "query", "schema": {"type": "string"}, "description": "手机号码（可选）"}, {"name": "beginTime", "in": "query", "schema": {"type": "string"}, "description": "开始时间（可选）"}, {"name": "endTime", "in": "query", "schema": {"type": "string"}, "description": "结束时间（可选）"}, {"name": "limit", "in": "query", "schema": {"type": "integer"}, "description": "每页记录数（可选）"}, {"name": "offset", "in": "query", "schema": {"type": "integer"}, "description": "偏移量（可选）"}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "获取短信发送记录成功"}, "data": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "获取成功"}, "data": {"type": "array", "items": {"type": "object"}}, "totalCount": {"type": "integer", "example": 10}}}}}}}}, "400": {"description": "参数错误"}, "500": {"description": "服务器错误"}}}}, "/master/system/configure/SMS/{key}/default": {"put": {"summary": "设置默认短信平台", "description": "将指定的短信平台设置为默认平台", "tags": ["系统管理/配置中心/配置管理"], "security": [{"bearerAuth": []}], "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}, "description": "短信平台标识，如 aliyun, tencent"}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "设置默认短信平台成功"}, "data": {"type": "object", "properties": {"id": {"type": "string", "example": "1"}, "config_type": {"type": "string", "example": "SMS"}, "config_key": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "is_default": {"type": "integer", "example": 1}}}}}}}}, "404": {"description": "配置不存在"}, "500": {"description": "服务器错误"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}