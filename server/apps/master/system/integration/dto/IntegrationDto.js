/**
 * 第三方平台集成DTO
 * 用于验证第三方平台集成相关的请求参数
 */
const Joi = require('joi');
const BaseDto = require('../../../../../core/dto/BaseDto');

class IntegrationDto extends BaseDto {
  /**
   * 验证获取平台列表请求
   * @param {Object} data 请求数据
   * @returns {Object} 验证结果
   */
  validateListPlatforms(data) {
    const schema = Joi.object({
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(10),
      status: Joi.number().integer().valid(0, 1)
    });
    
    return this.validate(schema, data);
  }

  /**
   * 验证获取平台配置请求
   * @param {Object} data 请求数据
   * @returns {Object} 验证结果
   */
  validateGetPlatformConfig(data) {
    const schema = Joi.object({
      platform: Joi.string().required().valid('wechat', 'aliyun')
    });
    
    return this.validate(schema, data);
  }

  /**
   * 验证更新平台配置请求
   * @param {Object} data 请求数据
   * @returns {Object} 验证结果
   */
  validateUpdatePlatformConfig(data) {
    const schema = Joi.object({
      platform: Joi.string().required().valid('wechat', 'aliyun'),
      configs: Joi.array().items(
        Joi.object({
          config_key: Joi.string().required(),
          config_value: Joi.string().allow(''),
          config_name: Joi.string().required(),
          config_type: Joi.string().required(),
          remark: Joi.string().allow(''),
          status: Joi.number().integer().valid(0, 1).default(1)
        })
      ).required()
    });
    
    return this.validate(schema, data);
  }

  /**
   * 验证测试平台连接请求
   * @param {Object} data 请求数据
   * @returns {Object} 验证结果
   */
  validateTestPlatformConnection(data) {
    const schema = Joi.object({
      platform: Joi.string().required().valid('wechat', 'aliyun'),
      service: Joi.string().required().valid('oauth', 'pay', 'message', 'oss', 'sms')
    });
    
    return this.validate(schema, data);
  }
}

module.exports = IntegrationDto;
