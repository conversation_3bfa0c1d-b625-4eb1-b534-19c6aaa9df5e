/**
 * 微信认证服务
 * 处理微信登录、授权等功能
 */
const axios = require('axios');
const crypto = require('crypto');
const { PrismaClient } = require('@prisma/client');

class WechatOAuthService {
  constructor(prisma) {
    this.prisma = prisma || new PrismaClient();
  }

  /**
   * 获取微信配置
   * @returns {Promise<Array>} 微信配置列表
   */
  async getConfig() {
    try {
      console.log('获取微信配置');
      
      // 从配置表中查询微信平台的所有配置
      const configs = await this.prisma.baseSystemConfig.findMany({
        where: {
          config_type: 'wechat',
          deleted_at: null
        },
        select: {
          config_key: true,
          config_value: true
        }
      });
      
      return configs;
    } catch (error) {
      console.error('获取微信配置失败:', error);
      throw new Error(`获取微信配置失败: ${error.message}`);
    }
  }

  /**
   * 获取微信授权URL
   * @param {string} redirectUrl 重定向URL
   * @param {string} scope 授权范围，snsapi_base或snsapi_userinfo
   * @returns {Promise<string>} 授权URL
   */
  async getAuthUrl(redirectUrl, scope = 'snsapi_base') {
    try {
      console.log('获取微信授权URL，重定向URL:', redirectUrl, '授权范围:', scope);
      
      // 获取微信配置
      const configs = await this.getConfig();
      const appId = configs.find(item => item.config_key === 'appid')?.config_value;
      
      if (!appId) {
        throw new Error('微信配置中缺少appid');
      }
      
      // 生成随机state
      const state = crypto.randomBytes(16).toString('hex');
      
      // 构建授权URL
      const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${encodeURIComponent(redirectUrl)}&response_type=code&scope=${scope}&state=${state}#wechat_redirect`;
      
      return authUrl;
    } catch (error) {
      console.error('获取微信授权URL失败:', error);
      throw new Error(`获取微信授权URL失败: ${error.message}`);
    }
  }

  /**
   * 通过code获取访问令牌
   * @param {string} code 授权码
   * @returns {Promise<Object>} 访问令牌信息
   */
  async getAccessToken(code) {
    try {
      console.log('通过code获取访问令牌，code:', code);
      
      // 获取微信配置
      const configs = await this.getConfig();
      const appId = configs.find(item => item.config_key === 'appid')?.config_value;
      const appSecret = configs.find(item => item.config_key === 'secret')?.config_value;
      
      if (!appId || !appSecret) {
        throw new Error('微信配置中缺少appid或secret');
      }
      
      // 请求微信API获取访问令牌
      const url = `https://api.weixin.qq.com/sns/oauth2/access_token?appid=${appId}&secret=${appSecret}&code=${code}&grant_type=authorization_code`;
      const response = await axios.get(url);
      
      if (response.data.errcode) {
        throw new Error(`微信API返回错误: ${response.data.errmsg}`);
      }
      
      return response.data;
    } catch (error) {
      console.error('通过code获取访问令牌失败:', error);
      throw new Error(`通过code获取访问令牌失败: ${error.message}`);
    }
  }

  /**
   * 获取用户信息
   * @param {string} accessToken 访问令牌
   * @param {string} openid 用户的openid
   * @returns {Promise<Object>} 用户信息
   */
  async getUserInfo(accessToken, openid) {
    try {
      console.log('获取用户信息，openid:', openid);
      
      // 请求微信API获取用户信息
      const url = `https://api.weixin.qq.com/sns/userinfo?access_token=${accessToken}&openid=${openid}&lang=zh_CN`;
      const response = await axios.get(url);
      
      if (response.data.errcode) {
        throw new Error(`微信API返回错误: ${response.data.errmsg}`);
      }
      
      return response.data;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw new Error(`获取用户信息失败: ${error.message}`);
    }
  }

  /**
   * 获取微信JS SDK配置
   * @param {string} url 页面URL
   * @returns {Promise<Object>} JS SDK配置
   */
  async getJsConfig(url) {
    try {
      console.log('获取微信JS SDK配置，URL:', url);
      
      // 获取微信配置
      const configs = await this.getConfig();
      const appId = configs.find(item => item.config_key === 'appid')?.config_value;
      
      if (!appId) {
        throw new Error('微信配置中缺少appid');
      }
      
      // 获取jsapi_ticket
      const ticket = await this.getJsApiTicket();
      
      // 生成签名
      const nonceStr = crypto.randomBytes(16).toString('hex');
      const timestamp = Math.floor(Date.now() / 1000);
      const str = `jsapi_ticket=${ticket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${url}`;
      const signature = crypto.createHash('sha1').update(str).digest('hex');
      
      return {
        appId,
        timestamp,
        nonceStr,
        signature,
        jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData', 'onMenuShareTimeline', 'onMenuShareAppMessage', 'chooseWXPay']
      };
    } catch (error) {
      console.error('获取微信JS SDK配置失败:', error);
      throw new Error(`获取微信JS SDK配置失败: ${error.message}`);
    }
  }

  /**
   * 获取jsapi_ticket
   * @returns {Promise<string>} jsapi_ticket
   */
  async getJsApiTicket() {
    try {
      // 先从缓存中获取
      const cachedTicket = await this.prisma.baseSystemConfig.findFirst({
        where: {
          config_type: 'wechat',
          config_key: 'jsapi_ticket_cache',
          deleted_at: null
        }
      });
      
      if (cachedTicket) {
        const ticketData = JSON.parse(cachedTicket.config_value);
        // 检查是否过期
        if (ticketData.expires_at > Date.now()) {
          console.log('使用缓存的jsapi_ticket');
          return ticketData.ticket;
        }
      }
      
      // 缓存不存在或已过期，重新获取
      console.log('重新获取jsapi_ticket');
      
      // 获取access_token
      const accessToken = await this.getGlobalAccessToken();
      
      // 请求微信API获取jsapi_ticket
      const url = `https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=${accessToken}&type=jsapi`;
      const response = await axios.get(url);
      
      if (response.data.errcode !== 0) {
        throw new Error(`微信API返回错误: ${response.data.errmsg}`);
      }
      
      const ticket = response.data.ticket;
      const expiresIn = response.data.expires_in;
      
      // 缓存jsapi_ticket
      const ticketData = {
        ticket,
        expires_at: Date.now() + (expiresIn - 300) * 1000 // 提前5分钟过期
      };
      
      await this.prisma.baseSystemConfig.upsert({
        where: {
          config_type_config_key: {
            config_type: 'wechat',
            config_key: 'jsapi_ticket_cache'
          }
        },
        update: {
          config_value: JSON.stringify(ticketData),
          updated_at: BigInt(Date.now())
        },
        create: {
          config_type: 'wechat',
          config_key: 'jsapi_ticket_cache',
          config_value: JSON.stringify(ticketData),
          name: '微信JSAPI票据缓存',
          sort: 0,
          status: 1,
          is_system: 1,
          remark: '系统自动生成，请勿手动修改',
          created_at: BigInt(Date.now()),
          updated_at: BigInt(Date.now())
        }
      });
      
      return ticket;
    } catch (error) {
      console.error('获取jsapi_ticket失败:', error);
      throw new Error(`获取jsapi_ticket失败: ${error.message}`);
    }
  }

  /**
   * 获取全局access_token
   * @returns {Promise<string>} access_token
   */
  async getGlobalAccessToken() {
    try {
      // 先从缓存中获取
      const cachedToken = await this.prisma.baseSystemConfig.findFirst({
        where: {
          config_type: 'wechat',
          config_key: 'access_token_cache',
          deleted_at: null
        }
      });
      
      if (cachedToken) {
        const tokenData = JSON.parse(cachedToken.config_value);
        // 检查是否过期
        if (tokenData.expires_at > Date.now()) {
          console.log('使用缓存的access_token');
          return tokenData.access_token;
        }
      }
      
      // 缓存不存在或已过期，重新获取
      console.log('重新获取access_token');
      
      // 获取微信配置
      const configs = await this.getConfig();
      const appId = configs.find(item => item.config_key === 'appid')?.config_value;
      const appSecret = configs.find(item => item.config_key === 'secret')?.config_value;
      
      if (!appId || !appSecret) {
        throw new Error('微信配置中缺少appid或secret');
      }
      
      // 请求微信API获取access_token
      const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appId}&secret=${appSecret}`;
      const response = await axios.get(url);
      
      if (response.data.errcode) {
        throw new Error(`微信API返回错误: ${response.data.errmsg}`);
      }
      
      const accessToken = response.data.access_token;
      const expiresIn = response.data.expires_in;
      
      // 缓存access_token
      const tokenData = {
        access_token: accessToken,
        expires_at: Date.now() + (expiresIn - 300) * 1000 // 提前5分钟过期
      };
      
      await this.prisma.baseSystemConfig.upsert({
        where: {
          config_type_config_key: {
            config_type: 'wechat',
            config_key: 'access_token_cache'
          }
        },
        update: {
          config_value: JSON.stringify(tokenData),
          updated_at: BigInt(Date.now())
        },
        create: {
          config_type: 'wechat',
          config_key: 'access_token_cache',
          config_value: JSON.stringify(tokenData),
          name: '微信访问令牌缓存',
          sort: 0,
          status: 1,
          is_system: 1,
          remark: '系统自动生成，请勿手动修改',
          created_at: BigInt(Date.now()),
          updated_at: BigInt(Date.now())
        }
      });
      
      return accessToken;
    } catch (error) {
      console.error('获取access_token失败:', error);
      throw new Error(`获取access_token失败: ${error.message}`);
    }
  }

  /**
   * 测试微信连接
   * @returns {Promise<Object>} 测试结果
   */
  async testConnection() {
    try {
      console.log('测试微信连接');
      
      // 获取微信配置
      const configs = await this.getConfig();
      const appId = configs.find(item => item.config_key === 'appid')?.config_value;
      const appSecret = configs.find(item => item.config_key === 'secret')?.config_value;
      
      if (!appId || !appSecret) {
        return {
          success: false,
          message: '微信配置中缺少appid或secret'
        };
      }
      
      // 尝试获取access_token
      const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appId}&secret=${appSecret}`;
      const response = await axios.get(url);
      
      if (response.data.errcode) {
        return {
          success: false,
          message: `微信API返回错误: ${response.data.errmsg}`
        };
      }
      
      return {
        success: true,
        message: '微信连接测试成功'
      };
    } catch (error) {
      console.error('测试微信连接失败:', error);
      return {
        success: false,
        message: `测试微信连接失败: ${error.message}`
      };
    }
  }
}

module.exports = WechatOAuthService;
