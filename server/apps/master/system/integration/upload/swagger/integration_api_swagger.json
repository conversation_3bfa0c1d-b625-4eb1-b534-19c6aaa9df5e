{"openapi": "3.0.0", "info": {"title": "集成服务 API", "version": "1.0.0", "description": "集成服务模块 API 文档"}, "paths": {"/api/v1/master/system/integration/upload/credentials": {"post": {"tags": ["文件上传"], "summary": "获取上传凭证", "description": "获取上传凭证信息", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"platform": {"type": "string", "description": "存储平台类型（可选）", "example": "<PERSON><PERSON><PERSON>"}, "dir": {"type": "string", "description": "存储目录", "example": "uploads"}, "maxSize": {"type": "integer", "description": "最大文件大小（MB）", "example": 10}}}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "获取上传凭证成功"}, "data": {"type": "object", "properties": {"credentials": {"type": "object", "description": "平台相关的凭证信息"}}}}}}}}, "401": {"description": "未授权", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 401}, "message": {"type": "string", "example": "未授权"}}}}}}}}}, "/api/v1/master/system/integration/upload/file": {"post": {"tags": ["文件上传"], "summary": "上传文件", "description": "上传文件到默认存储平台", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary", "description": "要上传的文件"}, "dir": {"type": "string", "description": "存储目录", "example": "uploads"}, "module": {"type": "string", "description": "业务模块", "example": "default"}, "bizType": {"type": "string", "description": "业务类型", "example": ""}, "bizId": {"type": "string", "description": "业务ID", "example": ""}, "isPublic": {"type": "boolean", "description": "是否公开访问", "example": true}}, "required": ["file"]}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "文件上传成功"}, "data": {"type": "object", "properties": {"id": {"type": "string", "description": "文件ID"}, "fileName": {"type": "string", "description": "文件名称"}, "originalName": {"type": "string", "description": "原始文件名"}, "filePath": {"type": "string", "description": "文件路径"}, "fileUrl": {"type": "string", "description": "文件URL"}, "fileSize": {"type": "string", "description": "文件大小(字节)"}, "fileType": {"type": "string", "description": "文件类型"}, "extension": {"type": "string", "description": "文件扩展名"}, "storageType": {"type": "string", "description": "存储类型"}, "md5": {"type": "string", "description": "文件MD5值"}}}}}}}}, "400": {"description": "请求参数错误"}, "401": {"description": "未授权"}}}}, "/api/v1/master/system/integration/upload/files": {"get": {"tags": ["文件上传"], "summary": "获取文件列表", "description": "分页获取文件列表", "security": [{"bearerAuth": []}], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "default": 1}, "description": "页码"}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "default": 20}, "description": "每页记录数"}, {"name": "module", "in": "query", "schema": {"type": "string"}, "description": "业务模块"}, {"name": "bizType", "in": "query", "schema": {"type": "string"}, "description": "业务类型"}, {"name": "bizId", "in": "query", "schema": {"type": "string"}, "description": "业务ID"}, {"name": "platform", "in": "query", "schema": {"type": "string"}, "description": "存储平台类型"}, {"name": "dir", "in": "query", "schema": {"type": "string"}, "description": "存储目录"}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "获取文件列表成功"}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "文件ID"}, "fileName": {"type": "string", "description": "文件名称"}, "originalName": {"type": "string", "description": "原始文件名"}, "filePath": {"type": "string", "description": "文件路径"}, "fileUrl": {"type": "string", "description": "文件URL"}, "fileSize": {"type": "string", "description": "文件大小(字节)"}, "fileType": {"type": "string", "description": "文件类型"}, "extension": {"type": "string", "description": "文件扩展名"}, "storageType": {"type": "string", "description": "存储类型"}}}}, "pagination": {"type": "object", "properties": {"current": {"type": "integer", "description": "当前页码"}, "pageSize": {"type": "integer", "description": "每页记录数"}, "total": {"type": "integer", "description": "总记录数"}}}}}}}}}}}}}, "/api/v1/master/system/integration/upload/file/{id}": {"get": {"tags": ["文件上传"], "summary": "获取文件详情", "description": "根据文件ID获取文件详情", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "文件ID"}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "获取文件详情成功"}, "data": {"type": "object", "properties": {"id": {"type": "string", "description": "文件ID"}, "fileName": {"type": "string", "description": "文件名称"}, "originalName": {"type": "string", "description": "原始文件名"}, "filePath": {"type": "string", "description": "文件路径"}, "fileUrl": {"type": "string", "description": "文件URL"}, "fileSize": {"type": "string", "description": "文件大小(字节)"}, "fileType": {"type": "string", "description": "文件类型"}, "extension": {"type": "string", "description": "文件扩展名"}, "storageType": {"type": "string", "description": "存储类型"}, "md5": {"type": "string", "description": "文件MD5值"}, "module": {"type": "string", "description": "业务模块"}, "bizType": {"type": "string", "description": "业务类型"}, "bizId": {"type": "string", "description": "业务ID"}, "isPublic": {"type": "boolean", "description": "是否公开访问"}, "createdAt": {"type": "string", "description": "创建时间"}, "updatedAt": {"type": "string", "description": "更新时间"}}}}}}}}}}, "delete": {"tags": ["文件上传"], "summary": "删除文件", "description": "根据文件ID删除文件", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "文件ID"}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "删除文件成功"}}}}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}