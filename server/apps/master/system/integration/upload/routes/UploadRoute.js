/**
 * 上传服务路由配置
 * 处理文件上传相关的路由
 */
const express = require('express');
const router = express.Router();
const multer = require('multer');
const { prisma: globalPrisma } = require('../../../../../../core/database/prisma');
const UploadController = require('../controllers/UploadController');
const authMiddleware = require('../../../../../../core/middleware/AuthMiddleware');

/**
 * 创建上传服务路由
 * @param {Object} prisma Prisma客户端实例
 * @returns {Object} Express路由对象
 */
function createUploadRouter(prismaInstance) {
  // 使用传入的 prisma 实例或全局实例
  const prisma = prismaInstance || globalPrisma;
  // 创建控制器实例
  const controller = new UploadController(prisma);
  
  console.log('[DEBUG] 创建上传路由实例');
  
  // 配置multer中间件
  const upload = multer({ storage: multer.memoryStorage() });
  
  
  /**
   * @swagger
   * /api/v1/master/system/integration/upload/credentials:
   *   post:
   *     tags: [系统管理/集成服务/文件上传]
   *     summary: 获取上传凭证
   *     description: 根据存储平台类型获取上传凭证
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               dir:
   *                 type: string
   *                 description: 存储目录
   *                 example: "images"
   *     responses:
   *       200:
   *         description: 返回上传凭证
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "获取上传凭证成功"
   *                 data:
   *                   type: object
   *                   properties:
   *                     credentials:
   *                       type: object
   *                       description: 平台相关的凭证信息
   *       401:
   *         $ref: '#/components/responses/Unauthorized'
   *       500:
   *         description: 服务器错误或业务逻辑错误
   *         content:
   *           application/json:
   *             schema:
   *               oneOf:
   *                 - $ref: '#/components/responses/Error'
   *                 - $ref: '#/components/responses/BadRequest'
   */
  router.post('/credentials', authMiddleware, controller.getUploadCredentials.bind(controller));

  /**
   * @swagger
   * /api/v1/master/system/integration/upload/file:
   *   post:
   *     tags: [系统管理/集成服务/文件上传]
   *     summary: 上传文件
   *     description: 上传文件到默认存储平台
   *     consumes:
   *       - multipart/form-data
   *     parameters:
   *       - in: formData
   *         name: file
   *         type: file
   *         required: true
   *         description: 要上传的文件
   *       - in: formData
   *         name: dir
   *         type: string
   *         description: 存储目录
   *       - in: formData
   *         name: module
   *         type: string
   *         description: 业务模块
   *       - in: formData
   *         name: bizType
   *         type: string
   *         description: 业务类型
   *       - in: formData
   *         name: bizId
   *         type: string
   *         description: 业务ID
   *       - in: formData
   *         name: isPublic
   *         type: boolean
   *         description: 是否公开访问
   *     responses:
   *       200:
   *         description: 文件上传成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "文件上传成功"
   *                 data:
   *                   type: object
   *                   properties:
   *                     id:
   *                       type: string
   *                       description: 文件ID
   *                     fileName:
   *                       type: string
   *                       description: 文件名称
   *                     originalName:
   *                       type: string
   *                       description: 原始文件名
   *                     filePath:
   *                       type: string
   *                       description: 文件路径
   *                     fileUrl:
   *                       type: string
   *                       description: 文件URL
   *                     fileSize:
   *                       type: integer
   *                       description: 文件大小(字节)
   *                     fileType:
   *                       type: string
   *                       description: 文件类型
   *                     extension:
   *                       type: string
   *                       description: 文件扩展名
   *                     storageType:
   *                       type: string
   *                       description: 存储类型
   *                     md5:
   *                       type: string
   *                       description: 文件MD5值
   *       401:
   *         $ref: '#/components/responses/Unauthorized'
   *       500:
   *         description: 服务器错误或业务逻辑错误
   *         content:
   *           application/json:
   *             schema:
   *               oneOf:
   *                 - $ref: '#/components/responses/Error'
   *                 - $ref: '#/components/responses/BadRequest'
   */
  router.post('/file', authMiddleware, (req, res, next) => {
    console.log('[DEBUG] 上传路由 - 进入路由处理');
    next();
  }, upload.single('file'), (req, res) => {
    console.log('[DEBUG] 上传路由 - multer处理完成');
    // 检查是否有文件上传
    if (!req.file) {
      console.log('[DEBUG] 上传路由 - 未检测到文件');
      return controller.fail(res, '未检测到上传文件');
    }
    
    console.log('[DEBUG] 上传路由 - 调用控制器');
    // 调用控制器方法处理上传
    controller.uploadFile(req, res);
  });

  /**
   * @swagger
   * /api/v1/master/system/integration/upload/files:
   *   get:
   *     tags: [系统管理/集成服务/文件上传]
   *     summary: 获取文件列表
   *     description: 分页获取文件列表
   *     parameters:
   *       - $ref: '#/components/parameters/PageParam'
   *       - $ref: '#/components/parameters/PageSizeParam'
   *       - in: query
   *         name: module
   *         schema:
   *           type: string
   *         description: 业务模块
   *       - in: query
   *         name: bizType
   *         schema:
   *           type: string
   *         description: 业务类型
   *       - in: query
   *         name: bizId
   *         schema:
   *           type: string
   *         description: 业务ID
   *       - in: query
   *         name: dir
   *         schema:
   *           type: string
   *         description: 存储目录
   *     responses:
   *       200:
   *         description: 返回文件列表
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "获取文件列表成功"
   *                 data:
   *                   type: object
   *                   properties:
   *                     list:
   *                       type: array
   *                       items:
   *                         type: object
   *                         properties:
   *                           id:
   *                             type: string
   *                             description: 文件ID
   *                           fileName:
   *                             type: string
   *                             description: 文件名称
   *                           originalName:
   *                             type: string
   *                             description: 原始文件名
   *                           filePath:
   *                             type: string
   *                             description: 文件路径
   *                           fileUrl:
   *                             type: string
   *                             description: 文件URL
   *                           fileSize:
   *                             type: integer
   *                             description: 文件大小(字节)
   *                           fileType:
   *                             type: string
   *                             description: 文件类型
   *                           extension:
   *                             type: string
   *                             description: 文件扩展名
   *                           storageType:
   *                             type: string
   *                             description: 存储类型
   *                     pagination:
   *                       type: object
   *                       properties:
   *                         current:
   *                           type: integer
   *                           description: 当前页码
   *                         pageSize:
   *                           type: integer
   *                           description: 每页记录数
   *                         total:
   *                           type: integer
   *                           description: 总记录数
   *       401:
   *         $ref: '#/components/responses/Unauthorized'
   *       500:
   *         description: 服务器错误或业务逻辑错误
   *         content:
   *           application/json:
   *             schema:
   *               oneOf:
   *                 - $ref: '#/components/responses/Error'
   *                 - $ref: '#/components/responses/BadRequest'
   */
  router.get('/files', authMiddleware, controller.listFiles.bind(controller));

  /**
   * @swagger
   * /api/v1/master/system/integration/upload/file/{id}:
   *   get:
   *     tags: [系统管理/集成服务/文件上传]
   *     summary: 获取文件详情
   *     description: 根据文件ID获取文件详情
   *     parameters:
   *       - in: path
   *         name: id
   *         schema:
   *           type: string
   *         required: true
   *         description: 文件ID
   *     responses:
   *       200:
   *         description: 返回文件详情
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "获取文件详情成功"
   *                 data:
   *                   type: object
   *                   properties:
   *                     id:
   *                       type: string
   *                       description: 文件ID
   *                     fileName:
   *                       type: string
   *                       description: 文件名称
   *                     originalName:
   *                       type: string
   *                       description: 原始文件名
   *                     filePath:
   *                       type: string
   *                       description: 文件路径
   *                     fileUrl:
   *                       type: string
   *                       description: 文件URL
   *                     fileSize:
   *                       type: integer
   *                       description: 文件大小(字节)
   *                     fileType:
   *                       type: string
   *                       description: 文件类型
   *                     extension:
   *                       type: string
   *                       description: 文件扩展名
   *                     storageType:
   *                       type: string
   *                       description: 存储类型
   *       401:
   *         $ref: '#/components/responses/Unauthorized'
   *       500:
   *         description: 服务器错误或业务逻辑错误
   *         content:
   *           application/json:
   *             schema:
   *               oneOf:
   *                 - $ref: '#/components/responses/Error'
   *                 - $ref: '#/components/responses/BadRequest'
   */
  router.get('/file/:id', authMiddleware, controller.getFileDetail.bind(controller));

  /**
   * @swagger
   * /api/v1/master/system/integration/upload/file/{id}:
   *   delete:
   *     tags: [系统管理/集成服务/文件上传]
   *     summary: 删除文件
   *     description: 根据文件ID删除文件
   *     parameters:
   *       - in: path
   *         name: id
   *         schema:
   *           type: string
   *         required: true
   *         description: 文件ID
   *     responses:
   *       200:
   *         description: 删除成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "删除文件成功"
   *       401:
   *         $ref: '#/components/responses/Unauthorized'
   *       500:
   *         description: 服务器错误或业务逻辑错误
   *         content:
   *           application/json:
   *             schema:
   *               oneOf:
   *                 - $ref: '#/components/responses/Error'
   *                 - $ref: '#/components/responses/BadRequest'
   */
  router.delete('/file/:id', authMiddleware, controller.deleteFile.bind(controller));

  console.log('上传服务路由创建成功');
  return router;
}

module.exports = createUploadRouter;
