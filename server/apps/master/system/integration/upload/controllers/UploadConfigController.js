/**
 * 上传平台配置控制器
 * 管理上传平台的配置、测试和默认平台设置
 */
const BaseController = require('../../../../../../core/controllers/BaseController');
const UploadServiceFactory = require('../services/UploadServiceFactory');

class UploadConfigController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma数据库实例
   */
  constructor(prisma) {
    super(prisma);
    this.uploadServiceFactory = new UploadServiceFactory(prisma);
  }

  /**
   * 获取所有上传平台配置
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getAllPlatforms(req, res) {
    try {
      const configs = await this.uploadServiceFactory.getUploadConfigs(true);
      
      // 处理配置数据，转换成可用的格式
      const platforms = configs.map(config => {
        let configValue = {};
        try {
          if (typeof config.config_value === 'string') {
            configValue = JSON.parse(config.config_value);
          } else {
            configValue = config.config_value;
          }
        } catch (e) {
          console.error('解析配置数据失败:', e);
        }
        
        return {
          id: config.id,
          platform: config.config_key,
          config: configValue,
          isDefault: config.is_default === 1,
          status: config.status === 1
        };
      });
      
      return this.success(res, '获取所有上传平台配置成功', platforms);
    } catch (error) {
      console.error('获取所有上传平台配置失败:', error);
      return this.fail(res, '获取所有上传平台配置失败: ' + error.message);
    }
  }

  /**
   * 获取单个上传平台配置
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getPlatform(req, res) {
    try {
      const { platform } = req.params;
      if (!platform) {
        return this.fail(res, '平台标识不能为空');
      }
      
      const configs = await this.uploadServiceFactory.getUploadConfigs();
      const config = configs.find(c => c.config_key === platform);
      
      if (!config) {
        return this.fail(res, `平台 ${platform} 不存在`);
      }
      
      let configValue = {};
      try {
        if (typeof config.config_value === 'string') {
          configValue = JSON.parse(config.config_value);
        } else {
          configValue = config.config_value;
        }
      } catch (e) {
        console.error('解析配置数据失败:', e);
      }
      
      const platformData = {
        id: config.id,
        platform: config.config_key,
        config: configValue,
        isDefault: config.is_default === 1,
        status: config.status === 1
      };
      
      return this.success(res, `获取 ${platform} 平台配置成功`, platformData);
    } catch (error) {
      console.error('获取平台配置失败:', error);
      return this.fail(res, '获取平台配置失败: ' + error.message);
    }
  }

  /**
   * 更新上传平台配置
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async updatePlatform(req, res) {
    try {
      const { platform } = req.params;
      if (!platform) {
        return this.fail(res, '平台标识不能为空');
      }
      
      const { config, isDefault, name } = req.body;
      if (!config) {
        return this.fail(res, '配置数据不能为空');
      }
      
      // 检查 platform 是否为数字 ID
      const isId = /^\d+$/.test(platform);
      
      if (isId) {
        // 如果是 ID，则通过 ID 更新配置
        const id = BigInt(platform);
        const configs = await this.uploadServiceFactory.getUploadConfigs(true);
        const existingConfig = configs.find(c => c.id === id);
        
        if (!existingConfig) {
          return this.fail(res, `未找到 ID 为 ${platform} 的配置`);
        }
        
        // 更新配置
        await this.uploadServiceFactory.updateConfigById(id, {
          config_value: typeof config === 'string' ? config : JSON.stringify(config),
          name: name || existingConfig.name,
          is_default: isDefault ? 1 : 0,
          status: 1,
          updated_at: BigInt(Date.now())
        });
        
        // 如果需要设为默认，则调用设置默认服务方法
        if (isDefault) {
          await this.uploadServiceFactory.setDefaultService(existingConfig.config_key);
        }
      } else {
        // 如果是平台类型，则通过类型更新配置
        await this.uploadServiceFactory.updateServiceConfig(platform, config, isDefault, name);
      }
      
      return this.success(res, `更新 ${platform} 平台配置成功`);
    } catch (error) {
      console.error('更新平台配置失败:', error);
      return this.fail(res, '更新平台配置失败: ' + error.message);
    }
  }

  /**
   * 设置默认上传平台
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async setDefaultPlatform(req, res) {
    try {
      const { platform } = req.params;
      if (!platform) {
        return this.fail(res, '平台标识不能为空');
      }
      
      // 设置默认平台
      await this.uploadServiceFactory.setDefaultService(platform);
      
      return this.success(res, `已设置 ${platform} 为默认上传平台`);
    } catch (error) {
      console.error('设置默认平台失败:', error);
      return this.fail(res, '设置默认平台失败: ' + error.message);
    }
  }

  /**
   * 测试上传平台连接
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async testPlatform(req, res) {
    try {
      const { platform } = req.params;
      if (!platform) {
        return this.fail(res, '平台标识不能为空');
      }
      
      // 获取服务实例
      const service = await this.uploadServiceFactory.getServiceByType(platform);
      
      // 如果服务实例有测试连接方法，则调用测试连接方法
      if (service && typeof service.testConnection === 'function') {
        const testResult = await service.testConnection();
        return this.success(res, `测试 ${platform} 平台连接成功`, testResult);
      }
      
      // 如果服务实例没有测试连接方法，则返回成功
      return this.success(res, `${platform} 平台服务实例正常`);
    } catch (error) {
      console.error('测试平台连接失败:', error);
      return this.fail(res, '测试平台连接失败: ' + error.message);
    }
  }

  /**
   * 获取当前默认上传平台
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getDefaultPlatform(req, res) {
    try {
      const configs = await this.uploadServiceFactory.getUploadConfigs();
      const defaultConfig = configs.find(c => c.is_default === 1);
      
      if (!defaultConfig) {
        return this.fail(res, '没有找到默认上传平台');
      }
      
      let configValue = {};
      try {
        if (typeof defaultConfig.config_value === 'string') {
          configValue = JSON.parse(defaultConfig.config_value);
        } else {
          configValue = defaultConfig.config_value;
        }
      } catch (e) {
        console.error('解析配置数据失败:', e);
      }
      
      const platformData = {
        id: defaultConfig.id,
        platform: defaultConfig.config_key,
        config: configValue,
        isDefault: true,
        status: defaultConfig.status === 1
      };
      
      return this.success(res, '获取当前默认上传平台成功', platformData);
    } catch (error) {
      console.error('获取当前默认上传平台失败:', error);
      return this.fail(res, '获取当前默认上传平台失败: ' + error.message);
    }
  }
}

module.exports = UploadConfigController;
