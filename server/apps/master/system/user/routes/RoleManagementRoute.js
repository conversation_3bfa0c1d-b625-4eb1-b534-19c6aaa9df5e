const express = require('express');
const router = express.Router();
const RoleController = require('../controllers/RoleManagementController');
const { prisma } = require('../../../../../core/database/prisma');
const RouterConfig = require('../../../../../core/routes/RouterConfig');
const controller = new RoleController(prisma);

// 创建受JWT保护的路由
const protectedRouter = RouterConfig.authRoute(router);

/**
 * @swagger
 * /api/v1/system/role:
 *   get:
 *     tags: [系统管理/用户中心/角色管理]
 *     summary: 获取角色列表
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 搜索关键词（角色名称或代码）
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *           enum: [1, 2]
 *         description: 状态，1-正常，2-停用
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页条数
 *     responses:
 *       200:
 *         $ref: '#/components/responses/SystemRoleListResponse'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 */
protectedRouter.get('/', controller.list.bind(controller));

/**
 * @swagger
 * /api/v1/system/role:
 *   post:
 *     tags: [系统管理/用户中心/角色管理]
 *     summary: 创建角色
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SystemRoleCreate'
 *     responses:
 *       201:
 *         $ref: '#/components/responses/SystemRoleCreateResponse'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 */
protectedRouter.post('/', controller.create.bind(controller));

/**
 * @swagger
 * /api/v1/system/role/{id}:
 *   get:
 *     tags: [系统管理/用户中心/角色管理]
 *     summary: 获取角色详情
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *           format: int64
 *         description: 角色ID
 *     responses:
 *       200:
 *         $ref: '#/components/responses/SystemRoleResponse'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 *       404:
 *         description: 角色不存在
 */
// --- 优先注册更具体的路由，避免被 /:id 吞掉 ---
protectedRouter.get('/:id/menus', controller.getRoleMenus.bind(controller));
protectedRouter.get('/:id', controller.getById.bind(controller));

/**
 * @swagger
 * /api/v1/system/role/{id}:
 *   put:
 *     tags: [系统管理/用户中心/角色管理]
 *     summary: 更新角色
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *           format: int64
 *         description: 角色ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SystemRoleUpdate'
 *     responses:
 *       200:
 *         $ref: '#/components/responses/SystemRoleUpdateResponse'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 *       404:
 *         description: 角色不存在
 */
protectedRouter.put('/:id', controller.update.bind(controller));

/**
 * @swagger
 * /api/v1/system/role/{id}:
 *   delete:
 *     tags: [系统管理/用户中心/角色管理]
 *     summary: 删除角色
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *           format: int64
 *         description: 角色ID
 *     responses:
 *       200:
 *         $ref: '#/components/responses/SystemRoleDeleteResponse'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 *       404:
 *         description: 角色不存在
 */
protectedRouter.delete('/:id', controller.delete.bind(controller));

/**
 * @swagger
 * /api/v1/system/role/options/all:
 *   get:
 *     tags: [系统管理/用户中心/角色管理]
 *     summary: 获取所有角色选项（用于下拉选择）
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         $ref: '#/components/responses/SystemRoleOptionsResponse'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 */
protectedRouter.get('/options/all', controller.getOptions.bind(controller));

// 获取角色关联菜单
protectedRouter.get('/:id/menus', (req, res) => {
  controller.getRoleMenus(req, res);
});

// 获取角色关联部门
protectedRouter.get('/:id/depts', (req, res) => {
  controller.getRoleDepts(req, res);
});

module.exports = router;
