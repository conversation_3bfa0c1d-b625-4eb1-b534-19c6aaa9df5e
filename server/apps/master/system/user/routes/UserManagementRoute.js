const express = require('express');
const router = express.Router();
const UserController = require('../controllers/UserManagementController');
const { prisma } = require('../../../../../core/database/prisma');
const RouterConfig = require('../../../../../core/routes/RouterConfig');
const controller = new UserController(prisma);

// 创建受JWT保护的路由
const protectedRouter = RouterConfig.authRoute(router);

/**
 * @swagger
 * /api/v1/master/system/user:
 *   get:
 *     tags: [系统管理/用户中心/用户管理]
 *     summary: 获取用户列表
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/PageParam'
 *       - $ref: '#/components/parameters/PageSizeParam'
 *     responses:
 *       200:
 *         $ref: '#/components/responses/SystemUserListResponse'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 */
protectedRouter.get('/', controller.list.bind(controller));

/**
 * @swagger
 * /api/v1/master/system/user:
 *   post:
 *     tags: [系统管理/用户中心/用户管理]
 *     summary: 创建用户
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SystemUserCreateRequest'
 *     responses:
 *       201:
 *         $ref: '#/components/responses/SystemUserResponse'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 */
protectedRouter.post('/', controller.create.bind(controller));

/**
 * @swagger
 * /api/v1/master/system/user/{id}:
 *   put:
 *     tags: [系统管理/用户中心/用户管理]
 *     summary: 更新用户
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/IdParam'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SystemUserUpdateRequest'
 *     responses:
 *       200:
 *         $ref: '#/components/responses/SystemUserResponse'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 *       404:
 *         description: 用户不存在
 */
protectedRouter.put('/:id', controller.update.bind(controller));

/**
 * @swagger
 * /api/v1/master/system/user/{id}:
 *   delete:
 *     tags: [系统管理/用户中心/用户管理]
 *     summary: 删除用户
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/IdParam'
 *     responses:
 *       204:
 *         description: 删除成功
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 *       404:
 *         description: 用户不存在
 */
protectedRouter.delete('/:id', controller.delete.bind(controller));

/**
 * @swagger
 * /api/v1/master/system/user/{id}:
 *   get:
 *     tags: [系统管理/用户中心/用户管理]
 *     summary: 获取用户详情
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/IdParam'
 *     responses:
 *       200:
 *         $ref: '#/components/responses/SystemUserResponse'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 *       404:
 *         description: 用户不存在
 */
protectedRouter.get('/:id', controller.getById.bind(controller));

/**
 * @swagger
 * /api/v1/master/system/user/{id}/force-logout:
 *   post:
 *     tags: [系统管理/用户中心/用户管理]
 *     summary: 强制用户下线
 *     description: 管理员操作，使指定用户的所有会话失效
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/IdParam'
 *     responses:
 *       200:
 *         $ref: '#/components/responses/Success'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 *       403:
 *         description: 没有权限执行此操作
 *       404:
 *         $ref: '#/components/responses/Error'
 */
protectedRouter.post('/:id/force-logout', controller.forceLogout.bind(controller));

module.exports = router;

// 导出 paths 对象供动态扫描
module.exports.paths = {
  '/api/v1/master/system/user': {
    get: {
      tags: ['系统管理/用户中心/用户管理'],
      summary: '获取用户列表',
      parameters: [
        { $ref: '#/components/parameters/PageParam' },
        { $ref: '#/components/parameters/PageSizeParam' }
      ],
      responses: {
        200: { $ref: '#/components/responses/SystemUserListResponse' },
        '200_error': { $ref: '#/components/responses/Error' },
        401: { description: '未授权，请先登录' }
      }
    },
    post: {
      tags: ['系统管理/用户中心/用户管理'],
      summary: '创建用户',
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/SystemUserCreateRequest' }
          }
        }
      },
      responses: {
        201: { $ref: '#/components/responses/SystemUserResponse' },
        '200_error': { $ref: '#/components/responses/Error' },
        401: { description: '未授权，请先登录' }
      }
    }
  },
  '/api/v1/master/system/user/{id}': {
    get: {
      tags: ['系统管理/用户中心/用户管理'],
      summary: '获取用户详情',
      parameters: [
        { $ref: '#/components/parameters/IdParam' }
      ],
      responses: {
        200: { $ref: '#/components/responses/SystemUserResponse' },
        '200_error': { $ref: '#/components/responses/Error' },
        401: { description: '未授权，请先登录' },
        404: { description: '用户不存在' }
      }
    },
    put: {
      tags: ['系统管理/用户中心/用户管理'],
      summary: '更新用户',
      parameters: [
        { $ref: '#/components/parameters/IdParam' }
      ],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/SystemUserUpdateRequest' }
          }
        }
      },
      responses: {
        200: { $ref: '#/components/responses/SystemUserResponse' },
        '200_error': { $ref: '#/components/responses/Error' },
        401: { description: '未授权，请先登录' },
        404: { description: '用户不存在' }
      }
    },
    delete: {
      tags: ['系统管理/用户中心/用户管理'],
      summary: '删除用户',
      parameters: [
        { $ref: '#/components/parameters/IdParam' }
      ],
      responses: {
        204: { description: '删除成功' },
        '200_error': { $ref: '#/components/responses/Error' },
        401: { description: '未授权，请先登录' },
        404: { description: '用户不存在' }
      }
    }
  }
};
