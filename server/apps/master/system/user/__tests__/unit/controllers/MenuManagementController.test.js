const { describe, it, beforeEach, afterEach, expect } = require('@jest/globals');
const MenuController = require('../../../controllers/MenuManagementController');
const { PrismaClient } = require('@prisma/client');

// Mock Prisma Client
jest.mock('@prisma/client', () => ({
    PrismaClient: jest.fn()
}));

describe('MenuManagementController', () => {
    // 1. 变量声明
    let menuController;
    let mockPrisma;
    let mockReq;
    let mockRes;

    // 2. 测试准备
    beforeEach(() => {
        // 清理所有 mock
        jest.clearAllMocks();

        // 初始化 mock prisma
        mockPrisma = {
            menu: {
                create: jest.fn(),
                findUnique: jest.fn(),
                findMany: jest.fn(),
                update: jest.fn(),
                delete: jest.fn(),
            }
        };

        // 初始化控制器
        menuController = new MenuController(mockPrisma);

        // 初始化请求对象
        mockReq = {
            params: {},
            body: {},
            query: {},
            user: { id: BigInt(1) }
        };

        // 初始化响应对象
        mockRes = {
            json: jest.fn(),
            status: jest.fn().mockReturnThis()
        };
    });

    // 3. 测试清理
    afterEach(() => {
        jest.clearAllMocks();
    });

    // 4. 测试用例
    describe('create()', () => {
        // 4.1 正向测试
        describe('正向测试', () => {
            it('应该成功创建顶级菜单', async () => {
                // 准备测试数据
                const menuData = {
                    parent_id: 0,
                    name: '系统管理',
                    code: 'system',
                    type: 'M',
                    icon: 'setting',
                    sort: 1
                };
                mockReq.body = menuData;

                // 模拟数据库操作
                const createdMenu = {
                    id: BigInt(1),
                    ...menuData,
                    level: '0',
                    created_at: BigInt(Date.now()),
                    updated_at: BigInt(Date.now())
                };
                mockPrisma.menu.create.mockResolvedValue(createdMenu);

                // 执行测试
                await menuController.create(mockReq, mockRes);

                // 验证结果
                expect(mockPrisma.menu.create).toHaveBeenCalledWith({
                    data: expect.objectContaining({
                        ...menuData,
                        parent_id: BigInt(0),
                        level: '0',
                        created_by: BigInt(1),
                        updated_by: BigInt(1)
                    })
                });
                expect(mockRes.json).toHaveBeenCalledWith({
                    code: 200,
                    message: '操作成功',
                    data: createdMenu
                });
            });

            it('应该成功创建子菜单', async () => {
                // 准备测试数据
                const menuData = {
                    parent_id: 1,
                    name: '用户管理',
                    code: 'user',
                    type: 'M',
                    icon: 'user',
                    sort: 1
                };
                mockReq.body = menuData;

                // 模拟父菜单查询
                const parentMenu = {
                    id: BigInt(1),
                    level: '0'
                };
                mockPrisma.menu.findUnique.mockResolvedValue(parentMenu);

                // 模拟创建操作
                const createdMenu = {
                    id: BigInt(2),
                    ...menuData,
                    level: '0,1',
                    created_at: BigInt(Date.now()),
                    updated_at: BigInt(Date.now())
                };
                mockPrisma.menu.create.mockResolvedValue(createdMenu);

                // 执行测试
                await menuController.create(mockReq, mockRes);

                // 验证结果
                expect(mockPrisma.menu.findUnique).toHaveBeenCalledWith({
                    where: { id: BigInt(1) },
                    select: { level: true }
                });
                expect(mockPrisma.menu.create).toHaveBeenCalledWith({
                    data: expect.objectContaining({
                        ...menuData,
                        parent_id: BigInt(1),
                        level: '0,1',
                        created_by: BigInt(1),
                        updated_by: BigInt(1)
                    })
                });
                expect(mockRes.json).toHaveBeenCalledWith({
                    code: 200,
                    message: '操作成功',
                    data: createdMenu
                });
            });
        });

        // 4.2 异常测试
        describe('异常测试', () => {
            it('当父菜单不存在时应该返回错误', async () => {
                // 准备测试数据
                mockReq.body = {
                    parent_id: 999,
                    name: '测试菜单',
                    code: 'test',
                    type: 'M'
                };

                // 模拟父菜单不存在
                mockPrisma.menu.findUnique.mockResolvedValue(null);

                // 执行测试
                await menuController.create(mockReq, mockRes);

                // 验证结果
                expect(mockRes.json).toHaveBeenCalledWith({
                    code: 200,
                    message: '父级菜单不存在',
                    data: null
                });
                expect(mockPrisma.menu.create).not.toHaveBeenCalled();
            });

            it('当数据库操作失败时应该返回错误', async () => {
                // 准备测试数据
                mockReq.body = {
                    parent_id: 0,
                    name: '测试菜单',
                    code: 'test',
                    type: 'M'
                };

                // 模拟数据库错误
                const dbError = new Error('数据库错误');
                mockPrisma.menu.create.mockRejectedValue(dbError);

                // 执行测试
                await menuController.create(mockReq, mockRes);

                // 验证结果
                expect(mockRes.json).toHaveBeenCalledWith({
                    code: 200,
                    message: '创建菜单失败：数据库错误',
                    data: null
                });
            });
        });

        // 4.3 边界测试
        describe('边界测试', () => {
            it('应该处理空的请求体', async () => {
                // 准备测试数据
                mockReq.body = {};

                // 执行测试
                await menuController.create(mockReq, mockRes);

                // 验证结果
                expect(mockPrisma.menu.create).not.toHaveBeenCalled();
                expect(mockRes.json).toHaveBeenCalledWith(expect.objectContaining({
                    code: 200
                }));
            });
        });
    });

    describe('update()', () => {
        // 4.1 正向测试
        describe('正向测试', () => {
            it('应该成功更新菜单基本信息', async () => {
                // 准备测试数据
                const menuId = '1';
                const updateData = {
                    name: '系统设置',
                    icon: 'settings'
                };
                mockReq.params = { id: menuId };
                mockReq.body = updateData;

                // 模拟菜单存在
                mockPrisma.menu.findUnique.mockResolvedValue({
                    id: BigInt(menuId),
                    name: '系统管理'
                });

                // 模拟更新操作
                const updatedMenu = {
                    id: BigInt(menuId),
                    ...updateData,
                    updated_at: BigInt(Date.now())
                };
                mockPrisma.menu.update.mockResolvedValue(updatedMenu);

                // 执行测试
                await menuController.update(mockReq, mockRes);

                // 验证结果
                expect(mockPrisma.menu.update).toHaveBeenCalledWith({
                    where: { id: BigInt(menuId) },
                    data: expect.objectContaining({
                        ...updateData,
                        updated_by: BigInt(1),
                        updated_at: expect.any(BigInt)
                    })
                });
                expect(mockRes.json).toHaveBeenCalledWith({
                    code: 200,
                    message: '操作成功',
                    data: updatedMenu
                });
            });
        });

        // 4.2 异常测试
        describe('异常测试', () => {
            it('当菜单不存在时应该返回错误', async () => {
                // 准备测试数据
                mockReq.params = { id: '999' };
                mockReq.body = { name: '测试菜单' };

                // 模拟菜单不存在
                mockPrisma.menu.findUnique.mockResolvedValue(null);

                // 执行测试
                await menuController.update(mockReq, mockRes);

                // 验证结果
                expect(mockRes.json).toHaveBeenCalledWith({
                    code: 200,
                    message: '菜单不存在',
                    data: null
                });
                expect(mockPrisma.menu.update).not.toHaveBeenCalled();
            });

            it('当数据库更新失败时应该返回错误', async () => {
                // 准备测试数据
                mockReq.params = { id: '1' };
                mockReq.body = { name: '测试菜单' };

                // 模拟菜单存在
                mockPrisma.menu.findUnique.mockResolvedValue({
                    id: BigInt(1)
                });

                // 模拟数据库错误
                const dbError = new Error('数据库错误');
                mockPrisma.menu.update.mockRejectedValue(dbError);

                // 执行测试
                await menuController.update(mockReq, mockRes);

                // 验证结果
                expect(mockRes.json).toHaveBeenCalledWith({
                    code: 200,
                    message: '更新菜单失败：数据库错误',
                    data: null
                });
            });
        });
    });

    describe('delete()', () => {
        // 4.1 正向测试
        describe('正向测试', () => {
            it('应该成功删除叶子节点菜单', async () => {
                // 准备测试数据
                const menuId = '1';
                mockReq.params = { id: menuId };

                // 模拟没有子菜单
                mockPrisma.menu.findMany.mockResolvedValue([]);

                // 模拟删除操作
                mockPrisma.menu.delete.mockResolvedValue({ id: BigInt(menuId) });

                // 执行测试
                await menuController.delete(mockReq, mockRes);

                // 验证结果
                expect(mockPrisma.menu.findMany).toHaveBeenCalledWith({
                    where: { parent_id: BigInt(menuId) }
                });
                expect(mockPrisma.menu.delete).toHaveBeenCalledWith({
                    where: { id: BigInt(menuId) }
                });
                expect(mockRes.json).toHaveBeenCalledWith({
                    code: 200,
                    message: '删除成功',
                    data: null
                });
            });
        });

        // 4.2 异常测试
        describe('异常测试', () => {
            it('当存在子菜单时不应该删除', async () => {
                // 准备测试数据
                mockReq.params = { id: '1' };

                // 模拟存在子菜单
                mockPrisma.menu.findMany.mockResolvedValue([{ id: BigInt(2) }]);

                // 执行测试
                await menuController.delete(mockReq, mockRes);

                // 验证结果
                expect(mockRes.json).toHaveBeenCalledWith({
                    code: 200,
                    message: '该菜单下有子菜单，不能删除',
                    data: null
                });
                expect(mockPrisma.menu.delete).not.toHaveBeenCalled();
            });

            it('当数据库删除失败时应该返回错误', async () => {
                // 准备测试数据
                mockReq.params = { id: '1' };

                // 模拟没有子菜单
                mockPrisma.menu.findMany.mockResolvedValue([]);

                // 模拟数据库错误
                const dbError = new Error('数据库错误');
                mockPrisma.menu.delete.mockRejectedValue(dbError);

                // 执行测试
                await menuController.delete(mockReq, mockRes);

                // 验证结果
                expect(mockRes.json).toHaveBeenCalledWith({
                    code: 200,
                    message: '删除菜单失败：数据库错误',
                    data: null
                });
            });
        });
    });

    describe('list()', () => {
        // 4.1 正向测试
        describe('正向测试', () => {
            it('应该返回树形结构的菜单列表', async () => {
                // 准备测试数据
                const menus = [
                    {
                        id: BigInt(1),
                        parent_id: BigInt(0),
                        name: '系统管理',
                        sort: 1
                    },
                    {
                        id: BigInt(2),
                        parent_id: BigInt(1),
                        name: '用户管理',
                        sort: 1
                    }
                ];

                // 模拟查询操作
                mockPrisma.menu.findMany.mockResolvedValue(menus);

                // 执行测试
                await menuController.list(mockReq, mockRes);

                // 验证结果
                expect(mockPrisma.menu.findMany).toHaveBeenCalledWith({
                    where: { deleted_at: null },
                    orderBy: [
                        { sort: 'asc' },
                        { id: 'asc' }
                    ]
                });
                expect(mockRes.json).toHaveBeenCalledWith({
                    code: 200,
                    message: '操作成功',
                    data: [
                        {
                            ...menus[0],
                            children: [menus[1]]
                        }
                    ]
                });
            });

            it('应该支持按名称搜索菜单', async () => {
                // 准备测试数据
                mockReq.query = { name: '用户' };

                // 模拟查询操作
                const menus = [{
                    id: BigInt(2),
                    parent_id: BigInt(1),
                    name: '用户管理'
                }];
                mockPrisma.menu.findMany.mockResolvedValue(menus);

                // 模拟 buildTree 方法
                const buildTreeSpy = jest.spyOn(menuController, 'buildTree');
                buildTreeSpy.mockReturnValue(menus);

                // 执行测试
                await menuController.list(mockReq, mockRes);

                // 验证结果
                expect(mockPrisma.menu.findMany).toHaveBeenCalledWith({
                    where: {
                        deleted_at: null,
                        name: { contains: '用户' }
                    },
                    orderBy: [
                        { sort: 'asc' },
                        { id: 'asc' }
                    ]
                });
                
                expect(buildTreeSpy).toHaveBeenCalledWith(menus);
                expect(mockRes.json).toHaveBeenCalledWith({
                    code: 200,
                    message: '操作成功',
                    data: menus
                });

                // 清理 spy
                buildTreeSpy.mockRestore();
            });
        });

        // 4.2 异常测试
        describe('异常测试', () => {
            it('当数据库查询失败时应该返回错误', async () => {
                // 模拟数据库错误
                const dbError = new Error('数据库错误');
                mockPrisma.menu.findMany.mockRejectedValue(dbError);

                // 执行测试
                await menuController.list(mockReq, mockRes);

                // 验证结果
                expect(mockRes.json).toHaveBeenCalledWith({
                    code: 200,
                    message: '获取菜单列表失败：数据库错误',
                    data: null
                });
            });
        });
    });

    describe('detail()', () => {
        // 4.1 正向测试
        describe('正向测试', () => {
            it('应该返回菜单详情', async () => {
                // 准备测试数据
                const menuId = '1';
                const menu = {
                    id: BigInt(menuId),
                    name: '系统管理',
                    code: 'system'
                };
                mockReq.params = { id: menuId };

                // 模拟查询操作
                mockPrisma.menu.findUnique.mockResolvedValue(menu);

                // 执行测试
                await menuController.detail(mockReq, mockRes);

                // 验证结果
                expect(mockPrisma.menu.findUnique).toHaveBeenCalledWith({
                    where: { id: BigInt(menuId) }
                });
                expect(mockRes.json).toHaveBeenCalledWith({
                    code: 200,
                    message: '操作成功',
                    data: menu
                });
            });
        });

        // 4.2 异常测试
        describe('异常测试', () => {
            it('当菜单不存在时应该返回错误', async () => {
                // 准备测试数据
                mockReq.params = { id: '999' };

                // 模拟菜单不存在
                mockPrisma.menu.findUnique.mockResolvedValue(null);

                // 执行测试
                await menuController.detail(mockReq, mockRes);

                // 验证结果
                expect(mockRes.json).toHaveBeenCalledWith({
                    code: 200,
                    message: '菜单不存在',
                    data: null
                });
            });

            it('当数据库查询失败时应该返回错误', async () => {
                // 准备测试数据
                mockReq.params = { id: '1' };

                // 模拟数据库错误
                const dbError = new Error('数据库错误');
                mockPrisma.menu.findUnique.mockRejectedValue(dbError);

                // 执行测试
                await menuController.detail(mockReq, mockRes);

                // 验证结果
                expect(mockRes.json).toHaveBeenCalledWith({
                    code: 200,
                    message: '获取菜单详情失败：数据库错误',
                    data: null
                });
            });
        });
    });
});
