const { describe, it, beforeEach, afterEach, expect } = require('@jest/globals');
const UserController = require('@/apps/master/system/user/controllers/UserManagementController');
const UserService = require('@/apps/master/system/user/services/UserManagementService');
const BaseController = require('@/core/controllers/BaseController');

// Mock dependencies
jest.mock('@/core/controllers/BaseController');
jest.mock('@/apps/master/system/user/services/UserManagementService');

describe('UserManagementController', () => {
  let controller;
  let mockService;
  let mockPrisma;

  beforeEach(() => {
    // 清理所有 mock
    jest.clearAllMocks();
    
    // 初始化 mock service
    mockService = {
      list: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      getById: jest.fn(),
      login: jest.fn(),
      count: jest.fn()
    };
    UserService.mockImplementation(() => mockService);

    // Mock BaseController methods
    const mockBaseController = {
      success: jest.fn((res, data, message, code = 200) => {
        res.status(code).json({
          success: true,
          message,
          data
        });
      }),
      fail: jest.fn((res, message, code = 200) => {
        res.status(code).json({
          success: false,
          message
        });
      }),
      handleDbError: jest.fn((error) => ({
        message: error.message,
        code: error.code === 'P2025' ? 404 : 200
      })),
      getPagination: jest.fn((query) => ({
        page: parseInt(query.page) || 1,
        pageSize: parseInt(query.pageSize) || 10,
        skip: (parseInt(query.page) - 1) * parseInt(query.pageSize) || 0,
        take: parseInt(query.pageSize) || 10
      })),
      buildPaginationResponse: jest.fn((list, total, pagination) => ({
        list,
        total,
        page: pagination.page,
        pageSize: pagination.pageSize
      }))
    };

    BaseController.prototype.success = mockBaseController.success;
    BaseController.prototype.fail = mockBaseController.fail;
    BaseController.prototype.handleDbError = mockBaseController.handleDbError;
    BaseController.prototype.getPagination = mockBaseController.getPagination;
    BaseController.prototype.buildPaginationResponse = mockBaseController.buildPaginationResponse;
    
    // 创建控制器实例
    controller = new UserController();
  });

  describe('list()', () => {
    it('应该返回用户列表', async () => {
      // 准备
      const mockUsers = [
        { id: 1, username: 'user1' },
        { id: 2, username: 'user2' }
      ];
      const mockTotal = 2;
      mockService.list.mockResolvedValue(mockUsers);
      mockService.count.mockResolvedValue(mockTotal);

      const mockReq = {
        query: { page: 1, pageSize: 10 }
      };
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };

      // 执行
      await controller.list(mockReq, mockRes);

      // 验证
      expect(mockService.list).toHaveBeenCalledWith(1, 10);
      expect(mockService.count).toHaveBeenCalled();
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: {
          list: mockUsers,
          total: mockTotal,
          page: 1,
          pageSize: 10
        }
      });
    });

    it('应该处理查询错误', async () => {
      // 准备
      const mockError = new Error('数据库错误');
      mockService.list.mockRejectedValue(mockError);

      const mockReq = {
        query: { page: 1, pageSize: 10 }
      };
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };

      // 执行
      await controller.list(mockReq, mockRes);

      // 验证
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: mockError.message
      });
    });
  });

  describe('create()', () => {
    it('应该成功创建用户', async () => {
      // 准备
      const mockUser = {
        username: 'newuser',
        password: '123456',
        email: '<EMAIL>'
      };
      mockService.create.mockResolvedValue(mockUser);

      const mockReq = {
        body: mockUser
      };
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };

      // 执行
      await controller.create(mockReq, mockRes);

      // 验证
      expect(mockService.create).toHaveBeenCalledWith(mockUser);
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        message: '创建用户成功',
        data: mockUser
      });
    });

    it('应该处理创建错误', async () => {
      // 准备
      const mockError = new Error('用户名已存在');
      mockService.create.mockRejectedValue(mockError);

      const mockReq = {
        body: {
          username: 'existinguser',
          password: '123456'
        }
      };
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };

      // 执行
      await controller.create(mockReq, mockRes);

      // 验证
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: mockError.message
      });
    });
  });

  describe('update()', () => {
    it('应该成功更新用户', async () => {
      const mockUser = {
        id: 1,
        username: 'updateduser',
        email: '<EMAIL>'
      };
      mockService.update.mockResolvedValue(mockUser);

      const mockReq = {
        params: { id: 1 },
        body: { username: 'updateduser' }
      };
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };

      await controller.update(mockReq, mockRes);

      expect(mockService.update).toHaveBeenCalledWith(1, { username: 'updateduser' });
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        message: '更新用户成功',
        data: mockUser
      });
    });
  });

  describe('delete()', () => {
    it('应该成功删除用户', async () => {
      mockService.delete.mockResolvedValue(undefined);

      const mockReq = {
        params: { id: 1 }
      };
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };

      await controller.delete(mockReq, mockRes);

      expect(mockService.delete).toHaveBeenCalledWith(1);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        message: '删除用户成功',
        data: null
      });
    });
  });

  describe('login()', () => {
    it('应该成功登录', async () => {
      const mockLoginResult = {
        token: 'mock_token',
        user: {
          id: 1,
          username: 'testuser',
          nickname: '测试用户'
        }
      };
      mockService.login.mockResolvedValue(mockLoginResult);

      const mockReq = {
        body: {
          username: 'testuser',
          password: '123456'
        }
      };
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      };

      await controller.login(mockReq, mockRes);

      expect(mockService.login).toHaveBeenCalledWith('testuser', '123456');
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        message: '登录成功',
        data: mockLoginResult
      });
    });
  });
});
