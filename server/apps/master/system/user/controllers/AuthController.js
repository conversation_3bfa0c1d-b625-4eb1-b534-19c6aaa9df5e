const BaseController = require('../../../../../core/controllers/BaseController');
const UserService = require('../services/UserManagementService');
const CaptchaService = require('../../../../../core/services/CaptchaService');
const encryptionUtil = require('../../../../../core/utils/EncryptionUtil');
const jwt = require('jsonwebtoken');
const redisUtil = require('../../../../../core/utils/RedisUtil');
const MenuManagementService = require('../services/MenuManagementService');
const RoleMenuService = require('../services/RoleMenuService');
const VerificationCodeService = require('../../integration/services/VerificationCodeService');
const SMSServiceFactory = require('../../integration/services/sms/SMSServiceFactory');

/**
 * 认证控制器 - 负责处理用户登录和注销
 */
class AuthController extends BaseController {
  constructor(prisma) {
    super(prisma);
    this.userService = new UserService(prisma);
    this.captchaService = new CaptchaService();
    this.menuService = new MenuManagementService(prisma);
    this.roleMenuService = new RoleMenuService();
    this.verificationCodeService = new VerificationCodeService(prisma);
    
    // 记录登录失败的IP和账号信息
    this.loginAttempts = new Map();
  }
  
  /**
   * 获取图形验证码
   * @swagger
   */
  async getCaptcha(req, res) {
    try {
      // 生成验证码
      const captchaData = await this.captchaService.generateCaptcha({
        size: 4,
        noise: 3,
        color: true,
        width: 120,
        height: 40
      });
      
      // 返回验证码ID和图片
      this.success(res, '获取验证码成功', {
        id: captchaData.id,
        image: captchaData.image
      });
    } catch (err) {
      console.error('生成验证码失败:', err);
      this.fail(res, '生成验证码失败', null, 500);
    }
  }
  
  /**
   * 获取短信验证码
   * @swagger
   */
  async getSmsVerificationCode(req, res) {
    console.log("auth")
    try {
      const { phoneNumber, type = 'default', templateCode } = req.body;
      
      if (!phoneNumber) {
        return this.fail(res, '手机号码不能为空');
      }

      // 验证手机号格式
      if (!/^1[3-9]\d{9}$/.test(phoneNumber)) {
        return this.fail(res, '手机号格式不正确');
      }
      
      // 检查短信模板编码
      if (!templateCode) {
        return this.fail(res, '短信模板编码不能为空');
      }

      // 使用验证码服务生成验证码
      const codeResult = await this.verificationCodeService.getCode(phoneNumber, type);
      
      // 获取短信服务工厂
      const smsFactory = new SMSServiceFactory(this.prisma);
      
      try {
        // 获取默认短信服务
        const smsService = await smsFactory.getDefaultService();
        
        // 发送验证码短信
        const smsResult = await smsService.sendSMS(
          phoneNumber, 
          templateCode, 
          { code: codeResult.code }
        );

        if (!smsResult.success) {
          return this.fail(res, '验证码短信发送失败: ' + smsResult.message);
        }

        return this.success(res, {
          success: true,
          message: `验证码已发送，有效期5分钟，可重复登录使用`,
          expireTime: codeResult.expireTime
        }, '获取验证码成功');
      } catch (error) {
        console.error('发送短信验证码失败:', error);
        return this.fail(res, '发送短信验证码失败: ' + error.message);
      }
    } catch (error) {
      console.error('获取验证码失败:', error);
      return this.fail(res, '获取验证码失败: ' + error.message);
    }
  }

  /**
   * 用户登录
   * @swagger
   */
  async login(req, res) {
    try {
      const { username, password, captcha, captchaId } = req.body;
      
      // 获取客户端IP
      const clientIp = req.headers['x-forwarded-for'] || 
                      req.connection.remoteAddress || 
                      req.socket.remoteAddress || 
                      '0.0.0.0';
      
      // 检查IP是否被锁定（尝试次数过多）
      //const isIpLocked = await this.captchaService.isIpLocked(clientIp);
      //if (isIpLocked) {
      //  return this.fail(res, '尝试次数过多，请稍后再试', 429);
      //}
      
      // 验证码验证
      if (!captcha || !captchaId) {
        return this.fail(res, '验证码不能为空', 200);
      }
      
      // 验证验证码是否正确
      // 开发测试阶段，暂时略过验证码验证
      const captchaValid = true; // await this.captchaService.verifyCaptcha(captchaId, captcha);
      if (!captchaValid) {
        // 增加IP尝试次数
        await this.captchaService.incrementIpAttempts(clientIp);
        return this.fail(res, '验证码错误或已过期', 200);
      }
      
      try {
        // 默认使用原始密码
        let decryptedPassword = password;
        
        // 如果密码被加密（以加密形式传输）
        if (req.body.encrypted === true) {
          try {
            // 解密密码
            decryptedPassword = encryptionUtil.decrypt(password);
          } catch (decryptErr) {
            console.error('密码解密失败:', decryptErr.message);
            return this.fail(res, '密码格式错误，无法解密', 200);
          }
        }
        
        // 验证用户名和解密后的密码，传递IP地址
        const result = await this.userService.login(username, decryptedPassword, clientIp);
        
        // 获取用户角色对应的菜单树
        if (result.user && result.user.role_id) {
          try {
            // 获取用户权限菜单树
            const menuTreeResult = await this.roleMenuService.getUserMenuTree(result.user.role_id);
            result.menuTree = menuTreeResult.menuTree;
          } catch (menuErr) {
            console.error('获取菜单树失败:', menuErr.message);
            result.menuTree = [];
          }
        } else {
          console.log('用户没有角色，不获取菜单树');
          result.menuTree = [];
        }
        console.log('登录成功，返回结果:', result);
        // 登录成功，返回结果
        this.success(res, result, '登录成功');
      } catch (loginErr) {
        // 登录失败，增加IP尝试次数
        await this.captchaService.incrementIpAttempts(clientIp);
        console.error('登录失败:', loginErr.message);
        throw loginErr;
      }
    } catch (err) {
      const { message, code } = this.handleDbError(err);
      console.error('登录失败:', message);
      this.fail(res, message, code);
    }
  }

  /**
   * 手机号登录
   * @swagger
   */
  async phoneLogin(req, res) {
    try {
      const { phone, code, type = 'login' } = req.body;
      
      // 获取客户端IP
      const clientIp = req.headers['x-forwarded-for'] || 
                      req.connection.remoteAddress || 
                      req.socket.remoteAddress || 
                      '0.0.0.0';
      
      // 验证手机号格式
      if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
        return this.fail(res, '手机号格式不正确', 200);
      }
      
      // 验证码不能为空
      if (!code) {
        return this.fail(res, '验证码不能为空', 200);
      }
      
      try {
        // 验证短信验证码
        try {
          await this.verificationCodeService.verifyCode(phone, code, type);
        } catch (verifyErr) {
          console.error('验证码验证失败:', verifyErr.message);
          return this.fail(res, verifyErr.message, 200);
        }
        
        // 验证通过，使用手机号登录
        const result = await this.userService.loginByPhone(phone, clientIp);
        
        // 获取用户角色对应的菜单树
        if (result.user && result.user.role_id) {
          try {
            // 获取用户权限菜单树
            const menuTreeResult = await this.roleMenuService.getUserMenuTree(result.user.role_id);
            result.menuTree = menuTreeResult.menuTree;
          } catch (menuErr) {
            console.error('获取菜单树失败:', menuErr.message);
            result.menuTree = [];
          }
        } else {
          console.log('用户没有角色，不获取菜单树');
          result.menuTree = [];
        }
        
        console.log('手机号登录成功，返回结果:', result);
        // 登录成功，返回结果
        this.success(res, result, '登录成功');
      } catch (loginErr) {
        console.error('手机号登录失败:', loginErr.message);
        throw loginErr;
      }
    } catch (err) {
      const { message, code } = this.handleDbError(err);
      console.error('手机号登录失败:', message);
      this.fail(res, message, code);
    }
  }

  /**
   * 用户注销
   * @swagger
   */
  async logout(req, res) {
    try {
      // 从请求头或请求体中获取token
      const token = req.headers.authorization?.split(' ')[1] || req.body.token;
      
      if (!token) {
        return this.fail(res, '未提供有效的令牌', 200);
      }
      
      try {
        // 解析JWT令牌，获取用户ID
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'julingcloud-secret-key');
        const userId = decoded.id;
        
        // 从Redis中删除该用户的令牌
        await redisUtil.removeUserToken(userId, token);
        
        this.success(res, null, '注销成功');
      } catch (jwtErr) {
        console.error('解析JWT令牌失败:', jwtErr.message);
        this.fail(res, '令牌无效或已过期', 200);
      }
    } catch (err) {
      const { message, code } = this.handleDbError(err);
      console.error('注销失败:', message);
      this.fail(res, message, code);
    }
  }
}

module.exports = AuthController;
