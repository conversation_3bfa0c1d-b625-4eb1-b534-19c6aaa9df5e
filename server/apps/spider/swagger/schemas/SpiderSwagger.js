/**
 * 爬虫Swagger文档定义
 */

const definitions = {
  Spider: {
    type: 'object',
    properties: {
      id: {
        type: 'integer',
        format: 'int64',
        description: '爬虫ID'
      },
      name: {
        type: 'string',
        description: '爬虫名称'
      },
      code: {
        type: 'string',
        description: '爬虫代码，唯一标识'
      },
      command: {
        type: 'string',
        description: '爬虫命令'
      },
      remark: {
        type: 'string',
        description: '备注'
      },
      version: {
        type: 'string',
        description: '爬虫版本'
      },
      status: {
        type: 'integer',
        description: '状态：1-启用，0-禁用'
      },
      created_at: {
        type: 'integer',
        format: 'int64',
        description: '创建时间戳（毫秒）'
      },
      updated_at: {
        type: 'integer',
        format: 'int64',
        description: '更新时间戳（毫秒）'
      }
    }
  },
  SpiderCreateRequest: {
    type: 'object',
    required: ['name', 'code', 'version', 'command'],
    properties: {
      name: {
        type: 'string',
        description: '爬虫名称'
      },
      code: {
        type: 'string',
        description: '爬虫代码，唯一标识'
      },
      command: {
        type: 'string',
        description: '爬虫命令'
      },
      remark: {
        type: 'string',
        description: '备注'
      },
      version: {
        type: 'string',
        description: '爬虫版本'
      },
      status: {
        type: 'integer',
        description: '状态：1-启用，0-禁用'
      }
    }
  },
  SpiderUpdateRequest: {
    type: 'object',
    required: ['name', 'code', 'version', 'command'],
    properties: {
      name: {
        type: 'string',
        description: '爬虫名称'
      },
      code: {
        type: 'string',
        description: '爬虫代码，唯一标识'
      },
      command: {
        type: 'string',
        description: '爬虫命令'
      },
      remark: {
        type: 'string',
        description: '备注'
      },
      version: {
        type: 'string',
        description: '爬虫版本'
      },
      status: {
        type: 'integer',
        description: '状态：1-启用，0-禁用'
      }
    }
  },
  SpiderResponse: {
    type: 'object',
    properties: {
      code: {
        type: 'integer',
        description: '状态码'
      },
      data: {
        $ref: '#/definitions/Spider',
        description: '爬虫数据'
      },
      message: {
        type: 'string',
        description: '响应消息'
      }
    }
  },
  SpiderListResponse: {
    type: 'object',
    properties: {
      code: {
        type: 'integer',
        description: '状态码'
      },
      data: {
        type: 'object',
        properties: {
          total: {
            type: 'integer',
            description: '总记录数'
          },
          items: {
            type: 'array',
            description: '爬虫列表',
            items: {
              $ref: '#/definitions/Spider'
            }
          }
        }
      },
      message: {
        type: 'string',
        description: '响应消息'
      }
    }
  }
};

/**
 * 爬虫API文档
 * @swagger
 * /api/spider/spiders:
 *   get:
 *     tags:
 *       - 爬虫管理
 *     summary: 获取爬虫列表
 *     description: 分页获取爬虫列表，支持按名称、代码和状态筛选
 *     parameters:
 *       - name: page
 *         in: query
 *         description: 页码，默认为1
 *         required: false
 *         type: integer
 *       - name: pageSize
 *         in: query
 *         description: 每页记录数，默认为10
 *         required: false
 *         type: integer
 *       - name: name
 *         in: query
 *         description: 爬虫名称（模糊查询）
 *         required: false
 *         type: string
 *       - name: code
 *         in: query
 *         description: 爬虫代码（精确查询）
 *         required: false
 *         type: string
 *       - name: status
 *         in: query
 *         description: 状态：1-启用，0-禁用
 *         required: false
 *         type: integer
 *     responses:
 *       200:
 *         description: 成功
 *         schema:
 *           $ref: '#/definitions/SpiderListResponse'
 *   post:
 *     tags:
 *       - 爬虫管理
 *     summary: 创建爬虫
 *     description: 创建新的爬虫
 *     parameters:
 *       - name: body
 *         in: body
 *         description: 爬虫创建参数
 *         required: true
 *         schema:
 *           $ref: '#/definitions/SpiderCreateRequest'
 *     responses:
 *       201:
 *         description: 创建成功
 *         schema:
 *           $ref: '#/definitions/SpiderResponse'
 *       400:
 *         description: 参数错误
 *         schema:
 *           type: object
 *           properties:
 *             code:
 *               type: integer
 *               description: 状态码
 *             message:
 *               type: string
 *               description: 错误消息
 * 
 * /api/spider/spiders/{id}:
 *   get:
 *     tags:
 *       - 爬虫管理
 *     summary: 获取爬虫详情
 *     description: 根据ID获取爬虫详情
 *     parameters:
 *       - name: id
 *         in: path
 *         description: 爬虫ID
 *         required: true
 *         type: integer
 *         format: int64
 *     responses:
 *       200:
 *         description: 成功
 *         schema:
 *           $ref: '#/definitions/SpiderResponse'
 *       404:
 *         description: 爬虫不存在
 *         schema:
 *           type: object
 *           properties:
 *             code:
 *               type: integer
 *               description: 状态码
 *             message:
 *               type: string
 *               description: 错误消息
 *   put:
 *     tags:
 *       - 爬虫管理
 *     summary: 更新爬虫
 *     description: 根据ID更新爬虫信息
 *     parameters:
 *       - name: id
 *         in: path
 *         description: 爬虫ID
 *         required: true
 *         type: integer
 *         format: int64
 *       - name: body
 *         in: body
 *         description: 爬虫更新参数
 *         required: true
 *         schema:
 *           $ref: '#/definitions/SpiderUpdateRequest'
 *     responses:
 *       200:
 *         description: 更新成功
 *         schema:
 *           $ref: '#/definitions/SpiderResponse'
 *       400:
 *         description: 参数错误
 *         schema:
 *           type: object
 *           properties:
 *             code:
 *               type: integer
 *               description: 状态码
 *             message:
 *               type: string
 *               description: 错误消息
 *       404:
 *         description: 爬虫不存在
 *         schema:
 *           type: object
 *           properties:
 *             code:
 *               type: integer
 *               description: 状态码
 *             message:
 *               type: string
 *               description: 错误消息
 *   delete:
 *     tags:
 *       - 爬虫管理
 *     summary: 删除爬虫
 *     description: 根据ID删除爬虫（软删除）
 *     parameters:
 *       - name: id
 *         in: path
 *         description: 爬虫ID
 *         required: true
 *         type: integer
 *         format: int64
 *     responses:
 *       200:
 *         description: 删除成功
 *         schema:
 *           type: object
 *           properties:
 *             code:
 *               type: integer
 *               description: 状态码
 *             message:
 *               type: string
 *               description: 响应消息
 *       404:
 *         description: 爬虫不存在
 *         schema:
 *           type: object
 *           properties:
 *             code:
 *               type: integer
 *               description: 状态码
 *             message:
 *               type: string
 *               description: 错误消息
 *
 */

module.exports = {
  definitions
};
