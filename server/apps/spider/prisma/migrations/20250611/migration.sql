-- 修改 spider_task 表结构
ALTER TABLE spider.spider_task ADD COLUMN IF NOT EXISTS spider_id BIGINT;
ALTER TABLE spider.spider_task ADD COLUMN IF NOT EXISTS store_id BIGINT;
ALTER TABLE spider.spider_task ADD COLUMN IF NOT EXISTS run_interval INTEGER;

-- 删除不再需要的字段
ALTER TABLE spider.spider_task DROP COLUMN IF EXISTS task_name;
ALTER TABLE spider.spider_task DROP COLUMN IF EXISTS parameters;
ALTER TABLE spider.spider_task DROP COLUMN IF EXISTS status;
ALTER TABLE spider.spider_task DROP COLUMN IF EXISTS timeout;
ALTER TABLE spider.spider_task DROP COLUMN IF EXISTS retry_count;
ALTER TABLE spider.spider_task DROP COLUMN IF EXISTS priority;
ALTER TABLE spider.spider_task DROP COLUMN IF EXISTS remark;

-- 添加索引
CREATE INDEX IF NOT EXISTS spider_task_spider_id_idx ON spider.spider_task(spider_id);
CREATE INDEX IF NOT EXISTS spider_task_store_id_idx ON spider.spider_task(store_id);

-- 删除不再需要的索引
DROP INDEX IF EXISTS spider.spider_task_status_idx;
