/**
 * 服务商用户认证路径 Swagger 定义
 */
module.exports = {
  '/api/v1/provider/user/captcha': {
    get: {
      tags: ['服务商用户管理'],
      summary: '获取验证码',
      description: '获取短信验证码',
      parameters: [
        {
          in: 'query',
          name: 'phone',
          required: true,
          schema: {
            type: 'string'
          },
          description: '手机号'
        }
      ],
      responses: {
        200: {
          description: '获取成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  code: {
                    type: 'integer',
                    example: 200
                  },
                  message: {
                    type: 'string',
                    example: '获取验证码成功'
                  },
                  data: {
                    $ref: '#/components/schemas/CaptchaResponse'
                  }
                }
              }
            }
          }
        },
        400: {
          description: '请求参数错误'
        }
      }
    }
  },
  '/api/v1/provider/user/register': {
    post: {
      tags: ['服务商用户管理'],
      summary: '用户注册',
      description: '服务商用户注册接口',
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ProviderUserRegistration'
            }
          }
        }
      },
      responses: {
        200: {
          description: '注册成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  code: {
                    type: 'integer',
                    example: 200
                  },
                  message: {
                    type: 'string',
                    example: '注册成功'
                  },
                  data: {
                    type: 'object',
                    properties: {
                      user: {
                        type: 'object',
                        properties: {
                          id: {
                            type: 'string',
                            example: '123456789012345678'
                          },
                          username: {
                            type: 'string',
                            example: 'testuser'
                          },
                          nickname: {
                            type: 'string',
                            example: '测试用户'
                          },
                          phone: {
                            type: 'string',
                            example: '13800138000'
                          },
                          created_at: {
                            type: 'integer',
                            example: 1623123456789
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        400: {
          description: '请求参数错误'
        },
        500: {
          description: '服务器内部错误'
        }
      }
    }
  },
  '/api/v1/provider/user/login': {
    post: {
      tags: ['服务商用户管理'],
      summary: '用户登录',
      description: '服务商用户登录接口',
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              required: ['username', 'password'],
              properties: {
                username: {
                  type: 'string',
                  example: 'testuser',
                  description: '用户名或手机号'
                },
                password: {
                  type: 'string',
                  example: 'password123',
                  description: '密码'
                }
              }
            }
          }
        }
      },
      responses: {
        200: {
          description: '登录成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  code: {
                    type: 'integer',
                    example: 200
                  },
                  message: {
                    type: 'string',
                    example: '登录成功'
                  },
                  data: {
                    type: 'object',
                    properties: {
                      id: {
                        type: 'string',
                        example: '123456789012345678'
                      },
                      username: {
                        type: 'string',
                        example: 'testuser'
                      },
                      nickname: {
                        type: 'string',
                        example: '测试用户'
                      },
                      phone: {
                        type: 'string',
                        example: '13800138000'
                      },
                      token: {
                        type: 'string',
                        example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
                      },
                      expires_in: {
                        type: 'integer',
                        example: 86400
                      }
                    }
                  }
                }
              }
            }
          }
        },
        400: {
          description: '用户名或密码错误'
        },
        403: {
          description: '账号已被禁用'
        },
        500: {
          description: '服务器内部错误'
        }
      }
    }
  },
  '/api/v1/provider/user/reset-password/captcha': {
    get: {
      tags: ['服务商用户管理'],
      summary: '获取重置密码验证码',
      description: '获取重置密码的短信验证码',
      parameters: [
        {
          in: 'query',
          name: 'phone',
          required: true,
          schema: {
            type: 'string'
          },
          description: '手机号'
        }
      ],
      responses: {
        200: {
          description: '获取成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  code: {
                    type: 'integer',
                    example: 200
                  },
                  message: {
                    type: 'string',
                    example: '获取验证码成功'
                  },
                  data: {
                    $ref: '#/components/schemas/CaptchaResponse'
                  }
                }
              }
            }
          }
        },
        400: {
          description: '手机号不正确或未注册'
        },
        500: {
          description: '服务器内部错误'
        }
      }
    }
  },
  '/api/v1/provider/user/reset-password': {
    post: {
      tags: ['服务商用户管理'],
      summary: '重置密码',
      description: '服务商用户重置密码接口',
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              required: ['phone', 'captcha', 'newPassword'],
              properties: {
                phone: {
                  type: 'string',
                  example: '13800138000',
                  description: '手机号'
                },
                captcha: {
                  type: 'string',
                  example: '123456',
                  description: '验证码'
                },
                newPassword: {
                  type: 'string',
                  example: 'newpassword123',
                  description: '新密码'
                }
              }
            }
          }
        }
      },
      responses: {
        200: {
          description: '重置成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  code: {
                    type: 'integer',
                    example: 200
                  },
                  message: {
                    type: 'string',
                    example: '密码重置成功'
                  },
                  data: {
                    type: 'null'
                  }
                }
              }
            }
          }
        },
        400: {
          description: '请求参数错误或验证码错误'
        },
        404: {
          description: '用户不存在'
        },
        500: {
          description: '服务器内部错误'
        }
      }
    }
  },
  '/api/v1/provider/user/logout': {
    post: {
      tags: ['服务商用户管理'],
      summary: '退出登录',
      description: '服务商用户退出登录接口',
      security: [
        { bearerAuth: [] }
      ],
      responses: {
        200: {
          description: '退出成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  code: {
                    type: 'integer',
                    example: 200
                  },
                  message: {
                    type: 'string',
                    example: '退出登录成功'
                  },
                  data: {
                    type: 'null'
                  }
                }
              }
            }
          }
        },
        500: {
          description: '服务器内部错误'
        }
      }
    }
  },
  '/api/v1/provider/user/phone-login/captcha': {
    get: {
      tags: ['服务商用户管理'],
      summary: '获取手机登录验证码',
      description: '获取手机登录的短信验证码',
      parameters: [
        {
          in: 'query',
          name: 'phone',
          required: true,
          schema: {
            type: 'string'
          },
          description: '手机号'
        }
      ],
      responses: {
        200: {
          description: '获取成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  code: {
                    type: 'integer',
                    example: 200
                  },
                  message: {
                    type: 'string',
                    example: '获取验证码成功'
                  },
                  data: {
                    $ref: '#/components/schemas/CaptchaResponse'
                  }
                }
              }
            }
          }
        },
        400: {
          description: '手机号不正确或未注册'
        },
        500: {
          description: '服务器内部错误'
        }
      }
    }
  },
  '/api/v1/provider/user/phone-login': {
    post: {
      tags: ['服务商用户管理'],
      summary: '手机验证码登录',
      description: '手机号+验证码登录接口',
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              required: ['phone', 'captcha'],
              properties: {
                phone: {
                  type: 'string',
                  example: '13800138000',
                  description: '手机号'
                },
                captcha: {
                  type: 'string',
                  example: '123456',
                  description: '验证码'
                }
              }
            }
          }
        }
      },
      responses: {
        200: {
          description: '登录成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  code: {
                    type: 'integer',
                    example: 200
                  },
                  message: {
                    type: 'string',
                    example: '登录成功'
                  },
                  data: {
                    type: 'object',
                    properties: {
                      id: {
                        type: 'string',
                        example: '123456789012345678'
                      },
                      username: {
                        type: 'string',
                        example: 'testuser'
                      },
                      nickname: {
                        type: 'string',
                        example: '测试用户'
                      },
                      phone: {
                        type: 'string',
                        example: '13800138000'
                      },
                      token: {
                        type: 'string',
                        example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
                      },
                      expires_in: {
                        type: 'integer',
                        example: 86400
                      }
                    }
                  }
                }
              }
            }
          }
        },
        400: {
          description: '验证码错误'
        },
        403: {
          description: '账号已被禁用'
        },
        404: {
          description: '手机号未注册'
        },
        500: {
          description: '服务器内部错误'
        }
      }
    }
  }
};
