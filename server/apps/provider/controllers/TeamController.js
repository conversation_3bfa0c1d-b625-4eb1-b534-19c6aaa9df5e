const BaseController = require('../../../core/controllers/BaseController');
const TeamService = require('../services/TeamService');
const TeamDto = require('../dto/TeamDto');

class TeamController extends BaseController {
  constructor(prisma) {
    super(prisma);
    this.teamService = new TeamService(prisma);
  }

  /**
   * 获取团队列表
   */
  async list(req, res) {
    try {
      // 参数校验（包含分页参数）
      const { error, value } = TeamDto.validateQuery(req.query);
      if (error) {
        return this.fail(res, error.details[0].message, 200);
      }
      
      // 提取分页参数
      const { page, pageSize, ...filters } = value;
      
      // 组装分页参数
      const pagination = this.getPagination({ page, pageSize });
      
      // 调用服务获取团队列表
      const result = await this.teamService.list({ ...filters, ...pagination });
      
      // 使用标准列表响应格式
      this.successList(
        res,
        result.list,
        result.total,
        page,
        pageSize,
        '获取团队列表成功'
      );
    } catch (err) {
      console.error('获取团队列表失败:', err);
      const { message, code } = this.handleDbError(err);
      this.fail(res, message, code);
    }
  }

  /**
   * 创建团队
   */
  async create(req, res) {
    try {
      // 验证请求数据
      const { error, value } = TeamDto.validateCreate(req.body);
      if (error) {
        return this.fail(res, error.details[0].message);
      }
      
      console.log('创建团队请求数据:', req.body);
      console.log('验证后的数据:', value);
      
      // 添加创建者信息
      value.created_by = req.user?.id;
      
      console.log('传递给service的数据:', value);
      
      const team = await this.teamService.create(value);
      this.success(res, team, '创建团队成功', 200);
    } catch (err) {
      console.error('创建团队失败:', err);
      const { message, code } = this.handleDbError(err);
      this.fail(res, message, code);
    }
  }

  /**
   * 更新团队
   */
  async update(req, res) {
    try {
      const data = { ...req.body, id: req.params.id };
      const { error, value } = TeamDto.validateUpdate(data);
      if (error) {
        return this.fail(res, error.details[0].message, 200);
      }
      value.updated_by = req.user?.id;
      const result = await this.teamService.update(value.id, value);
      this.success(res, result, '更新团队成功');
    } catch (error) {
      console.error('更新团队失败:', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 删除团队
   */
  async delete(req, res) {
    try {
      // 传入当前操作用户ID作为更新人
      await this.teamService.delete(req.params.id, req.user?.id);
      this.success(res, null, '删除团队成功');
    } catch (err) {
      console.error('删除团队失败:', err);
      const { message, code } = this.handleDbError(err);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取团队详情
   */
  async getById(req, res) {
    try {
      const team = await this.teamService.getById(req.params.id);
      this.success(res, team, '获取团队详情成功');
    } catch (err) {
      console.error('获取团队详情失败:', err);
      const { message, code } = this.handleDbError(err);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取用户列表（用于团队成员选择）
   */
  async getUserList(req, res) {
    try {
      // 获取系统用户列表
      const users = await this.prisma.baseSystemUser.findMany({
        where: {
          deleted_at: null
          // 不过滤status，获取所有未删除的用户
        },
        select: {
          id: true,
          username: true,
          nickname: true,
          dept_id: true
        },
        orderBy: {
          created_at: 'desc'
        }
      });

      // 获取部门信息
      const deptIds = users.filter(u => u.dept_id).map(u => u.dept_id);
      const depts = await this.prisma.baseSystemDept.findMany({
        where: {
          id: { in: deptIds },
          deleted_at: null
        },
        select: {
          id: true,
          name: true
        }
      });

      const deptMap = new Map(depts.map(d => [d.id.toString(), d.name]));

      // 格式化用户数据
      const formattedUsers = users.map(user => ({
        id: user.id.toString(),
        name: user.nickname || user.username,
        username: user.username,
        nickname: user.nickname,
        department: user.dept_id ? deptMap.get(user.dept_id.toString()) || '未知部门' : '无部门',
        dept_id: user.dept_id ? user.dept_id.toString() : null
      }));

      this.success(res, formattedUsers, '获取用户列表成功');
    } catch (err) {
      console.error('获取用户列表失败:', err);
      const { message, code } = this.handleDbError(err);
      this.fail(res, message, code);
    }
  }
}

module.exports = TeamController;
