/**
 * 服务商用户认证控制器
 * 处理用户登录、注册、退出登录、修改密码等认证相关功能
 */
const ProviderBaseController = require('./base/ProviderBaseController');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const redisUtil = require('../../../core/utils/RedisUtil');
const authConfig = require('../../../config/auth.config');
const JWT_SECRET = authConfig.jwt.secret;
const JWT_EXPIRES_IN = authConfig.jwt.expire;
const CaptchaService = require('../../../core/services/CaptchaService');
const VerificationCodeService = require('../../../apps/master/system/integration/services/VerificationCodeService');

// 创建验证码服务实例
const verificationCodeService = new VerificationCodeService();

class UserAuthController extends ProviderBaseController {
  /**
   * 根据用户ID更新账号信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateUserById(req, res) {
    try {
      const userId = req.params.id;
      if (!userId) {
        return this.fail(res, '用户ID不能为空', 400);
      }

      // 获取要更新的字段
      const { username, phone } = req.body;

      // 至少需要提供一个字段进行更新
      if (!username && !phone) {
        return this.fail(res, '请提供要更新的账号信息', 400);
      }

      try {
        // 将用户ID转换为BigInt类型
        const userIdBigInt = BigInt(userId);

        console.log('根据ID更新账号信息 - 用户ID:', userId);

        // 验证用户是否存在
        const existingUsers = await this.prisma.$queryRaw`
          SELECT id FROM "provider"."provider_user" 
          WHERE "id" = ${userIdBigInt}
          AND "deleted_at" IS NULL
        `;

        if (!existingUsers || existingUsers.length === 0) {
          return this.fail(res, '用户不存在', 404);
        }

        // 如果提供了手机号，验证手机号格式
        if (phone) {
          const phoneRegex = /^1[3-9]\d{9}$/;
          if (!phoneRegex.test(phone)) {
            return this.fail(res, '手机号格式不正确', 400);
          }

          // 验证手机号是否已被使用
          const existingPhone = await this.prisma.$queryRaw`
            SELECT id FROM "provider"."provider_user" 
            WHERE "phone" = ${phone}
            AND "id" != ${userIdBigInt}
            AND "deleted_at" IS NULL
          `;

          if (existingPhone && existingPhone.length > 0) {
            return this.fail(res, '手机号已被使用', 400);
          }
        }

        // 如果提供了账号名，验证是否已被使用
        if (username) {
          const existingUsername = await this.prisma.$queryRaw`
            SELECT id FROM "provider"."provider_user" 
            WHERE "username" = ${username}
            AND "id" != ${userIdBigInt}
            AND "deleted_at" IS NULL
          `;

          if (existingUsername && existingUsername.length > 0) {
            return this.fail(res, '账号名已被使用', 400);
          }
        }

        // 直接使用 Prisma 的 $queryRaw 进行更新
        const now = Date.now();

        if (username && phone) {
          await this.prisma.$executeRaw`
            UPDATE "provider"."provider_user"
            SET "username" = ${username}, 
                "phone" = ${phone}, 
                "updated_at" = ${now}
            WHERE "id" = ${userIdBigInt}
          `;
        } else if (username) {
          await this.prisma.$executeRaw`
            UPDATE "provider"."provider_user"
            SET "username" = ${username}, 
                "updated_at" = ${now}
            WHERE "id" = ${userIdBigInt}
          `;
        } else if (phone) {
          await this.prisma.$executeRaw`
            UPDATE "provider"."provider_user"
            SET "phone" = ${phone}, 
                "updated_at" = ${now}
            WHERE "id" = ${userIdBigInt}
          `;
        }

        console.log('用户信息更新成功');

        return this.success(res, null, '账号信息更新成功');
      } catch (err) {
        console.error('解析用户ID失败:', err.message);
        return this.fail(res, '用户ID无效或更新失败', 400);
      }
    } catch (err) {
      console.error('更新账号信息失败:', err.message);
      return this.fail(res, err.message, 500);
    }
  }

  /**
   * 根据用户ID获取账号信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getUserById(req, res) {
    try {
      const userId = req.params.id;
      if (!userId) {
        return this.fail(res, '用户ID不能为空', 400);
      }

      try {
        // 将用户ID转换为BigInt类型
        const userIdBigInt = BigInt(userId);

        console.log('根据ID获取账号信息 - 用户ID:', userId);

        // 查询用户信息
        const users = await this.prisma.$queryRaw`
          SELECT id, username, phone, created_at, last_login_time, last_login_ip
          FROM "provider"."provider_user" 
          WHERE "id" = ${userIdBigInt}
          AND "deleted_at" IS NULL
        `;

        if (!users || users.length === 0) {
          return this.fail(res, '用户不存在', 404);
        }

        const user = users[0];

        // 构造返回数据
        const accountInfo = {
          id: user.id.toString(),
          username: user.username,
          phone: user.phone,
          createdAt: user.created_at ? user.created_at.toString() : null,
          lastLoginTime: user.last_login_time ? user.last_login_time.toString() : null,
          lastLoginIp: user.last_login_ip || null
        };

        return this.success(res, accountInfo, '获取账号信息成功');
      } catch (err) {
        console.error('解析用户ID失败:', err.message);
        return this.fail(res, '用户ID无效', 400);
      }
    } catch (err) {
      console.error('获取账号信息失败:', err.message);
      return this.fail(res, err.message, 500);
    }
  }

  /**
   * 用户注册
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async register(req, res) {
    try {
      // 验证必填字段
      const requiredFields = ['username', 'password', 'phone', 'captcha'];
      const validation = this.validateFields(req.body, requiredFields);
      if (!validation.valid) {
        return this.fail(res, '用户名、密码、手机号和验证码不能为空', 400);
      }

      const { username, phone, captcha } = req.body;

      // 验证手机号格式
      if (!this.validatePhoneFormat(phone)) {
        return this.fail(res, '手机号格式不正确', 400);
      }

      // 验证验证码
      try {
        console.log('开始验证验证码，手机号:', phone, '验证码:', captcha);

        // 检查Redis连接状态
        console.log('Redis连接状态:', redisUtil.isConnected ? '已连接' : '未连接');

        // 直接从 Redis 中获取验证码进行比较
        const codeKey = `verification_code:register:${phone}`;
        console.log('验证码键:', codeKey);

        const savedCode = await redisUtil.getClient().get(codeKey);
        console.log('Redis中存储的验证码:', savedCode || '无');

        if (savedCode) {
          console.log('验证码比较:');
          console.log(`- 输入验证码: ${captcha}, 类型: ${typeof captcha}, 长度: ${captcha.length}`);
          console.log(`- 存储验证码: ${savedCode}, 类型: ${typeof savedCode}, 长度: ${savedCode.length}`);

          if (savedCode !== captcha) {
            console.log('验证码不匹配');
            return this.fail(res, '验证码错误', 400);
          }

          console.log('验证码验证成功');
        } else {
          console.log('验证码不存在或已过期');
          return this.fail(res, '验证码不存在或已过期，请重新获取', 400);
        }
      } catch (error) {
        console.error('验证验证码失败:', error);
        return this.fail(res, '验证验证码失败: ' + error.message, 500);
      }

      // 检查用户名是否已存在
      try {
        const existingUsers = await this.prisma.$queryRaw`
          SELECT id FROM "provider"."provider_user" 
          WHERE "username" = ${username}
          AND "deleted_at" IS NULL
        `;

        if (existingUsers && existingUsers.length > 0) {
          return this.fail(res, '用户名已存在', 400);
        }
      } catch (queryError) {
        console.error('查询用户名是否存在失败:', queryError);
        return this.fail(res, '验证用户名失败，请稍后再试', 500);
      }

      // 检查手机号是否已存在
      try {
        const existingUsers = await this.prisma.$queryRaw`
          SELECT id FROM "provider"."provider_user" 
          WHERE "phone" = ${phone}
          AND "deleted_at" IS NULL
        `;

        if (existingUsers && existingUsers.length > 0) {
          return this.fail(res, '手机号已被注册', 400);
        }
      } catch (queryError) {
        console.error('查询手机号是否存在失败:', queryError);
        return this.fail(res, '验证手机号失败，请稍后再试', 500);
      }

      // 创建用户
      try {
        // 加密密码
        const { password } = req.body;
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(password, salt);

        // 生成用户ID
        const userId = await this.userService.generateUserId();
        const now = Date.now();

        // 在事务中执行用户创建和验证码删除
        let result;
        try {
          // 开始事务
          await this.prisma.$transaction(async (prismaTransaction) => {
            // 1. 插入用户记录
            await prismaTransaction.$executeRaw`
              INSERT INTO "provider"."provider_user" (
                "id", "username", "password", "phone", 
                "status", "created_at", "updated_at"
              ) VALUES (
                ${userId}, ${username}, ${hashedPassword}, ${phone}, 
                1, ${now}, ${now}
              )
            `;

            // 2. 尝试删除 Redis 验证码
            try {
              await redisUtil.getClient().del(codeKey);
              console.log('验证码删除成功:', codeKey);
            } catch (redisError) {
              console.error('删除验证码失败，但不影响注册过程:', redisError);
              // 在这里我们不抛出异常，因为验证码删除失败不应影响注册成功
              // 验证码最终会自动过期
            }
          });

          // 事务完成，返回成功响应
          // 只返回用户ID
          result = {
            id: userId.toString()
          };
        } catch (transactionError) {
          console.error('注册事务失败:', transactionError);
          throw new Error('注册处理过程失败: ' + transactionError.message);
        }

        // 返回成功响应
        return this.success(res, result, '注册成功');
      } catch (createError) {
        console.error('创建用户失败:', createError);
        return this.fail(res, '注册失败，请稍后再试: ' + createError.message, 500);
      }
    } catch (error) {
      console.error('注册过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 用户登录
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async login(req, res) {
    try {
      const { username, password, captcha, captchaId } = req.body;

      // 验证用户名和密码不为空
      if (!username || !password) {
        return this.fail(res, '用户名和密码不能为空', 400);
      }

      // 验证码验证
      if (!captcha || !captchaId) {
        return this.fail(res, '验证码不能为空', 400);
      }

      // 验证图形验证码
      const captchaService = new CaptchaService();
      const captchaValid = await captchaService.verifyCaptcha(captchaId, captcha);
      if (!captchaValid) {
        return this.fail(res, '验证码错误或已过期', 400);
      }

      // 获取客户端IP
      const clientIp = req.headers['x-forwarded-for'] ||
        req.connection.remoteAddress ||
        req.socket.remoteAddress ||
        '0.0.0.0';

      console.log(`服务商用户登录尝试 [${clientIp}]: 用户名=${username}`);

      // 查询用户信息
      try {
        const users = await this.prisma.$queryRaw`
          SELECT id, username, password, phone, status, remark
          FROM "provider"."provider_user" 
          WHERE ("username" = ${username} OR "phone" = ${username})
          AND "deleted_at" IS NULL
        `;

        if (!users || users.length === 0) {
          return this.fail(res, '用户名或密码错误', 401);
        }

        const user = users[0];

        // // 检查用户状态
        // if (user.status === 0 || user.status === 2 || user.status === 3) {
        //   return this.fail(res, '账号已被禁用或者审核中驳回状态，请联系客服', 403);
        // }

        // 验证密码
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
          return this.fail(res, '用户名或密码错误', 401);
        }

        // 生成JWT令牌
        const token = jwt.sign(
          { id: user.id.toString(), username: user.username, type: 'provider' },
          JWT_SECRET,
          { expiresIn: JWT_EXPIRES_IN }
        );

        // 计算令牌过期时间（秒）
        const expiresIn = parseInt(JWT_EXPIRES_IN.replace(/[^0-9]/g, '')) * (
          JWT_EXPIRES_IN.includes('h') ? 3600 :
            JWT_EXPIRES_IN.includes('d') ? 86400 :
              JWT_EXPIRES_IN.includes('m') ? 60 : 1
        );

        // 更新用户登录信息
        const now = Date.now();
        await this.prisma.$executeRaw`
          UPDATE "provider"."provider_user"
          SET "last_login_time" = ${now},
              "login_count" = "login_count" + 1,
              "updated_at" = ${now},
              "last_login_ip" = ${clientIp}
          WHERE "id" = ${user.id}
        `;

        // 构造响应数据
        const userData = {
          id: user.id.toString(),
          username: user.username,
          phone: user.phone,
          status: user.status,
          remark: user.remark || '',
          token: token,
          expires_in: expiresIn
        };

        return this.success(res, userData, '登录成功');
      } catch (queryError) {
        console.error('查询用户信息失败:', queryError);
        return this.fail(res, '登录失败，请稍后再试', 500);
      }
    } catch (error) {
      console.error('登录过程中出错:', error);
      return this.fail(res, '登录失败，请稍后再试', 500);
    }
  }

  /**
   * 用户退出登录
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async logout(req, res) {
    try {
      // 从请求头或请求体中获取token
      const token = req.headers.authorization?.split(' ')[1] || req.body.token;

      if (!token) {
        return this.fail(res, '未提供有效的令牌', 200);
      }

      try {
        // 解析JWT令牌，获取用户ID
        const decoded = jwt.verify(token, JWT_SECRET);
        const userId = decoded.id;

        // 从 Redis 中删除该用户的令牌
        if (redisUtil.isConnected) {
          await redisUtil.getClient().del(`user_token:${userId}:${token}`);
        }

        return this.success(res, null, '注销成功');
      } catch (jwtErr) {
        console.error('解析JWT令牌失败:', jwtErr.message);
        return this.fail(res, '令牌无效或已过期', 200);
      }
    } catch (err) {
      console.error('注销失败:', err.message);
      return this.fail(res, '注销失败，请稍后再试', 500);
    }
  }

  /**
   * 获取账号信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getAccountInfo(req, res) {
    try {
      // 从请求头或请求体中获取token
      const token = req.headers.authorization?.split(' ')[1];

      if (!token) {
        return this.fail(res, '未授权，请先登录', 401);
      }

      try {
        // 解析JWT令牌，获取用户ID
        const decoded = jwt.verify(token, JWT_SECRET);
        const userId = decoded.id;

        // 查询用户信息
        const userIdBigInt = BigInt(userId);
        const users = await this.prisma.$queryRaw`
          SELECT 
            id, 
            username, 
            phone, 
            created_at, 
            last_login_time, 
            last_login_ip 
          FROM "provider"."provider_user" 
          WHERE "id" = ${userIdBigInt}
          AND "deleted_at" IS NULL
        `;

        if (!users || users.length === 0) {
          return this.fail(res, '用户不存在', 404);
        }

        const user = users[0];

        // 构造返回数据
        const accountInfo = {
          id: user.id.toString(),
          username: user.username,
          phone: user.phone,
          createdAt: user.created_at,
          lastLoginTime: user.last_login_time,
          lastLoginIp: user.last_login_ip || ''
        };

        return this.success(res, accountInfo, '获取账号信息成功');
      } catch (jwtErr) {
        console.error('解析JWT令牌失败:', jwtErr.message);
        return this.fail(res, '令牌无效或已过期', 401);
      }
    } catch (err) {
      console.error('获取账号信息失败:', err.message);
      const { message, code } = this.handleDbError(err);
    }
  }

  /**
   * 获取账号信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getAccountInfo(req, res) {
    try {
      // 从请求头中获取token
      const token = req.headers.authorization?.split(' ')[1];

      if (!token) {
        return this.fail(res, '未授权，请先登录', 401);
      }

      try {
        // 解析JWT令牌，获取用户ID
        const decoded = jwt.verify(token, JWT_SECRET);
        const userId = decoded.id;
        const userIdBigInt = BigInt(userId);

        console.log('获取账号信息 - 用户ID:', userId);

        // 查询用户信息
        const users = await this.prisma.$queryRaw`
          SELECT id, username, phone, created_at, last_login_time, last_login_ip
          FROM "provider"."provider_user" 
          WHERE "id" = ${userIdBigInt}
          AND "deleted_at" IS NULL
        `;

        if (!users || users.length === 0) {
          return this.fail(res, '用户不存在', 404);
        }

        const user = users[0];

        // 构造返回数据
        const accountInfo = {
          id: user.id.toString(),
          username: user.username,
          phone: user.phone,
          createdAt: user.created_at ? user.created_at.toString() : null,
          lastLoginTime: user.last_login_time ? user.last_login_time.toString() : null,
          lastLoginIp: user.last_login_ip || null
        };

        return this.success(res, accountInfo, '获取账号信息成功');
      } catch (jwtErr) {
        console.error('解析JWT令牌失败:', jwtErr.message);
        return this.fail(res, '令牌无效或已过期', 401);
      }
    } catch (err) {
      console.error('获取账号信息失败:', err.message);
      const { message, code } = this.handleDbError(err);
      return this.fail(res, message || err.message, code || 500);
    }
  }

  /**
   * 修改账号信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateAccountInfo(req, res) {
    try {
      // 从请求头或请求体中获取token
      const token = req.headers.authorization?.split(' ')[1];

      if (!token) {
        return this.fail(res, '未授权，请先登录', 401);
      }

      try {
        // 解析JWT令牌，获取用户ID
        const decoded = jwt.verify(token, JWT_SECRET);
        const userId = decoded.id;
        const userIdBigInt = BigInt(userId);

        const { username, phone } = req.body;

        // 验证必填字段
        if (!username && !phone) {
          return this.fail(res, '账号名和手机号至少提供一项', 400);
        }

        // 校验手机号格式如果提供了手机号
        if (phone && !this.validatePhoneNumber(phone)) {
          return this.fail(res, '手机号格式不正确', 400);
        }

        // 如果更改账号名，检查账号名是否已存在
        if (username) {
          const existingUsers = await this.prisma.$queryRaw`
            SELECT id FROM "provider"."provider_user" 
            WHERE "username" = ${username}
            AND "id" != ${userIdBigInt}
            AND "deleted_at" IS NULL
          `;

          if (existingUsers && existingUsers.length > 0) {
            return this.fail(res, '账号名已存在', 400);
          }
        }

        // 如果更改手机号，检查手机号是否已存在
        if (phone) {
          const existingUsers = await this.prisma.$queryRaw`
            SELECT id FROM "provider"."provider_user" 
            WHERE "phone" = ${phone}
            AND "id" != ${userIdBigInt}
            AND "deleted_at" IS NULL
          `;

          if (existingUsers && existingUsers.length > 0) {
            return this.fail(res, '手机号已被注册', 400);
          }
        }

        // 构造更新SQL
        let updateFields = [];
        let updateValues = [];
        let updateTypes = [];

        if (username) {
          updateFields.push('"username" = ?');
          updateValues.push(username);
          updateTypes.push('text');
        }

        if (phone) {
          updateFields.push('"phone" = ?');
          updateValues.push(phone);
          updateTypes.push('text');
        }

        // 添加更新时间
        const now = Date.now();
        updateFields.push('"updated_at" = ?');
        updateValues.push(now);
        updateTypes.push('bigint');

        // 执行更新
        const updateSql = `
          UPDATE "provider"."provider_user"
          SET ${updateFields.join(', ')}
          WHERE "id" = ?
        `;

        // 添加用户ID到参数列表
        updateValues.push(userIdBigInt);
        updateTypes.push('bigint');

        await this.prisma.$executeRawUnsafe(updateSql, ...updateValues);

        // 查询更新后的用户信息
        const updatedUsers = await this.prisma.$queryRaw`
          SELECT id, username, phone
          FROM "provider"."provider_user" 
          WHERE "id" = ${userIdBigInt}
          AND "deleted_at" IS NULL
        `;

        if (!updatedUsers || updatedUsers.length === 0) {
          return this.fail(res, '用户不存在', 404);
        }

        const updatedUser = updatedUsers[0];

        // 构造返回数据
        const accountInfo = {
          id: updatedUser.id.toString(),
          username: updatedUser.username,
          phone: updatedUser.phone
        };

        return this.success(res, accountInfo, '账号信息更新成功');
      } catch (jwtErr) {
        console.error('解析JWT令牌失败:', jwtErr.message);
        return this.fail(res, '令牌无效或已过期', 401);
      }
    } catch (err) {
      console.error('更新账号信息失败:', err.message);
      const { message, code } = this.handleDbError(err);
      return this.fail(res, message, code);
    }
  }

  /**
   * 手机号验证码登录
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async phoneLogin(req, res) {
    try {
      const { phone, captcha } = req.body;

      // 验证必填字段
      const requiredFields = ['phone', 'captcha'];
      const validation = this.validateFields(req.body, requiredFields);
      if (!validation.valid) {
        return this.fail(res, '手机号和验证码不能为空', 400);
      }

      // 验证手机号格式
      if (!this.validatePhoneFormat(phone)) {
        return this.fail(res, '手机号格式不正确', 400);
      }

      // 验证验证码
      try {
        console.log('开始验证验证码:', { phone, captcha, type: 'login' });
        const isValid = await verificationCodeService.verifyCode(phone, captcha, 'login');

        if (!isValid) {
          return this.fail(res, '验证码错误或已过期', 400);
        }

        console.log('验证码验证成功');
      } catch (error) {
        console.error('验证验证码失败:', error);
        return this.fail(res, '验证验证码失败: ' + error.message, 500);
      }

      // 查询用户信息
      try {
        const users = await this.prisma.$queryRaw`
          SELECT id, username, phone, status, remark
          FROM "provider"."provider_user" 
          WHERE "phone" = ${phone}
          AND "deleted_at" IS NULL
        `;

        if (!users || users.length === 0) {
          return this.fail(res, '手机号未注册', 404);
        }

        const user = users[0];

        // 检查用户状态
        // if (user.status === 0) {
        //   return this.fail(res, '账号已被禁用，请联系客服', 403);
        // }
        
        // if (user.status === 2 || user.status === 3){
        //   return this.fail(res, '账号在审核中或者驳回状态，请联系客服',403)
        // }


        // 生成JWT令牌
        const token = jwt.sign(
          { id: user.id.toString(), username: user.username, type: 'provider' },
          JWT_SECRET,
          { expiresIn: JWT_EXPIRES_IN }
        );

        // 计算令牌过期时间（秒）
        const expiresIn = parseInt(JWT_EXPIRES_IN.replace(/[^0-9]/g, '')) * (
          JWT_EXPIRES_IN.includes('h') ? 3600 :
            JWT_EXPIRES_IN.includes('d') ? 86400 :
              JWT_EXPIRES_IN.includes('m') ? 60 : 1
        );

        // 更新用户登录信息
        const now = Date.now();
        await this.prisma.$executeRaw`
          UPDATE "provider"."provider_user"
          SET "last_login_time" = ${now},
              "login_count" = "login_count" + 1,
              "updated_at" = ${now}
          WHERE "id" = ${user.id}
        `;

        // 构造响应数据
        const userData = {
          id: user.id.toString(),
          username: user.username,
          phone: user.phone,
          status: user.status,
          remark: user.remark || '',
          token: token,
          expires_in: expiresIn
        };

        return this.success(res, userData, '登录成功');
      } catch (queryError) {
        console.error('查询用户信息失败:', queryError);
        return this.fail(res, '登录失败，请稍后再试', 500);
      }
    } catch (error) {
      console.error('手机号验证码登录过程中出错:', error);
      return this.fail(res, '登录失败，请稍后再试', 500);
    }
  }

  /**
   * 修改密码
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async resetPassword(req, res) {
    try {
      console.log('开始处理修改密码请求');
      console.log('请求头:', req.headers);

      // 获取令牌
      const authHeader = req.headers.authorization;
      console.log('授权头:', authHeader);

      const token = authHeader && authHeader.startsWith('Bearer ')
        ? authHeader.substring(7)
        : null;

      console.log('解析出的令牌:', token ? '有效令牌' : '无效令牌');

      if (!token) {
        return this.fail(res, '未提供令牌，请先登录', 401);
      }

      // 解析JWT令牌
      let userId;
      try {
        console.log('开始验证令牌');
        const decoded = jwt.verify(token, JWT_SECRET);
        console.log('令牌验证成功，解析结果:', decoded);
        userId = decoded.id;
      } catch (jwtErr) {
        console.error('解析JWT令牌失败:', jwtErr.message);
        return this.fail(res, '令牌无效或已过期', 401);
      }

      const { oldPassword, newPassword, confirmPassword } = req.body;
      console.log('提交的密码参数:', {
        oldPassword: oldPassword ? '已提供' : '未提供',
        newPassword: newPassword ? '已提供' : '未提供',
        confirmPassword: confirmPassword ? '已提供' : '未提供'
      });

      // 验证必填字段
      const requiredFields = ['oldPassword', 'newPassword', 'confirmPassword'];
      const validation = this.validateFields(req.body, requiredFields);
      if (!validation.valid) {
        console.log('缺失必填字段:', validation.missing);
        return this.fail(res, '原密码、新密码和确认密码不能为空', 400);
      }

      // 验证新密码和确认密码是否一致
      if (newPassword !== confirmPassword) {
        return this.fail(res, '新密码和确认密码不一致', 400);
      }

      console.log('查询用户信息, 用户ID:', userId);
      // 将用户ID转换为整数类型
      const userIdBigInt = BigInt(userId);
      console.log('转换后的用户ID(类型):', typeof userIdBigInt);

      // 查询用户信息
      const users = await this.prisma.$queryRaw`
        SELECT id, password 
        FROM "provider"."provider_user" 
        WHERE "id" = ${userIdBigInt}
        AND "deleted_at" IS NULL
      `;

      console.log('查询结果:', users ? `找到${users.length}个用户` : '未找到用户');

      if (!users || users.length === 0) {
        return this.fail(res, '用户不存在', 404);
      }

      const user = users[0];

      // 验证原密码
      console.log('开始验证原密码');
      const isPasswordValid = await bcrypt.compare(oldPassword, user.password);
      console.log('原密码验证结果:', isPasswordValid ? '正确' : '错误');

      if (!isPasswordValid) {
        return this.fail(res, '原密码错误', 400);
      }

      // 加密新密码
      console.log('开始加密新密码');
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(newPassword, salt);

      // 更新密码
      console.log('开始更新密码');
      const now = Date.now();
      await this.prisma.$executeRaw`
        UPDATE "provider"."provider_user"
        SET "password" = ${hashedPassword},
            "updated_at" = ${now}
        WHERE "id" = ${userIdBigInt}
      `;

      console.log('密码修改成功');
      return this.success(res, null, '密码修改成功');
    } catch (error) {
      console.error('修改密码过程中出错:', error);
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }
}

// 创建控制器实例并传入 prisma 实例
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const controller = new UserAuthController(prisma);

// 导出控制器实例中的方法
module.exports = {
  register: controller.register.bind(controller),
  login: controller.login.bind(controller),
  resetPassword: controller.resetPassword.bind(controller),
  logout: controller.logout.bind(controller),
  phoneLogin: controller.phoneLogin.bind(controller),
  getAccountInfo: controller.getAccountInfo.bind(controller),
  updateAccountInfo: controller.updateAccountInfo.bind(controller),
  getUserById: controller.getUserById.bind(controller),
  updateUserById: controller.updateUserById.bind(controller)
};
