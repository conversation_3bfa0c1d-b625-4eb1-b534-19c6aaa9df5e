/**
 * 服务商模块基础控制器
 * 提供通用的响应处理方法
 */
class ProviderBaseController {
  /**
   * 成功响应
   * @param {Object} res - Express响应对象
   * @param {*} data - 返回数据
   * @param {String} message - 成功消息
   * @param {Number} code - 状态码，默认为0（成功）
   */
  success(res, data = null, message = '操作成功', code = 0) {
    return res.status(200).json({
      code,
      data,
      message
    });
  }
  
  /**
   * 失败响应
   * @param {Object} res - Express响应对象
   * @param {String} message - 错误消息
   * @param {Number} status - HTTP状态码，默认为400
   * @param {Number} code - 业务状态码，默认为-1（失败）
   */
  fail(res, message = '操作失败', status = 400, code = -1) {
    return res.status(status).json({
      code,
      data: null,
      message
    });
  }
}

module.exports = ProviderBaseController;
