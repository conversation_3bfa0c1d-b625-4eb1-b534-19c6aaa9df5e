const BaseController = require('../../../core/controllers/BaseController');
const Joi = require('joi');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

class GoalSettingController extends BaseController {
  constructor(prisma) {
    super();
    this.prisma = prisma;
    console.log('GoalSettingController initialized with prisma:', !!this.prisma);
  }

  /**
   * 获取目标设置列表
   */
  async getGoalList(req, res) {
    try {
      const { 
        target_type = 'member', 
        fiscal_year, 
        goal_type, 
        page = 1, 
        pageSize = 10,
        target_name 
      } = req.query;

      // 构建查询条件
      const where = {
        deleted_at: null,
        target_type
      };

      if (fiscal_year) {
        where.fiscal_year = parseInt(fiscal_year);
      }

      if (goal_type) {
        where.goal_type = goal_type;
      }

      if (target_name) {
        where.target_name = {
          contains: target_name
        };
      }

      // 分页参数
      const skip = (parseInt(page) - 1) * parseInt(pageSize);
      const take = parseInt(pageSize);

      // 查询目标设置列表
      const [goals, total] = await Promise.all([
        this.prisma.providerGoalSetting.findMany({
          where,
          skip,
          take,
          orderBy: {
            created_at: 'desc'
          }
        }),
        this.prisma.providerGoalSetting.count({ where })
      ]);

      // 格式化数据
      const formattedGoals = goals.map(goal => ({
        id: goal.id.toString(),
        name: goal.target_name,
        target_type: goal.target_type,
        target_id: goal.target_id.toString(),
        fiscalYear: goal.fiscal_year,
        targetType: goal.goal_type,
        unit: goal.unit,
        yearlyTarget: parseFloat(goal.yearly_target),
        q1Target: parseFloat(goal.q1_target),
        q2Target: parseFloat(goal.q2_target),
        q3Target: parseFloat(goal.q3_target),
        q4Target: parseFloat(goal.q4_target),
        m1Target: parseFloat(goal.m1_target),
        m2Target: parseFloat(goal.m2_target),
        m3Target: parseFloat(goal.m3_target),
        m4Target: parseFloat(goal.m4_target),
        m5Target: parseFloat(goal.m5_target),
        m6Target: parseFloat(goal.m6_target),
        m7Target: parseFloat(goal.m7_target),
        m8Target: parseFloat(goal.m8_target),
        m9Target: parseFloat(goal.m9_target),
        m10Target: parseFloat(goal.m10_target),
        m11Target: parseFloat(goal.m11_target),
        m12Target: parseFloat(goal.m12_target),
        status: goal.status,
        createTime: new Date(Number(goal.created_at)).toLocaleString('zh-CN'),
        created_at: goal.created_at.toString(),
        updated_at: goal.updated_at.toString()
      }));

      this.success(res, {
        items: formattedGoals,
        pageInfo: {
          total,
          currentPage: parseInt(page),
          totalPage: Math.ceil(total / parseInt(pageSize))
        }
      }, '获取目标设置列表成功');
    } catch (err) {
      console.error('获取目标设置列表失败:', err);
      const { message, code } = this.handleDbError(err);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取目标设置详情
   */
  async getGoalDetail(req, res) {
    try {
      const { id } = req.params;

      const goal = await this.prisma.providerGoalSetting.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });

      if (!goal) {
        return this.fail(res, '目标设置不存在', 404);
      }

      // 格式化数据
      const formattedGoal = {
        id: goal.id.toString(),
        name: goal.target_name,
        target_type: goal.target_type,
        target_id: goal.target_id.toString(),
        fiscalYear: goal.fiscal_year,
        targetType: goal.goal_type,
        unit: goal.unit,
        yearlyTarget: parseFloat(goal.yearly_target),
        q1Target: parseFloat(goal.q1_target),
        q2Target: parseFloat(goal.q2_target),
        q3Target: parseFloat(goal.q3_target),
        q4Target: parseFloat(goal.q4_target),
        m1Target: parseFloat(goal.m1_target),
        m2Target: parseFloat(goal.m2_target),
        m3Target: parseFloat(goal.m3_target),
        m4Target: parseFloat(goal.m4_target),
        m5Target: parseFloat(goal.m5_target),
        m6Target: parseFloat(goal.m6_target),
        m7Target: parseFloat(goal.m7_target),
        m8Target: parseFloat(goal.m8_target),
        m9Target: parseFloat(goal.m9_target),
        m10Target: parseFloat(goal.m10_target),
        m11Target: parseFloat(goal.m11_target),
        m12Target: parseFloat(goal.m12_target),
        status: goal.status,
        createTime: new Date(Number(goal.created_at)).toLocaleString('zh-CN'),
        created_at: goal.created_at.toString(),
        updated_at: goal.updated_at.toString()
      };

      this.success(res, formattedGoal, '获取目标设置详情成功');
    } catch (err) {
      console.error('获取目标设置详情失败:', err);
      const { message, code } = this.handleDbError(err);
      this.fail(res, message, code);
    }
  }

  /**
   * 创建目标设置
   */
  async createGoal(req, res) {
    try {
      // 数据验证
      const schema = Joi.object({
        target_type: Joi.string().valid('member', 'department', 'company').required(),
        target_id: Joi.string().required(),
        target_name: Joi.string().max(100).required(),
        fiscal_year: Joi.number().integer().min(2020).max(2050).required(),
        goal_type: Joi.string().valid('成交金额', '订单数量').required(),
        unit: Joi.string().valid('万', '个').required(),
        yearly_target: Joi.number().min(0).default(0),
        q1_target: Joi.number().min(0).default(0),
        q2_target: Joi.number().min(0).default(0),
        q3_target: Joi.number().min(0).default(0),
        q4_target: Joi.number().min(0).default(0),
        m1_target: Joi.number().min(0).default(0),
        m2_target: Joi.number().min(0).default(0),
        m3_target: Joi.number().min(0).default(0),
        m4_target: Joi.number().min(0).default(0),
        m5_target: Joi.number().min(0).default(0),
        m6_target: Joi.number().min(0).default(0),
        m7_target: Joi.number().min(0).default(0),
        m8_target: Joi.number().min(0).default(0),
        m9_target: Joi.number().min(0).default(0),
        m10_target: Joi.number().min(0).default(0),
        m11_target: Joi.number().min(0).default(0),
        m12_target: Joi.number().min(0).default(0)
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        return this.fail(res, error.details[0].message, 400);
      }

      const userId = req.user?.id;
      const now = Date.now();
      const goalId = generateSnowflakeId();

      // 检查是否已存在相同的目标设置
      const existingGoal = await this.prisma.providerGoalSetting.findFirst({
        where: {
          target_type: value.target_type,
          target_id: BigInt(value.target_id),
          fiscal_year: value.fiscal_year,
          goal_type: value.goal_type,
          deleted_at: null
        }
      });

      if (existingGoal) {
        return this.fail(res, '该对象在此财年已存在相同类型的目标设置', 400);
      }

      // 创建目标设置
      const goalData = {
        id: goalId,
        target_type: value.target_type,
        target_id: BigInt(value.target_id),
        target_name: value.target_name,
        fiscal_year: value.fiscal_year,
        goal_type: value.goal_type,
        unit: value.unit,
        yearly_target: value.yearly_target,
        q1_target: value.q1_target,
        q2_target: value.q2_target,
        q3_target: value.q3_target,
        q4_target: value.q4_target,
        m1_target: value.m1_target,
        m2_target: value.m2_target,
        m3_target: value.m3_target,
        m4_target: value.m4_target,
        m5_target: value.m5_target,
        m6_target: value.m6_target,
        m7_target: value.m7_target,
        m8_target: value.m8_target,
        m9_target: value.m9_target,
        m10_target: value.m10_target,
        m11_target: value.m11_target,
        m12_target: value.m12_target,
        status: 1,
        created_by: userId ? BigInt(userId) : null,
        updated_by: userId ? BigInt(userId) : null,
        created_at: BigInt(now),
        updated_at: BigInt(now)
      };

      const newGoal = await this.prisma.providerGoalSetting.create({
        data: goalData
      });

      // 格式化返回数据
      const formattedGoal = {
        id: newGoal.id.toString(),
        name: newGoal.target_name,
        target_type: newGoal.target_type,
        target_id: newGoal.target_id.toString(),
        fiscalYear: newGoal.fiscal_year,
        targetType: newGoal.goal_type,
        unit: newGoal.unit,
        yearlyTarget: parseFloat(newGoal.yearly_target),
        q1Target: parseFloat(newGoal.q1_target),
        q2Target: parseFloat(newGoal.q2_target),
        q3Target: parseFloat(newGoal.q3_target),
        q4Target: parseFloat(newGoal.q4_target),
        m1Target: parseFloat(newGoal.m1_target),
        m2Target: parseFloat(newGoal.m2_target),
        m3Target: parseFloat(newGoal.m3_target),
        m4Target: parseFloat(newGoal.m4_target),
        m5Target: parseFloat(newGoal.m5_target),
        m6Target: parseFloat(newGoal.m6_target),
        m7Target: parseFloat(newGoal.m7_target),
        m8Target: parseFloat(newGoal.m8_target),
        m9Target: parseFloat(newGoal.m9_target),
        m10Target: parseFloat(newGoal.m10_target),
        m11Target: parseFloat(newGoal.m11_target),
        m12Target: parseFloat(newGoal.m12_target),
        status: newGoal.status,
        createTime: new Date(Number(newGoal.created_at)).toLocaleString('zh-CN'),
        created_at: newGoal.created_at.toString(),
        updated_at: newGoal.updated_at.toString()
      };

      this.success(res, formattedGoal, '创建目标设置成功', 200);
    } catch (err) {
      console.error('创建目标设置失败:', err);
      const { message, code } = this.handleDbError(err);
      this.fail(res, message, code);
    }
  }

  /**
   * 更新目标设置
   */
  async updateGoal(req, res) {
    try {
      const { id } = req.params;

      // 数据验证
      const schema = Joi.object({
        target_name: Joi.string().max(100),
        fiscal_year: Joi.number().integer().min(2020).max(2050),
        goal_type: Joi.string().valid('成交金额', '订单数量'),
        unit: Joi.string().valid('万', '个'),
        yearly_target: Joi.number().min(0),
        q1_target: Joi.number().min(0),
        q2_target: Joi.number().min(0),
        q3_target: Joi.number().min(0),
        q4_target: Joi.number().min(0),
        m1_target: Joi.number().min(0),
        m2_target: Joi.number().min(0),
        m3_target: Joi.number().min(0),
        m4_target: Joi.number().min(0),
        m5_target: Joi.number().min(0),
        m6_target: Joi.number().min(0),
        m7_target: Joi.number().min(0),
        m8_target: Joi.number().min(0),
        m9_target: Joi.number().min(0),
        m10_target: Joi.number().min(0),
        m11_target: Joi.number().min(0),
        m12_target: Joi.number().min(0)
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        return this.fail(res, error.details[0].message, 400);
      }

      // 检查目标设置是否存在
      const existingGoal = await this.prisma.providerGoalSetting.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });

      if (!existingGoal) {
        return this.fail(res, '目标设置不存在', 404);
      }

      const userId = req.user?.id;
      const now = Date.now();

      // 更新目标设置
      const updateData = {
        ...value,
        updated_by: userId ? BigInt(userId) : null,
        updated_at: BigInt(now)
      };

      const updatedGoal = await this.prisma.providerGoalSetting.update({
        where: { id: BigInt(id) },
        data: updateData
      });

      // 格式化返回数据
      const formattedGoal = {
        id: updatedGoal.id.toString(),
        name: updatedGoal.target_name,
        target_type: updatedGoal.target_type,
        target_id: updatedGoal.target_id.toString(),
        fiscalYear: updatedGoal.fiscal_year,
        targetType: updatedGoal.goal_type,
        unit: updatedGoal.unit,
        yearlyTarget: parseFloat(updatedGoal.yearly_target),
        q1Target: parseFloat(updatedGoal.q1_target),
        q2Target: parseFloat(updatedGoal.q2_target),
        q3Target: parseFloat(updatedGoal.q3_target),
        q4Target: parseFloat(updatedGoal.q4_target),
        m1Target: parseFloat(updatedGoal.m1_target),
        m2Target: parseFloat(updatedGoal.m2_target),
        m3Target: parseFloat(updatedGoal.m3_target),
        m4Target: parseFloat(updatedGoal.m4_target),
        m5Target: parseFloat(updatedGoal.m5_target),
        m6Target: parseFloat(updatedGoal.m6_target),
        m7Target: parseFloat(updatedGoal.m7_target),
        m8Target: parseFloat(updatedGoal.m8_target),
        m9Target: parseFloat(updatedGoal.m9_target),
        m10Target: parseFloat(updatedGoal.m10_target),
        m11Target: parseFloat(updatedGoal.m11_target),
        m12Target: parseFloat(updatedGoal.m12_target),
        status: updatedGoal.status,
        createTime: new Date(Number(updatedGoal.created_at)).toLocaleString('zh-CN'),
        created_at: updatedGoal.created_at.toString(),
        updated_at: updatedGoal.updated_at.toString()
      };

      this.success(res, formattedGoal, '更新目标设置成功');
    } catch (err) {
      console.error('更新目标设置失败:', err);
      const { message, code } = this.handleDbError(err);
      this.fail(res, message, code);
    }
  }

  /**
   * 删除目标设置
   */
  async deleteGoal(req, res) {
    try {
      const { id } = req.params;

      // 检查目标设置是否存在
      const existingGoal = await this.prisma.providerGoalSetting.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null
        }
      });

      if (!existingGoal) {
        return this.fail(res, '目标设置不存在', 404);
      }

      const userId = req.user?.id;
      const now = Date.now();

      // 软删除目标设置
      await this.prisma.providerGoalSetting.update({
        where: { id: BigInt(id) },
        data: {
          deleted_at: BigInt(now),
          updated_by: userId ? BigInt(userId) : null,
          updated_at: BigInt(now)
        }
      });

      this.success(res, null, '删除目标设置成功');
    } catch (err) {
      console.error('删除目标设置失败:', err);
      const { message, code } = this.handleDbError(err);
      this.fail(res, message, code);
    }
  }

  /**
   * 批量更新目标设置
   */
  async batchUpdateGoals(req, res) {
    try {
      // 数据验证
      const schema = Joi.object({
        goal_ids: Joi.array().items(Joi.string()).min(1).required(),
        update_data: Joi.object({
          yearly_target: Joi.number().min(0),
          goal_type: Joi.string().valid('成交金额', '订单数量'),
          unit: Joi.string().valid('万', '个')
        }).min(1).required()
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        return this.fail(res, error.details[0].message, 400);
      }

      const userId = req.user?.id;
      const now = Date.now();

      // 批量更新
      const updateData = {
        ...value.update_data,
        updated_by: userId ? BigInt(userId) : null,
        updated_at: BigInt(now)
      };

      const goalIds = value.goal_ids.map(id => BigInt(id));

      await this.prisma.providerGoalSetting.updateMany({
        where: {
          id: { in: goalIds },
          deleted_at: null
        },
        data: updateData
      });

      this.success(res, null, '批量更新目标设置成功');
    } catch (err) {
      console.error('批量更新目标设置失败:', err);
      const { message, code } = this.handleDbError(err);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取用户列表（用于成员目标选择）
   */
  async getUserList(req, res) {
    try {
      // 获取系统用户列表
      const users = await this.prisma.baseSystemUser.findMany({
        where: {
          deleted_at: null
          // 不过滤status，获取所有未删除的用户
        },
        select: {
          id: true,
          username: true,
          nickname: true,
          dept_id: true
        },
        orderBy: {
          created_at: 'desc'
        }
      });

      // 获取部门信息
      const deptIds = users.filter(u => u.dept_id).map(u => u.dept_id);
      const depts = await this.prisma.baseSystemDept.findMany({
        where: {
          id: { in: deptIds },
          deleted_at: null
        },
        select: {
          id: true,
          name: true
        }
      });

      const deptMap = new Map(depts.map(d => [d.id.toString(), d.name]));

      // 格式化用户数据
      const formattedUsers = users.map(user => ({
        id: user.id.toString(),
        name: user.nickname || user.username,
        username: user.username,
        nickname: user.nickname,
        department: user.dept_id ? deptMap.get(user.dept_id.toString()) || '未知部门' : '无部门',
        dept_id: user.dept_id ? user.dept_id.toString() : null
      }));

      this.success(res, formattedUsers, '获取用户列表成功');
    } catch (err) {
      console.error('获取用户列表失败:', err);
      const { message, code } = this.handleDbError(err);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取部门列表（用于部门目标选择）
   */
  async getDepartmentList(req, res) {
    try {
      // 获取部门列表
      const departments = await this.prisma.baseSystemDept.findMany({
        where: {
          deleted_at: null
        },
        select: {
          id: true,
          name: true,
          sort: true,
          status: true
        },
        orderBy: {
          sort: 'asc'
        }
      });

      // 格式化部门数据
      const formattedDepartments = departments.map(dept => ({
        id: dept.id.toString(),
        name: dept.name,
        sort: dept.sort || 0,
        status: dept.status || 1
      }));

      this.success(res, formattedDepartments, '获取部门列表成功');
    } catch (err) {
      console.error('获取部门列表失败:', err);
      const { message, code } = this.handleDbError(err);
      this.fail(res, message, code);
    }
  }
}

module.exports = GoalSettingController;
