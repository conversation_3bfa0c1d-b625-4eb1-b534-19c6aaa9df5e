/**
 * 服务商用户验证码控制器
 * 处理验证码生成、发送和验证等功能
 */
const redisUtil = require('../../../core/utils/RedisUtil');
const VerificationCodeService = require('../../../apps/master/system/integration/services/VerificationCodeService');
const AliyunSMSService = require('../../../apps/master/system/integration/services/aliyun/AliyunSMSService');
const { PrismaClient } = require('@prisma/client');
const Core = require('@alicloud/pop-core');
const axios = require('axios');
const bcrypt = require('bcryptjs');

// 创建验证码服务实例
const verificationCodeService = new VerificationCodeService();
const prisma = new PrismaClient();
const aliyunSMSService = new AliyunSMSService(prisma);

/**
 * 验证手机号格式
 * @param {string} phone - 手机号
 * @returns {boolean} - 是否符合格式
 */
function validatePhoneFormat(phone) {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
}

/**
 * 成功响应
 * @param {Object} res - Express响应对象
 * @param {*} data - 响应数据
 * @param {String} message - 成功消息
 * @param {Number} code - 状态码
 */
function success(res, data = null, message = '操作成功', code = 200) {
  // 处理 BigInt 类型，将其转换为字符串
  const replacer = (key, value) => {
    if (typeof value === 'bigint') {
      return value.toString();
    }
    return value;
  };

  // 使用 replacer 先将数据转换为 JSON 字符串，然后再解析回 JSON 对象
  const jsonString = JSON.stringify({ code, message, data }, replacer);
  const jsonData = JSON.parse(jsonString);

  return res.status(200).json(jsonData);
}

/**
 * 失败响应
 * @param {Object} res - Express响应对象
 * @param {string} message - 错误消息
 * @param {*} data - 响应数据
 * @param {number} code - 错误代码
 * @returns {Object} 统一格式的错误响应
 */
function fail(res, message = '操作失败', data = null, code = 500) {
  // 处理 BigInt 类型，将其转换为字符串
  const replacer = (key, value) => {
    if (typeof value === 'bigint') {
      return value.toString();
    }
    return value;
  };

  // 使用 replacer 先将数据转换为 JSON 字符串，然后再解析回 JSON 对象
  const jsonString = JSON.stringify({ code, message, data }, replacer);
  const jsonData = JSON.parse(jsonString);

  return res.status(200).json(jsonData);
}


/**
 * 验证验证码
 * @param {string} phone - 手机号
 * @param {string} captcha - 验证码
 * @param {string} type - 验证码类型
 * @returns {Promise<boolean>} - 验证结果
 */
async function verifyCaptcha(phone, captcha, type = 'register') {
  try {
    // 检查Redis连接状态
    if (!redisUtil.isConnected) {
      throw new Error('验证服务暂时不可用，请稍后再试');
    }

    // 从Redis中获取验证码
    const codeKey = `verification_code:${type}:${phone}`;

    const savedCode = await redisUtil.getClient().get(codeKey);

    if (!savedCode) {
      return false;
    }

    // 比较验证码
    const isValid = savedCode === captcha;

    if (isValid) {
      // 验证成功后删除验证码
      await redisUtil.getClient().del(codeKey);
    }

    return isValid;
  } catch (error) {
    throw error;
  }
}

/**
 * 获取重置密码验证码
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getResetPasswordCaptcha(req, res) {
  try {
    const { phone } = req.query;

    // 验证手机号
    if (!phone) {
      return fail(res, '手机号不能为空', null, 400);
    }

    // 验证手机号格式
    if (!validatePhoneFormat(phone)) {
      return fail(res, '手机号格式不正确', null, 400);
    }

    // 检查用户是否存在
    try {
      // 这里需要使用 Prisma 客户端查询用户信息
      const { PrismaClient } = require('@prisma/client');
      const prisma = new PrismaClient();

      const users = await prisma.$queryRaw`
        SELECT id FROM "provider"."provider_user" 
        WHERE "phone" = ${phone}
        AND "deleted_at" IS NULL
      `;

      if (!users || users.length === 0) {
        return fail(res, '该手机号未注册', null, 400);
      }

      // 检查Redis连接状态
      console.log('Redis连接状态:', redisUtil.isConnected ? '已连接' : '未连接');

      // 使用验证码服务生成和发送验证码
      const result = await verificationCodeService.getCode(phone, 'reset_password');

      // 在生产环境中，不应该返回验证码
      // 这里为了方便开发和测试，返回验证码
      if (process.env.NODE_ENV !== 'production') {
        return success(res, { captcha: result.code }, '获取验证码成功');
      } else {
        return success(res, {}, '验证码已发送到您的手机');
      }
    } catch (error) {
      console.error('生成或发送验证码失败:', error);
      return fail(res, '获取验证码失败: ' + error.message, null, 500);
    }
  } catch (error) {
    console.error('获取重置密码验证码过程中出错:', error);
    return fail(res, error.message || '获取验证码失败', null, 500);
  }
}

/**
 * 获取登录验证码
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getLoginCaptcha(req, res) {
  try {
    const { phone } = req.query;

    // 验证手机号
    if (!phone) {
      return fail(res, '手机号不能为空', null, 400);
    }

    // 验证手机号格式
    if (!validatePhoneFormat(phone)) {
      return fail(res, '手机号格式不正确', null, 400);
    }

    // 检查用户是否存在
    try {
      // 使用 Prisma 客户端查询用户信息
      const { PrismaClient } = require('@prisma/client');
      const prisma = new PrismaClient();

      const users = await prisma.$queryRaw`
        SELECT id FROM "provider"."provider_user" 
        WHERE "phone" = ${phone}
        AND "deleted_at" IS NULL
      `;

      if (!users || users.length === 0) {
        return fail(res, '该手机号未注册', null, 400);
      }

      // 调用阿里云短信验证码接口
      try {
        const request = await import('../../../../utils/request.js');
        const response = await request.default.post('/api/v1/master/system/integration/aliyun/sms/verification-code', {
          phoneNumber: phone,
          templateCode: 'SMS_276465873',
          type: 'login'
        });

        if (response.code === 200 && response.message.success) {
          return success(res, {}, response.message.message);
        } else {
          console.error('发送验证码失败:', response);
          return fail(res, '发送验证码失败: ' + (response.message.message || '未知错误'), null, 500);
        }
      } catch (smsError) {
        console.error('调用短信服务失败:', smsError);
        return fail(res, '发送验证码失败: ' + (smsError.response?.data?.message || smsError.message), null, 500);
      }
    } catch (error) {
      console.error('生成或发送验证码失败:', error);
      return fail(res, '获取验证码失败: ' + error.message, null, 500);
    }
  } catch (error) {
    console.error('获取登录验证码过程中出错:', error);
    return fail(res, error.message || '获取验证码失败', null, 500);
  }
}

/**
 * 获取验证码
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function getCaptcha(req, res) {
  try {
    // 获取请求参数
    const { phoneNumber, templateCode = 'SMS_276465873', type = 'register' } = req.body;

    console.log('获取验证码请求参数:', { phoneNumber, templateCode, type });

    // 验证手机号
    if (!phoneNumber) {
      return fail(res, '手机号不能为空', null, 400);
    }

    // 验证手机号格式
    if (!validatePhoneFormat(phoneNumber)) {
      return fail(res, '手机号格式不正确', null, 400);
    }

    try {
      // 使用验证码服务生成验证码
      const result = await verificationCodeService.getCode(phoneNumber, type);
      console.log('验证码生成结果:', result);

      // 调用短信服务发送验证码
      const response = await axios.post('http://127.0.0.1:4000/api/v1/master/system/integration/aliyun/sms/verification-code', {
        phoneNumber,
        templateCode,
        type,
        code: result.code // 传递生成的验证码
      });

      console.log('短信验证码接口响应:', response.data);

      if (response.data.code === 200 && response.data.message.success) {
        return success(res, {}, response.data.message.message);
      } else {
        console.error('发送验证码失败:', response.data);
        return fail(res, '发送验证码失败: ' + (response.data.message.message || '未知错误'), null, 500);
      }
    } catch (smsError) {
      console.error('调用短信服务失败:', smsError);
      return fail(res, '发送验证码失败: ' + (smsError.response?.data?.message || smsError.message), null, 500);
    }
  } catch (error) {
    console.error('获取验证码失败:', error);
    return fail(res, '获取验证码失败: ' + error.message, null, 500);
  }
}

/**
 * 用户注册
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
async function register(req, res) {
  try {
    const { username, password, phone, captcha, type = 'register' } = req.body;

    console.log('注册请求参数:', { username, phone, type });

    // 验证必填字段
    if (!username || !password || !phone || !captcha) {
      return fail(res, '用户名、密码、手机号和验证码不能为空', null, 400);
    }

    // 验证手机号格式
    if (!validatePhoneFormat(phone)) {
      return fail(res, '手机号格式不正确', null, 400);
    }

    // 验证用户名长度
    if (username.length < 3 || username.length > 50) {
      return fail(res, '用户名长度必须在3-50个字符之间', null, 400);
    }

    // 验证密码长度
    if (password.length < 6 || password.length > 50) {
      return fail(res, '密码长度必须在6-50个字符之间', null, 400);
    }

    // 检查用户名是否已存在
    const existUser = await prisma.providerUser.findFirst({
      where: {
        username,
        deleted_at: null
      }
    });

    if (existUser) {
      return fail(res, '用户名已存在', null, 400);
    }

    // 检查手机号是否已被注册
    const existPhone = await prisma.providerUser.findFirst({
      where: {
        phone,
        deleted_at: null
      }
    });

    if (existPhone) {
      return fail(res, '该手机号已被注册', null, 400);
    }

    // 验证短信验证码
    try {
      console.log('开始验证验证码:', { phone, captcha, type });
      const isValid = await verificationCodeService.verifyCode(phone, captcha, type);

      if (!isValid) {
        return fail(res, '验证码错误', null, 400);
      }

      console.log('验证码验证成功');
    } catch (verifyErr) {
      console.error('验证码验证失败:', verifyErr);
      return fail(res, '验证码验证失败: ' + verifyErr.message, null, 400);
    }

    // 创建用户
    const now = new Date();
    const hashedPassword = await bcrypt.hash(password, 10);

    console.log('开始创建用户...');
    const user = await prisma.providerUser.create({
      data: {
        username,
        password: hashedPassword,
        phone,
        status: 0, // 默认启用
        created_at: now,
        updated_at: now
      }
    });

    // 移除密码字段
    delete user.password;
    console.log('用户创建成功:', user);

    return success(res, user, '注册成功');
  } catch (error) {
    console.error('注册失败:', error);
    return fail(res, '注册失败: ' + error.message, null, 500);
  }
}

module.exports = {
  getCaptcha,
  verifyCaptcha,
  getResetPasswordCaptcha,
  getLoginCaptcha,
  register
};
