/**
 * 框架协议管理控制器
 */
const { PrismaClient, Prisma } = require('@prisma/client');
const ProviderBaseController = require('./base/ProviderBaseController');

class FrameworkController extends ProviderBaseController {
  /**
   * 构造函数
   * @param {PrismaClient} prisma - Prisma客户端实例
   */
  constructor(prisma) {
    if (!prisma) {
      throw new Error('Prisma client is required');
    }
    super(prisma);
  }

  /**
   * 生成雪花ID
   * @returns {string} 雪花ID
   */
  generateSnowflakeId() {
    const timestamp = BigInt(Date.now());
    const workerId = BigInt(1); // 工作机器ID
    const sequence = BigInt(Math.floor(Math.random() * 4096)); // 序列号
    const snowflakeId = (timestamp << BigInt(22)) | (workerId << BigInt(12)) | sequence;
    return snowflakeId.toString();
  }

  /**
   * 创建框架协议
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async create(req, res) {
    try {
      const {
        file_name,        // 文件名
        file_url,         // 文件地址
        express_no,       // 快递单号
        express_company,  // 快递公司
        user_id          // 用户ID
      } = req.body;

      // 验证必填参数
      if (!file_name || !file_url || !user_id) {
        return this.fail(res, '文件名、文件地址和用户ID不能为空', 400);
      }

      try {
        // 将用户ID转换为BigInt类型
        const userIdBigInt = BigInt(user_id);

        // 验证用户是否存在
        const existingUser = await this.prisma.$queryRaw`
          SELECT id FROM "provider"."provider_user" 
          WHERE id = ${userIdBigInt}
          AND deleted_at IS NULL
        `;

        if (!existingUser || existingUser.length === 0) {
          return this.fail(res, '关联用户不存在', 404);
        }

        const timestamp = BigInt(Math.floor(Date.now() / 1000));
        const id = BigInt(this.generateSnowflakeId());

        // 创建框架协议
        const result = await this.prisma.$queryRaw`
          INSERT INTO "provider"."provider_framework" (
            id, file_name, file_url, express_no, express_company,
            user_id, created_at, updated_at
          ) VALUES (
            ${id}, ${file_name}, ${file_url}, ${express_no || null}, ${express_company || null},
            ${userIdBigInt}, ${timestamp}, ${timestamp}
          ) RETURNING id
        `;

        if (!result || result.length === 0) {
          return this.fail(res, '创建失败', 500);
        }

        return this.success(res, {
          id: result[0].id.toString()
        }, '创建成功');
      } catch (err) {
        console.error('创建框架协议失败:', err.message);
        return this.fail(res, '创建失败: ' + err.message, 400);
      }
    } catch (err) {
      console.error('创建框架协议失败:', err.message);
      return this.fail(res, err.message, 500);
    }
  }

  /**
   * 获取框架协议列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async list(req, res) {
    try {
      const {
        page = 1,
        pageSize = 10,
        user_id,
        express_no,
        startTime,
        endTime
      } = req.query;

      const offset = BigInt((page - 1) * pageSize);
      const limit = BigInt(pageSize);

      // 构建查询条件
      let whereConditions = ['"deleted_at" IS NULL'];

      if (user_id) {
        try {
          const userIdBigInt = BigInt(user_id);
          whereConditions.push(`"user_id" = ${userIdBigInt}`);
        } catch (err) {
          return this.fail(res, '用户ID无效', 400);
        }
      }

      if (express_no) {
        whereConditions.push(`"express_no" = '${express_no}'`);
      }

      if (startTime) {
        whereConditions.push(`"created_at" >= ${BigInt(startTime)}`);
      }

      if (endTime) {
        whereConditions.push(`"created_at" <= ${BigInt(endTime)}`);
      }

      const whereClause = whereConditions.join(' AND ');

      // 获取总数
      const totalResult = await this.prisma.$queryRaw`
        SELECT COUNT(*) as total
        FROM "provider"."provider_framework"
        WHERE ${Prisma.raw(whereClause)}
      `;
      const total = parseInt(totalResult[0].total);

      // 获取列表
      const frameworks = await this.prisma.$queryRaw`
        SELECT 
          "id",
          "file_name",
          "file_url",
          "express_no",
          "express_company",
          "user_id",
          "created_at",
          "updated_at"
        FROM "provider"."provider_framework"
        WHERE ${Prisma.raw(whereClause)}
        ORDER BY "created_at" DESC
        LIMIT ${limit}
        OFFSET ${offset}
      `;

      // 处理返回数据
      const items = frameworks.map(framework => ({
        id: framework.id.toString(),
        fileName: framework.file_name,
        fileUrl: framework.file_url,
        expressNo: framework.express_no || '',
        expressCompany: framework.express_company || '',
        userId: framework.user_id.toString(),
        createdAt: framework.created_at.toString(),
        updatedAt: framework.updated_at.toString()
      }));

      return this.success(res, {
        items,
        pageInfo: {
          current: parseInt(page),
          pageSize: parseInt(pageSize),
          total
        }
      });
    } catch (err) {
      console.error('获取框架协议列表失败:', err.message);
      return this.fail(res, err.message, 500);
    }
  }

  /**
   * 获取框架协议详情
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getDetail(req, res) {
    try {
      const id = req.params.id;
      if (!id) {
        return this.fail(res, '协议ID不能为空', 400);
      }

      try {
        // 将ID转换为BigInt类型
        const frameworkId = BigInt(id);

        // 查询协议详情
        const frameworks = await this.prisma.$queryRaw`
          SELECT 
            "id",
            "file_name",
            "file_url",
            "express_no",
            "express_company",
            "user_id",
            "created_at",
            "updated_at"
          FROM "provider"."provider_framework"
          WHERE "id" = ${frameworkId}
          AND "deleted_at" IS NULL
        `;

        if (!frameworks || frameworks.length === 0) {
          return this.fail(res, '协议不存在', 404);
        }

        const framework = frameworks[0];

        // 构造返回数据
        const detail = {
          id: framework.id.toString(),
          fileName: framework.file_name,
          fileUrl: framework.file_url,
          expressNo: framework.express_no || '',
          expressCompany: framework.express_company || '',
          userId: framework.user_id.toString(),
          createdAt: framework.created_at.toString(),
          updatedAt: framework.updated_at.toString()
        };

        return this.success(res, detail);
      } catch (err) {
        console.error('解析协议ID失败:', err.message);
        return this.fail(res, '协议ID无效', 400);
      }
    } catch (err) {
      console.error('获取框架协议详情失败:', err.message);
      return this.fail(res, err.message, 500);
    }
  }

  /**
   * 删除框架协议
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async delete(req, res) {
    try {
      const id = req.params.id;
      if (!id) {
        return this.fail(res, '协议ID不能为空', 400);
      }

      try {
        // 将ID转换为BigInt类型
        const frameworkId = BigInt(id);

        // 验证协议是否存在
        const existingFrameworks = await this.prisma.$queryRaw`
          SELECT id FROM "provider"."provider_framework" 
          WHERE "id" = ${frameworkId}
          AND "deleted_at" IS NULL
        `;

        if (!existingFrameworks || existingFrameworks.length === 0) {
          return this.fail(res, '协议不存在', 404);
        }

        // 软删除协议
        const timestamp = Math.floor(Date.now() / 1000);
        await this.prisma.$executeRaw`
          UPDATE "provider"."provider_framework"
          SET "deleted_at" = ${timestamp},
              "updated_at" = ${timestamp}
          WHERE "id" = ${frameworkId}
          AND "deleted_at" IS NULL
        `;

        return this.success(res, null, '删除成功');
      } catch (err) {
        console.error('解析协议ID失败:', err.message);
        return this.fail(res, '协议ID无效', 400);
      }
    } catch (err) {
      console.error('删除框架协议失败:', err.message);
      return this.fail(res, err.message, 500);
    }
  }
}

// 直接导出类
module.exports = FrameworkController;
