// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["master", "merchant", "supplier"]
}

// Master 模块的模型
model MasterUser {
  id        Int       @id @default(autoincrement())
  username  String    @unique
  password  String
  email     String    @unique
  status    String    @default("active") // active, inactive, suspended
  role      String    @default("admin") // admin, operator
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  @@map("users")
  @@schema("master")
}

/// 部门信息表，存储组织架构信息
model dept {
  // 主键
  id         BigInt  @id @default(autoincrement()) /// @db.Comment('主键，系统自动生成')

  // 基本信息
  parent_id  BigInt  /// @db.Comment('父ID，关联本表id字段，空表示一级部门')
  level      String  /// @db.Comment('组级集合，存储部门层级路径')
  name       String  /// @db.Comment('部门名称，必填')
  leader     String? /// @db.Comment('负责人')
  phone      String? /// @db.Comment('联系电话，11位手机号')

  // 状态信息
  status     Int?    @default(1) /// @db.Comment('状态：1-正常，2-停用')
  sort       Int?    @default(0) /// @db.Comment('排序，值越小越靠前')

  // 审计字段
  created_by BigInt? /// @db.Comment('创建者ID')
  updated_by BigInt? /// @db.Comment('更新者ID')
  created_at BigInt? /// @db.Comment('创建时间戳（毫秒）')
  updated_at BigInt? /// @db.Comment('更新时间戳（毫秒）')
  deleted_at BigInt? /// @db.Comment('删除时间戳（毫秒），空表示未删除')
  remark     String? /// @db.Comment('备注信息')

  @@index([parent_id])
  @@map("system_dept")
  @@schema("master")
}

// Merchant 模块的模型
model MerchantUser {
  id          Int       @id @default(autoincrement())
  username    String    @unique
  password    String
  email       String    @unique
  companyName String    // 公司名称
  status      String    @default("active") // active, inactive, suspended
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?

  @@map("users")
  @@schema("merchant")
}

// Supplier 模块的模型
model SupplierUser {
  id           Int       @id @default(autoincrement())
  username     String    @unique
  password     String
  email        String    @unique
  businessType String    // 供应商类型
  status       String    @default("active") // active, inactive, suspended
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  deletedAt    DateTime?

  @@map("users")
  @@schema("supplier")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model base_goods_associations {
  id                                                                           BigInt          @id @default(autoincrement())
  source_goods_spu_id                                                          BigInt
  target_goods_spu_id                                                          BigInt
  association_type                                                             String          @db.VarChar(50)
  sort_order                                                                   Int             @default(0)
  created_at                                                                   DateTime        @default(now()) @db.Timestamptz(6)
  updated_at                                                                   DateTime        @default(now()) @db.Timestamptz(6)
  base_goods_spus_base_goods_associations_source_goods_spu_idTobase_goods_spus base_goods_spus @relation("base_goods_associations_source_goods_spu_idTobase_goods_spus", fields: [source_goods_spu_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  base_goods_spus_base_goods_associations_target_goods_spu_idTobase_goods_spus base_goods_spus @relation("base_goods_associations_target_goods_spu_idTobase_goods_spus", fields: [target_goods_spu_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([source_goods_spu_id, target_goods_spu_id, association_type])
  @@index([target_goods_spu_id], map: "idx_base_goods_associations_target_goods_spu_id")
  @@index([association_type], map: "idx_base_goods_associations_type")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model base_goods_attribute_items {
  id                          BigInt                        @id @default(autoincrement())
  goods_attribute_set_id      BigInt
  name                        String                        @db.VarChar(100)
  slug                        String                        @db.VarChar(100)
  type                        String                        @db.VarChar(50)
  options                     Json?
  unit                        String?                       @db.VarChar(20)
  is_required                 Boolean                       @default(false)
  is_filterable               Boolean                       @default(false)
  sort_order                  Int                           @default(0)
  is_enabled                  Boolean                       @default(true)
  created_at                  DateTime                      @default(now()) @db.Timestamptz(6)
  updated_at                  DateTime                      @default(now()) @db.Timestamptz(6)
  deleted_at                  DateTime?                     @db.Timestamptz(6)
  base_goods_attribute_sets   base_goods_attribute_sets     @relation(fields: [goods_attribute_set_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  base_goods_attribute_values base_goods_attribute_values[]

  @@unique([goods_attribute_set_id, slug])
  @@index([deleted_at], map: "idx_base_goods_attribute_items_deleted_at")
  @@index([is_filterable], map: "idx_base_goods_attribute_items_filterable")
  @@index([is_enabled], map: "idx_base_goods_attribute_items_is_enabled")
  @@index([goods_attribute_set_id], map: "idx_base_goods_attribute_items_set_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model base_goods_attribute_set_category_associations {
  goods_attribute_set_id    BigInt
  goods_category_id         BigInt
  base_goods_attribute_sets base_goods_attribute_sets @relation(fields: [goods_attribute_set_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "base_goods_attribute_set_category_a_goods_attribute_set_id_fkey")
  base_goods_categories     base_goods_categories     @relation(fields: [goods_category_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "base_goods_attribute_set_category_associ_goods_category_id_fkey")

  @@id([goods_attribute_set_id, goods_category_id])
  @@index([goods_category_id], map: "idx_base_goods_attribute_set_category_associations_category_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model base_goods_attribute_sets {
  id                                             BigInt                                           @id @default(autoincrement())
  name                                           String                                           @unique @db.VarChar(100)
  slug                                           String                                           @unique @db.VarChar(100)
  sort_order                                     Int                                              @default(0)
  created_at                                     DateTime                                         @default(now()) @db.Timestamptz(6)
  updated_at                                     DateTime                                         @default(now()) @db.Timestamptz(6)
  deleted_at                                     DateTime?                                        @db.Timestamptz(6)
  base_goods_attribute_items                     base_goods_attribute_items[]
  base_goods_attribute_set_category_associations base_goods_attribute_set_category_associations[]

  @@index([deleted_at], map: "idx_base_goods_attribute_sets_deleted_at")
  @@index([slug], map: "idx_base_goods_attribute_sets_slug")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model base_goods_attribute_values {
  id                         BigInt                     @id @default(autoincrement())
  goods_spu_id               BigInt
  goods_attribute_item_id    BigInt
  value                      String?
  created_at                 DateTime                   @default(now()) @db.Timestamptz(6)
  updated_at                 DateTime                   @default(now()) @db.Timestamptz(6)
  base_goods_attribute_items base_goods_attribute_items @relation(fields: [goods_attribute_item_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  base_goods_spus            base_goods_spus            @relation(fields: [goods_spu_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([goods_spu_id, goods_attribute_item_id])
  @@index([goods_attribute_item_id], map: "idx_base_goods_attribute_values_item_id")
  @@index([goods_spu_id], map: "idx_base_goods_attribute_values_spu_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model base_goods_brands {
  id              BigInt            @id @default(autoincrement())
  name            String
  logo_url        String?
  description     String?
  created_at      DateTime          @default(now()) @db.Timestamptz(6)
  updated_at      DateTime          @default(now()) @db.Timestamptz(6)
  deleted_at      DateTime?         @db.Timestamptz(6)
  base_goods_spus base_goods_spus[]

  @@index([deleted_at], map: "idx_base_goods_brands_deleted_at")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model base_goods_categories {
  id                                             BigInt                                           @id @default(autoincrement())
  goods_parent_category_id                       BigInt?
  name                                           String
  image_url                                      String?
  description                                    String?
  sort_order                                     Int                                              @default(0)
  is_enabled                                     Boolean                                          @default(true)
  level                                          Int?
  meta_title                                     String?
  meta_keywords                                  String?
  meta_description                               String?
  created_at                                     DateTime                                         @default(now()) @db.Timestamptz(6)
  updated_at                                     DateTime                                         @default(now()) @db.Timestamptz(6)
  deleted_at                                     DateTime?                                        @db.Timestamptz(6)
  base_goods_attribute_set_category_associations base_goods_attribute_set_category_associations[]
  base_goods_categories                          base_goods_categories?                           @relation("base_goods_categoriesTobase_goods_categories", fields: [goods_parent_category_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  other_base_goods_categories                    base_goods_categories[]                          @relation("base_goods_categoriesTobase_goods_categories")
  base_goods_category_associations               base_goods_category_associations[]

  @@index([deleted_at], map: "idx_base_goods_categories_deleted_at")
  @@index([goods_parent_category_id], map: "idx_base_goods_categories_goods_parent_id")
  @@index([is_enabled], map: "idx_base_goods_categories_is_enabled")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model base_goods_category_associations {
  goods_spu_id          BigInt
  goods_category_id     BigInt
  is_primary            Boolean               @default(false)
  base_goods_categories base_goods_categories @relation(fields: [goods_category_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  base_goods_spus       base_goods_spus       @relation(fields: [goods_spu_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@id([goods_spu_id, goods_category_id])
  @@index([goods_category_id], map: "idx_base_goods_category_associations_category_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model base_goods_freight_templates {
  id                        BigInt                      @id @default(autoincrement())
  name                      String                      @unique @db.VarChar(100)
  charge_type               Int                         @db.SmallInt
  is_default                Boolean                     @default(false)
  created_at                DateTime                    @default(now()) @db.Timestamptz(6)
  updated_at                DateTime                    @default(now()) @db.Timestamptz(6)
  deleted_at                DateTime?                   @db.Timestamptz(6)
  base_goods_shipping_rules base_goods_shipping_rules[]
  base_goods_spus           base_goods_spus[]

  @@index([deleted_at], map: "idx_base_goods_freight_templates_deleted_at")
  @@index([is_default], map: "idx_base_goods_freight_templates_is_default")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model base_goods_images {
  id              BigInt           @id @default(autoincrement())
  goods_spu_id    BigInt
  goods_sku_id    BigInt?
  image_url       String
  alt_text        String?
  sort_order      Int              @default(0)
  is_default      Boolean          @default(false)
  created_at      DateTime         @default(now()) @db.Timestamptz(6)
  updated_at      DateTime         @default(now()) @db.Timestamptz(6)
  base_goods_skus base_goods_skus? @relation(fields: [goods_sku_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  base_goods_spus base_goods_spus  @relation(fields: [goods_spu_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([goods_sku_id], map: "idx_base_goods_images_goods_sku_id")
  @@index([goods_spu_id], map: "idx_base_goods_images_goods_spu_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model base_goods_service_associations {
  goods_spu_id        BigInt
  goods_service_id    BigInt
  base_goods_services base_goods_services @relation(fields: [goods_service_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  base_goods_spus     base_goods_spus     @relation(fields: [goods_spu_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@id([goods_spu_id, goods_service_id])
  @@index([goods_service_id], map: "idx_base_goods_service_associations_service_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model base_goods_services {
  id                              BigInt                            @id @default(autoincrement())
  name                            String                            @unique @db.VarChar(100)
  image_url                       String?
  description                     String?
  created_at                      DateTime                          @default(now()) @db.Timestamptz(6)
  updated_at                      DateTime                          @default(now()) @db.Timestamptz(6)
  deleted_at                      DateTime?                         @db.Timestamptz(6)
  base_goods_service_associations base_goods_service_associations[]

  @@index([deleted_at], map: "idx_base_goods_services_deleted_at")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model base_goods_shipping_rule_regions {
  id                                      BigInt                    @id @default(autoincrement())
  goods_shipping_rule_id                  BigInt
  region_codes                            String?
  region_names                            String?
  first_unit                              Decimal                   @db.Decimal(10, 3)
  first_fee                               Decimal                   @db.Decimal(10, 2)
  additional_unit                         Decimal                   @db.Decimal(10, 3)
  additional_fee                          Decimal                   @db.Decimal(10, 2)
  override_free_shipping_threshold_amount Decimal?                  @db.Decimal(12, 2)
  override_free_shipping_threshold_unit   Decimal?                  @db.Decimal(10, 3)
  created_at                              DateTime                  @default(now()) @db.Timestamptz(6)
  updated_at                              DateTime                  @default(now()) @db.Timestamptz(6)
  base_goods_shipping_rules               base_goods_shipping_rules @relation(fields: [goods_shipping_rule_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([goods_shipping_rule_id], map: "idx_base_goods_shipping_rule_regions_rule_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model base_goods_shipping_rules {
  id                               BigInt                             @id @default(autoincrement())
  goods_freight_template_id        BigInt
  shipping_method                  String                             @db.VarChar(50)
  is_default_rule                  Boolean                            @default(false)
  default_first_unit               Decimal?                           @db.Decimal(10, 3)
  default_first_fee                Decimal?                           @db.Decimal(10, 2)
  default_additional_unit          Decimal?                           @db.Decimal(10, 3)
  default_additional_fee           Decimal?                           @db.Decimal(10, 2)
  free_shipping_threshold_amount   Decimal?                           @db.Decimal(12, 2)
  free_shipping_threshold_unit     Decimal?                           @db.Decimal(10, 3)
  created_at                       DateTime                           @default(now()) @db.Timestamptz(6)
  updated_at                       DateTime                           @default(now()) @db.Timestamptz(6)
  deleted_at                       DateTime?                          @db.Timestamptz(6)
  base_goods_shipping_rule_regions base_goods_shipping_rule_regions[]
  base_goods_freight_templates     base_goods_freight_templates       @relation(fields: [goods_freight_template_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([deleted_at], map: "idx_base_goods_shipping_rules_deleted_at")
  @@index([goods_freight_template_id], map: "idx_base_goods_shipping_rules_template_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model base_goods_sku_specification_values {
  goods_sku_id                    BigInt
  goods_specification_value_id    BigInt
  base_goods_specification_values base_goods_specification_values @relation(fields: [goods_specification_value_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "base_goods_sku_specification__goods_specification_value_id_fkey")
  base_goods_skus                 base_goods_skus                 @relation(fields: [goods_sku_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@id([goods_sku_id, goods_specification_value_id])
  @@index([goods_specification_value_id], map: "idx_base_goods_sku_specification_values_value_id")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model base_goods_skus {
  id                                  BigInt                                @id @default(autoincrement())
  goods_spu_id                        BigInt
  sku_code                            String                                @unique @db.VarChar(100)
  sales_price                         Decimal                               @db.Decimal(12, 2)
  market_price                        Decimal?                              @db.Decimal(12, 2)
  cost_price                          Decimal?                              @db.Decimal(12, 2)
  stock                               Int
  low_stock_threshold                 Int?
  unit                                String?                               @db.VarChar(20)
  weight                              Decimal?                              @db.Decimal(10, 3)
  weight_unit                         String?                               @default("kg") @db.VarChar(10)
  volume                              Decimal?                              @db.Decimal(10, 6)
  volume_unit                         String?                               @default("m3") @db.VarChar(10)
  length                              Decimal?                              @db.Decimal(10, 2)
  width                               Decimal?                              @db.Decimal(10, 2)
  height                              Decimal?                              @db.Decimal(10, 2)
  dimension_unit                      String?                               @default("cm") @db.VarChar(10)
  barcode                             String?                               @db.VarChar(100)
  is_enabled                          Boolean                               @default(true)
  sales_count                         Int                                   @default(0)
  created_at                          DateTime                              @default(now()) @db.Timestamptz(6)
  updated_at                          DateTime                              @default(now()) @db.Timestamptz(6)
  deleted_at                          DateTime?                             @db.Timestamptz(6)
  base_goods_images                   base_goods_images[]
  base_goods_sku_specification_values base_goods_sku_specification_values[]
  base_goods_spus                     base_goods_spus                       @relation(fields: [goods_spu_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([deleted_at], map: "idx_base_goods_skus_deleted_at")
  @@index([goods_spu_id], map: "idx_base_goods_skus_goods_spu_id")
  @@index([is_enabled], map: "idx_base_goods_skus_is_enabled")
  @@index([sku_code], map: "idx_base_goods_skus_sku_code")
  @@index([stock], map: "idx_base_goods_skus_stock")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model base_goods_specification_names {
  id                              BigInt                            @id @default(autoincrement())
  name                            String                            @unique @db.VarChar(100)
  created_at                      DateTime                          @default(now()) @db.Timestamptz(6)
  updated_at                      DateTime                          @default(now()) @db.Timestamptz(6)
  deleted_at                      DateTime?                         @db.Timestamptz(6)
  base_goods_specification_values base_goods_specification_values[]

  @@index([deleted_at], map: "idx_base_goods_specification_names_deleted_at")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model base_goods_specification_values {
  id                                  BigInt                                @id @default(autoincrement())
  goods_specification_name_id         BigInt
  value                               String
  image_url                           String?
  sort_order                          Int                                   @default(0)
  created_at                          DateTime                              @default(now()) @db.Timestamptz(6)
  updated_at                          DateTime                              @default(now()) @db.Timestamptz(6)
  deleted_at                          DateTime?                             @db.Timestamptz(6)
  base_goods_sku_specification_values base_goods_sku_specification_values[]
  base_goods_specification_names      base_goods_specification_names        @relation(fields: [goods_specification_name_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "base_goods_specification_value_goods_specification_name_id_fkey")

  @@unique([goods_specification_name_id, value], map: "base_goods_specification_valu_goods_specification_name_id_v_key")
  @@index([deleted_at], map: "idx_base_goods_specification_values_deleted_at")
  @@index([goods_specification_name_id], map: "idx_base_goods_specification_values_name_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model base_goods_spus {
  id                                                                                   BigInt                             @id @default(autoincrement())
  spu_code                                                                             String?                            @unique @db.VarChar(100)
  name                                                                                 String
  subtitle                                                                             String?
  slug                                                                                 String                             @unique @db.VarChar(255)
  description                                                                          String?
  goods_brand_id                                                                       BigInt?
  goods_freight_template_id                                                            BigInt?
  meta_title                                                                           String?
  meta_keywords                                                                        String?
  meta_description                                                                     String?
  sort_order                                                                           Int                                @default(0)
  status                                                                               Int                                @default(0) @db.SmallInt
  published_at                                                                         DateTime?                          @db.Timestamptz(6)
  is_virtual                                                                           Boolean                            @default(false)
  is_shipping_required                                                                 Boolean?                           @default(dbgenerated("(NOT is_virtual)"))
  created_at                                                                           DateTime                           @default(now()) @db.Timestamptz(6)
  updated_at                                                                           DateTime                           @default(now()) @db.Timestamptz(6)
  deleted_at                                                                           DateTime?                          @db.Timestamptz(6)
  total_sales                                                                          Int                                @default(0)
  total_stock                                                                          Int                                @default(0)
  base_goods_associations_base_goods_associations_source_goods_spu_idTobase_goods_spus base_goods_associations[]          @relation("base_goods_associations_source_goods_spu_idTobase_goods_spus")
  base_goods_associations_base_goods_associations_target_goods_spu_idTobase_goods_spus base_goods_associations[]          @relation("base_goods_associations_target_goods_spu_idTobase_goods_spus")
  base_goods_attribute_values                                                          base_goods_attribute_values[]
  base_goods_category_associations                                                     base_goods_category_associations[]
  base_goods_images                                                                    base_goods_images[]
  base_goods_service_associations                                                      base_goods_service_associations[]
  base_goods_skus                                                                      base_goods_skus[]
  base_goods_brands                                                                    base_goods_brands?                 @relation(fields: [goods_brand_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_base_goods_spus_brand")
  base_goods_freight_templates                                                         base_goods_freight_templates?      @relation(fields: [goods_freight_template_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_base_goods_spus_freight_template")
  base_goods_tag_associations                                                          base_goods_tag_associations[]
  base_goods_videos                                                                    base_goods_videos[]

  @@index([created_at], map: "idx_base_goods_spus_created_at")
  @@index([deleted_at], map: "idx_base_goods_spus_deleted_at")
  @@index([goods_brand_id], map: "idx_base_goods_spus_goods_brand_id")
  @@index([goods_freight_template_id], map: "idx_base_goods_spus_goods_freight_template_id")
  @@index([slug], map: "idx_base_goods_spus_slug")
  @@index([status], map: "idx_base_goods_spus_status")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model base_goods_tag_associations {
  goods_spu_id    BigInt
  goods_tag_id    BigInt
  base_goods_spus base_goods_spus @relation(fields: [goods_spu_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  base_goods_tags base_goods_tags @relation(fields: [goods_tag_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@id([goods_spu_id, goods_tag_id])
  @@index([goods_tag_id], map: "idx_base_goods_tag_associations_tag_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model base_goods_tags {
  id                          BigInt                        @id @default(autoincrement())
  name                        String                        @unique @db.VarChar(100)
  slug                        String                        @unique @db.VarChar(100)
  image_url                   String?
  description                 String?
  tag_type                    String?                       @db.VarChar(50)
  sort_order                  Int                           @default(0)
  is_enabled                  Boolean                       @default(true)
  created_at                  DateTime                      @default(now()) @db.Timestamptz(6)
  updated_at                  DateTime                      @default(now()) @db.Timestamptz(6)
  deleted_at                  DateTime?                     @db.Timestamptz(6)
  goods_spu_count             Int                           @default(0)
  base_goods_tag_associations base_goods_tag_associations[]

  @@index([deleted_at], map: "idx_base_goods_tags_deleted_at")
  @@index([is_enabled], map: "idx_base_goods_tags_is_enabled")
  @@index([slug], map: "idx_base_goods_tags_slug")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model base_goods_videos {
  id              BigInt          @id @default(autoincrement())
  goods_spu_id    BigInt
  video_url       String
  cover_image_url String?
  sort_order      Int             @default(0)
  created_at      DateTime        @default(now()) @db.Timestamptz(6)
  updated_at      DateTime        @default(now()) @db.Timestamptz(6)
  base_goods_spus base_goods_spus @relation(fields: [goods_spu_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([goods_spu_id], map: "idx_base_goods_videos_goods_spu_id")
}


// 从 apps/master/prisma/models/goods/goods_associations.prisma 导入的模型

model GoodsAssociation {
  id                                                            BigInt     @id @default(autoincrement())
  source_goods_spu_id                                           BigInt
  target_goods_spu_id                                           BigInt
  association_type                                              String     @db.VarChar(50)
  created_at                                                    DateTime   @default(now()) @db.Timestamptz(6)
  updated_at                                                    DateTime   @default(now()) @db.Timestamptz(6)
  // 暂时注释掉与GoodsSpu的关系，以便解决循环引用问题
  // goods_spus_goods_associations_source_goods_spu_idTogoods_spus GoodsSpu  @relation("goods_associations_source_goods_spu_idTogoods_spus", fields: [source_goods_spu_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  // goods_spus_goods_associations_target_goods_spu_idTogoods_spus GoodsSpu  @relation("goods_associations_target_goods_spu_idTogoods_spus", fields: [target_goods_spu_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([source_goods_spu_id], map: "idx_goods_associations_source_goods_spu_id")
  @@index([target_goods_spu_id], map: "idx_goods_associations_target_goods_spu_id")
  @@index([association_type], map: "idx_goods_associations_type")
  @@map("goods_associations")
  @@schema("base")
}

// 从 apps/master/prisma/models/goods/goods_attribute_items.prisma 导入的模型

model GoodsAttributeItem {
  id                     BigInt                   @id @default(autoincrement())
  goods_attribute_set_id BigInt
  name                   String                   @db.VarChar(100)
  type                   String                   @db.VarChar(50)
  value                  Json?
  is_required            Boolean                  @default(false)
  is_filterable          Boolean                  @default(false)
  sort_order             Int                      @default(0)
  is_enabled             Boolean                  @default(true)
  created_at             BigInt                   @default(0)
  updated_at             BigInt                   @default(0)
  deleted_at             BigInt?
  goods_attribute_sets   GoodsAttributeSet        @relation(fields: [goods_attribute_set_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  goods_attribute_values GoodsAttributeValue[]

  @@index([deleted_at], map: "idx_goods_attribute_items_deleted_at")
  @@index([is_filterable], map: "idx_goods_attribute_items_filterable")
  @@index([is_enabled], map: "idx_goods_attribute_items_is_enabled")
  @@index([goods_attribute_set_id], map: "idx_goods_attribute_items_set_id")
  @@map("goods_attribute_items")
  @@schema("base")
}

model GoodsAttributeSetCategoryAssociation {
  goods_attribute_set_id BigInt
  goods_category_id      BigInt
  goods_attribute_sets   GoodsAttributeSet @relation(fields: [goods_attribute_set_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  // goods_categories       GoodsCategory     @relation(fields: [goods_category_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@id([goods_attribute_set_id, goods_category_id])
  @@index([goods_category_id], map: "idx_goods_attribute_set_category_associations_category_id")
  @@map("goods_attribute_set_category_associations")
  @@schema("base")
}

model GoodsAttributeSet {
  id                                        BigInt                                      @id @default(autoincrement())
  name                                      String                                      @unique @db.VarChar(100)
  sort_order                                Int                                         @default(0)
  created_at                                BigInt                                      @default(0)
  updated_at                                BigInt                                      @default(0)
  deleted_at                                BigInt?
  created_by                                BigInt?
  updated_by                                BigInt?
  goods_attribute_items                     GoodsAttributeItem[]
  goods_attribute_set_category_associations GoodsAttributeSetCategoryAssociation[]

  @@index([deleted_at], map: "idx_goods_attribute_sets_deleted_at")
  @@map("goods_attribute_sets")
  @@schema("base")
}

model GoodsAttributeValue {
  id                      BigInt                @id @default(autoincrement())
  goods_spu_id            BigInt
  goods_attribute_item_id BigInt
  value                   String?
  created_at              BigInt                @default(0)
  updated_at              BigInt                @default(0)
  goods_attribute_items   GoodsAttributeItem    @relation(fields: [goods_attribute_item_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  goods_spus              GoodsSpu              @relation(fields: [goods_spu_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([goods_spu_id, goods_attribute_item_id], map: "goods_attribute_values_goods_spu_id_goods_attribute_it_key")
  @@index([goods_attribute_item_id], map: "idx_goods_attribute_values_item_id")
  @@index([goods_spu_id], map: "idx_goods_attribute_values_spu_id")
  @@map("goods_attribute_values")
  @@schema("base")
}

// 从 apps/master/prisma/models/goods/goods_categories.prisma 导入的模型

model GoodsCategory {
  id                                        BigInt                                      @id @default(autoincrement())
  goods_parent_category_id                  BigInt?
  name                                      String
  slug                                      String                                      @unique @db.VarChar(100)
  image_url                                 String?
  description                               String?
  meta_title                                String?
  meta_keywords                             String?
  meta_description                          String?
  sort_order                                Int                                         @default(0)
  is_enabled                                Boolean                                     @default(true)
  created_at                                DateTime                                    @default(now()) @db.Timestamptz(6)
  updated_at                                DateTime                                    @default(now()) @db.Timestamptz(6)
  deleted_at                                DateTime?                                   @db.Timestamptz(6)
  goods_spu_count                           Int                                        @default(0)
  parent                                    GoodsCategory?                              @relation("CategoryToSubcategory", fields: [goods_parent_category_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  subcategories                             GoodsCategory[]                             @relation("CategoryToSubcategory")
  // goods_category_associations               GoodsCategoryAssociation[]
  // goods_attribute_set_category_associations GoodsAttributeSetCategoryAssociation[]

  @@index([deleted_at], map: "idx_goods_categories_deleted_at")
  @@index([goods_parent_category_id], map: "idx_goods_categories_goods_parent_id")
  @@index([is_enabled], map: "idx_goods_categories_is_enabled")
  @@map("goods_categories")
  @@schema("base")
}

// 从 apps/master/prisma/models/goods/goods_freight_region_configs.prisma 导入的模型

// 运费模板区域配置模型
model GoodsFreightRegionConfig {
  id                  BigInt   @id @default(autoincrement())
  freight_template_id BigInt   @map("freight_template_id") // 关联的运费模板ID
  region_codes        String?  @db.Text @map("region_codes") // 区域编码，多个用逗号分隔，null表示全国
  region_names        String   @db.Text @map("region_names") // 区域名称，多个用逗号分隔，如"北京市,上海市"等
  first_item          Int      @default(1) @map("first_item") // 首件/首重/首体积数量
  first_fee           Decimal  @default(0) @db.Decimal(10, 2) @map("first_fee") // 首件/首重/首体积费用
  additional_item     Int      @default(1) @map("additional_item") // 续件/续重/续体积数量
  additional_fee      Decimal  @default(0) @db.Decimal(10, 2) @map("additional_fee") // 续件/续重/续体积费用
  created_at          BigInt   @default(dbgenerated("extract(epoch from CURRENT_TIMESTAMP)::BIGINT")) @map("created_at") // 记录创建时间
  updated_at          BigInt   @default(dbgenerated("extract(epoch from CURRENT_TIMESTAMP)::BIGINT")) @map("updated_at") // 记录最后修改时间
  deleted_at          BigInt?  @map("deleted_at") // 记录软删除时间（时间戳，NULL 表示未删除）
  created_by          BigInt?  @map("created_by") // 创建人ID
  updated_by          BigInt?  @map("updated_by") // 最后更新人ID
  
  // 关联运费模板
  freight_template    GoodsFreightTemplates @relation(fields: [freight_template_id], references: [id], onDelete: Cascade)

  @@map("goods_freight_region_configs")
  @@schema("base")
  @@index([freight_template_id], name: "idx_goods_freight_region_configs_template_id")
  @@index([deleted_at], name: "idx_goods_freight_region_configs_deleted_at")
  @@index([created_by], name: "idx_goods_freight_region_configs_created_by")
  @@index([updated_by], name: "idx_goods_freight_region_configs_updated_by")
}

// 从 apps/master/prisma/models/goods/goods_freight_templates.prisma 导入的模型

// 运费模板模型
model GoodsFreightTemplates {
  id         BigInt   @id @default(autoincrement())
  name       String   @unique @db.VarChar(100) @map("name") // 运费模板名称 (必填, 唯一)
  charge_type Int     @db.SmallInt @map("charge_type") // 计价方式 (1: 按件数, 2: 按重量, 3: 按体积)
  is_default Boolean  @default(false) @map("is_default") // 是否为默认运费模板
  created_at BigInt   @default(dbgenerated("extract(epoch from CURRENT_TIMESTAMP)::BIGINT")) @map("created_at") // 记录创建时间（时间戳）
  updated_at BigInt   @default(dbgenerated("extract(epoch from CURRENT_TIMESTAMP)::BIGINT")) @map("updated_at") // 记录最后修改时间（时间戳）
  deleted_at BigInt?  @map("deleted_at") // 记录软删除时间（时间戳，NULL 表示未删除）
  created_by BigInt?  @map("created_by") // 创建人ID
  updated_by BigInt?  @map("updated_by") // 最后更新人ID

  // 关联区域配置
  region_configs GoodsFreightRegionConfig[] @relation

  @@map("goods_freight_templates")
  @@schema("base")
  @@index([deleted_at], name: "idx_goods_freight_templates_deleted_at")
  @@index([is_default], name: "idx_goods_freight_templates_is_default")
  @@index([created_by], name: "idx_goods_freight_templates_created_by")
  @@index([updated_by], name: "idx_goods_freight_templates_updated_by")
}

// 从 apps/master/prisma/models/goods/goods_spus.prisma 导入的模型

model GoodsSpu {
  id                                                                    BigInt                        @id @default(autoincrement())
  spu_code                                                              String?                       @unique @db.VarChar(100)
  name                                                                  String
  subtitle                                                              String?
  slug                                                                  String                        @unique @db.VarChar(255)
  description                                                           String?
  goods_brand_id                                                        BigInt?
  goods_freight_template_id                                             BigInt?
  meta_title                                                            String?
  meta_keywords                                                         String?
  meta_description                                                      String?
  sort_order                                                            Int                           @default(0)
  status                                                                Int                           @default(0) @db.SmallInt
  published_at                                                          DateTime?                     @db.Timestamptz(6)
  is_virtual                                                            Boolean                       @default(false)
  is_shipping_required                                                  Boolean?                      @default(dbgenerated("(NOT is_virtual)"))
  created_at                                                            DateTime                      @default(now()) @db.Timestamptz(6)
  updated_at                                                            DateTime                      @default(now()) @db.Timestamptz(6)
  deleted_at                                                            DateTime?                     @db.Timestamptz(6)
  total_sales                                                           Int                           @default(0)
  total_stock                                                           Int                           @default(0)

  // 关联 - 注释掉尚未定义的模型关联
  // goods_associations_goods_associations_source_goods_spu_idTogoods_spus GoodsAssociation[]          @relation("goods_associations_source_goods_spu_idTogoods_spus")
  // goods_associations_goods_associations_target_goods_spu_idTogoods_spus GoodsAssociation[]          @relation("goods_associations_target_goods_spu_idTogoods_spus")
  goods_attribute_values                                                GoodsAttributeValue[]
  // goods_category_associations                                           GoodsCategoryAssociation[]
  // goods_images                                                          GoodsImage[]
  // goods_service_associations                                            GoodsServiceAssociation[]
  // goods_skus                                                            GoodsSku[]
  // goods_brands                                                          GoodsBrand?                 @relation(fields: [goods_brand_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_goods_spus_brand")
  // goods_freight_templates                                               GoodsFreightTemplate?      @relation(fields: [goods_freight_template_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_goods_spus_freight_template")
  // goods_tag_associations                                                GoodsTagAssociation[]
  // goods_videos                                                          GoodsVideo[]

  @@index([created_at], map: "idx_goods_spus_created_at")
  @@index([deleted_at], map: "idx_goods_spus_deleted_at")
  @@index([goods_brand_id], map: "idx_goods_spus_goods_brand_id")
  @@index([goods_freight_template_id], map: "idx_goods_spus_goods_freight_template_id")
  @@index([slug], map: "idx_goods_spus_slug")
  @@index([status], map: "idx_goods_spus_status")
  @@map("goods_spus")
  @@schema("base")
}

// 从 apps/master/prisma/models/goods/goods_tags.prisma 导入的模型

model GoodsTagAssociation {
  goods_spu_id BigInt
  goods_tag_id BigInt
  // 暂时注释掉与GoodsSpu的关系，以便解决循环引用问题
  // goods_spus   GoodsSpu @relation(fields: [goods_spu_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  goods_tags   GoodsTag @relation(fields: [goods_tag_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@id([goods_spu_id, goods_tag_id])
  @@index([goods_tag_id], map: "idx_goods_tag_associations_tag_id")
  @@map("goods_tag_associations")
  @@schema("base")
}

model GoodsTag {
  id                     BigInt                   @id @default(autoincrement())
  name                   String                   @unique @db.VarChar(100)
  slug                   String                   @unique @db.VarChar(100)
  image_url              String?
  description            String?
  tag_type               String?                  @db.VarChar(50)
  sort_order             Int                      @default(0)
  is_enabled             Boolean                  @default(true)
  created_at             BigInt
  updated_at             BigInt
  deleted_at             BigInt?
  goods_spu_count        Int                      @default(0)
  created_by             BigInt?
  updated_by             BigInt?
  goods_tag_associations GoodsTagAssociation[]

  @@index([deleted_at], map: "idx_goods_tags_deleted_at")
  @@index([is_enabled], map: "idx_goods_tags_is_enabled")
  @@index([slug], map: "idx_goods_tags_slug")
  @@index([created_by], map: "idx_goods_tags_created_by")
  @@index([updated_by], map: "idx_goods_tags_updated_by")
  @@map("goods_tags")
  @@schema("base")
}

// 从 apps/master/prisma/models/goods/goods_videos.prisma 导入的模型

model GoodsVideo {
  id              BigInt     @id @default(autoincrement())
  goods_spu_id    BigInt
  video_url       String
  cover_image_url String?
  sort_order      Int        @default(0)
  created_at      DateTime   @default(now()) @db.Timestamptz(6)
  updated_at      DateTime   @default(now()) @db.Timestamptz(6)
  // 暂时注释掉与GoodsSpu的关系，以便解决循环引用问题
  // goods_spus      GoodsSpu @relation(fields: [goods_spu_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([goods_spu_id], map: "idx_goods_videos_goods_spu_id")
  @@map("goods_videos")
  @@schema("base")
}

// 从 apps/master/prisma/models/region.prisma 导入的模型

model Region {
  id             Int     @id
  citycode       Int?    @default(0) @map("citycode")
  cityname       String? @default("") @map("cityname") @db.VarChar(32)
  cityfullname   String? @default("") @map("cityfullname") @db.VarChar(500)
  citycodeprefix Int?    @default(0) @map("citycodeprefix")
  level          Int?    @default(0) @map("level") @db.SmallInt
  parent_id      Int?    @default(0) @map("parent_id")
  out_field      Int?    @default(0) @map("out_field") @db.SmallInt

  @@map("region")
  @@schema("base")
  @@index([citycode], name: "idx_region_citycode")
  @@index([level], name: "idx_region_level")
  @@index([parent_id], name: "idx_region_parent_id")
}

// 从 apps/master/prisma/models/system_dept.prisma 导入的模型

/// 部门信息表，存储组织架构信息
model SystemDept {
  // 主键
  id         BigInt  @id @default(autoincrement()) /// @db.Comment('主键，系统自动生成')

  // 基本信息
  parent_id  BigInt  /// @db.Comment('父ID，关联本表id字段，空表示一级部门')
  level      String  /// @db.Comment('组级集合，存储部门层级路径')
  name       String  /// @db.Comment('部门名称，必填')
  leader     String? /// @db.Comment('负责人')
  phone      String? /// @db.Comment('联系电话，11位手机号')

  // 状态信息
  status     Int?    @default(1) /// @db.Comment('状态：1-正常，2-停用')
  sort       Int?    @default(0) /// @db.Comment('排序，值越小越靠前')

  // 审计字段
  created_by BigInt? /// @db.Comment('创建者ID')
  updated_by BigInt? /// @db.Comment('更新者ID')
  created_at BigInt? /// @db.Comment('创建时间戳（毫秒）')
  updated_at BigInt? /// @db.Comment('更新时间戳（毫秒）')
  deleted_at BigInt? /// @db.Comment('删除时间戳（毫秒），空表示未删除')
  remark     String? /// @db.Comment('备注信息')

  @@index([parent_id])
  @@map("system_dept")
}

// 从 apps/master/prisma/models/system_menu.prisma 导入的模型

/// 菜单信息表
model Menu {
  // 主键
  id            BigInt    @id @default(autoincrement())  /// 主键
  
  // 菜单基本信息
  parent_id     BigInt                                  /// 父ID
  level         String    @db.VarChar(500)              /// 组级集合
  name          String    @db.VarChar(50)               /// 菜单名称
  code          String    @db.VarChar(100)              /// 菜单标识代码
  icon          String?   @db.VarChar(50)               /// 菜单图标
  route         String?   @db.VarChar(200)              /// 路由地址
  component     String?   @db.VarChar(255)              /// 组件路径
  redirect      String?   @db.VarChar(255)              /// 跳转地址
  
  // 菜单配置
  is_hidden     Int       @default(1)                   /// 是否隐藏 (1是 2否)
  type          String    @db.VarChar(1)                /// 菜单类型 (M菜单 B按钮 L链接 I iframe)
  status        Int?      @default(1)                   /// 状态 (1正常 2停用)
  sort          Int?      @default(0)                   /// 排序
  
  // 审计字段
  created_by    BigInt?                                 /// 创建者ID
  updated_by    BigInt?                                 /// 更新者ID
  created_at    BigInt    @default(dbgenerated("(extract(epoch from now()) * 1000)::bigint"))  /// 创建时间戳
  updated_at    BigInt    @default(dbgenerated("(extract(epoch from now()) * 1000)::bigint"))  /// 更新时间戳
  deleted_at    BigInt?                                 /// 删除时间戳
  
  // 其他信息
  remark        String?   @db.VarChar(255)              /// 备注

  @@map("system_menu")
  @@schema("master")
}

// 从 apps/master/prisma/models/system_role.prisma 导入的模型

/// 角色信息表
model Role {
  // 主键
  id          BigInt    @id @default(autoincrement())  /// 角色ID

  // 基本信息
  name        String    @db.VarChar(30)               /// 角色名称
  code        String    @unique @db.VarChar(100)      /// 角色代码，唯一
  data_scope  Int       @default(1) @db.SmallInt      /// 数据范围：1-全部数据权限 2-自定义数据权限 3-本部门数据权限 4-本部门及以下数据权限 5-本人数据权限
  status      Int       @default(1) @db.SmallInt      /// 状态：1-正常 2-停用
  sort        Int       @default(0) @db.SmallInt      /// 排序
  remark      String?   @db.VarChar(255)              /// 备注

  // 审计字段
  created_by  BigInt?                                 /// 创建者ID
  updated_by  BigInt?                                 /// 更新者ID
  created_at  BigInt    @default(dbgenerated("(extract(epoch from now()) * 1000)::bigint"))  /// 创建时间戳（毫秒）
  updated_at  BigInt    @default(dbgenerated("(extract(epoch from now()) * 1000)::bigint"))  /// 更新时间戳（毫秒）
  deleted_at  BigInt?                                 /// 删除时间戳（毫秒）

  @@map("system_role")
  @@schema("master")
}

// 从 apps/master/prisma/models/users.prisma 导入的模型

/// 用户信息表，存储系统用户数据
model User {
  // 主键
  id            BigInt    @id                           /// 用户ID，16位雪花算法，系统自动生成
  
  // 基本信息
  user_type     Int       @default(60)                  /// 用户类型：100-主账户，60-子账号，默认子账号
  username      String    @unique                       /// 用户名，必填，唯一，用于登录
  password      String                                  /// 密码，必填，加密存储
  nickname      String?                                 /// 用户昵称，选填，用于显示
  phone         String?   @db.VarChar(11)               /// 手机号，11位数字，可用于登录
  email         String?   @unique                       /// 邮箱，唯一，可用于找回密码
  avatar        String?                                 /// 头像地址，存储图片URL
  
  // 组织架构
  dept_id       BigInt?                                 /// 部门ID，关联 Department 表的 id 字段
  role_id       BigInt?                                 /// 角色ID，关联 Role 表的 id 字段
  
  // 状态信息
  status        Int       @default(1)                   /// 状态：1-正常，0-禁用
  login_ip      String?   @db.VarChar(45)               /// 最后登录IP
  login_time    BigInt?                                 /// 最后登录时间戳（毫秒）
  login_token   String?                                 /// 登录token
  
  // 其他信息
  remark        String?                                 /// 备注信息
  
  // 审计字段
  created_by    BigInt?                                 /// 创建者ID，16位
  updated_by    BigInt?                                 /// 更新者ID，16位
  created_at    BigInt    @default(dbgenerated("(extract(epoch from now()) * 1000)::bigint"))  /// 创建时间戳（毫秒）
  updated_at    BigInt    @default(dbgenerated("(extract(epoch from now()) * 1000)::bigint"))  /// 更新时间戳（毫秒）
  deleted_at    BigInt?                                 /// 删除时间戳（毫秒）（软删除）

  @@map("users")
  @@schema("master")
}