/// 平台信息表，存储销售平台数据
model platform {
  // 主键字段
  id            BigInt   @id /// @db.Comment('平台ID，16位雪花算法，系统自动生成')
  
  // 基本信息
  name          String   @db.VarChar(50) /// @db.Comment('平台名称')
  code          String   @db.VarChar(50) /// @db.Comment('平台代码')
  spider_config Json?    /// @db.Comment('spider配置，JSON格式')
  
  // 状态信息
  status        Int      @default(1) /// @db.Comment('状态：1-启用，0-禁用')
  
  // 关联字段
  channel_id    BigInt   /// @db.Comment('所属渠道ID')
  
  // 审计字段
  created_at    BigInt   /// @db.Comment('创建时间')
  updated_at    BigInt   /// @db.Comment('更新时间')
  deleted_at    BigInt?  /// @db.Comment('删除时间，软删除标记')
  created_by    BigInt?  /// @db.Comment('创建人ID')
  updated_by    BigInt?  /// @db.Comment('更新人ID')

  // 索引
  @@index([name], name: "idx_platform_name")
  @@index([code], name: "idx_platform_code")
  @@index([channel_id], name: "idx_platform_channel_id")
  @@index([status], name: "idx_platform_status")
  @@schema("base")
}
