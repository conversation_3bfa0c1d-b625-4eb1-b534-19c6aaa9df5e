/**
 * Prisma模型合并脚本
 * 将models目录下的所有.prisma文件合并到schema.prisma中
 */
const fs = require('fs');
const path = require('path');
const glob = require('glob');

// 基础schema内容
const baseSchema = `generator client {
  provider = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["master", "merchant", "supplier", "base", "official", "spider", "provider"]
}
`;

/**
 * 合并所有Prisma模型文件
 */
async function mergeSchemas() {
  console.log('===== Prisma Schema合并工具 =====');
  console.log('开始合并模型定义...');
  
  // 收集所有模块中的Prisma模型文件
  const appsDir = path.join(__dirname, '..', 'apps');
  const prismaFiles = [];
  
  // 检查apps目录是否存在
  if (!fs.existsSync(appsDir)) {
    console.error(`错误: 应用目录不存在: ${appsDir}`);
    process.exit(1);
  }
  
  // 1. 收集apps下各模块的prisma模型
  const modulesPrismaFiles = glob.sync(path.join(appsDir, '*', 'prisma', 'models', '**', '*.prisma'));
  console.log(`从模块目录找到 ${modulesPrismaFiles.length} 个Prisma模型文件`);
  prismaFiles.push(...modulesPrismaFiles);
  
  // 2. 可选：收集prisma/models目录下的模型（如果存在）
  const rootModelsDir = path.join(__dirname, 'models');
  if (fs.existsSync(rootModelsDir)) {
    const rootPrismaFiles = glob.sync(path.join(rootModelsDir, '**', '*.prisma'));
    console.log(`从根模型目录找到 ${rootPrismaFiles.length} 个Prisma模型文件`);
    prismaFiles.push(...rootPrismaFiles);
  }
  
  if (prismaFiles.length === 0) {
    console.error('错误: 没有找到任何Prisma模型文件');
    process.exit(1);
  } else {
    console.log(`总共找到 ${prismaFiles.length} 个Prisma模型文件`);
  }
  
  // 读取并合并所有文件内容
  let mergedSchema = '';
  
  for (const file of prismaFiles) {
    const content = fs.readFileSync(file, 'utf8');
    
    // 跳过文件中的generator和datasource定义
    let processedContent = content;
    
    // 移除generator和datasource块
    processedContent = processedContent.replace(/generator\s+client\s+{[\s\S]*?}/g, '');
    processedContent = processedContent.replace(/datasource\s+db\s+{[\s\S]*?}/g, '');
    
    mergedSchema += '\n// ' + path.relative(__dirname, file) + '\n';
    mergedSchema += processedContent + '\n';
    console.log(`已合并: ${path.relative(__dirname, file)}`);
  }
  
  // 清空并创建新的主schema文件
  const outputFile = path.join(__dirname, 'schema.prisma');
  // 先检查文件是否存在
  if (fs.existsSync(outputFile)) {
    console.log(`清空主schema文件: ${outputFile}`);
    // 只保留基础配置，清空其他内容
    fs.writeFileSync(outputFile, baseSchema);
  }
  
  // 写入合并后的内容
  fs.writeFileSync(outputFile, baseSchema + '\n' + mergedSchema);
  console.log(`合并完成，输出文件: ${outputFile}`);
  
  console.log('===== 合并完成 =====');
}

// 运行合并脚本
mergeSchemas().catch(error => {
  console.error('合并失败:', error);
  process.exit(1);
});
