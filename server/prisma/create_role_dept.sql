-- 创建角色部门关联表
CREATE TABLE base.role_dept (
    role_id BIGINT NOT NULL,
    dept_id BIGINT NOT NULL,
    created_at BIGINT NOT NULL,
    
    -- 设置联合主键
    PRIMARY KEY (role_id, dept_id)
);

-- 创建索引
CREATE INDEX idx_role_dept_role_id ON base.role_dept(role_id);
CREATE INDEX idx_role_dept_dept_id ON base.role_dept(dept_id);

-- 添加注释
COMMENT ON TABLE base.role_dept IS '角色与部门多对多关联表，用于数据权限控制';
COMMENT ON COLUMN base.role_dept.role_id IS '角色ID';
COMMENT ON COLUMN base.role_dept.dept_id IS '部门ID';
COMMENT ON COLUMN base.role_dept.created_at IS '创建时间戳';
