-- AlterTable
ALTER TABLE "base"."goods_brands" ALTER COLUMN "created_at" SET DEFAULT extract(epoch from now()) * 1000,
ALTER COLUMN "updated_at" SET DEFAULT extract(epoch from now()) * 1000;

-- AlterTable
ALTER TABLE "base"."goods_categories" ALTER COLUMN "created_at" SET DEFAULT extract(epoch from now()) * 1000,
ALTER COLUMN "updated_at" SET DEFAULT extract(epoch from now()) * 1000;

-- AlterTable
ALTER TABLE "base"."goods_images" ALTER COLUMN "created_at" SET DEFAULT extract(epoch from now()) * 1000,
ALTER COLUMN "updated_at" SET DEFAULT extract(epoch from now()) * 1000;

-- AlterTable
ALTER TABLE "base"."goods_services" ALTER COLUMN "created_at" SET DEFAULT extract(epoch from now()) * 1000,
ALTER COLUMN "updated_at" SET DEFAULT extract(epoch from now()) * 1000;

-- AlterTable
ALTER TABLE "base"."goods_skus" ALTER COLUMN "created_at" SET DEFAULT extract(epoch from now()) * 1000,
ALTER COLUMN "updated_at" SET DEFAULT extract(epoch from now()) * 1000;

-- AlterTable
ALTER TABLE "base"."goods_specification_names" ALTER COLUMN "created_at" SET DEFAULT extract(epoch from now()) * 1000,
ALTER COLUMN "updated_at" SET DEFAULT extract(epoch from now()) * 1000;

-- AlterTable
ALTER TABLE "base"."goods_specification_values" ALTER COLUMN "created_at" SET DEFAULT extract(epoch from now()) * 1000,
ALTER COLUMN "updated_at" SET DEFAULT extract(epoch from now()) * 1000;

-- AlterTable
ALTER TABLE "base"."goods_spus" ALTER COLUMN "created_at" SET DEFAULT extract(epoch from now()) * 1000,
ALTER COLUMN "updated_at" SET DEFAULT extract(epoch from now()) * 1000;

-- AlterTable
ALTER TABLE "base"."goods_videos" ALTER COLUMN "created_at" SET DEFAULT extract(epoch from now()) * 1000,
ALTER COLUMN "updated_at" SET DEFAULT extract(epoch from now()) * 1000;
