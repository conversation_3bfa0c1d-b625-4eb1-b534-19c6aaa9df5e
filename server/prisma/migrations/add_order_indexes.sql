-- 为订单表添加索引以提高查询性能

-- 订单状态索引（用于按状态筛选订单）
CREATE INDEX IF NOT EXISTS "orders_order_status_idx" ON "base"."orders" ("order_status");

-- 创建时间索引（用于时间范围查询）
CREATE INDEX IF NOT EXISTS "orders_created_at_idx" ON "base"."orders" ("created_at");

-- 支付状态索引
CREATE INDEX IF NOT EXISTS "orders_payment_status_idx" ON "base"."orders" ("payment_status");

-- 发货状态索引
CREATE INDEX IF NOT EXISTS "orders_shipping_status_idx" ON "base"."orders" ("shipping_status");

-- 订单编号索引（用于按订单号查询）
CREATE INDEX IF NOT EXISTS "orders_order_sn_idx" ON "base"."orders" ("order_sn");

-- 用户ID索引（用于查询用户订单）
CREATE INDEX IF NOT EXISTS "orders_user_id_idx" ON "base"."orders" ("user_id");

-- 复合索引：订单状态+创建时间（常用组合查询）
CREATE INDEX IF NOT EXISTS "orders_status_created_at_idx" ON "base"."orders" ("order_status", "created_at");

-- 订单项表索引
CREATE INDEX IF NOT EXISTS "order_items_order_id_idx" ON "base"."order_items" ("order_id");
CREATE INDEX IF NOT EXISTS "order_items_product_name_idx" ON "base"."order_items" ("product_name");

-- 订单配送信息表索引
CREATE INDEX IF NOT EXISTS "order_shipping_info_order_id_idx" ON "base"."order_shipping_info" ("order_id");
CREATE INDEX IF NOT EXISTS "order_shipping_info_recipient_phone_idx" ON "base"."order_shipping_info" ("recipient_phone");
