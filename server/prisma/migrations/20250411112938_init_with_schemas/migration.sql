-- CreateSchema
CREATE SCHEMA IF NOT <PERSON>XISTS "master";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "merchant";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "supplier";

-- CreateTable
CREATE TABLE "master"."goods_category" (
    "id" SERIAL NOT NULL,
    "goodsParentCategoryId" INTEGER NOT NULL DEFAULT 0,
    "name" TEXT NOT NULL,
    "slug" TEXT,
    "imageUrl" TEXT,
    "description" TEXT,
    "sortOrder" INTEGER NOT NULL DEFAULT 100,
    "isEnabled" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "goods_category_pkey" PRIMARY KEY ("id")
);
