/*
  Warnings:

  - You are about to drop the `goods_category` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `users` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `users` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `users` table. If the table is not empty, all the data it contains will be lost.

*/
-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "base";

-- DropTable
DROP TABLE "master"."goods_category";

-- DropTable
DROP TABLE "master"."users";

-- DropTable
DROP TABLE "merchant"."users";

-- DropTable
DROP TABLE "supplier"."users";

-- CreateTable
CREATE TABLE "base"."goods_associations" (
    "id" BIGSERIAL NOT NULL,
    "source_goods_spu_id" BIGINT NOT NULL,
    "target_goods_spu_id" BIGINT NOT NULL,
    "association_type" VARCHAR(50) NOT NULL,
    "created_at" BIGINT NOT NULL,
    "updated_at" BIGINT NOT NULL,

    CONSTRAINT "goods_associations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "base"."goods_attribute_items" (
    "id" BIGSERIAL NOT NULL,
    "goods_attribute_set_id" BIGINT NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "type" VARCHAR(50) NOT NULL,
    "value" JSONB,
    "is_required" BOOLEAN NOT NULL DEFAULT false,
    "is_filterable" BOOLEAN NOT NULL DEFAULT false,
    "sort_order" INTEGER NOT NULL DEFAULT 0,
    "is_enabled" BOOLEAN NOT NULL DEFAULT true,
    "created_at" BIGINT NOT NULL DEFAULT 0,
    "updated_at" BIGINT NOT NULL DEFAULT 0,
    "deleted_at" BIGINT,

    CONSTRAINT "goods_attribute_items_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "base"."goods_attribute_set_category_associations" (
    "goods_attribute_set_id" BIGINT NOT NULL,
    "goods_category_id" BIGINT NOT NULL,

    CONSTRAINT "goods_attribute_set_category_associations_pkey" PRIMARY KEY ("goods_attribute_set_id","goods_category_id")
);

-- CreateTable
CREATE TABLE "base"."goods_attribute_sets" (
    "id" BIGSERIAL NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "sort_order" INTEGER NOT NULL DEFAULT 0,
    "created_at" BIGINT NOT NULL DEFAULT 0,
    "updated_at" BIGINT NOT NULL DEFAULT 0,
    "deleted_at" BIGINT,
    "created_by" BIGINT,
    "updated_by" BIGINT,

    CONSTRAINT "goods_attribute_sets_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "base"."goods_attribute_values" (
    "id" BIGSERIAL NOT NULL,
    "goods_spu_id" BIGINT NOT NULL,
    "goods_attribute_item_id" BIGINT NOT NULL,
    "value" TEXT,
    "created_at" BIGINT NOT NULL DEFAULT 0,
    "updated_at" BIGINT NOT NULL DEFAULT 0,

    CONSTRAINT "goods_attribute_values_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "base"."goods_brands" (
    "id" BIGSERIAL NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "logo_url" VARCHAR(255),
    "description" TEXT,
    "created_at" BIGINT NOT NULL DEFAULT extract(epoch from now()) * 1000,
    "updated_at" BIGINT NOT NULL DEFAULT extract(epoch from now()) * 1000,
    "deleted_at" BIGINT,

    CONSTRAINT "goods_brands_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "base"."goods_categories" (
    "id" BIGSERIAL NOT NULL,
    "goods_parent_category_id" BIGINT,
    "name" TEXT NOT NULL,
    "image_url" TEXT,
    "description" TEXT,
    "meta_title" TEXT,
    "meta_keywords" TEXT,
    "meta_description" TEXT,
    "sort_order" INTEGER NOT NULL DEFAULT 0,
    "is_enabled" BOOLEAN NOT NULL DEFAULT true,
    "level" INTEGER,
    "created_at" BIGINT NOT NULL DEFAULT extract(epoch from now()) * 1000,
    "updated_at" BIGINT NOT NULL DEFAULT extract(epoch from now()) * 1000,
    "deleted_at" BIGINT,

    CONSTRAINT "goods_categories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "base"."goods_category_associations" (
    "goods_spu_id" BIGINT NOT NULL,
    "goods_category_id" BIGINT NOT NULL,
    "is_primary" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "goods_category_associations_pkey" PRIMARY KEY ("goods_spu_id","goods_category_id")
);

-- CreateTable
CREATE TABLE "base"."goods_freight_region_configs" (
    "id" BIGSERIAL NOT NULL,
    "freight_template_id" BIGINT NOT NULL,
    "region_codes" TEXT,
    "region_names" TEXT NOT NULL,
    "first_item" INTEGER NOT NULL DEFAULT 1,
    "first_fee" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "additional_item" INTEGER NOT NULL DEFAULT 1,
    "additional_fee" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "created_at" BIGINT NOT NULL,
    "updated_at" BIGINT NOT NULL,
    "deleted_at" BIGINT,
    "created_by" BIGINT,
    "updated_by" BIGINT,

    CONSTRAINT "goods_freight_region_configs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "base"."goods_freight_templates" (
    "id" BIGSERIAL NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "charge_type" SMALLINT NOT NULL,
    "is_default" BOOLEAN NOT NULL DEFAULT false,
    "created_at" BIGINT NOT NULL,
    "updated_at" BIGINT NOT NULL,
    "deleted_at" BIGINT,
    "created_by" BIGINT,
    "updated_by" BIGINT,

    CONSTRAINT "goods_freight_templates_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "base"."goods_images" (
    "id" BIGSERIAL NOT NULL,
    "goods_spu_id" BIGINT NOT NULL,
    "goods_sku_id" BIGINT,
    "image_url" TEXT NOT NULL,
    "alt_text" TEXT,
    "sort_order" INTEGER NOT NULL DEFAULT 0,
    "is_default" BOOLEAN NOT NULL DEFAULT false,
    "created_at" BIGINT NOT NULL DEFAULT extract(epoch from now()) * 1000,
    "updated_at" BIGINT NOT NULL DEFAULT extract(epoch from now()) * 1000,

    CONSTRAINT "goods_images_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "base"."goods_service_associations" (
    "goods_spu_id" BIGINT NOT NULL,
    "goods_service_id" BIGINT NOT NULL,

    CONSTRAINT "goods_service_associations_pkey" PRIMARY KEY ("goods_spu_id","goods_service_id")
);

-- CreateTable
CREATE TABLE "base"."goods_services" (
    "id" BIGSERIAL NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "image_url" VARCHAR(255),
    "description" TEXT,
    "created_at" BIGINT NOT NULL DEFAULT extract(epoch from now()) * 1000,
    "updated_at" BIGINT NOT NULL DEFAULT extract(epoch from now()) * 1000,
    "deleted_at" BIGINT,

    CONSTRAINT "goods_services_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "base"."goods_sku_specification_values" (
    "goods_sku_id" BIGINT NOT NULL,
    "goods_specification_value_id" BIGINT NOT NULL,

    CONSTRAINT "goods_sku_specification_values_pkey" PRIMARY KEY ("goods_sku_id","goods_specification_value_id")
);

-- CreateTable
CREATE TABLE "base"."goods_skus" (
    "id" BIGSERIAL NOT NULL,
    "goods_spu_id" BIGINT NOT NULL,
    "sku_code" VARCHAR(100) NOT NULL,
    "barcode" VARCHAR(100),
    "sales_price" DECIMAL(10,2) NOT NULL,
    "market_price" DECIMAL(10,2),
    "cost_price" DECIMAL(10,2),
    "weight" DECIMAL(10,2),
    "volume" DECIMAL(10,2),
    "stock" INTEGER NOT NULL DEFAULT 0,
    "low_stock_threshold" INTEGER,
    "is_enabled" BOOLEAN NOT NULL DEFAULT true,
    "created_at" BIGINT NOT NULL DEFAULT extract(epoch from now()) * 1000,
    "updated_at" BIGINT NOT NULL DEFAULT extract(epoch from now()) * 1000,
    "deleted_at" BIGINT,

    CONSTRAINT "goods_skus_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "base"."goods_specification_names" (
    "id" BIGSERIAL NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "created_at" BIGINT NOT NULL DEFAULT extract(epoch from now()) * 1000,
    "updated_at" BIGINT NOT NULL DEFAULT extract(epoch from now()) * 1000,
    "deleted_at" BIGINT,

    CONSTRAINT "goods_specification_names_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "base"."goods_specification_values" (
    "id" BIGSERIAL NOT NULL,
    "goods_specification_name_id" BIGINT NOT NULL,
    "value" VARCHAR(100) NOT NULL,
    "created_at" BIGINT NOT NULL DEFAULT extract(epoch from now()) * 1000,
    "updated_at" BIGINT NOT NULL DEFAULT extract(epoch from now()) * 1000,
    "deleted_at" BIGINT,

    CONSTRAINT "goods_specification_values_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "base"."goods_spus" (
    "id" BIGSERIAL NOT NULL,
    "spu_code" VARCHAR(100),
    "name" TEXT NOT NULL,
    "subtitle" TEXT,
    "slug" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "goods_brand_id" BIGINT,
    "goods_freight_template_id" BIGINT,
    "meta_title" TEXT,
    "meta_keywords" TEXT,
    "meta_description" TEXT,
    "sort_order" INTEGER NOT NULL DEFAULT 0,
    "status" SMALLINT NOT NULL DEFAULT 0,
    "published_at" BIGINT,
    "is_virtual" BOOLEAN NOT NULL DEFAULT false,
    "is_shipping_required" BOOLEAN NOT NULL DEFAULT true,
    "created_at" BIGINT NOT NULL DEFAULT extract(epoch from now()) * 1000,
    "updated_at" BIGINT NOT NULL DEFAULT extract(epoch from now()) * 1000,
    "deleted_at" BIGINT,
    "total_sales" INTEGER NOT NULL DEFAULT 0,
    "total_stock" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "goods_spus_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "base"."goods_tag_associations" (
    "goods_spu_id" BIGINT NOT NULL,
    "goods_tag_id" BIGINT NOT NULL,

    CONSTRAINT "goods_tag_associations_pkey" PRIMARY KEY ("goods_spu_id","goods_tag_id")
);

-- CreateTable
CREATE TABLE "base"."goods_tags" (
    "id" BIGSERIAL NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "slug" VARCHAR(100) NOT NULL,
    "image_url" TEXT,
    "description" TEXT,
    "tag_type" VARCHAR(50),
    "sort_order" INTEGER NOT NULL DEFAULT 0,
    "is_enabled" BOOLEAN NOT NULL DEFAULT true,
    "created_at" BIGINT NOT NULL,
    "updated_at" BIGINT NOT NULL,
    "deleted_at" BIGINT,
    "goods_spu_count" INTEGER NOT NULL DEFAULT 0,
    "created_by" BIGINT,
    "updated_by" BIGINT,

    CONSTRAINT "goods_tags_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "base"."goods_videos" (
    "id" BIGSERIAL NOT NULL,
    "goods_spu_id" BIGINT NOT NULL,
    "video_url" TEXT NOT NULL,
    "cover_image_url" TEXT,
    "sort_order" INTEGER NOT NULL DEFAULT 0,
    "created_at" BIGINT NOT NULL DEFAULT extract(epoch from now()) * 1000,
    "updated_at" BIGINT NOT NULL DEFAULT extract(epoch from now()) * 1000,

    CONSTRAINT "goods_videos_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "base"."region" (
    "id" INTEGER NOT NULL,
    "citycode" INTEGER DEFAULT 0,
    "cityname" VARCHAR(32) DEFAULT '',
    "cityfullname" VARCHAR(500) DEFAULT '',
    "citycodeprefix" INTEGER DEFAULT 0,
    "level" SMALLINT DEFAULT 0,
    "parent_id" INTEGER DEFAULT 0,
    "out_field" SMALLINT DEFAULT 0,

    CONSTRAINT "region_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "base"."system_config" (
    "id" BIGSERIAL NOT NULL,
    "config_type" VARCHAR(50) NOT NULL,
    "config_key" VARCHAR(100) NOT NULL,
    "config_value" TEXT NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "sort" SMALLINT NOT NULL DEFAULT 0,
    "status" SMALLINT NOT NULL DEFAULT 1,
    "is_system" SMALLINT NOT NULL DEFAULT 0,
    "created_by" BIGINT,
    "updated_by" BIGINT,
    "created_at" BIGINT NOT NULL,
    "updated_at" BIGINT NOT NULL,
    "deleted_at" BIGINT,
    "remark" VARCHAR(255),

    CONSTRAINT "system_config_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "base"."system_dept" (
    "id" BIGSERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "leader" TEXT,
    "phone" TEXT,
    "status" INTEGER DEFAULT 1,
    "sort" INTEGER DEFAULT 0,
    "created_by" BIGINT,
    "updated_by" BIGINT,
    "created_at" BIGINT,
    "updated_at" BIGINT,
    "deleted_at" BIGINT,
    "remark" TEXT,

    CONSTRAINT "system_dept_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "base"."system_menu" (
    "id" BIGSERIAL NOT NULL,
    "parent_id" BIGINT NOT NULL,
    "level" VARCHAR(500) NOT NULL,
    "name" VARCHAR(50) NOT NULL,
    "code" VARCHAR(100) NOT NULL,
    "icon" VARCHAR(50),
    "route" VARCHAR(200),
    "component" VARCHAR(255),
    "redirect" VARCHAR(255),
    "is_hidden" INTEGER NOT NULL DEFAULT 1,
    "type" VARCHAR(1) NOT NULL,
    "status" INTEGER DEFAULT 1,
    "sort" INTEGER DEFAULT 0,
    "created_by" BIGINT,
    "updated_by" BIGINT,
    "created_at" BIGINT NOT NULL,
    "updated_at" BIGINT NOT NULL,
    "deleted_at" BIGINT,
    "remark" VARCHAR(255),

    CONSTRAINT "system_menu_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "base"."system_role" (
    "id" BIGSERIAL NOT NULL,
    "name" VARCHAR(30) NOT NULL,
    "data_scope" SMALLINT NOT NULL DEFAULT 1,
    "status" SMALLINT NOT NULL DEFAULT 1,
    "sort" SMALLINT NOT NULL DEFAULT 0,
    "remark" VARCHAR(255),
    "created_by" BIGINT,
    "updated_by" BIGINT,
    "created_at" BIGINT NOT NULL,
    "updated_at" BIGINT NOT NULL,
    "deleted_at" BIGINT,

    CONSTRAINT "system_role_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "base"."system_user" (
    "id" BIGINT NOT NULL,
    "user_type" INTEGER NOT NULL DEFAULT 60,
    "username" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "nickname" TEXT,
    "phone" VARCHAR(11),
    "email" TEXT,
    "avatar" TEXT,
    "dept_id" BIGINT,
    "role_id" BIGINT,
    "status" INTEGER NOT NULL DEFAULT 1,
    "login_ip" VARCHAR(45),
    "login_time" BIGINT,
    "login_token" TEXT,
    "remark" TEXT,
    "created_by" BIGINT,
    "updated_by" BIGINT,
    "created_at" BIGINT NOT NULL,
    "updated_at" BIGINT NOT NULL,
    "deleted_at" BIGINT,

    CONSTRAINT "system_user_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "idx_goods_associations_source_goods_spu_id" ON "base"."goods_associations"("source_goods_spu_id");

-- CreateIndex
CREATE INDEX "idx_goods_associations_target_goods_spu_id" ON "base"."goods_associations"("target_goods_spu_id");

-- CreateIndex
CREATE INDEX "idx_goods_associations_type" ON "base"."goods_associations"("association_type");

-- CreateIndex
CREATE INDEX "idx_goods_attribute_items_deleted_at" ON "base"."goods_attribute_items"("deleted_at");

-- CreateIndex
CREATE INDEX "idx_goods_attribute_items_filterable" ON "base"."goods_attribute_items"("is_filterable");

-- CreateIndex
CREATE INDEX "idx_goods_attribute_items_is_enabled" ON "base"."goods_attribute_items"("is_enabled");

-- CreateIndex
CREATE INDEX "idx_goods_attribute_items_set_id" ON "base"."goods_attribute_items"("goods_attribute_set_id");

-- CreateIndex
CREATE INDEX "idx_goods_attribute_set_category_associations_category_id" ON "base"."goods_attribute_set_category_associations"("goods_category_id");

-- CreateIndex
CREATE UNIQUE INDEX "goods_attribute_sets_name_key" ON "base"."goods_attribute_sets"("name");

-- CreateIndex
CREATE INDEX "idx_goods_attribute_sets_deleted_at" ON "base"."goods_attribute_sets"("deleted_at");

-- CreateIndex
CREATE INDEX "idx_goods_attribute_values_item_id" ON "base"."goods_attribute_values"("goods_attribute_item_id");

-- CreateIndex
CREATE INDEX "idx_goods_attribute_values_spu_id" ON "base"."goods_attribute_values"("goods_spu_id");

-- CreateIndex
CREATE UNIQUE INDEX "goods_attribute_values_goods_spu_id_goods_attribute_it_key" ON "base"."goods_attribute_values"("goods_spu_id", "goods_attribute_item_id");

-- CreateIndex
CREATE INDEX "idx_goods_brands_deleted_at" ON "base"."goods_brands"("deleted_at");

-- CreateIndex
CREATE INDEX "idx_goods_categories_deleted_at" ON "base"."goods_categories"("deleted_at");

-- CreateIndex
CREATE INDEX "idx_goods_categories_goods_parent_id" ON "base"."goods_categories"("goods_parent_category_id");

-- CreateIndex
CREATE INDEX "idx_goods_categories_is_enabled" ON "base"."goods_categories"("is_enabled");

-- CreateIndex
CREATE INDEX "idx_goods_category_associations_category_id" ON "base"."goods_category_associations"("goods_category_id");

-- CreateIndex
CREATE INDEX "idx_goods_freight_region_configs_template_id" ON "base"."goods_freight_region_configs"("freight_template_id");

-- CreateIndex
CREATE INDEX "idx_goods_freight_region_configs_deleted_at" ON "base"."goods_freight_region_configs"("deleted_at");

-- CreateIndex
CREATE INDEX "idx_goods_freight_region_configs_created_by" ON "base"."goods_freight_region_configs"("created_by");

-- CreateIndex
CREATE INDEX "idx_goods_freight_region_configs_updated_by" ON "base"."goods_freight_region_configs"("updated_by");

-- CreateIndex
CREATE UNIQUE INDEX "goods_freight_templates_name_key" ON "base"."goods_freight_templates"("name");

-- CreateIndex
CREATE INDEX "idx_goods_freight_templates_deleted_at" ON "base"."goods_freight_templates"("deleted_at");

-- CreateIndex
CREATE INDEX "idx_goods_freight_templates_is_default" ON "base"."goods_freight_templates"("is_default");

-- CreateIndex
CREATE INDEX "idx_goods_freight_templates_created_by" ON "base"."goods_freight_templates"("created_by");

-- CreateIndex
CREATE INDEX "idx_goods_freight_templates_updated_by" ON "base"."goods_freight_templates"("updated_by");

-- CreateIndex
CREATE INDEX "idx_goods_images_goods_spu_id" ON "base"."goods_images"("goods_spu_id");

-- CreateIndex
CREATE INDEX "idx_goods_images_goods_sku_id" ON "base"."goods_images"("goods_sku_id");

-- CreateIndex
CREATE INDEX "idx_goods_service_associations_service_id" ON "base"."goods_service_associations"("goods_service_id");

-- CreateIndex
CREATE INDEX "idx_goods_services_deleted_at" ON "base"."goods_services"("deleted_at");

-- CreateIndex
CREATE INDEX "idx_goods_sku_spec_values_sku_id" ON "base"."goods_sku_specification_values"("goods_sku_id");

-- CreateIndex
CREATE INDEX "idx_goods_sku_spec_values_spec_value_id" ON "base"."goods_sku_specification_values"("goods_specification_value_id");

-- CreateIndex
CREATE UNIQUE INDEX "goods_skus_sku_code_key" ON "base"."goods_skus"("sku_code");

-- CreateIndex
CREATE INDEX "idx_goods_skus_spu_id" ON "base"."goods_skus"("goods_spu_id");

-- CreateIndex
CREATE INDEX "idx_goods_skus_deleted_at" ON "base"."goods_skus"("deleted_at");

-- CreateIndex
CREATE UNIQUE INDEX "goods_specification_names_name_key" ON "base"."goods_specification_names"("name");

-- CreateIndex
CREATE INDEX "idx_goods_specification_names_deleted_at" ON "base"."goods_specification_names"("deleted_at");

-- CreateIndex
CREATE INDEX "idx_goods_specification_values_name_id" ON "base"."goods_specification_values"("goods_specification_name_id");

-- CreateIndex
CREATE INDEX "idx_goods_specification_values_deleted_at" ON "base"."goods_specification_values"("deleted_at");

-- CreateIndex
CREATE UNIQUE INDEX "goods_spus_spu_code_key" ON "base"."goods_spus"("spu_code");

-- CreateIndex
CREATE UNIQUE INDEX "goods_spus_slug_key" ON "base"."goods_spus"("slug");

-- CreateIndex
CREATE INDEX "idx_goods_spus_created_at" ON "base"."goods_spus"("created_at");

-- CreateIndex
CREATE INDEX "idx_goods_spus_deleted_at" ON "base"."goods_spus"("deleted_at");

-- CreateIndex
CREATE INDEX "idx_goods_spus_goods_brand_id" ON "base"."goods_spus"("goods_brand_id");

-- CreateIndex
CREATE INDEX "idx_goods_spus_goods_freight_template_id" ON "base"."goods_spus"("goods_freight_template_id");

-- CreateIndex
CREATE INDEX "idx_goods_spus_slug" ON "base"."goods_spus"("slug");

-- CreateIndex
CREATE INDEX "idx_goods_spus_status" ON "base"."goods_spus"("status");

-- CreateIndex
CREATE INDEX "idx_goods_tag_associations_tag_id" ON "base"."goods_tag_associations"("goods_tag_id");

-- CreateIndex
CREATE UNIQUE INDEX "goods_tags_name_key" ON "base"."goods_tags"("name");

-- CreateIndex
CREATE UNIQUE INDEX "goods_tags_slug_key" ON "base"."goods_tags"("slug");

-- CreateIndex
CREATE INDEX "idx_goods_tags_deleted_at" ON "base"."goods_tags"("deleted_at");

-- CreateIndex
CREATE INDEX "idx_goods_tags_is_enabled" ON "base"."goods_tags"("is_enabled");

-- CreateIndex
CREATE INDEX "idx_goods_tags_slug" ON "base"."goods_tags"("slug");

-- CreateIndex
CREATE INDEX "idx_goods_tags_created_by" ON "base"."goods_tags"("created_by");

-- CreateIndex
CREATE INDEX "idx_goods_tags_updated_by" ON "base"."goods_tags"("updated_by");

-- CreateIndex
CREATE INDEX "idx_goods_videos_goods_spu_id" ON "base"."goods_videos"("goods_spu_id");

-- CreateIndex
CREATE INDEX "idx_region_citycode" ON "base"."region"("citycode");

-- CreateIndex
CREATE INDEX "idx_region_level" ON "base"."region"("level");

-- CreateIndex
CREATE INDEX "idx_region_parent_id" ON "base"."region"("parent_id");

-- CreateIndex
CREATE INDEX "system_config_config_type_idx" ON "base"."system_config"("config_type");

-- CreateIndex
CREATE UNIQUE INDEX "system_config_config_type_config_key_key" ON "base"."system_config"("config_type", "config_key");

-- CreateIndex
CREATE UNIQUE INDEX "system_user_username_key" ON "base"."system_user"("username");

-- CreateIndex
CREATE UNIQUE INDEX "system_user_email_key" ON "base"."system_user"("email");

-- AddForeignKey
ALTER TABLE "base"."goods_attribute_items" ADD CONSTRAINT "goods_attribute_items_goods_attribute_set_id_fkey" FOREIGN KEY ("goods_attribute_set_id") REFERENCES "base"."goods_attribute_sets"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "base"."goods_attribute_set_category_associations" ADD CONSTRAINT "goods_attribute_set_category_associations_goods_attribute__fkey" FOREIGN KEY ("goods_attribute_set_id") REFERENCES "base"."goods_attribute_sets"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "base"."goods_attribute_values" ADD CONSTRAINT "goods_attribute_values_goods_attribute_item_id_fkey" FOREIGN KEY ("goods_attribute_item_id") REFERENCES "base"."goods_attribute_items"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "base"."goods_attribute_values" ADD CONSTRAINT "goods_attribute_values_goods_spu_id_fkey" FOREIGN KEY ("goods_spu_id") REFERENCES "base"."goods_spus"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "base"."goods_categories" ADD CONSTRAINT "goods_categories_goods_parent_category_id_fkey" FOREIGN KEY ("goods_parent_category_id") REFERENCES "base"."goods_categories"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "base"."goods_category_associations" ADD CONSTRAINT "goods_category_associations_goods_spu_id_fkey" FOREIGN KEY ("goods_spu_id") REFERENCES "base"."goods_spus"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "base"."goods_freight_region_configs" ADD CONSTRAINT "goods_freight_region_configs_freight_template_id_fkey" FOREIGN KEY ("freight_template_id") REFERENCES "base"."goods_freight_templates"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "base"."goods_images" ADD CONSTRAINT "goods_images_goods_spu_id_fkey" FOREIGN KEY ("goods_spu_id") REFERENCES "base"."goods_spus"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "base"."goods_images" ADD CONSTRAINT "goods_images_goods_sku_id_fkey" FOREIGN KEY ("goods_sku_id") REFERENCES "base"."goods_skus"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "base"."goods_service_associations" ADD CONSTRAINT "goods_service_associations_goods_spu_id_fkey" FOREIGN KEY ("goods_spu_id") REFERENCES "base"."goods_spus"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "base"."goods_service_associations" ADD CONSTRAINT "goods_service_associations_goods_service_id_fkey" FOREIGN KEY ("goods_service_id") REFERENCES "base"."goods_services"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "base"."goods_sku_specification_values" ADD CONSTRAINT "goods_sku_specification_values_goods_sku_id_fkey" FOREIGN KEY ("goods_sku_id") REFERENCES "base"."goods_skus"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "base"."goods_sku_specification_values" ADD CONSTRAINT "goods_sku_specification_values_goods_specification_value_i_fkey" FOREIGN KEY ("goods_specification_value_id") REFERENCES "base"."goods_specification_values"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "base"."goods_skus" ADD CONSTRAINT "goods_skus_goods_spu_id_fkey" FOREIGN KEY ("goods_spu_id") REFERENCES "base"."goods_spus"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "base"."goods_specification_values" ADD CONSTRAINT "goods_specification_values_goods_specification_name_id_fkey" FOREIGN KEY ("goods_specification_name_id") REFERENCES "base"."goods_specification_names"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "base"."goods_spus" ADD CONSTRAINT "fk_goods_spus_brand" FOREIGN KEY ("goods_brand_id") REFERENCES "base"."goods_brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "base"."goods_tag_associations" ADD CONSTRAINT "goods_tag_associations_goods_spu_id_fkey" FOREIGN KEY ("goods_spu_id") REFERENCES "base"."goods_spus"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "base"."goods_tag_associations" ADD CONSTRAINT "goods_tag_associations_goods_tag_id_fkey" FOREIGN KEY ("goods_tag_id") REFERENCES "base"."goods_tags"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "base"."goods_videos" ADD CONSTRAINT "goods_videos_goods_spu_id_fkey" FOREIGN KEY ("goods_spu_id") REFERENCES "base"."goods_spus"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
