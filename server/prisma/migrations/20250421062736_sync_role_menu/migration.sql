-- AlterTable
ALTER TABLE "base"."goods_brands" ALTER COLUMN "created_at" SET DEFAULT extract(epoch from now()) * 1000,
ALTER COLUMN "updated_at" SET DEFAULT extract(epoch from now()) * 1000;

-- AlterTable
ALTER TABLE "base"."goods_categories" ALTER COLUMN "created_at" SET DEFAULT extract(epoch from now()) * 1000,
ALTER COLUMN "updated_at" SET DEFAULT extract(epoch from now()) * 1000;

-- AlterTable
ALTER TABLE "base"."goods_images" ALTER COLUMN "created_at" SET DEFAULT extract(epoch from now()) * 1000,
ALTER COLUMN "updated_at" SET DEFAULT extract(epoch from now()) * 1000;

-- AlterTable
ALTER TABLE "base"."goods_services" ALTER COLUMN "created_at" SET DEFAULT extract(epoch from now()) * 1000,
ALTER COLUMN "updated_at" SET DEFAULT extract(epoch from now()) * 1000;

-- AlterTable
ALTER TABLE "base"."goods_skus" ALTER COLUMN "created_at" SET DEFAULT extract(epoch from now()) * 1000,
ALTER COLUMN "updated_at" SET DEFAULT extract(epoch from now()) * 1000;

-- AlterTable
ALTER TABLE "base"."goods_specification_names" ALTER COLUMN "created_at" SET DEFAULT extract(epoch from now()) * 1000,
ALTER COLUMN "updated_at" SET DEFAULT extract(epoch from now()) * 1000;

-- AlterTable
ALTER TABLE "base"."goods_specification_values" ALTER COLUMN "created_at" SET DEFAULT extract(epoch from now()) * 1000,
ALTER COLUMN "updated_at" SET DEFAULT extract(epoch from now()) * 1000;

-- AlterTable
ALTER TABLE "base"."goods_spus" ALTER COLUMN "created_at" SET DEFAULT extract(epoch from now()) * 1000,
ALTER COLUMN "updated_at" SET DEFAULT extract(epoch from now()) * 1000;

-- AlterTable
ALTER TABLE "base"."goods_videos" ALTER COLUMN "created_at" SET DEFAULT extract(epoch from now()) * 1000,
ALTER COLUMN "updated_at" SET DEFAULT extract(epoch from now()) * 1000;

-- AlterTable
ALTER TABLE "base"."system_menu" ALTER COLUMN "status" SET DEFAULT 0;

-- AlterTable
ALTER TABLE "base"."system_role" ALTER COLUMN "id" DROP DEFAULT;

-- CreateTable
CREATE TABLE "base"."role_menu" (
    "role_id" BIGINT NOT NULL,
    "menu_id" BIGINT NOT NULL,
    "created_at" BIGINT NOT NULL,

    CONSTRAINT "role_menu_pkey" PRIMARY KEY ("role_id","menu_id")
);

-- CreateIndex
CREATE INDEX "role_menu_role_id_idx" ON "base"."role_menu"("role_id");

-- CreateIndex
CREATE INDEX "role_menu_menu_id_idx" ON "base"."role_menu"("menu_id");
