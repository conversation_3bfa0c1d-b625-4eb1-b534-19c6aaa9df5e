// 加载环境配置
require('./config/env')();

const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const swaggerConfig = require('./core/swagger/config');
const errorHandler = require('./core/middleware/error');
const operationLogMiddleware = require('./core/middleware/OperationLogMiddleware');
const bigIntMiddleware = require('./core/middleware/BigIntMiddleware');
const { firewallMiddleware } = require('./core/middleware/FirewallMiddleware');
const ModuleManager = require('./core/module/ModuleManager');
const commonRoutes = require('./core/routes');
const routeManager = require('./core/routes/RouteManager');
const { initAdmin } = require('./scripts/init-admin');
const { prisma } = require('./core/database/prisma');
const morgan = require('morgan');
const esTestRoutes = require('./core/routes/es-test');
const gitlabContributionsRoutes = require('./core/routes/gitlab/contributions');
const { createCronJob } = require('./scripts/gitlab-contribution-sync');
const rabbitMQInit = require('./core/mq/init');

class Application {
  constructor() {
    this.app = this.initExpress();
    this.moduleManager = new ModuleManager();

    // 监听模块的 Swagger 注册事件
    this.moduleManager.on('swagger:register', (event) => {
      console.log('收到 swagger:register 事件:', event);
      if (event && event.module) {
        swaggerConfig.registerModule(event.module, event.paths, event);
      } else {
        console.log('事件数据不完整:', event);
      }
    });
    
    // 监听所有事件
    this.moduleManager.on('*', (eventName, data) => {
      console.log('收到事件:', eventName, data);
    });
  }

  /**
   * 初始化Express应用
   * @returns {Object} Express应用实例
   */
  initExpress() {
    const app = express();
    
    // 启用CORS
    app.use(cors());
    
    // 解析JSON请求体
    app.use(express.json());
    
    // 添加 BigInt 处理中间件
    app.use(bigIntMiddleware);
    
    // 解析URL编码的请求体
    app.use(express.urlencoded({ extended: true }));
    
    // 静态文件服务
    app.use(express.static(path.join(__dirname, 'public')));
    app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
    
    // 日志记录
    app.use(morgan('dev'));

    // 防火墙中间件（在认证中间件之前）
    app.use(firewallMiddleware({
      exclude: [
        '/api/v1/common/health',
        '/api-docs',
        '/uploads'
      ],
      enableWhitelist: true,
      enableBlacklist: true,
      defaultAction: 'allow'
    }));

    // 操作日志中间件
    app.use(operationLogMiddleware({
      exclude: [
        '/api/v1/common/health',
        '/api-docs'
      ],
      logResponse: true,
      logRequest: true,
      sensitiveFields: ['password', 'token', 'secret', 'accessKey', 'secretKey']
    }));
    
    // 全局错误处理中间件
    app.use(errorHandler);
    
    return app;
  }

  /**
   * 启动应用
   */
  async start(port = 4000) {
    try {
      console.log('\n=== 开始扫描模块 ===');
      // 扫描并加载模块
      await this.moduleManager.scanModules();
      console.log('=== 模块扫描完成 ===\n');

      // 初始化超级管理员
      try {
        console.log('\n=== 初始化超级管理员 ===');
        console.log('正在加载 initAdmin 函数...');
        const adminInit = await initAdmin();
        console.log('initAdmin 函数执行结果:', adminInit);
        if (adminInit.success) {
          console.log('超级管理员初始化成功');
          console.log('角色ID:', adminInit.role.id.toString());
          console.log('用户ID:', adminInit.user.id.toString());
        } else {
          console.error('超级管理员初始化失败:', adminInit.error);
        }
        console.log('=== 超级管理员初始化完成 ===\n');
      } catch (adminError) {
        console.error('初始化超级管理员过程中发生错误:', adminError);
      }

      // API版本前缀
      const API_VERSION = '/api/v1';
      
      // 注册公共路由
      this.app.use(`${API_VERSION}/common`, commonRoutes);
      console.log(`已注册公共路由: ${API_VERSION}/common`);
      
      // 注册ES测试路由
      this.app.use(`${API_VERSION}/es-test`, esTestRoutes);
      console.log(`已注册ES测试路由: ${API_VERSION}/es-test`);
      
      // 注册GitLab贡献统计路由
      this.app.use(`${API_VERSION}/gitlab`, gitlabContributionsRoutes);
      console.log(`已注册GitLab贡献统计路由: ${API_VERSION}/gitlab`);
      
      // 加载模块
      await this.moduleManager.scanModules();
      
      // 注册模块路由
      this.moduleManager.registerRoutes(this.app);
      
      // 注册 Swagger UI
      const { serve, setup } = swaggerConfig.getSwaggerUI();
      this.app.use('/api-docs', serve, setup);

      // 启动GitLab贡献统计定时任务
      console.log('\n=== 启动GitLab贡献统计定时任务 ===');
      try {
        createCronJob();
        console.log('GitLab贡献统计定时任务启动成功');
      } catch (cronError) {
        console.error('GitLab贡献统计定时任务启动失败:', cronError);
      }
      console.log('=== GitLab贡献统计定时任务启动完成 ===\n');
      
      // 初始化 RabbitMQ 并启动消费者
      console.log('\n=== 初始化 RabbitMQ ===');
      try {
        await rabbitMQInit.initialize();
        console.log('RabbitMQ 初始化成功');
      } catch (mqError) {
        console.error('RabbitMQ 初始化失败:', mqError);
      }
      console.log('=== RabbitMQ 初始化完成 ===\n');

      // 初始化快递订阅定时任务
      console.log('\n=== 初始化快递订阅定时任务 ===');
      try {
        const { getScheduleManager } = require('./apps/master/system/integration/services/express/ExpressScheduleManager');
        const scheduleManager = getScheduleManager(prisma);
        await scheduleManager.initialize();
        console.log('快递订阅定时任务初始化成功');
      } catch (expressError) {
        console.error('快递订阅定时任务初始化失败:', expressError);
      }
      console.log('=== 快递订阅定时任务初始化完成 ===\n');

      // 初始化物化视图相关服务
      console.log('\n=== 初始化物化视图服务 ===');
      try {
        // 启动物化视图定时任务
        const MaterializedViewScheduleService = require('./core/services/MaterializedViewScheduleService');
        MaterializedViewScheduleService.start();
        console.log('物化视图定时任务启动成功');

        // 启动数据库通知监听服务
        const DatabaseNotificationService = require('./core/services/DatabaseNotificationService');
        await DatabaseNotificationService.initialize();
        console.log('数据库通知监听服务启动成功');

      } catch (mvError) {
        console.error('物化视图服务初始化失败:', mvError);
      }
      console.log('=== 物化视图服务初始化完成 ===\n');

      // 启动服务器
      this.app.listen(port, () => {
        console.log('\n=== 服务器启动成功 ===');
        console.log(`服务器运行在 http://localhost:${port}`);
        console.log(`API 文档地址: http://localhost:${port}/api-docs`);
        console.log('===================\n');
      });
    } catch (err) {
      console.error('应用启动失败:', err);
      process.exit(1);
    }
  }
}

const application = new Application();

// 处理进程退出事件，优雅关闭所有服务
const gracefulShutdown = async () => {
  console.log('\n正在关闭服务...');

  try {
    // 关闭 RabbitMQ 连接
    console.log('关闭 RabbitMQ 连接...');
    await rabbitMQInit.shutdown();

    // 关闭数据库通知服务
    console.log('关闭数据库通知服务...');
    const DatabaseNotificationService = require('./core/services/DatabaseNotificationService');
    await DatabaseNotificationService.stop();

    // 停止物化视图定时任务
    console.log('停止物化视图定时任务...');
    const MaterializedViewScheduleService = require('./core/services/MaterializedViewScheduleService');
    MaterializedViewScheduleService.stop();

    console.log('所有服务已关闭');
  } catch (error) {
    console.error('关闭服务时出错:', error);
  }

  console.log('应用程序正在退出...');
  process.exit(0);
};

process.on('SIGINT', gracefulShutdown);
process.on('SIGTERM', gracefulShutdown);

module.exports = application;