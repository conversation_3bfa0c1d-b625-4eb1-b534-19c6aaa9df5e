/**
 * 安全地添加客户端字段到操作日志表
 * 此脚本使用原生SQL语句直接修改数据库结构，不会清空数据
 */
const { PrismaClient } = require('@prisma/client');

async function addClientColumnToOperationLog() {
  const prisma = new PrismaClient();
  
  try {
    console.log('开始添加客户端字段到操作日志表...');
    
    // 使用原生SQL添加列
    const addColumnSql = `
      ALTER TABLE base.system_operation_log 
      ADD COLUMN IF NOT EXISTS client VARCHAR(50) NULL
    `;
    
    // 添加列注释
    const addCommentSql = `
      COMMENT ON COLUMN base.system_operation_log.client IS '客户端类型，如中控端、商户端等'
    `;
    
    // 执行添加列SQL
    await prisma.$executeRawUnsafe(addColumnSql);
    console.log('客户端字段添加成功');
    
    // 执行添加注释的SQL
    await prisma.$executeRawUnsafe(addCommentSql);
    
    console.log('客户端字段添加成功！');
    return { success: true, message: '客户端字段添加成功' };
  } catch (error) {
    console.error('添加客户端字段失败:', error);
    return { success: false, error: error.message };
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本，则执行迁移
if (require.main === module) {
  addClientColumnToOperationLog()
    .then(result => {
      if (result.success) {
        console.log('迁移成功:', result.message);
        process.exit(0);
      } else {
        console.error('迁移失败:', result.error);
        process.exit(1);
      }
    })
    .catch(err => {
      console.error('迁移过程中发生错误:', err);
      process.exit(1);
    });
}

module.exports = { addClientColumnToOperationLog };
