/**
 * 用户登录测试脚本
 * 此脚本用于测试登录API
 */
const axios = require('axios');

// 登录信息
const loginData = {
  username: 'testadmin4',
  password: '123456',
  captcha: '1234',
  captchaId: '12345678',
  encrypted: false  // 明确指定不使用加密
};

// 登录请求
async function testLogin() {
  try {
    console.log('尝试登录...');
    const response = await axios.post(
      'http://localhost:4000/api/v1/master/auth/login',
      loginData,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('登录成功!');
    console.log(JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('登录失败:', error.response ? error.response.data : error.message);
  }
}

// 执行测试
testLogin();
