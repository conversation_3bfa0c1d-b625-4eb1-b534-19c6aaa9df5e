const ElasticsearchService = require('../core/services/ElasticsearchService');
const config = require('../config');

/**
 * 初始化商品索引
 */
async function initProductIndex() {
  const productIndex = config.elasticsearch.elasticsearch.indices.product.index;
  
  // 商品索引设置
  const settings = {
    analysis: {
      analyzer: {
        text_analyzer: {
          type: 'custom',
          tokenizer: 'standard',
          filter: ['lowercase', 'trim']
        }
      }
    },
    number_of_shards: 3,
    number_of_replicas: 1
  };

  // 商品索引映射
  const mappings = {
    properties: {
      id: { type: 'keyword' },
      name: { 
        type: 'text',
        analyzer: 'text_analyzer',
        fields: {
          keyword: { type: 'keyword' }
        }
      },
      description: { type: 'text', analyzer: 'text_analyzer' },
      price: { type: 'double' },
      originalPrice: { type: 'double' },
      stock: { type: 'integer' },
      sales: { type: 'integer' },
      category: { 
        type: 'text', 
        analyzer: 'text_analyzer',
        fields: {
          keyword: { type: 'keyword' }
        }
      },
      categoryPath: { 
        type: 'text', 
        analyzer: 'text_analyzer',
        fields: {
          keyword: { type: 'keyword' }
        }
      },
      brand: { 
        type: 'text', 
        analyzer: 'text_analyzer',
        fields: {
          keyword: { type: 'keyword' }
        }
      },
      tags: { 
        type: 'text', 
        analyzer: 'text_analyzer',
        fields: {
          keyword: { type: 'keyword' }
        }
      },
      attributes: {
        type: 'nested',
        properties: {
          name: { 
            type: 'text', 
            analyzer: 'text_analyzer',
            fields: {
              keyword: { type: 'keyword' }
            }
          },
          value: { 
            type: 'text', 
            analyzer: 'text_analyzer',
            fields: {
              keyword: { type: 'keyword' }
            }
          }
        }
      },
      images: { 
        type: 'text', 
        analyzer: 'text_analyzer',
        fields: {
          keyword: { type: 'keyword' }
        }
      },
      mainImage: { 
        type: 'text', 
        analyzer: 'text_analyzer',
        fields: {
          keyword: { type: 'keyword' }
        }
      },
      status: { type: 'keyword' },
      createdAt: { type: 'date' },
      updatedAt: { type: 'date' },
      // 添加全文搜索字段，将所有可搜索内容合并
      all_text: { 
        type: 'text', 
        analyzer: 'text_analyzer' 
      }
    }
  };

  try {
    const result = await ElasticsearchService.createIndex(productIndex, settings, mappings);
    if (result) {
      console.log(`商品索引 ${productIndex} 初始化成功`);
    } else {
      console.error(`商品索引 ${productIndex} 初始化失败`);
    }
  } catch (error) {
    console.error('初始化商品索引时出错:', error);
  }
}

/**
 * 初始化所有索引
 */
async function initAllIndices() {
  try {
    await initProductIndex();
    console.log('所有索引初始化完成');
    process.exit(0);
  } catch (error) {
    console.error('初始化索引时出错:', error);
    process.exit(1);
  }
}

// 执行初始化
initAllIndices();
