/**
 * 生成系统用户模块的Swagger API文档JSON文件
 * 此脚本用于导出API文档以便导入APIfox
 */
const fs = require('fs');
const path = require('path');
const swaggerJsdoc = require('swagger-jsdoc');

// 定义Swagger选项
const options = {
  swaggerDefinition: {
    openapi: '3.0.0',
    info: {
      title: '聚灵云平台用户模块 API 文档',
      version: '1.0.0',
      description: '系统用户管理、认证相关API接口文档'
    },
    servers: [
      {
        url: 'http://localhost:4000/api/v1',
        description: '开发服务器 (v1)'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
          description: "JWT认证，在请求头中添加 'Authorization: Bearer {token}'"
        }
      },
      schemas: {
        Error: {
          type: 'object',
          properties: {
            code: {
              type: 'integer',
              example: 400
            },
            message: {
              type: 'string',
              example: '错误信息'
            },
            data: {
              type: 'object',
              example: null
            }
          }
        },
        Success: {
          type: 'object',
          properties: {
            code: {
              type: 'integer',
              example: 200
            },
            message: {
              type: 'string',
              example: '操作成功'
            },
            data: {
              type: 'object',
              example: null
            }
          }
        },
        // 用户相关Schema可以根据需要扩展
      }
    }
  },
  // 指定包含API路由文件的路径
  apis: [
    // 系统用户模块路由
    path.resolve(__dirname, '../apps/master/system/user/routes/*.js')
  ]
};

// 生成Swagger规范
const specs = swaggerJsdoc(options);

// 输出目录
const outputDir = path.resolve(__dirname, '../api-docs');

// 确保输出目录存在
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// 输出文件路径
const outputPath = path.join(outputDir, 'user-api-docs.json');

// 写入JSON文件
fs.writeFileSync(outputPath, JSON.stringify(specs, null, 2), 'utf8');

console.log(`API文档已生成: ${outputPath}`);
console.log('您可以将此JSON文件导入到APIfox中使用。');
