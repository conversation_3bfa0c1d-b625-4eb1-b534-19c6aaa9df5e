/**
 * 初始化超级管理员用户和角色
 * 在系统启动时自动创建超级管理员账号和角色
 */
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const { generateSnowflakeId } = require('../shared/utils/snowflake');

// 超级管理员配置
const ADMIN_CONFIG = {
  // 超级管理员角色
  role: {
    name: '超级管理员',
    data_scope: 1, // 全部数据权限
    status: 0,     // 正常状态
    sort: 0,       // 排序
    remark: '系统内置超级管理员角色，拥有所有权限，请勿删除'
  },
  // 超级管理员用户
  user: {
    username: 'admin',
    password: 'admin123',  // 默认密码，建议首次登录后修改
    nickname: '系统管理员',
    user_type: 100,        // 主账户
    status: 0,             // 正常状态
    remark: '系统内置超级管理员账号，请勿删除'
  }
};

/**
 * 初始化超级管理员
 */
async function initAdmin() {
  console.log('开始初始化超级管理员...');
  const prisma = new PrismaClient();
  
  try {
    // 检查是否已存在超级管理员角色
    let adminRole = await prisma.baseSystemRole.findFirst({
      where: {
        name: ADMIN_CONFIG.role.name,
        deleted_at: null
      }
    });
    
    // 如果不存在，创建超级管理员角色
    if (!adminRole) {
      console.log('创建超级管理员角色...');
      const now = Date.now();
      const roleId = generateSnowflakeId();
      
      adminRole = await prisma.baseSystemRole.create({
        data: {
          id: roleId,
          name: ADMIN_CONFIG.role.name,
          data_scope: ADMIN_CONFIG.role.data_scope,
          status: ADMIN_CONFIG.role.status,
          sort: ADMIN_CONFIG.role.sort,
          remark: ADMIN_CONFIG.role.remark,
          created_at: BigInt(now),
          updated_at: BigInt(now)
        }
      });
      
      console.log(`超级管理员角色创建成功，ID: ${adminRole.id}`);
    } else {
      console.log(`超级管理员角色已存在，ID: ${adminRole.id}`);
    }
    
    // 检查是否已存在超级管理员用户
    let adminUser = await prisma.baseSystemUser.findFirst({
      where: {
        username: ADMIN_CONFIG.user.username,
        deleted_at: null
      }
    });
    
    // 如果不存在，创建超级管理员用户
    if (!adminUser) {
      console.log('创建超级管理员用户...');
      const now = Date.now();
      const userId = generateSnowflakeId();
      
      // 密码加密
      const salt = bcrypt.genSaltSync(10);
      const hashedPassword = bcrypt.hashSync(ADMIN_CONFIG.user.password, salt);
      
      adminUser = await prisma.baseSystemUser.create({
        data: {
          id: userId,
          username: ADMIN_CONFIG.user.username,
          password: hashedPassword,
          nickname: ADMIN_CONFIG.user.nickname,
          user_type: ADMIN_CONFIG.user.user_type,
          status: ADMIN_CONFIG.user.status,
          role_id: adminRole.id,
          remark: ADMIN_CONFIG.user.remark,
          created_at: BigInt(now),
          updated_at: BigInt(now)
        }
      });
      
      console.log(`超级管理员用户创建成功，ID: ${adminUser.id}`);
    } else {
      console.log(`超级管理员用户已存在，ID: ${adminUser.id}`);
      
      // 确保超级管理员关联了超级管理员角色
      if (adminUser.role_id !== adminRole.id) {
        console.log('更新超级管理员用户的角色...');
        await prisma.baseSystemUser.update({
          where: { id: adminUser.id },
          data: {
            role_id: adminRole.id,
            updated_at: BigInt(Date.now())
          }
        });
        console.log('超级管理员用户角色更新成功');
      }
    }
    
    console.log('超级管理员初始化完成');
    return { success: true, role: adminRole, user: adminUser };
  } catch (error) {
    console.error('初始化超级管理员失败:', error);
    return { success: false, error };
  } finally {
    await prisma.$disconnect();
  }
}

module.exports = { initAdmin };

// 如果直接运行此脚本，则执行初始化
if (require.main === module) {
  initAdmin()
    .then(() => process.exit(0))
    .catch(error => {
      console.error('初始化脚本执行失败:', error);
      process.exit(1);
    });
}
