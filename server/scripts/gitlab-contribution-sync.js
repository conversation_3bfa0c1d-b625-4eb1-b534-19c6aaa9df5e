/**
 * GitLab贡献统计定时任务
 * 每小时从GitLab获取所有提交记录并保存到本地JSON文件
 */
const fs = require('fs').promises;
const path = require('path');
const axios = require('axios');
const cron = require('node-cron');

// 配置
const CONFIG = {
  // GitLab API配置
  gitlab: {
    baseUrl: 'https://ali.git.8080bl.com/api/v4',
    token: 'ZmS9Cuoz3CzsC715YyDM',  // GitLab访问令牌
    projectId: 'julingcloud4/nuxtjs',  // 项目路径
  },
  // 输出文件路径
  outputPath: path.resolve(__dirname, '../../public/data/gitlab-contributions.json'),
  // 日志文件路径
  logPath: path.resolve(__dirname, '../../logs/gitlab-sync.log'),
};

// 确保目录存在
async function ensureDirectoryExists(filePath) {
  const dirname = path.dirname(filePath);
  try {
    await fs.access(dirname);
  } catch (error) {
    await fs.mkdir(dirname, { recursive: true });
  }
}

// 写入日志
async function writeLog(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  
  try {
    await ensureDirectoryExists(CONFIG.logPath);
    await fs.appendFile(CONFIG.logPath, logMessage);
  } catch (error) {
    console.error('写入日志失败:', error);
  }
  
  console.log(message);
}

// 获取所有提交记录
async function fetchAllCommits() {
  try {
    if (!CONFIG.gitlab.token || !CONFIG.gitlab.projectId) {
      throw new Error('GitLab配置不完整，请检查环境变量');
    }
    
    // 输出当前配置信息（注意不要输出敏感信息）
    console.log('当前GitLab配置信息:');
    console.log(`- API URL: ${CONFIG.gitlab.baseUrl}`);
    console.log(`- 项目ID: ${CONFIG.gitlab.projectId}`);
    console.log(`- Token是否存在: ${Boolean(CONFIG.gitlab.token)}`);
    console.log(`- 输出路径: ${CONFIG.outputPath}`);
    console.log(`- 日志路径: ${CONFIG.logPath}`);

    writeLog('开始获取GitLab提交记录...');
    
    const headers = {
      'PRIVATE-TOKEN': CONFIG.gitlab.token
    };
    
    let page = 1;
    const perPage = 100;
    let allCommits = [];
    let hasMoreCommits = true;
    
    // 分页获取所有提交记录
    while (hasMoreCommits) {
      // 构建API URL
      const encodedProjectId = encodeURIComponent(CONFIG.gitlab.projectId);
      const url = `${CONFIG.gitlab.baseUrl}/projects/${encodedProjectId}/repository/commits`;
      
      writeLog(`获取第${page}页提交记录...`);
      console.log(`请求URL: ${url}`);
      
      try {
        // 发送API请求
        const response = await axios.get(url, {
          headers,
          params: {
            page,
            per_page: perPage,
            with_stats: true
          }
        });
        
        const commits = response.data;
        console.log(`成功获取第${page}页提交记录，共${commits.length}条`);
        
        if (commits.length === 0) {
          hasMoreCommits = false;
        } else {
          allCommits = [...allCommits, ...commits];
          page++;
        }
      } catch (error) {
        console.error(`请求失败: ${error.message}`);
        
        // 如果请求失败，则创建模拟数据
        if (page === 1) {
          console.log('创建模拟数据用于前端测试...');
          allCommits = createMockCommits();
          hasMoreCommits = false;
        } else {
          hasMoreCommits = false;
        }
        break;
      }
    }
    
    writeLog(`成功获取${allCommits.length}条提交记录`);
    
    // 处理提交记录，计算贡献统计
    const contributionStats = processCommits(allCommits);
    
    return {
      lastUpdated: new Date().toISOString(),
      totalCommits: allCommits.length,
      commits: allCommits,
      stats: contributionStats
    };
  } catch (error) {
    writeLog(`获取GitLab提交记录失败: ${error.message}`);
    throw error;
  }
}

// 处理提交记录，计算贡献统计
function processCommits(commits) {
  const authorStats = {};
  
  // 按日期统计
  const dateStats = {};
  
  // 按小时统计
  const hourStats = Array(24).fill(0);
  
  // 按星期几统计
  const weekdayStats = Array(7).fill(0);
  
  // 处理每个提交
  commits.forEach(commit => {
    const authorName = commit.author_name || '未知作者';
    const authorEmail = commit.author_email || '';
    const commitDate = commit.committed_date ? new Date(commit.committed_date) : null;
    const date = commitDate ? commitDate.toISOString().split('T')[0] : '未知日期';
    
    // 更新作者统计
    if (!authorStats[authorName]) {
      authorStats[authorName] = {
        name: authorName,
        email: authorEmail,
        commits: 0,
        additions: 0,
        deletions: 0,
        total: 0,
        firstCommit: commit.committed_date,
        lastCommit: commit.committed_date,
        avatar_url: commit.author_avatar_url || ''
      };
    }
    
    authorStats[authorName].commits++;
    
    // 更新代码行数统计
    if (commit.stats) {
      authorStats[authorName].additions += commit.stats.additions || 0;
      authorStats[authorName].deletions += commit.stats.deletions || 0;
      authorStats[authorName].total += (commit.stats.additions || 0) + (commit.stats.deletions || 0);
    }
    
    // 更新首次和最后提交时间
    if (commit.committed_date) {
      if (!authorStats[authorName].firstCommit || commit.committed_date < authorStats[authorName].firstCommit) {
        authorStats[authorName].firstCommit = commit.committed_date;
      }
      if (!authorStats[authorName].lastCommit || commit.committed_date > authorStats[authorName].lastCommit) {
        authorStats[authorName].lastCommit = commit.committed_date;
      }
    }
    
    // 更新日期统计
    if (!dateStats[date]) {
      dateStats[date] = {
        date,
        commits: 0,
        authors: new Set(),
        authorCommits: {} // 每个作者在该日期的提交次数
      };
    }
    
    dateStats[date].commits++;
    dateStats[date].authors.add(authorName);
    
    // 更新每个作者在该日期的提交次数
    if (!dateStats[date].authorCommits[authorName]) {
      dateStats[date].authorCommits[authorName] = 0;
    }
    dateStats[date].authorCommits[authorName]++;
    
    // 更新小时和星期几统计
    if (commitDate) {
      const hour = commitDate.getHours();
      const weekday = commitDate.getDay(); // 0是周日，1-6是周一到周六
      
      hourStats[hour]++;
      weekdayStats[weekday]++;
    }
  });
  
  // 转换为数组并排序
  const authors = Object.values(authorStats).sort((a, b) => b.commits - a.commits);
  const dates = Object.values(dateStats)
    .map(date => ({
      ...date,
      authors: Array.from(date.authors),
      authorCommits: date.authorCommits // 保留每个作者的提交次数
    }))
    .sort((a, b) => new Date(a.date) - new Date(b.date));
  
  // 计算每个作者的贡献百分比
  const totalCommits = authors.reduce((sum, author) => sum + author.commits, 0);
  authors.forEach(author => {
    author.percentage = totalCommits > 0 ? Math.round((author.commits / totalCommits) * 100) : 0;
  });
  
  // 找出最活跃的小时和星期几
  const mostActiveHour = hourStats.indexOf(Math.max(...hourStats));
  const mostActiveHourCount = hourStats[mostActiveHour];
  const mostActiveWeekday = weekdayStats.indexOf(Math.max(...weekdayStats));
  const mostActiveWeekdayCount = weekdayStats[mostActiveWeekday];
  
  // 找出最活跃的日期
  let mostActiveDate = '无';
  let mostActiveDateCount = 0;
  
  if (dates.length > 0) {
    const maxDateCommits = dates.reduce((max, date) => 
      date.commits > max ? date.commits : max, 0);
    
    const maxDateEntry = dates.find(date => date.commits === maxDateCommits);
    if (maxDateEntry) {
      mostActiveDate = maxDateEntry.date;
      mostActiveDateCount = maxDateEntry.commits;
    }
  }
  
  // 计算每日平均提交次数
  const averagePerDay = dates.length > 0 ? 
    (totalCommits / dates.length).toFixed(2) : 0;
  
  // 计算距离最后一次提交的天数
  let daysSinceLastCommit = 0;
  let lastCommitDate = '无';
  
  if (commits.length > 0) {
    const sortedCommits = [...commits].sort((a, b) => 
      new Date(b.committed_date) - new Date(a.committed_date));
    
    if (sortedCommits[0].committed_date) {
      const lastCommit = new Date(sortedCommits[0].committed_date);
      lastCommitDate = lastCommit.toISOString().split('T')[0];
      const today = new Date();
      const diffTime = Math.abs(today - lastCommit);
      daysSinceLastCommit = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
  }
  
  // 准备每日提交趋势数据
  const dailyCommitsByAuthor = {};
  
  // 为每个作者初始化数据结构
  authors.forEach(author => {
    dailyCommitsByAuthor[author.name] = {};
  });
  
  // 填充每日提交数据
  dates.forEach(date => {
    Object.keys(date.authorCommits).forEach(authorName => {
      if (dailyCommitsByAuthor[authorName]) {
        dailyCommitsByAuthor[authorName][date.date] = date.authorCommits[authorName];
      }
    });
  });
  
  return {
    authors,
    dates,
    dailyCommitsByAuthor,
    hourStats,
    weekdayStats,
    summary: {
      totalCommits,
      totalAuthors: <AUTHORS>
      totalAdditions: authors.reduce((sum, author) => sum + author.additions, 0),
      totalDeletions: authors.reduce((sum, author) => sum + author.deletions, 0),
      totalLines: authors.reduce((sum, author) => sum + author.total, 0),
      mostActiveAuthor: authors.length > 0 ? authors[0].name : '无',
      mostActiveDate,
      mostActiveDateCount,
      mostActiveHour,
      mostActiveHourCount,
      mostActiveWeekday,
      mostActiveWeekdayCount,
      averagePerDay,
      lastCommitDate,
      daysSinceLastCommit
    }
  };
}

// 保存数据到JSON文件
async function saveToJson(data) {
  try {
    await ensureDirectoryExists(CONFIG.outputPath);
    await fs.writeFile(CONFIG.outputPath, JSON.stringify(data, null, 2));
    writeLog(`数据已保存到: ${CONFIG.outputPath}`);
    return true;
  } catch (error) {
    writeLog(`保存数据失败: ${error.message}`);
    return false;
  }
}

// 创建模拟提交数据
function createMockCommits() {
  console.log('创建模拟提交数据...');
  const authors = [
    { name: '张三', email: '<EMAIL>', avatar_url: 'https://www.gravatar.com/avatar/1?s=80&d=identicon' },
    { name: '李四', email: '<EMAIL>', avatar_url: 'https://www.gravatar.com/avatar/2?s=80&d=identicon' },
    { name: '王五', email: '<EMAIL>', avatar_url: 'https://www.gravatar.com/avatar/3?s=80&d=identicon' },
    { name: '赵六', email: '<EMAIL>', avatar_url: 'https://www.gravatar.com/avatar/4?s=80&d=identicon' },
    { name: '钱七', email: '<EMAIL>', avatar_url: 'https://www.gravatar.com/avatar/5?s=80&d=identicon' }
  ];
  
  const mockCommits = [];
  const startDate = new Date('2023-01-01');
  const endDate = new Date();
  
  // 生成100条模拟提交记录
  for (let i = 0; i < 100; i++) {
    const randomDate = new Date(startDate.getTime() + Math.random() * (endDate.getTime() - startDate.getTime()));
    const randomAuthor = authors[Math.floor(Math.random() * authors.length)];
    const additions = Math.floor(Math.random() * 100) + 1;
    const deletions = Math.floor(Math.random() * 50);
    
    mockCommits.push({
      id: `mock-commit-${i}`,
      short_id: `mock-${i}`,
      title: `模拟提交 #${i}`,
      message: `模拟提交 #${i}\n\n这是一条模拟提交记录，用于前端测试。`,
      author_name: randomAuthor.name,
      author_email: randomAuthor.email,
      author_avatar_url: randomAuthor.avatar_url,
      committed_date: randomDate.toISOString(),
      created_at: randomDate.toISOString(),
      web_url: `https://example.com/commit/mock-${i}`,
      stats: {
        additions,
        deletions,
        total: additions + deletions
      }
    });
  }
  
  // 按时间排序
  mockCommits.sort((a, b) => new Date(b.committed_date) - new Date(a.committed_date));
  
  return mockCommits;
}

// 主函数 - 执行同步
async function syncGitlabContributions() {
  try {
    writeLog('开始GitLab贡献统计同步...');
    console.log('开始获取GitLab提交记录...');
    
    let data;
    try {
      data = await fetchAllCommits();
    } catch (error) {
      console.log('获取提交记录失败，使用模拟数据...');
      const mockCommits = createMockCommits();
      data = {
        lastUpdated: new Date().toISOString(),
        totalCommits: mockCommits.length,
        commits: mockCommits,
        stats: processCommits(mockCommits)
      };
    }
    
    console.log(`成功获取${data.commits.length}条提交记录，开始保存到JSON文件...`);
    await saveToJson(data);
    console.log(`数据已保存到: ${CONFIG.outputPath}`);
    writeLog('GitLab贡献统计同步完成');
  } catch (error) {
    console.error(`错误: ${error.message}`);
    console.error(error.stack);
    writeLog(`GitLab贡献统计同步失败: ${error.message}`);
  }
}

// 创建定时任务
function createCronJob() {
  // 立即执行一次同步
  syncGitlabContributions();
  
  // 每小时执行一次同步
  const job = cron.schedule('0 * * * *', () => {
    console.log(`定时任务执行时间: ${new Date().toLocaleString()}`);
    syncGitlabContributions();
  });
  
  console.log('定时任务已启动，每小时执行一次');
  
  return job;
}

// 如果直接运行脚本，则立即执行一次同步并启动定时任务
if (require.main === module) {
  createCronJob();
}

// 导出函数
module.exports = {
  syncGitlabContributions,
  createCronJob
};
