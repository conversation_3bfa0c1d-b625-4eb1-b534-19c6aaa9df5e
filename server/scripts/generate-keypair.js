/**
 * RSA密钥对生成脚本
 * 生成2048位RSA密钥对并输出
 */

const crypto = require('crypto');

// 生成RSA密钥对
function generateRSAKeyPair() {
  console.log('正在生成新的RSA密钥对...');
  
  // 生成新密钥对
  const { privateKey, publicKey } = crypto.generateKeyPairSync('rsa', {
    modulusLength: 2048,
    publicKeyEncoding: {
      type: 'spki',
      format: 'pem'
    },
    privateKeyEncoding: {
      type: 'pkcs8',
      format: 'pem'
    }
  });
  
  console.log('RSA密钥对生成成功！');
  console.log('\n--- 公钥 ---\n');
  console.log(publicKey);
  console.log('\n--- 私钥 ---\n');
  console.log(privateKey);
  
  return { privateKey, publicKey };
}

// 执行生成
generateRSAKeyPair();
