/**
 * 生成API文档JSON文件
 * 用于导入到APIfox等API管理工具
 */
const fs = require('fs');
const path = require('path');
const swaggerConfig = require('../core/swagger/config');

// 生成API文档规范
const specs = swaggerConfig.generateSpecs();

// 添加系统配置批量操作的路径
if (!specs.paths['/api/v1/master/system/config/batch']) {
  specs.paths['/api/v1/master/system/config/batch'] = {
    post: {
      tags: ['系统管理/配置中心/系统配置'],
      summary: '批量创建系统配置',
      description: '批量创建多个系统配置',
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              required: ['configs'],
              properties: {
                configs: {
                  type: 'array',
                  items: {
                    type: 'object',
                    required: ['configType', 'configKey', 'configValue', 'name'],
                    properties: {
                      configType: { type: 'string', description: '配置类型' },
                      configKey: { type: 'string', description: '配置键' },
                      configValue: { type: 'string', description: '配置值' },
                      name: { type: 'string', description: '配置名称' },
                      sort: { type: 'integer', description: '排序' },
                      isSystem: { type: 'integer', description: '是否系统内置(0-否，1-是)' },
                      remark: { type: 'string', description: '备注' }
                    }
                  }
                }
              }
            }
          }
        }
      },
      responses: {
        200: {
          description: '批量创建成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean', example: true },
                  message: { type: 'string', example: '批量创建成功' },
                  data: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        success: { type: 'boolean' },
                        config_type: { type: 'string' },
                        config_key: { type: 'string' },
                        data: { $ref: '#/components/schemas/SystemConfig' },
                        error: { type: 'string' }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        401: { $ref: '#/components/responses/Unauthorized' },
        500: {
          description: '服务器错误或业务逻辑错误',
          content: {
            'application/json': {
              schema: {
                oneOf: [
                  { $ref: '#/components/responses/Error' },
                  { $ref: '#/components/responses/BadRequest' }
                ]
              }
            }
          }
        }
      },
      security: [{ bearerAuth: [] }]
    },
    put: {
      tags: ['系统管理/配置中心/系统配置'],
      summary: '批量更新系统配置',
      description: '批量更新多个系统配置',
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              required: ['configs'],
              properties: {
                configs: {
                  type: 'array',
                  items: {
                    type: 'object',
                    required: ['id'],
                    properties: {
                      id: { type: 'string', description: '配置ID' },
                      configValue: { type: 'string', description: '配置值' },
                      name: { type: 'string', description: '配置名称' },
                      sort: { type: 'integer', description: '排序' },
                      isSystem: { type: 'integer', description: '是否系统内置(0-否，1-是)' },
                      remark: { type: 'string', description: '备注' }
                    }
                  }
                }
              }
            }
          }
        }
      },
      responses: {
        200: {
          description: '批量更新成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean', example: true },
                  message: { type: 'string', example: '批量更新成功' },
                  data: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        success: { type: 'boolean' },
                        id: { type: 'string' },
                        data: { $ref: '#/components/schemas/SystemConfig' },
                        error: { type: 'string' }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        401: { $ref: '#/components/responses/Unauthorized' },
        500: {
          description: '服务器错误或业务逻辑错误',
          content: {
            'application/json': {
              schema: {
                oneOf: [
                  { $ref: '#/components/responses/Error' },
                  { $ref: '#/components/responses/BadRequest' }
                ]
              }
            }
          }
        }
      },
      security: [{ bearerAuth: [] }]
    }
  };
}

// 移除单个创建和更新的路径
if (specs.paths['/api/v1/master/system/config'] && specs.paths['/api/v1/master/system/config'].post) {
  delete specs.paths['/api/v1/master/system/config'].post;
}

if (specs.paths['/api/v1/master/system/config/{id}'] && specs.paths['/api/v1/master/system/config/{id}'].put) {
  delete specs.paths['/api/v1/master/system/config/{id}'].put;
}

// 确保输出目录存在
const outputDir = path.join(__dirname, '../docs/api');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// 写入JSON文件
const outputPath = path.join(outputDir, 'api-docs.json');
fs.writeFileSync(outputPath, JSON.stringify(specs, null, 2), 'utf8');

console.log(`API文档已生成: ${outputPath}`);
