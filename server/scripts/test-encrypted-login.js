/**
 * 加密密码登录测试脚本
 * 此脚本演示如何获取公钥，加密密码，然后登录
 */
const axios = require('axios');
const crypto = require('crypto');

// 服务器地址
const SERVER_URL = 'http://localhost:4000';

// 获取公钥
async function getPublicKey() {
  try {
    console.log('获取公钥...');
    const response = await axios.get(`${SERVER_URL}/api/v1/common/public-key`);
    console.log('获取公钥成功');
    console.log('公钥响应数据:', JSON.stringify(response.data));
    
    // 根据返回格式处理，获取实际的公钥字符串
    const publicKey = response.data.data.publicKey || response.data.data;
    console.log('提取的公钥类型:', typeof publicKey);
    
    return publicKey;
  } catch (error) {
    console.error('获取公钥失败:', error.message);
    throw error;
  }
}

// 使用公钥加密密码
function encryptPassword(publicKey, password) {
  try {
    console.log('加密密码...');
    const buffer = Buffer.from(password, 'utf8');
    const encrypted = crypto.publicEncrypt(
      {
        key: publicKey,
        padding: crypto.constants.RSA_PKCS1_OAEP_PADDING
      },
      buffer
    );
    
    return encrypted.toString('base64');
  } catch (error) {
    console.error('加密密码失败:', error.message);
    throw error;
  }
}

// 登录测试
async function testEncryptedLogin() {
  try {
    // 1. 获取公钥
    const publicKey = await getPublicKey();
    
    // 2. 加密密码
    const password = '123456';
    const encryptedPassword = encryptPassword(publicKey, password);
    console.log('加密后的密码:', encryptedPassword);
    
    // 3. 发送登录请求
    console.log('发送登录请求...');
    const loginData = {
      username: 'testadmin4',
      password: encryptedPassword,
      captcha: '1234',
      captchaId: '12345678',
      encrypted: true  // 明确指定使用加密
    };
    
    const response = await axios.post(
      `${SERVER_URL}/api/v1/master/auth/login`,
      loginData,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('登录成功!');
    console.log(JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('测试失败:', error.response ? error.response.data : error.message);
  }
}

// 执行测试
testEncryptedLogin();
