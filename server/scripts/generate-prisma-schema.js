#!/usr/bin/env node

/**
 * Prisma模型自动收集和生成工具
 * 用于在构建和开发过程中自动收集和合并分散的Prisma模型定义
 */
const { spawn } = require('child_process');
const path = require('path');
const ModelCollector = require('../core/prisma/ModelCollector');

// 显示标题
console.log('\n===== Prisma模型自动收集工具 =====');

// 运行模型收集器
console.log('开始收集模型定义...');
const mergedSchemaPath = ModelCollector.run();

// 基于合并后的schema生成Prisma客户端
console.log('\n开始生成Prisma客户端...');
const prismaGenerate = spawn('npx', ['prisma', 'generate', '--schema', mergedSchemaPath], {
  stdio: 'inherit',
  shell: true
});

prismaGenerate.on('close', code => {
  if (code === 0) {
    console.log('\n✅ Prisma客户端生成成功!');
    console.log('\n提示: 如果您添加了新的模型或修改了现有模型，请确保运行数据库迁移命令:');
    console.log('npx prisma migrate dev --name <迁移名称> --schema ' + mergedSchemaPath);
  } else {
    console.error('\n❌ Prisma客户端生成失败，退出代码:', code);
  }
  
  console.log('\n===== 自动收集完成 =====\n');
});

prismaGenerate.on('error', (err) => {
  console.error('\n❌ 启动Prisma生成命令失败:', err);
  process.exit(1);
});
