-- 创建服务商目标设置相关表

-- 创建目标设置表
CREATE TABLE IF NOT EXISTS provider.goal_setting (
    id BIGINT PRIMARY KEY,
    target_type VARCHAR(20) NOT NULL, -- 目标类型：member-成员目标, department-部门目标, company-公司目标
    target_id BIGINT NOT NULL, -- 目标对象ID（用户ID、部门ID或公司ID）
    target_name VARCHAR(100) NOT NULL, -- 目标对象名称
    fiscal_year INTEGER NOT NULL, -- 财年
    goal_type VARCHAR(20) NOT NULL, -- 目标类型：成交金额、订单数量
    unit VARCHAR(10) NOT NULL, -- 单位：万、个
    
    -- 年度目标
    yearly_target DECIMAL(15,2) DEFAULT 0,
    
    -- 季度目标
    q1_target DECIMAL(15,2) DEFAULT 0,
    q2_target DECIMAL(15,2) DEFAULT 0,
    q3_target DECIMAL(15,2) DEFAULT 0,
    q4_target DECIMAL(15,2) DEFAULT 0,
    
    -- 月度目标
    m1_target DECIMAL(15,2) DEFAULT 0,
    m2_target DECIMAL(15,2) DEFAULT 0,
    m3_target DECIMAL(15,2) DEFAULT 0,
    m4_target DECIMAL(15,2) DEFAULT 0,
    m5_target DECIMAL(15,2) DEFAULT 0,
    m6_target DECIMAL(15,2) DEFAULT 0,
    m7_target DECIMAL(15,2) DEFAULT 0,
    m8_target DECIMAL(15,2) DEFAULT 0,
    m9_target DECIMAL(15,2) DEFAULT 0,
    m10_target DECIMAL(15,2) DEFAULT 0,
    m11_target DECIMAL(15,2) DEFAULT 0,
    m12_target DECIMAL(15,2) DEFAULT 0,
    
    -- 状态信息
    status INTEGER DEFAULT 1, -- 状态：1-正常，0-禁用
    
    -- 审计字段
    created_by BIGINT,
    updated_by BIGINT,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    deleted_at BIGINT,
    
    -- 唯一约束：同一对象在同一财年同一目标类型只能有一条记录
    CONSTRAINT unique_goal_setting UNIQUE (target_type, target_id, fiscal_year, goal_type)
);

-- 创建目标完成情况表（用于记录实际完成情况）
CREATE TABLE IF NOT EXISTS provider.goal_achievement (
    id BIGINT PRIMARY KEY,
    goal_setting_id BIGINT NOT NULL,
    achievement_period VARCHAR(20) NOT NULL, -- 完成周期：yearly, q1, q2, q3, q4, m1-m12
    target_value DECIMAL(15,2) DEFAULT 0, -- 目标值
    actual_value DECIMAL(15,2) DEFAULT 0, -- 实际完成值
    completion_rate DECIMAL(5,2) DEFAULT 0, -- 完成率（百分比）
    
    -- 审计字段
    created_by BIGINT,
    updated_by BIGINT,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    deleted_at BIGINT,
    
    CONSTRAINT fk_goal_achievement_setting FOREIGN KEY (goal_setting_id) REFERENCES provider.goal_setting(id),
    CONSTRAINT unique_goal_achievement UNIQUE (goal_setting_id, achievement_period)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_goal_setting_target_type ON provider.goal_setting(target_type);
CREATE INDEX IF NOT EXISTS idx_goal_setting_target_id ON provider.goal_setting(target_id);
CREATE INDEX IF NOT EXISTS idx_goal_setting_fiscal_year ON provider.goal_setting(fiscal_year);
CREATE INDEX IF NOT EXISTS idx_goal_setting_goal_type ON provider.goal_setting(goal_type);
CREATE INDEX IF NOT EXISTS idx_goal_setting_status ON provider.goal_setting(status);
CREATE INDEX IF NOT EXISTS idx_goal_setting_deleted_at ON provider.goal_setting(deleted_at);
CREATE INDEX IF NOT EXISTS idx_goal_setting_created_by ON provider.goal_setting(created_by);

CREATE INDEX IF NOT EXISTS idx_goal_achievement_goal_setting_id ON provider.goal_achievement(goal_setting_id);
CREATE INDEX IF NOT EXISTS idx_goal_achievement_period ON provider.goal_achievement(achievement_period);
CREATE INDEX IF NOT EXISTS idx_goal_achievement_deleted_at ON provider.goal_achievement(deleted_at);

-- 添加表注释
COMMENT ON TABLE provider.goal_setting IS '服务商目标设置表，存储成员、部门、公司的业绩目标';
COMMENT ON COLUMN provider.goal_setting.id IS '目标设置ID，16位雪花算法，系统自动生成';
COMMENT ON COLUMN provider.goal_setting.target_type IS '目标类型：member-成员目标, department-部门目标, company-公司目标';
COMMENT ON COLUMN provider.goal_setting.target_id IS '目标对象ID（用户ID、部门ID或公司ID）';
COMMENT ON COLUMN provider.goal_setting.target_name IS '目标对象名称';
COMMENT ON COLUMN provider.goal_setting.fiscal_year IS '财年';
COMMENT ON COLUMN provider.goal_setting.goal_type IS '目标类型：成交金额、订单数量';
COMMENT ON COLUMN provider.goal_setting.unit IS '单位：万、个';
COMMENT ON COLUMN provider.goal_setting.yearly_target IS '年度目标';
COMMENT ON COLUMN provider.goal_setting.q1_target IS '第一季度目标';
COMMENT ON COLUMN provider.goal_setting.q2_target IS '第二季度目标';
COMMENT ON COLUMN provider.goal_setting.q3_target IS '第三季度目标';
COMMENT ON COLUMN provider.goal_setting.q4_target IS '第四季度目标';
COMMENT ON COLUMN provider.goal_setting.m1_target IS '1月目标';
COMMENT ON COLUMN provider.goal_setting.m2_target IS '2月目标';
COMMENT ON COLUMN provider.goal_setting.m3_target IS '3月目标';
COMMENT ON COLUMN provider.goal_setting.m4_target IS '4月目标';
COMMENT ON COLUMN provider.goal_setting.m5_target IS '5月目标';
COMMENT ON COLUMN provider.goal_setting.m6_target IS '6月目标';
COMMENT ON COLUMN provider.goal_setting.m7_target IS '7月目标';
COMMENT ON COLUMN provider.goal_setting.m8_target IS '8月目标';
COMMENT ON COLUMN provider.goal_setting.m9_target IS '9月目标';
COMMENT ON COLUMN provider.goal_setting.m10_target IS '10月目标';
COMMENT ON COLUMN provider.goal_setting.m11_target IS '11月目标';
COMMENT ON COLUMN provider.goal_setting.m12_target IS '12月目标';
COMMENT ON COLUMN provider.goal_setting.status IS '状态：1-正常，0-禁用';
COMMENT ON COLUMN provider.goal_setting.created_by IS '创建者ID，16位';
COMMENT ON COLUMN provider.goal_setting.updated_by IS '更新者ID，16位';
COMMENT ON COLUMN provider.goal_setting.created_at IS '创建时间戳（毫秒）';
COMMENT ON COLUMN provider.goal_setting.updated_at IS '更新时间戳（毫秒）';
COMMENT ON COLUMN provider.goal_setting.deleted_at IS '删除时间戳（毫秒）（软删除）';

COMMENT ON TABLE provider.goal_achievement IS '目标完成情况表，记录实际完成情况';
COMMENT ON COLUMN provider.goal_achievement.id IS '完成记录ID，16位雪花算法，系统自动生成';
COMMENT ON COLUMN provider.goal_achievement.goal_setting_id IS '关联的目标设置ID';
COMMENT ON COLUMN provider.goal_achievement.achievement_period IS '完成周期：yearly, q1, q2, q3, q4, m1-m12';
COMMENT ON COLUMN provider.goal_achievement.target_value IS '目标值';
COMMENT ON COLUMN provider.goal_achievement.actual_value IS '实际完成值';
COMMENT ON COLUMN provider.goal_achievement.completion_rate IS '完成率（百分比）';
COMMENT ON COLUMN provider.goal_achievement.created_by IS '创建者ID，16位';
COMMENT ON COLUMN provider.goal_achievement.updated_by IS '更新者ID，16位';
COMMENT ON COLUMN provider.goal_achievement.created_at IS '创建时间戳（毫秒）';
COMMENT ON COLUMN provider.goal_achievement.updated_at IS '更新时间戳（毫秒）';
COMMENT ON COLUMN provider.goal_achievement.deleted_at IS '删除时间戳（毫秒）（软删除）';
