{"openapi": "3.0.0", "info": {"title": "聚灵云平台用户模块 API 文档", "version": "1.0.0", "description": "系统用户管理、认证相关API接口文档"}, "servers": [{"url": "http://localhost:4000/api/v1", "description": "开发服务器 (v1)"}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT认证，在请求头中添加 'Authorization: Bearer {token}'"}}, "schemas": {"Error": {"type": "object", "properties": {"code": {"type": "integer", "example": 400}, "message": {"type": "string", "example": "错误信息"}, "data": {"type": "object", "example": null}}}, "Success": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "操作成功"}, "data": {"type": "object", "example": null}}}}}, "paths": {"/api/v1/master/auth/login": {"post": {"tags": ["系统管理/用户中心/认证"], "summary": "用户登录", "description": "用户登录并获取访问令牌，支持加密密码和验证码", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["username", "password", "<PERSON><PERSON>a", "captchaId"], "properties": {"username": {"type": "string", "description": "用户名"}, "password": {"type": "string", "description": "密码"}, "captcha": {"type": "string", "description": "验证码"}, "captchaId": {"type": "string", "description": "验证码ID，生成验证码时返回"}}}}}}, "responses": {"200": {"description": "登录成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "登录成功"}, "data": {"type": "object", "properties": {"token": {"type": "string", "description": "访问令牌"}, "user": {"type": "object", "properties": {"id": {"type": "integer", "description": "用户ID"}, "username": {"type": "string", "description": "用户名"}, "nickname": {"type": "string", "description": "昵称"}}}}}}}}}}, "400": {"description": "用户名或密码错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/master/auth/logout": {"post": {"tags": ["系统管理/用户中心/认证"], "summary": "用户注销", "description": "用户注销并使访问令牌失效", "security": [{"bearerAuth": []}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "properties": {"token": {"type": "string", "description": "登录令牌（可选，也可以通过Authorization头部提供）"}}}}}}, "responses": {"200": {"description": "注销成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "注销成功"}, "data": {"type": "object", "example": null}}}}}}, "400": {"description": "无效的令牌", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/master/system/dept/tree": {"get": {"tags": ["系统管理/用户中心/部门管理"], "summary": "获取部门树结构", "description": "返回层级形式的部门树结构", "responses": {"200": {"description": "返回部门树结构", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DeptTree"}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/Error"}}}}, "/api/master/system/dept": {"get": {"tags": ["系统管理/用户中心/部门管理"], "summary": "获取部门列表", "description": "分页获取部门列表", "parameters": [{"$ref": "#/components/parameters/PageParam"}, {"$ref": "#/components/parameters/PageSizeParam"}, {"in": "query", "name": "name", "schema": {"type": "string"}, "description": "部门名称（可选查询条件）"}], "responses": {"200": {"description": "返回部门列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"total": {"type": "integer", "example": 10}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Department"}}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/Error"}}}, "post": {"tags": ["系统管理/用户中心/部门管理"], "summary": "创建部门", "description": "创建新的部门", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DepartmentCreate"}}}}, "responses": {"201": {"description": "部门创建成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/Department"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/Error"}}}}, "/api/master/system/dept/{id}": {"get": {"tags": ["系统管理/用户中心/部门管理"], "summary": "获取部门详情", "description": "根据ID获取部门详细信息", "parameters": [{"$ref": "#/components/parameters/IdParam"}], "responses": {"200": {"description": "返回部门详情", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/Department"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/Error"}}}, "put": {"tags": ["系统管理/用户中心/部门管理"], "summary": "更新部门", "description": "根据ID更新部门信息", "parameters": [{"$ref": "#/components/parameters/IdParam"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DepartmentUpdate"}}}}, "responses": {"200": {"description": "部门更新成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/Department"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/Error"}}}, "delete": {"tags": ["系统管理/用户中心/部门管理"], "summary": "删除部门", "description": "根据ID删除部门", "parameters": [{"$ref": "#/components/parameters/IdParam"}], "responses": {"200": {"description": "部门删除成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "删除成功"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/Error"}}}}, "/api/master/system/menu": {"post": {"tags": ["系统管理/用户中心/菜单管理"], "summary": "创建菜单", "description": "创建新的系统菜单", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MenuCreate"}}}}, "responses": {"201": {"description": "菜单创建成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/Menu"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/Error"}}}, "get": {"tags": ["系统管理/用户中心/菜单管理"], "summary": "获取菜单列表", "description": "获取菜单列表，支持分页和树形结构", "parameters": [{"$ref": "#/components/parameters/PageParam"}, {"$ref": "#/components/parameters/PageSizeParam"}, {"in": "query", "name": "tree", "schema": {"type": "boolean"}, "description": "是否返回树形结构"}, {"in": "query", "name": "name", "schema": {"type": "string"}, "description": "菜单名称（可选查询条件）"}], "responses": {"200": {"description": "返回菜单列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"oneOf": [{"type": "object", "properties": {"total": {"type": "integer", "example": 10}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/Menu"}}}}, {"type": "array", "items": {"$ref": "#/components/schemas/MenuTree"}}]}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/Error"}}}}, "/api/master/system/menu/{id}": {"put": {"tags": ["系统管理/用户中心/菜单管理"], "summary": "更新菜单", "description": "根据ID更新菜单信息", "parameters": [{"$ref": "#/components/parameters/IdParam"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MenuUpdate"}}}}, "responses": {"200": {"description": "菜单更新成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/Menu"}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/Error"}}}, "delete": {"tags": ["系统管理/用户中心/菜单管理"], "summary": "删除菜单", "description": "根据ID删除菜单", "parameters": [{"$ref": "#/components/parameters/IdParam"}], "responses": {"200": {"description": "菜单删除成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "删除成功"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/Error"}}}, "get": {"tags": ["系统管理/用户中心/菜单管理"], "summary": "获取菜单详情", "description": "根据ID获取菜单详细信息", "parameters": [{"$ref": "#/components/parameters/IdParam"}], "responses": {"200": {"description": "返回菜单详情", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/Menu"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/Error"}}}}, "/api/master/user-management/users": {"get": {"tags": ["系统管理/用户中心/用户管理"], "summary": "获取用户列表", "security": [{"bearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/PageParam"}, {"$ref": "#/components/parameters/PageSizeParam"}], "responses": {"200": {"$ref": "#/components/responses/UserListResponse"}, "400": {"$ref": "#/components/responses/Error"}, "401": {"description": "未授权，请先登录"}}}, "post": {"tags": ["系统管理/用户中心/用户管理"], "summary": "创建用户", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreateRequest"}}}}, "responses": {"201": {"$ref": "#/components/responses/UserResponse"}, "400": {"$ref": "#/components/responses/Error"}, "401": {"description": "未授权，请先登录"}}}}, "/api/master/user-management/users/{id}": {"put": {"tags": ["系统管理/用户中心/用户管理"], "summary": "更新用户", "security": [{"bearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/IdParam"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdateRequest"}}}}, "responses": {"200": {"$ref": "#/components/responses/UserResponse"}, "400": {"$ref": "#/components/responses/Error"}, "401": {"description": "未授权，请先登录"}, "404": {"$ref": "#/components/responses/Error"}}}, "delete": {"tags": ["系统管理/用户中心/用户管理"], "summary": "删除用户", "security": [{"bearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/IdParam"}], "responses": {"200": {"$ref": "#/components/responses/Success"}, "400": {"$ref": "#/components/responses/Error"}, "401": {"description": "未授权，请先登录"}, "404": {"$ref": "#/components/responses/Error"}}}, "get": {"tags": ["系统管理/用户中心/用户管理"], "summary": "获取用户详情", "security": [{"bearerAuth": []}], "parameters": [{"$ref": "#/components/parameters/IdParam"}], "responses": {"200": {"$ref": "#/components/responses/UserResponse"}, "400": {"$ref": "#/components/responses/Error"}, "401": {"description": "未授权，请先登录"}, "404": {"$ref": "#/components/responses/Error"}}}}}, "tags": []}