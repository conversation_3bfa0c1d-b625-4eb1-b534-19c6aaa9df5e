# 后端 Dockerfile - 使用多阶段构建

# 构建阶段
FROM node:20 AS builder

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm install

# 复制其余源代码
COPY . .

# 生成 Prisma 客户端
RUN npm run prisma:merge && \
    npm run prisma:generate

# 创建必要的目录
RUN mkdir -p uploads public

# 运行阶段
FROM node:20

# 设置工作目录
WORKDIR /app

# 从构建阶段复制文件
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/prisma ./prisma
COPY . .

# 创建必要的目录
RUN mkdir -p uploads public

# 设置环境变量
ENV PORT=4000
ENV NODE_ENV=production

# 暴露端口
EXPOSE 4000

# 启动应用
CMD ["npm", "run", "start"]
