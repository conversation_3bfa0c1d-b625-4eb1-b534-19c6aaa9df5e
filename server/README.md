# 聚灵云服务端

## 项目介绍
聚灵云服务端是一个基于 Node.js 和 Express 框架开发的模块化后端系统，使用 Prisma 作为 ORM 框架，采用依赖注入模式设计。

## 技术栈
- Node.js
- Express
- Prisma
- Jest
- Swagger

## 目录结构
```
server/
├── apps/                      # 业务模块目录
│   ├── master/               # 管理后台模块
│   │   ├── controllers/      # 控制器
│   │   ├── services/        # 业务服务
│   │   ├── models/          # 数据模型
│   │   ├── routes/          # 路由
│   │   ├── swagger/         # API文档
│   │   ├── prisma/          # 数据库模型
│   │   └── __tests__/       # 测试文件
│   ├── merchant/            # 商户模块
│   └── supplier/            # 供应商模块
├── core/                     # 核心功能
├── shared/                  # 共享资源
├── docs/                    # 项目文档
└── config/                 # 配置文件
```

## 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产环境
```bash
npm run build
```

### 启动生产服务器
```bash
npm start
```

## 测试指南

### 运行单元测试
```bash
# 运行所有单元测试
npm run test:unit

# 运行特定模块的测试
npm run test:unit -- apps/master

# 运行带覆盖率报告的测试
npm run test:unit -- --coverage
```

### 测试目录结构
```
__tests__/
├── unit/                    # 单元测试
│   ├── controllers/        # 控制器测试
│   ├── services/          # 服务层测试
│   └── models/           # 模型测试
└── integration/           # 集成测试
```

### 测试规范
1. **命名规范**
   - 测试文件：`*.test.js`
   - 测试用例：应该 + 预期结果

2. **目录组织**
   - 单元测试与源代码目录结构对应
   - 每个测试文件对应一个源代码文件

3. **测试覆盖率要求**
   - 控制器层：100%
   - 服务层：90%
   - 模型层：85%

4. **Mock 规范**
   ```javascript
   // Mock Prisma Client
   jest.mock('@prisma/client', () => ({
     PrismaClient: jest.fn()
   }));

   // Mock Service
   jest.mock('../services/UserService');
   ```

5. **依赖注入测试**
   ```javascript
   // 控制器测试
   const controller = new UserController(mockPrisma);
   
   // 服务层测试
   const service = new UserService(mockPrisma);
   ```

## API 文档
访问 `/api-docs` 查看 Swagger API 文档。

## 项目文档
- [架构设计](/docs/architecture.md)
- [单元测试标准](/docs/unit-test.md)
- [API 规范](/docs/api-standard.md)

## 贡献指南
1. Fork 本仓库
2. 创建特性分支
3. 提交代码
4. 创建 Pull Request

## 许可证
MIT
