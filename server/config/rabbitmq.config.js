/**
 * RabbitMQ 配置文件
 */
module.exports = {
  // 是否启用 RabbitMQ，默认为 true
  enable: process.env.ENABLE_MQ !== 'false',
  
  // RabbitMQ 连接配置
  connection: {
    protocol: process.env.RABBITMQ_PROTOCOL || 'amqp',
    hostname: process.env.RABBITMQ_HOST || 'localhost',
    port: parseInt(process.env.RABBITMQ_PORT || '5672'),
    username: process.env.RABBITMQ_USERNAME || 'guest',
    password: process.env.RABBITMQ_PASSWORD || 'guest',
    vhost: process.env.RABBITMQ_VHOST || '/',
    heartbeat: 60
  },
  
  // 交换机配置
  exchanges: {
    default: {
      name: 'julingcloud.default',
      type: 'direct',
      options: { durable: true }
    },
    order: {
      name: 'julingcloud.order',
      type: 'direct',
      options: { durable: true }
    },
    order_exchange: {
      name: 'order_exchange',
      type: 'direct',
      options: { durable: true }
    },
    order_dlx: {
      name: 'order_dlx',
      type: 'direct',
      options: { durable: true }
    }
  },
  
  // 队列配置
  queues: {
    order: {
      name: 'julingcloud.order.queue',
      options: { durable: true },
      bindingKey: 'order'
    },
    order_dead_letter_queue: {
      name: 'order_dead_letter_queue',
      options: { durable: true },
      bindingKey: 'order',
      exchange: 'order_dlx'
    },
    order_queue: {
      name: 'order_queue',
      options: { 
        durable: true,
        arguments: {
          'x-message-ttl': ********,  // 24小时的消息过期时间
          'x-dead-letter-exchange': 'order_dlx',  // 死信交换机
          'x-dead-letter-routing-key': 'order'  // 死信路由键
        }
      },
      bindingKey: 'order',
      exchange: 'order_exchange'
    },
    notification: {
      name: 'julingcloud.notification.queue',
      options: { durable: true }
    },
    email: {
      name: 'julingcloud.email.queue',
      options: { durable: true },
      bindingKey: 'email'
    }
  }
};
