module.exports = {
  elasticsearch: {
    node: process.env.ES_NODE || 'http://localhost:9200',
    auth: {
      username: process.env.ES_USERNAME || '',
      password: process.env.ES_PASSWORD || '',
    },
    maxRetries: parseInt(process.env.ES_MAX_RETRIES || '3'),
    requestTimeout: parseInt(process.env.ES_REQUEST_TIMEOUT || '30000'),
    // 商品索引配置
    indices: {
      product: {
        index: process.env.ES_PRODUCT_INDEX || 'products',
        type: '_doc'
      }
    }
  }
};
