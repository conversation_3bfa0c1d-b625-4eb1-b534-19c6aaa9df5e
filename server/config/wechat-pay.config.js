/**
 * 微信支付配置
 *
 * 现在从数据库读取配置，支持动态配置管理
 * 配置通过 /api/v1/master/payment-config 接口进行管理
 *
 * 环境变量说明（作为后备配置）：
 * WECHAT_MCH_ID - 微信支付商户号（在微信商户平台获取）
 * WECHAT_API_V3_KEY - 微信支付APIv3密钥（在微信商户平台设置）
 * WECHAT_APP_ID - 微信应用ID（小程序/公众号的AppID）
 * WECHAT_SERIAL_NO - 商户证书序列号（商户证书的序列号，不是公钥证书序列号）
 *
 * 证书文件说明：
 * 证书内容现在存储在数据库中，以Base64编码格式保存
 */
const PaymentConfigService = require('../apps/master/services/PaymentConfigService');

// 从数据库获取微信支付配置
async function getWechatPayConfig() {
  try {
    const config = await PaymentConfigService.getDefaultConfig('wechat');

    if (!config || config.enabled !== 1) {
      console.warn('微信支付配置未启用或不存在，使用环境变量配置');
      return getFallbackConfig();
    }

    // 解析配置数据
    const configData = config.config_data || {};

    // 解析证书文件（Base64解码）
    const privateKey = config.private_key ? Buffer.from(config.private_key, 'base64') : null;
    const publicKey = config.public_key ? Buffer.from(config.public_key, 'base64') : null;
    const wechatPayCert = config.wechat_pay_cert ? Buffer.from(config.wechat_pay_cert, 'base64') : null;

    return {
      // 微信支付商户号
      mchId: configData.mch_id,

      // 微信支付APIv3密钥
      apiV3Key: configData.api_v3_key,

      // 微信支付应用ID (小程序/公众号)
      appId: process.env.WECHAT_APP_ID || 'wx67623f0db557d371',

      // 商户证书序列号
      serialNo: configData.wechat_pay_public_key_id || process.env.WECHAT_SERIAL_NO,

      // 证书内容（Buffer格式）
      certs: {
        privateKey,
        publicKey,
        wechatPayCert,
        certificate: null // 暂不支持p12证书
      },

      // 支付回调地址
      notifyUrl: configData.native_callback_url || config.notify_url,

      // 支付环境
      environment: config.environment || 'sandbox',

      // 支付超时时间 (分钟)
      timeExpire: 30,

      // 测试金额范围 (分)
      testAmount: {
        min: 1,  // 0.01元
        max: 5   // 0.05元
      }
    };
  } catch (error) {
    console.error('获取微信支付配置失败:', error);
    return getFallbackConfig();
  }
}

// 后备配置（使用环境变量）
function getFallbackConfig() {
  const path = require('path');

  return {
    // 微信支付商户号
    mchId: process.env.WECHAT_MCH_ID || '1683015509',

    // 微信支付APIv3密钥
    apiV3Key: process.env.WECHAT_API_V3_KEY || 'RYbMQyx7XL4cduBP8fGto9LatfrQ0rcQ',

    // 微信支付应用ID (小程序/公众号)
    appId: process.env.WECHAT_APP_ID || 'wx67623f0db557d371',

    // 商户证书序列号
    serialNo: process.env.WECHAT_SERIAL_NO || '69B7D227C46553B9F452BCFF7BCC87CF6A18BB6C',

    // 证书文件路径（后备方案）
    certs: {
      privateKey: path.join(__dirname, '../public/certs/payment/wechat/apiclient_key.pem'),
      certificate: path.join(__dirname, '../public/certs/payment/wechat/apiclient_cert.p12'),
      wechatPayCert: path.join(__dirname, '../public/certs/payment/wechat/wechat_pay_cert.pem'),
      publicKey: path.join(__dirname, '../public/certs/payment/wechat/pub_key.pem')
    },

    // 支付回调地址
    notifyUrl: process.env.WECHAT_NOTIFY_URL || 'https://v4api.ioa.8080bl.com/api/v1/master/wechat-pay/notify',

    // 支付环境
    environment: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox',

    // 支付超时时间 (分钟)
    timeExpire: 30,

    // 测试金额范围 (分)
    testAmount: {
      min: 1,  // 0.01元
      max: 5   // 0.05元
    }
  };
}

// 导出异步配置获取函数
module.exports = {
  getWechatPayConfig,
  getFallbackConfig
};
