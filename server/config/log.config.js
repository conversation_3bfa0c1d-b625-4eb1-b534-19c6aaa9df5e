/**
 * 日志相关配置
 * 包括白名单、客户端映射等
 */

module.exports = {
  // 日志白名单配置（不记录日志的路径）
  whitelist: [
    // 系统健康检查
    '/api/v1/common/health',
    
    // 静态资源
    '/uploads/',
    '/public/',
    
    // 认证相关路径（不记录日志）
    '/api/v1/master/auth/login',   // 登录接口
    '/api/v1/master/auth/phone-login', // 手机号登录接口
    '/api/v1/master/auth/captcha',  // 验证码接口
    '/api/v1/master/auth/sms-verification-code', // 短信验证码接口
    
    // API文档路径
    '/api-docs',
    '/api-docs/*'
  ],
  
  // 客户端映射配置
  clientMap: {
    // 主要客户端
    'master': '中控端',
    'merchant': '商户端',
    'mall': '商城端',
    'supplier': '供应商端',
    'mobile': '移动端',
    
    // 其他客户端
    'admin': '管理端',
    'web': '网页端',
    'app': '应用端',
    'wechat': '微信端',
    'miniprogram': '小程序端',
    'h5': 'H5端',
    'api': 'API接口',
    
    // 默认值
    'default': '未知客户端'
  },
  
  // 模块映射配置
  moduleMap: {
    // 系统管理模块
    'system': {
      'user': '用户管理',
      'role': '角色管理',
      'menu': '菜单管理',
      'dept': '部门管理',
      'configure': '配置管理',
      'dict': '数据字典',
      'log': '日志管理',
      'integration': {
        'default': '第三方集成',
        'upload': '文件上传',
        'wechat': '微信集成',
        'aliyun': '阿里云集成',
        'sms': '短信服务'
      }
    },
    
    // 业务模块
    'merchant': '商户管理',
    'mall': '商城管理',
    'goods': '商品管理',
    'order': '订单管理',
    'supplier': '供应商管理',
    
    // 其他模块
    'common': '公共服务',
    'auth': '认证服务',
    'message': '消息服务',
    'dashboard': '仪表盘',
    'profile': '个人中心',
    'notification': '消息通知',
    
    // 默认值
    'default': '未知模块'
  }
};
