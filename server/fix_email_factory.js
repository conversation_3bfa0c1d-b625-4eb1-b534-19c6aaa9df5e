const fs = require('fs');
const path = require('path');

// 文件路径
const filePath = path.join(__dirname, 'apps/master/system/integration/email/services/EmailServiceFactory.js');

// 读取文件内容
let content = fs.readFileSync(filePath, 'utf8');

// 修复语法错误 - 删除第245行的多余右花括号
content = content.replace(/id: config\.id\.toString\(\),\n.*config_key: config\.config_key,\n.*config_value_length:.*\n.*is_default: config\.is_default\n.*\}\);(\n.*\})/, 'id: config.id.toString(),\n        config_key: config.config_key,\n        config_value_length: config.config_value ? config.config_value.length : 0,\n        is_default: config.is_default\n      });');

// 修复 setDefaultService 方法中对 status 字段的引用
content = content.replace(/if \(config\.status !== 1\) \{\n.*throw new Error\(`邮件服务 \${type} 未启用`\);\n.*\}/g, '// 所有配置都认为是启用的\n      console.log(`设置 ${type} 为默认邮件服务`);');

// 修复 updateServiceConfig 方法中对 status 字段的引用
content = content.replace(/status: 1,/g, '');

// 写入修复后的内容
fs.writeFileSync(filePath, content, 'utf8');

console.log('EmailServiceFactory.js 文件已修复');
