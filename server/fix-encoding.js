/**
 * 修复操作日志文件中的编码问题
 * 将Unicode转义序列替换为直接的中文字符
 */
const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const filesToFix = [
  './apps/master/system/log/controllers/OperationLogController.js',
  './apps/master/system/log/routes/OperationLogRoute.js',
  './apps/master/system/log/services/OperationLogService.js',
  './apps/master/system/log/decorators/LogOperation.js',
  './apps/master/system/log/index.js',
  './apps/master/system/log/swagger/OperationLogSwagger.js',
  './core/middleware/OperationLogMiddleware.js'
];

// 修复文件编码
function fixFileEncoding(filePath) {
  console.log(`正在修复文件: ${filePath}`);
  
  try {
    // 读取文件内容
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 将Unicode转义序列替换为直接的中文字符
    const fixedContent = content.replace(/\\u([0-9a-fA-F]{4})/g, (match, p1) => {
      return String.fromCharCode(parseInt(p1, 16));
    });
    
    // 写入修复后的内容
    fs.writeFileSync(filePath, fixedContent, 'utf8');
    
    console.log(`文件修复完成: ${filePath}`);
  } catch (error) {
    console.error(`修复文件失败: ${filePath}`, error);
  }
}

// 执行修复
console.log('开始修复文件编码问题...');
filesToFix.forEach(filePath => {
  const fullPath = path.resolve(__dirname, filePath);
  if (fs.existsSync(fullPath)) {
    fixFileEncoding(fullPath);
  } else {
    console.warn(`文件不存在: ${fullPath}`);
  }
});
console.log('文件编码修复完成!');
