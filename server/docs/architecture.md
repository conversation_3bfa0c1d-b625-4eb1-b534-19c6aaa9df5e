# 服务端架构设计

## 1. 模块化设计

### 1.1 目录结构
```
server/
├── apps/                      # 业务模块目录
│   ├── master/               # 管理后台模块
│   │   ├── controllers/      # 控制器
│   │   │   └── UserManagementController.js
│   │   ├── services/        # 业务服务
│   │   │   └── UserManagementService.js
│   │   ├── models/          # 数据模型
│   │   │   └── UserModel.js
│   │   ├── routes/          # 路由
│   │   │   └── UserManagementRoute.js
│   │   ├── swagger/         # API文档
│   │   │   ├── schemas/     # 数据模型
│   │   │   │   └── UserManagementModel.js
│   │   │   └── responses/   # 响应定义
│   │   │       └── UserManagementResponse.js
│   │   ├── prisma/          # 数据库模型
│   │   ├── index.js         # 模块入口
│   │   └── module.json      # 模块配置
│   ├── merchant/            # 商户模块
│   └── supplier/            # 供应商模块
├── core/                     # 核心功能
│   ├── module/             # 模块管理
│   ├── middleware/         # 通用中间件
│   └── swagger/            # Swagger核心
│       ├── schemas/         # 通用模型
│       ├── responses/       # 通用响应
│       └── parameters/      # 通用参数
├── shared/                  # 共享资源
│   ├── constants/          # 常量定义
│   ├── types/             # 类型定义
│   └── utils/             # 通用工具
├── docs/                    # 项目文档
├── prisma/                  # 主Prisma配置
└── config/                 # 配置文件
```

### 1.2 命名规范

#### 文件命名
- 控制器：`{Feature}Controller.js`，如 `UserManagementController.js`
- 服务：`{Feature}Service.js`，如 `UserManagementService.js`
- 模型：`{Entity}Model.js`，如 `UserModel.js`
- 路由：`{Feature}Route.js`，如 `UserManagementRoute.js`
- Swagger模型：`{Feature}Model.js`，如 `UserManagementModel.js`
- Swagger响应：`{Feature}Response.js`，如 `UserManagementResponse.js`

#### 命名规则
- 使用大驼峰命名法
- 文件名应体现完整的功能模块
- 后缀指明文件类型（Controller/Service/Model/Route）

### 1.3 模块设计

#### 核心原则
1. **自包含**：模块包含自己的路由、控制器和文档
2. **松耦合**：模块之间通过事件和接口通信
3. **可配置**：通过 module.json 配置模块的基本信息
4. **可插拔**：支持动态启用/禁用模块

#### 模块结构
- controllers/: 控制器层，处理请求和响应
  - 必须通过构造函数注入依赖
  - 导出类而不是实例
  - 在路由中创建实例并注入依赖
- services/: 业务逻辑层，处理业务逻辑
  - 必须通过构造函数注入数据库依赖
  - 封装数据库操作，提供业务方法
- models/: 数据模型层，定义数据结构
- routes/: 路由层，定义API路由
  - 负责创建控制器实例并注入依赖
  - 定义路由和中间件
- swagger/: API文档层，包含模型和响应定义
- __tests__/: 测试目录，包含单元测试和集成测试

### 1.4 模块配置
每个模块必须包含 `module.json` 配置文件，用于定义模块的基本信息和行为。

#### module.json 规范
```json
{
  "name": "master",              // 模块名称，必填，用于模块标识
  "version": "1.0.0",           // 模块版本，必填，遵循语义化版本
  "description": "管理后台模块",  // 模块描述，必填
  "schema": "master",           // 数据库 schema，必填，用于数据隔离
  "dependencies": [],           // 依赖的其他模块，可选
  "baseUrl": "/api/master",    // 模块基础路由，必填，所有API路由的前缀
  "enabled": true,              // 模块是否启用，必填，默认true
  "order": 1,                   // 模块加载顺序，必填，数字越小越先加载
  "swagger": {                  // Swagger文档配置，必填
    "title": "管理后台 API 文档",
    "description": "管理后台模块的 API 接口文档",
    "version": "1.0.0",
    "prefix": "/api/master",    // API文档中的路由前缀
    "servers": [
      {
        "url": "/api/master",
        "description": "管理后台接口"
      }
    ]
  }
}
```

#### 配置项说明
1. **name**: 模块的唯一标识符，用于依赖管理和模块间通信
2. **version**: 模块版本号，用于版本控制和升级管理
3. **schema**: 对应的数据库 schema，确保数据隔离
4. **dependencies**: 声明模块依赖，确保正确的加载顺序
5. **baseUrl**: 模块的API路由前缀，所有该模块的API都将使用此前缀
6. **enabled**: 控制模块是否启用，支持动态启用/禁用
7. **swagger**: Swagger文档配置，确保API文档的正确生成

#### 注意事项
1. 模块的路由必须以 baseUrl 为前缀
2. swagger.prefix 必须与 baseUrl 保持一致
3. 依赖的模块必须在当前模块之前加载
4. 禁用模块时会同时禁用其API路由和Swagger文档

## 2. RESTful API 设计

### 2.1 统一响应格式
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 响应数据
  }
}
```

### 2.1.1 列表响应标准格式
列表接口必须使用以下标准格式返回数据，所有模块必须遵循此规范：

```json
{
  "success": true,
  "message": "请求成功",
  "code": 200,
  "data": {
    "items": [
      // 列表数据项
    ],
    "pageInfo": {
      "total": 100,
      "currentPage": 1,
      "totalPage": 10
    }
  }
}
```

#### 实现方式
所有控制器必须继承自`BaseController`，并使用其提供的`successList`方法来返回列表数据：

```javascript
async list(req, res) {
  try {
    const { page = 1, pageSize = 10 } = this.getPagination(req.query);
    // 从服务层获取数据
    const result = await this.xxxService.list({ ...req.query, page, pageSize });
    // 使用BaseController中的successList方法
    this.successList(res, result.list, result.total, result.page, result.pageSize);
  } catch (error) {
    const { message, code } = this.handleDbError(error);
    this.fail(res, message, code);
  }
}
```

### 2.2 路由定义规范

#### 2.2.1 路由结构
```
/api/{module}/{feature}/{resource}
```

- **module**: 模块名称，如 master、merchant、supplier
- **feature**: 功能模块，使用短横线分隔，如 user-management、role-management
- **resource**: 资源名称，使用复数形式，如 users、roles

#### 2.2.2 命名规范
1. **模块前缀**：必须与 module.json 中的 baseUrl 保持一致
2. **功能模块**：使用短横线分隔的小写形式，如 user-management
3. **资源名称**：使用复数形式的小写单词，如 users
4. **参数**：使用短横线分隔的小写形式，如 user-id

#### 2.2.3 HTTP 方法使用
- **GET 列表**: /api/master/user-management/users
- **GET 详情**: /api/master/user-management/users/{id}
- **POST 创建**: /api/master/user-management/users
- **PUT 更新**: /api/master/user-management/users/{id}
- **DELETE 删除**: /api/master/user-management/users/{id}
- **POST 特殊操作**: /api/master/user-management/users/login

#### 2.2.4 Swagger 文档规范
1. **路径前缀**：必须与实际路由完全一致
2. **标签分组**：使用功能模块名称，如 [用户管理]
3. **接口摘要**：简洁明确的中文描述
4. **参数引用**：使用通用参数定义，如 $ref: '#/components/parameters/IdParam'

#### 2.2.5 示例代码
```javascript
/**
 * @swagger
 * /api/master/user-management/users:
 *   get:
 *     tags: [用户管理]
 *     summary: 获取用户列表
 */
router.get('/user-management/users', userController.list);
```

### 2.3 HTTP 方法使用
- GET：查询
- POST：创建
- PUT：更新（全量）
- PATCH：更新（部分）
- DELETE：删除

### 2.4 状态码使用
- 200：成功
- 201：创建成功
- 400：请求参数错误
- 401：未认证
- 403：无权限
- 404：资源不存在
- 500：服务器错误

## 3. 模块说明

### 3.1 Master 模块
管理后台核心模块，负责：
1. 系统用户管理
2. 认证授权
3. 系统配置

路由前缀：`/api/master`

### 3.2 Merchant 模块
商户管理模块，负责：
1. 商户用户管理
2. 商户资料管理
3. 商户业务处理

路由前缀：`/api/merchant`

### 3.3 Supplier 模块
供应商管理模块，负责：
1. 供应商用户管理
2. 供应商资料管理
3. 供应商业务处理

路由前缀：`/api/supplier`

## 4. 安全设计

### 4.1 认证
1. 统一使用 JWT Token
2. Token 存储在 Authorization Header
3. 刷新 Token 机制

### 4.2 授权
1. 基于角色的访问控制（RBAC）
2. 细粒度的权限控制
3. 数据权限隔离

## 5. 数据库设计

### 5.1 Schema 隔离
每个模块使用独立的 Schema：
- master：系统数据
- merchant：商户数据
- supplier：供应商数据

### 5.2 模型定义规范

#### 5.2.1 注释规范
1. **模型注释**
   ```prisma
   /// 用户信息表，存储系统用户数据
   model User {
     // 字段定义...
   }
   ```

2. **字段注释**
   ```prisma
   model User {
     id        BigInt    @id    // 用户ID，16位雪花算法
     username  String    @unique // 用户名，必填，唯一
     status    Int       @default(1) // 状态：1-正常，0-禁用
   }
   ```

3. **注释格式**
   - 字段注释必须包含：中文名称、类型说明、约束说明（如果有）
   - 枚举值必须列举所有可能的值和含义
   - 外键必须注明关联的表和字段

#### 5.2.2 审计字段
所有表必须包含以下审计字段：
```prisma
  // 审计字段
  created_at  DateTime  @default(now())  // 创建时间
  updated_at  DateTime  @updatedAt       // 更新时间
  created_by  String?                    // 创建人
  updated_by  String?                    // 更新人
  deleted_at  DateTime?                  // 删除时间，空表示未删除
```

## 6. 依赖注入与测试设计

### 6.1 BaseController

所有控制器必须继承 `core/controllers/BaseController`，它提供了统一的响应格式和常用功能。

#### 引用规范
```javascript
// 正确的引用方式
const BaseController = require('../../../core/controllers/BaseController');

// 错误的引用方式
const BaseController = require('./BaseController');
```

#### 继承规范
```javascript
class YourController extends BaseController {
  constructor(prisma) {
    super();
    this.service = new YourService(prisma);
  }
}
```

#### 标准功能
BaseController 提供以下标准功能：

#### 6.1.1 响应处理
```javascript
// 成功响应
success(res, data, message = '操作成功')

// 失败响应
fail(res, message = '操作失败', code = 400)
```

#### 6.1.2 分页功能
```javascript
// 处理分页参数
getPagination(query) {
  const page = parseInt(query.page) || 1;
  const pageSize = parseInt(query.pageSize) || 10;
  return {
    skip: (page - 1) * pageSize,
    take: pageSize,
    page,
    pageSize
  };
}

// 构建分页响应
buildPaginationResponse(list, total, { page, pageSize })
```

#### 6.1.3 错误处理
```javascript
// 处理数据库错误
handleDbError(error) {
  // 处理 Prisma 错误码
  if (error.code) {
    switch (error.code) {
      case 'P2002': return { message: '记录已存在', code: 400 };
      case 'P2003': return { message: '关联记录不存在', code: 400 };
      case 'P2025': return { message: '记录不存在', code: 404 };
      default: return { message: '数据库操作失败', code: 500 };
    }
  }
  return { message: error.message || '未知错误', code: 500 };
}

// 验证请求参数
validateFields(data, requiredFields)
```

### 6.2 控制器数据验证规范

控制器中的数据验证应遵循以下规则：

1. **优先使用DTO进行数据验证**：控制器接收到请求数据后，应优先通过对应的DTO进行数据验证，而非在控制器中直接编写验证逻辑。

```javascript
// 推荐做法：使用DTO进行数据验证
async create(req, res) {
  try {
    // 使用DTO验证请求数据
    const { error, value } = SystemUserDto.validateCreate(req.body);
    if (error) {
      return this.fail(res, error.details[0].message);
    }
    
    // 验证通过后处理业务逻辑
    const result = await this.userService.create(value);
    return this.success(res, result);
  } catch (error) {
    return this.error(res, error);
  }
}

// 不推荐做法：在控制器中直接编写验证逻辑
async create(req, res) {
  try {
    const { username, password, email } = req.body;
    
    // 直接在控制器中进行验证
    if (!username || username.length < 3) {
      return this.fail(res, '用户名不能为空且长度不能少于3个字符');
    }
    
    if (!password || password.length < 6) {
      return this.fail(res, '密码不能为空且长度不能少于6个字符');
    }
    
    if (email && !email.includes('@')) {
      return this.fail(res, '邮箱格式不正确');
    }
    
    // 处理业务逻辑
    const result = await this.userService.create(req.body);
    return this.success(res, result);
  } catch (error) {
    return this.error(res, error);
  }
}
```

2. **为新模块创建对应的DTO**：每次开发新功能模块时，应该首先创建对应的DTO类，并按照DTO设计规范实现验证方法。

3. **验证失败处理**：当DTO验证失败时，应直接返回验证错误信息，不再继续处理业务逻辑。

4. **验证覆盖范围**：除了创建和更新操作外，查询参数和删除操作也应该通过DTO进行验证。

5. **特殊验证逻辑处理**：对于不适合在DTO中处理的复杂验证逻辑（如需要查询数据库的唯一性验证），可以在通过DTO基础验证后，在控制器或服务层中进行二次验证。

### 6.3 依赖注入原则

#### 6.3.1 控制器层
```javascript
// 导出类而不是实例
class UserController extends BaseController {
  constructor(prisma) {
    super(prisma);
    this.userService = new UserService(prisma);
  }

  async list(req, res) {
    try {
      // 1. 获取分页参数
      const { skip, take, page, pageSize } = this.getPagination(req.query);
      
      // 2. 构建查询条件
      const where = { deleted_at: null };
      if (req.query.name) {
        where.name = { contains: req.query.name };
      }

      // 3. 执行查询
      const [list, total] = await Promise.all([
        this.prisma.user.findMany({ where, skip, take }),
        this.prisma.user.count({ where })
      ]);

      // 4. 返回分页数据
      return this.success(
        res,
        this.buildPaginationResponse(list, total, { page, pageSize })
      );
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }
}
module.exports = UserController;

// 在路由中创建实例
const UserController = require('../controllers/UserController');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const userController = new UserController(prisma);
```

#### 6.3.2 服务层
```javascript
class UserService {
  constructor(prisma) {
    this.prisma = prisma;
  }
  
  async list(page, pageSize) {
    return this.prisma.user.findMany({
      skip: (page - 1) * pageSize,
      take: pageSize
    });
  }
}
```

### 6.2 测试设计

#### 6.2.1 目录结构
```
apps/
├── master/
│   ├── __tests__/
│   │   ├── unit/
│   │   │   ├── controllers/
│   │   │   ├── services/
│   │   │   └── models/
│   │   └── integration/
```

#### 6.2.2 测试覆盖率要求
- 控制器层：100% 测试覆盖
- 服务层：90% 测试覆盖
- 模型层：85% 测试覆盖

#### 6.2.3 Mock 规范
```javascript
// 1. Mock 依赖模块
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn()
}));

// 2. 初始化 mock 对象
const mockPrisma = {
  user: {
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn()
  }
};

// 3. 模拟响应对象
const mockRes = {
  status: jest.fn().mockReturnThis(),
  json: jest.fn()
};

```

## 7. DTO (数据传输对象) 设计规范

### 7.1 DTO命名规范

#### 文件命名
- DTO文件名必须与Prisma模型对应，采用Pascal命名法
- 文件名格式：`{Schema}{Table}Dto.js`
- 示例：
  - Prisma模型: `system_user.prisma` → DTO文件名: `SystemUserDto.js` 
  - Prisma模型: `system_menu.prisma` → DTO文件名: `SystemMenuDto.js`
  - Prisma模型: `goods_category.prisma` → DTO文件名: `GoodsCategoryDto.js`

#### 类命名
- DTO类名与文件名保持一致
- 所有DTO类都应继承自`BaseDto`基类
- 示例：
```javascript
class SystemUserDto extends BaseDto {
  // 方法实现...
}
```

### 7.2 BaseDto基类
所有DTO都应继承自BaseDto基类，该基类提供通用的验证和字段格式转换功能：

```javascript
// BaseDto.js
const Joi = require('joi');
const { camelToSnake } = require('../../shared/utils/format');

class BaseDto {
  /**
   * 验证数据并转换字段格式
   * @param {Object} data 要验证的数据
   * @param {Object} schema Joi验证模式
   * @param {Object} options 验证选项
   * @returns {Object} 验证结果，包含转换后的数据
   */
  static validate(data, schema, options = { abortEarly: false }) {
    const validationResult = schema.validate(data, options);
    
    // 如果验证通过，转换字段名格式
    if (!validationResult.error && validationResult.value) {
      validationResult.value = camelToSnake(validationResult.value);
    }
    
    return validationResult;
  }
}
```

### 7.3 DTO方法规范

- 方法名应采用`validate{Action}`格式，例如：
  - `validateCreate`: 验证创建操作
  - `validateUpdate`: 验证更新操作
  - `validateQuery`: 验证查询操作
  - `validateDelete`: 验证删除操作

- 方法必须是静态方法，便于直接调用
- 每个方法应返回Joi验证结果，包含`error`和`value`

```javascript
static validateCreate(data) {
  const schema = Joi.object({
    // 字段定义...
  });
  
  return this.validate(data, schema);
}
```

### 7.4 验证规则规范

- 使用Joi库进行数据验证
- 为每个字段提供详细的错误消息
- 小驼峰命名的字段会自动转换为数据库中的下划线命名
- 示例：

```javascript
// 字段验证
username: Joi.string().required().min(3).max(50).messages({
  'string.base': '用户名必须是字符串',
  'string.empty': '用户名不能为空',
  'string.min': '用户名长度不能少于3个字符',
  'string.max': '用户名长度不能超过50个字符',
  'any.required': '用户名是必填项'
})
```

### 7.5 字段名转换规则

前端传入的参数通常使用小驼峰命名法（如`userType`），而数据库通常使用下划线命名法（如`user_type`）。
DTO负责在验证通过后自动将字段名从小驼峰格式转换为下划线格式，实现前后端字段命名风格的统一转换：

```javascript
// 字段名转换示例
// 前端传入: { userName: 'admin', userType: 100 }
// 转换后: { user_name: 'admin', user_type: 100 }
```

### 7.6 与控制器集成

在控制器中使用DTO验证请求数据：

```javascript
async create(req, res) {
  try {
    // 使用DTO验证请求数据
    const { error, value } = SystemUserDto.validateCreate(req.body);
    if (error) {
      return this.fail(res, error.details[0].message);
    }
    const user = await this.userService.create(value);
    return this.success(res, user);
  } catch (error) {
    return this.error(res, error);
  }
}
```

### 7.7 文件位置

DTO文件应放置在对应模块的dto目录下：

```
/apps/master/system/user/dto/
  ├── SystemUserDto.js
  ├── SystemMenuDto.js
  └── SystemDeptDto.js
```

### 7.8 注释规范

- 每个DTO类必须包含类注释，说明其用途
- 每个验证方法必须包含JSDoc格式的注释，包括参数和返回值说明
- 注释应当使用中文，确保团队成员能够清晰理解
