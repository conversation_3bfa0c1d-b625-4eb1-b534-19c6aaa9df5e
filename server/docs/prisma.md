# Prisma模型管理规范

## 1. Prisma 客户端单例模式规范

### 1.1 单例模式原则
- **必须使用单例模式创建和管理 Prisma 客户端实例**
- 禁止在业务代码中直接创建新的 PrismaClient 实例
- 所有代码必须使用全局共享的 Prisma 实例

### 1.2 单例模式实现
项目中已实现了 Prisma 客户端单例模式，位于 `core/database/prisma.js`：

```javascript
/**
 * Prisma 客户端单例模式
 * 避免创建过多的数据库连接
 */
const { PrismaClient } = require('@prisma/client');

// 检查全局变量中是否已存在 Prisma 实例
const globalForPrisma = global;

// 如果不存在，则创建一个新的实例
if (!globalForPrisma.prisma) {
  globalForPrisma.prisma = new PrismaClient({
    // 可以在这里添加日志配置
    log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],
  });
  
  // 如果是开发环境，输出提示信息
  if (process.env.NODE_ENV === 'development') {
    console.log('创建新的 Prisma 客户端实例');
  }
  
  // 连接数据库
  globalForPrisma.prisma.$connect()
    .then(() => console.log('数据库连接成功!'))
    .catch(err => {
      console.error('数据库连接错误:', err);
      process.exit(1);
    });
}

// 导出全局 Prisma 实例
const prisma = globalForPrisma.prisma;

module.exports = { prisma };
```

### 1.3 使用方式

在需要使用 Prisma 客户端的文件中，按照以下方式导入和使用：

```javascript
// 正确的导入方式
const { prisma } = require('path/to/core/database/prisma');

// 错误的导入方式 - 禁止使用
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient(); // 禁止直接创建实例
```

### 1.4 服务类使用规范

在服务类中使用 Prisma 客户端时，应遵循以下规范：

```javascript
const { prisma } = require('path/to/core/database/prisma');

class ExampleService {
  constructor(prismaInstance) {
    // 使用传入的 prisma 实例或全局实例
    this.prisma = prismaInstance || prisma;
  }
  
  async findById(id) {
    return await this.prisma.exampleModel.findUnique({
      where: { id }
    });
  }
}
```

### 1.5 连接管理注意事项

- 禁止手动关闭或断开 Prisma 客户端连接
- 应用程序关闭时会自动断开连接
- 避免在短时间内频繁创建和销毁大量对象

## 2. 模型命名规范

### 2.1 模型名称
- **模型名必须是schema+map命名**，且两个单词都首字母大写
- 例如：`master.user`表的模型应命名为`MasterUser`
- 例如：`merchant.goods_categories`表的模型应命名为`MerchantGoodsCategories`
- 表名映射使用`@@map`注解和`@@schema`注解

```prisma
/// 用户信息表
model MasterUser {
  id       BigInt  @id @default(autoincrement())
  username String  @db.VarChar(50)
  
  @@map("user")
  @@schema("master")
}
```

### 2.2 字段命名
- 字段名称使用下划线命名法（snake_case），与数据库字段保持一致
- 字段注释使用三斜线语法

```prisma
model MasterUser {
  id         BigInt @id @default(autoincrement())  /// @db.Comment('用户ID，系统自动生成')
  user_name  String @db.VarChar(50)                /// @db.Comment('用户名')
  created_at BigInt                                /// @db.Comment('创建时间戳（毫秒）')
}
```

## 2. 模块化模型管理

### 2.1 文件组织
按业务领域将模型拆分到不同的`.prisma`文件中，存放在以下目录结构：

```
/server/apps/master/prisma/
  ├── schema.prisma          # 主Schema文件（合并后）
  ├── merge-schema.js        # 合并脚本
  └── models/                # 模型定义目录
      ├── system/            # 系统模块
      │   ├── user.prisma
      │   └── role.prisma
      ├── goods/             # 商品模块
      │   ├── goods_spus.prisma
      │   └── goods_categories.prisma
      └── ...
```

### 2.2 模型文件命名
- **Prisma 文件名必须与数据库表名完全一致**（不包括 schema 名称）
- 文件扩展名统一使用 `.prisma`
- 示例：
  - 表名：`users` → 文件名：`users.prisma`
  - 表名：`system_menu` → 文件名：`system_menu.prisma`
  - 表名：`goods_categories` → 文件名：`goods_categories.prisma`

### 2.3 模型文件引用
在主 schema.prisma 文件中引用模型文件时，必须使用与表名一致的文件名：

```prisma
// 正确引用方式
// ../apps/master/prisma/models/system/system_user.prisma
// ../apps/master/prisma/models/system/system_menu.prisma

// 错误引用方式
// ../apps/master/prisma/models/system/user.prisma
// ../apps/master/prisma/models/system/menu.prisma
```

## 3. 字段类型规范

### 3.1 基础字段类型
- 主键统一用`id`，类型为`BigInt`（16位雪花算法）
- 外键格式：`实体名称_id`，如`dept_id`，类型与被引用字段一致
- 状态字段：`status`，使用`Int`类型
- **时间戳字段统一使用`BigInt`类型，不使用`DateTime`**
- **避免使用`dbgenerated`表达式**，这会导致数据库同步问题
- 时间戳字段不设置数据库默认值，由应用层负责设置

### 3.2 必备字段规范
```prisma
// 主键
id         BigInt  @id @default(autoincrement()) /// @db.Comment('主键，系统自动生成')

// 审计字段
created_by BigInt? /// @db.Comment('创建者ID')
updated_by BigInt? /// @db.Comment('更新者ID')
created_at BigInt  /// @db.Comment('创建时间戳（毫秒）')
updated_at BigInt  /// @db.Comment('更新时间戳（毫秒）')
deleted_at BigInt? /// @db.Comment('删除时间戳（毫秒），空表示未删除')
remark     String? /// @db.Comment('备注信息')
```

### 3.3 字段顺序
1. 主键字段
2. 基本信息字段
3. 状态相关字段
4. 关联字段
5. 审计字段

## 4. 模型关系处理

### 4.1 关系定义
确保关系两端的模型都正确定义了对应的关系字段，且关系名称应与模型命名保持一致（首字母大写）：

```prisma
// goods_categories.prisma
model BaseGoodsCategory {
  id            BigInt        @id @default(autoincrement())
  name          String
  goods_spus    BaseGoodsSpu[]    // 一对多关系

  @@map("goods_categories")
  @@schema("base")
}

// goods_spus.prisma
model BaseGoodsSpu {
  id                BigInt        @id @default(autoincrement())
  name              String
  goods_category_id BigInt
  goods_category    BaseGoodsCategory @relation(fields: [goods_category_id], references: [id])

  @@map("goods_spus")
  @@schema("base")
}
```

### 4.2 循环引用处理
处理循环引用问题时，可以暂时注释掉一侧的关系定义：

```prisma
// 在一个模型文件中保留关系
model BaseGoodsSpu {
  id                BigInt        @id @default(autoincrement())
  goods_category_id BigInt
  goods_category    BaseGoodsCategory @relation(fields: [goods_category_id], references: [id])
}

// 在另一个模型文件中暂时注释掉关系
model BaseGoodsCategory {
  id            BigInt        @id @default(autoincrement())
  // goods_spus    BaseGoodsSpu[]    // 暂时注释掉，解决循环引用
}
```

## 5. 多Schema支持

### 5.1 配置多Schema
在`schema.prisma`中启用多Schema支持：

```prisma
generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["base", "master", "merchant", "supplier"]
}
```

### 5.2 指定模型Schema
在每个模型定义中使用`@@schema`注解指定其所属的schema：

```prisma
model MasterUser {
  id       BigInt  @id @default(autoincrement())
  username String
  
  @@map("user")
  @@schema("master")  // 指定schema为master
}

model BaseGoodsSpu {
  id   BigInt  @id @default(autoincrement())
  name String
  
  @@map("goods_spus")
  @@schema("base")    // 指定schema为base
}
```

## 6. 合并与同步流程

### 6.1 合并模型文件
使用根目录下的合并脚本：

```bash
# 合并所有模块的模型文件（推荐）
npm run prisma:merge

# 也可以单独合并某个模块的模型文件
cd server/apps/master/prisma && node merge-schema.js
```

### 6.2 生成与同步
合并完成后，执行以下命令：

```bash
# 生成Prisma客户端
npm run prisma:generate

# 同步到数据库
npm run prisma:db:push

# 或者一步完成所有流程
npm run prisma:deploy
```

## 7. 常见问题处理

### 7.1 类型未定义
确保所有引用的类型都已在schema中定义，检查模型名称是否一致：

```prisma
// 错误示例
model BaseGoodsSpu {
  category Category  // 错误：Category类型未定义
}

// 正确示例
model BaseGoodsSpu {
  category BaseGoodsCategory  // 正确：使用已定义的BaseGoodsCategory类型
}
```

### 7.2 关系不匹配
检查关系两端的字段名和引用是否一致：

```prisma
// 正确的关系定义
// 模型A
model BaseGoodsCategory {
  id         BigInt        @id
  goods_spus BaseGoodsSpu[] // 字段名与模型B中的关系字段匹配
}

// 模型B
model BaseGoodsSpu {
  id                BigInt             @id
  goods_category_id BigInt
  goods_category    BaseGoodsCategory  @relation(fields: [goods_category_id], references: [id])
}
```

### 7.3 同步问题
如果遇到同步问题，尝试以下步骤：

1. 检查是否有使用`dbgenerated`表达式，如果有，替换为明确的默认值
2. 确保时间戳字段使用`BigInt`而不是`DateTime`
3. 检查模型名称是否符合`Schema+Map`命名规则且两个单词首字母大写
4. 确认所有关系引用的字段类型一致
