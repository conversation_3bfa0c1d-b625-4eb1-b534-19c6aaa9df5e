# Swagger 文档设计

## 1. 基本信息

### 1.1 API 版本

所有API均需遵循版本命名规范，当前版本为 `v1`。

```
/api/v1/{module}/{resource}
```

示例：
```
/api/v1/master/auth/login
/api/v1/common/public-key
```

### 1.2 基本配置

```yaml
openapi: 3.0.0
info:
  title: 聚灵云平台 API 文档
  version: 1.0.0
  description: 聚灵云平台后端 API 接口文档
servers:
  - url: http://localhost:4000/api/v1
    description: 开发服务器 (v1)
basePath: /api/v1
```

## 2. 目录结构

```
server/
├── apps/
│   └── {module}/
│       ├── routes/      # API 路由和文档
│       └── swagger/
│           ├── schemas/  # 模块特有的数据模型
│           └── responses/ # 模块特有的响应定义
├── core/
│   └── swagger/
│       ├── schemas/   # 通用数据模型
│       ├── responses/ # 通用响应模型
│       └── parameters/ # 通用参数定义
└── shared/
    └── types/      # 通用类型定义
```

## 3. 组件分类标准

### 3.1 模块特定组件（存放在对应模块路径下）

1. **模块特定的请求/响应模式**
   - `UserCreateRequest` - 用户创建请求
   - `UserUpdateRequest` - 用户更新请求
   - `RoleCreateRequest` - 角色创建请求

2. **模块特定的实体定义**
   - `User`（作为完整模型）
   - `Role`（作为完整模型）
   - `Department`（作为完整模型）

3. **模块特定的API响应**
   - `UserResponse`
   - `UserListResponse`

### 3.2 通用组件（存放在core或shared路径下）

1. **通用参数定义**
   - `PageParam` - 分页页码
   - `PageSizeParam` - 分页大小
   - `IdParam` - ID参数
   - `SortParam` - 排序参数

2. **通用响应结构**
   - `Error` - 错误响应
   - `Success` - 成功响应
   - `ListResponse` - 列表响应

3. **通用安全定义**
   - `bearerAuth` - JWT认证方式
   - `apiKeyAuth` - API密钥认证

4. **基础数据类型/枚举**
   - `StatusEnum` - 状态枚举

## 4. 通用响应模型

### 4.1 成功响应
```yaml
Success:
  type: object
  properties:
    success:
      type: boolean
      default: true
    message:
      type: string
      description: 成功信息
    data:
      type: object
      description: 响应数据
```

### 4.2 错误响应
```yaml
Error:
  type: object
  properties:
    success:
      type: boolean
      default: false
    message:
      type: string
      description: 错误信息
    code:
      type: integer
      description: 错误码
      example: 40001
      enum:
        - 40001  # 参数验证失败
        - 40002  # 参数格式错误
        - 40003  # 参数值超出范围
        - 40004  # 验证码错误
        - 40101  # 未登录或登录已过期
        - 40102  # 登录凭证无效
        - 40301  # 权限不足
        - 40302  # 账号已被禁用
        - 40401  # 资源不存在
        - 40402  # 接口不存在
        - 50001  # 服务器内部错误
        - 50002  # 数据库操作失败
        - 50003  # 第三方服务调用失败
```

### 4.3 分页响应
```yaml
PaginatedResponse:
  type: object
  properties:
    success:
      type: boolean
      default: true
    message:
      type: string
      description: 响应消息
      example: "请求成功"
    code:
      type: integer
      description: 状态码
      example: 200
    data:
      type: object
      properties:
        items:
          type: array
          description: 数据列表
        pageInfo:
          type: object
          properties:
            total:
              type: integer
              description: 总记录数
              example: 100
            currentPage:
              type: integer
              description: 当前页码
              example: 1
            totalPage:
              type: integer
              description: 总页数
              example: 10
```

## 5. 模块 API 文档

### 5.1 Master 模块

#### 用户管理
```yaml
paths:
  /api/master/user-management/users:
    get:
      tags: [用户管理]
      summary: 获取用户列表
    post:
      tags: [用户管理]
      summary: 创建用户
  
  /api/master/user-management/users/{id}:
    get:
      tags: [用户管理]
      summary: 获取用户详情
    put:
      tags: [用户管理]
      summary: 更新用户
    delete:
      tags: [用户管理]
      summary: 删除用户
```

#### 认证
```yaml
paths:
  /api/v1/master/auth/login:
    post:
      tags: [系统管理/用户中心/认证]
      summary: 用户登录
      description: 用户登录并获取访问令牌，支持加密密码和验证码
      requestBody:
        content:
          application/json:
            schema:
              properties:
                username:
                  type: string
                password:
                  type: string
                  description: 密码，当encrypted=true时为RSA加密形式
                encrypted:
                  type: boolean
                  description: 密码是否已经过RSA加密
                captcha:
                  type: string
                captchaId:
                  type: string
                  
  /api/v1/master/auth/logout:
    post:
      tags: [系统管理/用户中心/认证]
      summary: 用户注销
      security:
        - bearerAuth: []      

  /api/v1/common/public-key:
    get:
      tags: [公共服务/安全]
      summary: 获取RSA公钥
      
  /api/v1/common/captcha/generate:
    get:
      tags: [公共服务/验证码]
      summary: 生成验证码
```

### 5.2 Merchant 模块

#### 商户用户
```yaml
paths:
  /api/v1/merchant/user-management/users:
    get:
      tags: [商户用户]
      summary: 获取商户列表
    post:
      tags: [商户用户]
      summary: 创建商户
  
  /api/v1/merchant/user-management/users/{id}:
    get:
      tags: [商户用户]
      summary: 获取商户详情
    put:
      tags: [商户用户]
      summary: 更新商户
    delete:
      tags: [商户用户]
      summary: 删除商户
```

### 5.3 Supplier 模块

#### 供应商用户
```yaml
paths:
  /api/v1/supplier/user-management/users:
    get:
      tags: [供应商用户]
      summary: 获取供应商列表
    post:
      tags: [供应商用户]
      summary: 创建供应商
  
  /api/v1/supplier/user-management/users/{id}:
    get:
      tags: [供应商用户]
      summary: 获取供应商详情
    put:
      tags: [供应商用户]
      summary: 更新供应商
    delete:
      tags: [供应商用户]
      summary: 删除供应商
```

### 5.4 角色管理

#### 获取角色已分配菜单
```yaml
paths:
  /api/v1/master/role/:id/menus:
    get:
      tags: [角色管理]
      summary: 获取角色已分配菜单
      parameters:
        - name: id
          in: path
          required: true
          description: 角色ID
      responses:
        200:
          description: 菜单ID数组
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
```

#### 删除角色（同步清理关联）
```yaml
paths:
  /api/v1/master/role/:id:
    delete:
      tags: [角色管理]
      summary: 删除角色（同步清理关联）
      parameters:
        - name: id
          in: path
          required: true
          description: 角色ID
      responses:
        200:
          description: 操作结果
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
```

## 6. 文档规范与动态扫描

### 6.1 目录结构

#### 安全定义
```yaml
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT认证，在请求头中添加 'Authorization: Bearer {token}'
```

#### 通用定义（core/swagger/）
- schemas/: 通用数据模型
- responses/: 通用响应定义（Success、Error、PaginatedResponse）
- parameters/: 通用参数定义（分页、ID 等）

#### 模块定义（支持动态多级目录结构）
- apps/{module}/swagger/schemas/: 模块根目录下的模型定义
- apps/{module}/routes/: 模块根目录下的路由定义
- apps/{module}/{submodule}/swagger/schemas/: 子模块的模型定义
- apps/{module}/{submodule}/routes/: 子模块的路由定义

系统会自动递归扫描所有层级的 swagger/schemas 和 routes 目录，确保所有 API 文档都能被自动加载。

### 6.2 文件命名

#### 通用定义文件
- 通用模型：`core/swagger/schemas/CommonModel.js`
- 通用响应：`core/swagger/responses/CommonResponse.js`
- 通用参数：`core/swagger/parameters/CommonParam.js`

#### 模块定义文件
- 数据模型：`apps/{module}/swagger/schemas/{Feature}Model.js`
  例如：`UserManagementModel.js`、`OrderManagementModel.js`
- 响应定义：`apps/{module}/swagger/responses/{Feature}Response.js`
  例如：`UserManagementResponse.js`、`OrderManagementResponse.js`

#### 命名规范
- 使用大驼峰命名模块和类型：UserManagement、OrderManagement
- 使用大驼峰命名文件类型后缀：Model、Response、Param
- 文件名应体现完整的业务功能模块

### 6.3 接口文档

#### API 版本管理
- 所有API都应包含版本号前缀：`/api/v1/...`
- 当API需要大版本更新时，创建新的版本前缀：`/api/v2/...`
- API升级时保留旧版本，确保向后兼容

#### 路由定义与动态注册
- 在模块的任意子目录下的 routes 文件夹中定义（支持多级目录结构）
- 使用 JSDoc 格式的 Swagger 注释
- 参数和响应通过 $ref 引用已定义的模型
- 每个路由文件必须导出 paths 对象供动态扫描：
  ```javascript
  // 正确做法
  module.exports = router;
  module.exports.paths = { /* Swagger 路径定义 */ };
  ```
- 分层目录结构下的路由会自动按目录组织成分组，例如：
  ```
  /apps/master/system/user/routes/UserManagementRoute.js → system/user 分组
  ```

#### 标签规范
- 使用中文命名标签
- 按模块和功能分类，支持多层级分组
- 标签格式：`主模块/子模块/功能`，如：`系统管理/用户中心/认证`
- 也可在路由定义中手动指定 tags 属性
- 标签层级应保持简洁明了，不超过三层

### 6.4 数据模型

#### 通用响应模型
- Success: 成功响应，包含 success、message、data
- Error: 错误响应，包含 success、message、code
- ListResponse: 标准列表响应，包含 success、message、code、data.items、data.pageInfo

#### 组件命名规范
- 模块组件应添加业务领域前缀，避免命名冲突
  - SystemUser: 系统管理/用户管理模块的用户模型
  - SystemUserCreateRequest: 系统管理/用户创建请求
  - MerchantUser: 商户管理模块的用户模型
  - SupplierUser: 供应商管理模块的用户模型
- 按功能模块拆分文件
- 必须包含完整的字段说明和示例值

### 6.5 参数定义
- 通用参数放在 core/swagger/parameters 目录
- 必填参数标记 required
- 参数类型明确
- 参数描述清晰
- 必须包含示例值

### 6.6 示例数据
- 所有模型定义应包含示例数据
- 可使用 example 属性提供完整对象示例
```yaml
UserDto:
  type: object
  properties:
    id:
      type: string
    username:
      type: string
  example:
    id: "1001"
    username: "admin"
```

### 6.7 安全定义
- 需要认证的API应使用 security 字段声明认证类型
```yaml
/api/v1/master/users:
  get:
    security:
      - bearerAuth: []
```
- 在 securitySchemes 中定义认证方式

## 标签命名规范

- 使用`模块/子模块/功能`三级结构

## API文档定义位置规范

### 1. 统一文档源定义

为避免API文档多处定义导致的不一致问题，应遵循以下规则：

1. **模型(Model)定义**：
   - 位置：`/server/apps/{模块名}/swagger/schemas/`
   - 命名：按功能命名，如`UserManagementModel.js`
   - 内容：定义请求/响应数据结构

2. **路径(Path)定义**：
   - 位置：`/server/apps/{模块名}/swagger/paths/`
   - 命名：按控制器命名，如`AuthPaths.js`
   - 内容：定义API路径、方法、参数等

3. **引用规则**：
   - 控制器中使用`@ref`引用路径定义
   - 路径定义中使用`$ref`引用模型定义
   - 严格禁止在路由文件中直接定义完整的Swagger文档结构

### 2. 一致性保障措施

- 修改API接口时，必须同步更新所有相关文档
- 在代码审查中专门检查API文档一致性
- 定期进行API文档与实际实现的对比检查
