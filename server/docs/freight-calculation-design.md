# 运费计算系统设计文档

## 3. 运费计算公式说明

### 3.1 核心计算原理

运费计算采用**阶梯计费模式**，分为两个阶梯：
1. **首阶梯**：覆盖起始的件数/重量/体积，收取固定费用
2. **续阶梯**：超出首阶梯的部分，按续阶梯单位计费

### 3.2 通用计算公式

无论按什么类型计费，都遵循统一的阶梯计费公式：

```
总运费 = 首阶梯费用 + 续阶梯费用

其中：
首阶梯费用 = first_fee
续阶梯费用 = ceil((计算基数 - first_item) / additional_item) × additional_fee

注意：
- 如果 计算基数 <= first_item，则续阶梯费用 = 0
- ceil() 表示向上取整函数
- 计算基数根据不同计费类型而不同
```

### 3.3 按件计费 (charge_type = 1)

#### 3.3.1 公式说明

```
计算基数 = quantity (商品数量)

总运费 = first_fee + 续件费用

续件费用计算：
if (quantity <= first_item) {
    续件费用 = 0
} else {
    超出件数 = quantity - first_item
    续件数量 = ceil(超出件数 / additional_item)
    续件费用 = 续件数量 × additional_fee
}
```

#### 3.3.2 计算示例

**配置示例：**
- first_item = 1 (首件)
- first_fee = 8.00 (首件费用8元)
- additional_item = 1 (续件按1件计)
- additional_fee = 5.00 (续件费用5元/件)

**计算案例：**

| 购买数量 | 计算过程 | 运费结果 |
|---------|----------|----------|
| 1件 | 8.00 + 0 = 8.00 | 8.00元 |
| 2件 | 8.00 + ceil((2-1)/1) × 5.00 = 8.00 + 1 × 5.00 | 13.00元 |
| 3件 | 8.00 + ceil((3-1)/1) × 5.00 = 8.00 + 2 × 5.00 | 18.00元 |
| 5件 | 8.00 + ceil((5-1)/1) × 5.00 = 8.00 + 4 × 5.00 | 28.00元 |

**JavaScript实现代码：**
```javascript
function calculateByPiece(quantity, config) {
    const { first_item, first_fee, additional_item, additional_fee } = config;
    
    if (quantity <= first_item) {
        return parseFloat(first_fee);
    }
    
    const exceededItems = quantity - first_item;
    const additionalUnits = Math.ceil(exceededItems / additional_item);
    const additionalCost = additionalUnits * parseFloat(additional_fee);
    
    return parseFloat(first_fee) + additionalCost;
}
```

### 3.4 按重量计费 (charge_type = 2)

#### 3.4.1 公式说明

```
计算基数 = weight × quantity (总重量)

总运费 = first_fee + 续重费用

续重费用计算：
if (总重量 <= first_item) {
    续重费用 = 0
} else {
    超出重量 = 总重量 - first_item
    续重单位数 = ceil(超出重量 / additional_item)
    续重费用 = 续重单位数 × additional_fee
}
```

#### 3.4.2 计算示例

**配置示例：**
- first_item = 1.0 (首重1.0kg)
- first_fee = 10.00 (首重费用10元)
- additional_item = 0.5 (续重按0.5kg计)
- additional_fee = 3.00 (续重费用3元/0.5kg)

**计算案例：**

| 商品重量 | 购买数量 | 总重量 | 计算过程 | 运费结果 |
|---------|----------|--------|----------|----------|
| 0.8kg | 1件 | 0.8kg | 10.00 + 0 = 10.00 | 10.00元 |
| 0.8kg | 2件 | 1.6kg | 10.00 + ceil((1.6-1.0)/0.5) × 3.00 = 10.00 + 2 × 3.00 | 16.00元 |
| 1.2kg | 1件 | 1.2kg | 10.00 + ceil((1.2-1.0)/0.5) × 3.00 = 10.00 + 1 × 3.00 | 13.00元 |
| 2.0kg | 2件 | 4.0kg | 10.00 + ceil((4.0-1.0)/0.5) × 3.00 = 10.00 + 6 × 3.00 | 28.00元 |

**JavaScript实现代码：**
```javascript
function calculateByWeight(weight, quantity, config) {
    const { first_item, first_fee, additional_item, additional_fee } = config;
    
    const totalWeight = parseFloat(weight) * quantity;
    
    if (totalWeight <= first_item) {
        return parseFloat(first_fee);
    }
    
    const exceededWeight = totalWeight - first_item;
    const additionalUnits = Math.ceil(exceededWeight / additional_item);
    const additionalCost = additionalUnits * parseFloat(additional_fee);
    
    return parseFloat(first_fee) + additionalCost;
}
```

### 3.5 按体积计费 (charge_type = 3)

#### 3.5.1 公式说明

```
计算基数 = volume × quantity (总体积)

总运费 = first_fee + 续积费用

续积费用计算：
if (总体积 <= first_item) {
    续积费用 = 0
} else {
    超出体积 = 总体积 - first_item
    续积单位数 = ceil(超出体积 / additional_item)
    续积费用 = 续积单位数 × additional_fee
}
```

#### 3.5.2 计算示例

**配置示例：**
- first_item = 0.01 (首体积0.01m³)
- first_fee = 12.00 (首体积费用12元)
- additional_item = 0.005 (续体积按0.005m³计)
- additional_fee = 4.00 (续体积费用4元/0.005m³)

**计算案例：**

| 商品体积 | 购买数量 | 总体积 | 计算过程 | 运费结果 |
|---------|----------|--------|----------|----------|
| 0.008m³ | 1件 | 0.008m³ | 12.00 + 0 = 12.00 | 12.00元 |
| 0.008m³ | 2件 | 0.016m³ | 12.00 + ceil((0.016-0.01)/0.005) × 4.00 = 12.00 + 2 × 4.00 | 20.00元 |
| 0.012m³ | 1件 | 0.012m³ | 12.00 + ceil((0.012-0.01)/0.005) × 4.00 = 12.00 + 1 × 4.00 | 16.00元 |
| 0.015m³ | 3件 | 0.045m³ | 12.00 + ceil((0.045-0.01)/0.005) × 4.00 = 12.00 + 7 × 4.00 | 40.00元 |

**JavaScript实现代码：**
```javascript
function calculateByVolume(volume, quantity, config) {
    const { first_item, first_fee, additional_item, additional_fee } = config;
    
    const totalVolume = parseFloat(volume) * quantity;
    
    if (totalVolume <= first_item) {
        return parseFloat(first_fee);
    }
    
    const exceededVolume = totalVolume - first_item;
    const additionalUnits = Math.ceil(exceededVolume / additional_item);
    const additionalCost = additionalUnits * parseFloat(additional_fee);
    
    return parseFloat(first_fee) + additionalCost;
}
```

### 3.6 特殊情况处理

#### 3.6.1 包邮商品

```javascript
if (spu.is_free_shipping === 1) {
    return {
        freight: 0,
        message: "该商品享受包邮服务"
    };
}
```

#### 3.6.2 缺失重量/体积数据

```javascript
// 按重量计费但SKU重量为空
if (chargeType === 2 && (!sku.weight || sku.weight <= 0)) {
    throw new Error("按重量计费的商品必须设置有效重量");
}

// 按体积计费但SKU体积为空
if (chargeType === 3 && (!sku.volume || sku.volume <= 0)) {
    throw new Error("按体积计费的商品必须设置有效体积");
}
```

#### 3.6.3 运费精度处理

```javascript
// 统一保留2位小数
function formatFreight(amount) {
    return Math.round(amount * 100) / 100;
}
```

### 3.7 完整计算流程示例

#### 3.7.1 输入参数
```javascript
const input = {
    spuId: "123456789012345678",
    skuId: "987654321098765432", 
    provinceCode: "440000",
    cityCode: "440100",
    quantity: 3,
    weight: null,    // 使用SKU默认重量
    volume: null     // 使用SKU默认体积
};
```

#### 3.7.2 查询到的数据
```javascript
const spu = {
    id: "123456789012345678",
    name: "精品手机",
    goods_freight_template_id: "111111111111111111",
    is_free_shipping: 2  // 需要运费
};

const sku = {
    id: "987654321098765432",
    weight: 0.5,  // 0.5kg
    volume: 0.008 // 0.008m³
};

const template = {
    id: "111111111111111111",
    name: "标准运费模板",
    charge_type: 2  // 按重量计费
};

const matchedConfig = {
    id: "222222222222222222",
    first_item: 1.0,        // 首重1.0kg
    first_fee: 10.00,       // 首重费用10元
    additional_item: 0.5,   // 续重0.5kg
    additional_fee: 3.00,   // 续重费用3元
    is_default: 0
};
```

#### 3.7.3 计算过程
```javascript
// 1. 确定计算基数
const totalWeight = sku.weight * input.quantity; // 0.5 × 3 = 1.5kg

// 2. 应用运费公式
const firstFee = parseFloat(matchedConfig.first_fee); // 10.00
const exceededWeight = totalWeight - matchedConfig.first_item; // 1.5 - 1.0 = 0.5kg
const additionalUnits = Math.ceil(exceededWeight / matchedConfig.additional_item); // ceil(0.5/0.5) = 1
const additionalCost = additionalUnits * parseFloat(matchedConfig.additional_fee); // 1 × 3.00 = 3.00

const totalFreight = firstFee + additionalCost; // 10.00 + 3.00 = 13.00
```

#### 3.7.4 输出结果
```javascript
const result = {
    freight: 13.00,
    calculation_details: {
        charge_type: 2,
        charge_type_name: "按重量计费",
        total_weight: 1.5,
        first_weight: 1.0,
        first_fee: 10.00,
        exceeded_weight: 0.5,
        additional_units: 1,
        additional_fee_per_unit: 3.00,
        additional_cost: 3.00
    },
    config_info: {
        template_name: "标准运费模板",
        config_id: "222222222222222222",
        match_type: "PROVINCE"
    }
};
```

---

*运费计算系统设计文档完成*