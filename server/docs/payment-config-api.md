# 支付配置管理API文档

## 概述

支付配置管理API提供了完整的支付配置管理功能，支持微信支付、支付宝等多种支付方式的配置管理。所有的配置数据（包括证书文件）都存储在数据库中，确保数据的安全性和可管理性。

## 数据库设计

### 表结构：base.payment_configs

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGSERIAL | 主键ID |
| payment_type | VARCHAR(50) | 支付类型：wechat、alipay、unionpay |
| config_name | VARCHAR(100) | 配置名称 |
| enabled | SMALLINT | 是否启用：0-禁用，1-启用 |
| is_default | SMALLINT | 是否默认配置：0-否，1-是 |
| config_data | TEXT | 配置数据，JSON格式 |
| private_key | TEXT | 私钥文件，Base64编码 |
| public_key | TEXT | 公钥文件，Base64编码 |
| certificate | TEXT | 证书文件，Base64编码 |
| wechat_pay_cert | TEXT | 微信支付公钥证书，Base64编码 |
| alipay_public_key | TEXT | 支付宝公钥，Base64编码 |
| alipay_cert | TEXT | 支付宝根证书，Base64编码 |
| environment | VARCHAR(20) | 环境：sandbox、production |
| notify_url | VARCHAR(500) | 支付回调通知地址 |
| created_by | BIGINT | 创建者ID |
| updated_by | BIGINT | 更新者ID |
| created_at | BIGINT | 创建时间戳（毫秒） |
| updated_at | BIGINT | 更新时间戳（毫秒） |
| deleted_at | BIGINT | 删除时间戳（毫秒） |
| remark | VARCHAR(255) | 备注信息 |

## API接口

### 基础路径
```
/api/v1/master/payment-config
```

### 认证
所有接口都需要Bearer Token认证。

### 接口列表

#### 1. 获取所有支付配置
```http
GET /api/v1/master/payment-config
```

**查询参数：**
- `payment_type` (可选): 支付类型过滤
- `enabled` (可选): 启用状态过滤
- `page` (可选): 页码，默认1
- `limit` (可选): 每页数量，默认20

#### 2. 根据支付类型获取配置
```http
GET /api/v1/master/payment-config/type/{type}
```

**路径参数：**
- `type`: 支付类型（wechat、alipay、unionpay）

#### 3. 获取默认配置
```http
GET /api/v1/master/payment-config/default/{type}
```

#### 4. 根据ID获取配置详情
```http
GET /api/v1/master/payment-config/{id}
```

#### 5. 保存微信支付配置
```http
POST /api/v1/master/payment-config/wechat
```

**请求体示例：**
```json
{
  "enabled": true,
  "mch_id": "1234567890",
  "api_v3_key": "12345678901234567890123456789012",
  "mch_private_key": "-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----",
  "wechat_pay_cert": "-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----",
  "native_callback_url": "https://v4api.ioa.8080bl.com/api/v1/master/wechat-pay/notify",
  "config_name": "微信支付配置",
  "environment": "sandbox",
  "remark": "备注信息"
}
```

#### 6. 保存支付宝配置
```http
POST /api/v1/master/payment-config/alipay
```

**请求体示例：**
```json
{
  "enabled": true,
  "app_id": "2021000000000000",
  "private_key": "-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----",
  "alipay_public_key": "-----BEGIN PUBLIC KEY-----\n...\n-----END PUBLIC KEY-----",
  "config_name": "支付宝配置",
  "environment": "sandbox",
  "remark": "备注信息"
}
```

#### 7. 更新配置
```http
PUT /api/v1/master/payment-config/{id}
```

#### 8. 设置默认配置
```http
PUT /api/v1/master/payment-config/{id}/default
```

#### 9. 删除配置
```http
DELETE /api/v1/master/payment-config/{id}
```

#### 10. 获取统计信息
```http
GET /api/v1/master/payment-config/stats
```

#### 11. 测试配置连通性
```http
POST /api/v1/master/payment-config/{id}/test
```

## 特性

### 1. 证书文件数据库存储
- 所有证书文件都以Base64编码存储在数据库中
- 支持微信支付的私钥、公钥、证书文件
- 支持支付宝的私钥、公钥、根证书文件

### 2. 多环境支持
- 支持沙箱环境（sandbox）和生产环境（production）
- 可以为不同环境配置不同的参数

### 3. 默认配置管理
- 每种支付类型可以设置一个默认配置
- 默认配置不能被删除，需要先设置其他配置为默认

### 4. 数据验证
- 完整的参数验证，确保配置数据的有效性
- 启用状态下的必填字段验证

### 5. 软删除
- 支持软删除，删除的配置不会真正从数据库中移除
- 可以通过deleted_at字段进行恢复

## 使用示例

### 创建微信支付配置
```bash
curl -X POST http://localhost:4000/api/v1/master/payment-config/wechat \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "enabled": true,
    "mch_id": "1234567890",
    "api_v3_key": "your_api_v3_key",
    "mch_private_key": "your_private_key_content",
    "wechat_pay_cert": "your_cert_content",
    "native_callback_url": "https://your-domain.com/notify",
    "config_name": "生产环境微信支付",
    "environment": "production"
  }'
```

### 获取默认微信支付配置
```bash
curl -X GET http://localhost:4000/api/v1/master/payment-config/default/wechat \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 注意事项

1. **证书安全性**：虽然证书存储在数据库中，但建议定期轮换证书
2. **环境隔离**：生产环境和测试环境的配置应该分开管理
3. **权限控制**：支付配置管理需要管理员权限
4. **备份恢复**：重要的支付配置应该定期备份

## 测试

项目包含完整的API测试脚本：
```bash
node test_payment_config_api.js
```

测试脚本会验证所有接口的功能，包括：
- 配置的创建、更新、删除
- 默认配置的设置和获取
- 参数验证
- 权限控制

## 扩展性

系统设计支持未来扩展更多支付方式：
- 银联支付（unionpay）
- PayPal
- Stripe
- 其他第三方支付平台

只需要在PaymentConfigService中添加对应的保存方法，并在DTO中添加验证规则即可。
