# 路由设计方案

## 一、路由注册模式

项目采用两种主要的路由注册模式，根据模块的特性和需求选择合适的方式：

### 1. 路由实例导出模式
适用于标准的 CRUD 操作模块，如用户管理、菜单管理和平台管理等。

**实现方式**：
```javascript
// 模块路由文件
const express = require('express');
const router = express.Router();

// 路由定义...

module.exports = router;
module.exports.paths = { /* Swagger 路径定义 */ };

// 主应用中注册
app.use('/api/v1/master/system/user', userRoutes);
```

**特点**：
- 简单直接，适合标准 RESTful API
- 路由定义和注册分离清晰
- 适合无特殊中间件需求的模块

### 2. 函数式路由注册模式
适用于需要特殊中间件配置或动态路由生成的模块，如文件上传、第三方集成等。

**实现方式**：
```javascript
// 模块路由文件
function registerRoutes(app, prisma) {
  // 动态创建路由
  const uploadRouter = createUploadRouter(prisma);
  app.use('/api/v1/path', uploadRouter);
}

module.exports = registerRoutes;

// 主应用中注册
integrationRoutes(app, prisma);
```

**特点**：
- 更灵活，可在注册时传入依赖（如 prisma 实例）
- 支持动态路由生成和配置
- 适合需要特殊中间件（如 multer）的模块
- 适合有复杂子路由结构的模块

## 二、路由组织结构

### 1. 模块化组织
- 每个业务模块有独立的路由目录和文件
- 大型模块（如集成模块）有统一的路由入口文件（index.js）
- 子模块有各自的路由文件（如 WechatRoute.js, AliyunRoute.js）

### 2. 路由前缀规范
- API 版本前缀：`/api/v1`
- 系统模块前缀：`/master/system`
- 平台管理前缀：`/master/platform`
- 子模块前缀：如 `/integration/wechat`, `/platform/channel`

## 三、认证与权限控制

### 1. JWT 认证
- 使用 `RouterConfig.authRoute()` 创建受 JWT 保护的路由
- 公共路由和认证路由分开定义

```javascript
const protectedRouter = RouterConfig.authRoute(router);
protectedRouter.get('/', controller.list.bind(controller));
```

### 2. 特殊认证处理
- 文件上传等特殊路由可能需要自定义认证逻辑
- 通过函数式路由注册灵活配置认证中间件

## 四、Swagger 文档集成

### 1. 文档定义
- 每个路由文件中包含详细的 Swagger 注解
- 路由文件导出 `paths` 对象，包含 API 路径和文档

```javascript
module.exports.paths = {
  '/api/v1/master/system/user': {
    get: { /* API 定义 */ },
    post: { /* API 定义 */ }
  }
};
```

### 2. 文档合并
- 主应用程序中动态合并所有模块的 Swagger 路径
```javascript
if (userRoutes.paths) {
  app.swaggerPaths = { ...app.swaggerPaths, ...userRoutes.paths };
}
```

## 五、控制器绑定

路由与控制器方法的绑定采用一致的模式：

```javascript
// 创建控制器实例
const controller = new Controller(prisma);

// 绑定控制器方法（使用 bind 确保 this 上下文正确）
router.get('/', controller.list.bind(controller));
```

## 六、路由文件模板

### 1. 标准路由文件模板（路由实例导出模式）

```javascript
/**
 * 模块路由
 */
const express = require('express');
const router = express.Router();
const Controller = require('../controllers/Controller');
const { prisma } = require('../../../../../core/database/prisma');
const RouterConfig = require('../../../../../core/routes/RouterConfig');

// 创建控制器实例
const controller = new Controller(prisma);

// 创建受JWT保护的路由
const protectedRouter = RouterConfig.authRoute(router);

/**
 * @swagger
 * /api/v1/path:
 *   get:
 *     tags: [标签]
 *     summary: 接口描述
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功
 */
protectedRouter.get('/', controller.list.bind(controller));

// 导出路由实例
module.exports = router;

// 导出 paths 对象供动态扫描
module.exports.paths = {
  '/api/v1/path': {
    get: {
      tags: ['标签'],
      summary: '接口描述',
      responses: {
        200: { description: '成功' }
      }
    }
  }
};
```

### 2. 函数式路由注册模板（适用于特殊模块）

```javascript
/**
 * 模块路由入口
 */
const express = require('express');
const subModuleRoutes = require('./SubModuleRoute');

/**
 * 注册模块路由
 * @param {Object} app Express应用实例
 * @param {Object} prisma Prisma客户端实例
 */
function registerModuleRoutes(app, prisma) {
  // 注册子模块路由
  app.use('/api/v1/path/submodule', subModuleRoutes);
  
  // 合并Swagger文档
  if (subModuleRoutes.paths) {
    app.swaggerPaths = { ...app.swaggerPaths, ...subModuleRoutes.paths };
  }
  
  console.log('模块路由注册成功');
}

module.exports = registerModuleRoutes;
```

## 七、子模块路由注册

### 1. 子模块路由注册方式

项目中存在两种子模块路由注册方式：

#### (1) 通过 ModuleManager 自动注册（推荐）

适用于独立性强、需要单独管理生命周期的子模块。

**实现步骤**：
1. 在子模块根目录创建 `module.json` 配置文件：
```json
{
  "name": "子模块名称",
  "version": "1.0.0",
  "description": "子模块描述",
  "baseUrl": "/api/v1/master/path",
  "enabled": true,
  "swagger": {
    "tags": ["标签名称"]
  }
}
```

2. 创建子模块入口文件 `index.js`，继承 BaseModule：
```javascript
const BaseModule = require('../../../core/module/BaseModule');

class SubModule extends BaseModule {
  async initRoutes() {
    const routes = require('./routes/Route');
    this.router.use('/', routes);
  }
}

module.exports = SubModule;
```

#### (2) 在父模块路由文件中手动注册（简便方式）

适用于与父模块紧密关联的子模块。

**实现步骤**：
1. 在父模块的主路由文件中引入子模块路由：
```javascript
// 子模块路由
const subModuleRoutes = require('../subModule/routes/SubModuleRoute');

// 注册子模块路由
router.use('/sub-module', subModuleRoutes);
```

### 2. 子模块路由注册常见问题及解决方案

#### (1) 路由404问题

**问题表现**：API 路径返回 404，找不到路由。

**解决方案**：
- 检查路由注册路径是否与 Swagger 文档中定义的路径一致
- 确保路由文件正确导出 `router` 对象
- 检查父模块是否正确引入并注册了子模块路由

#### (2) 路由重复注册问题

**问题表现**：服务器启动报错，提示路由重复注册。

**解决方案**：
- 检查是否在多处注册了同一路由
- 避免在同一文件中多次引入相同的路由模块
- 确保路由路径唯一，不与其他模块冲突

#### (3) 模块加载失败问题

**问题表现**：服务器启动时，模块未被 ModuleManager 加载。

**解决方案**：
- 确保 `module.json` 文件格式正确，必要字段齐全
- 检查模块入口文件是否正确继承 BaseModule 并实现必要方法
- 避免同时存在 `module.js` 和 `module.json` 配置文件，以免造成冲突

## 八、最佳实践建议

1. **根据模块特性选择路由注册模式**：
   - 标准 CRUD 操作模块 → 路由实例导出模式
   - 文件上传、动态配置等特殊模块 → 函数式路由注册模式

2. **保持 Swagger 文档的一致性**：
   - 所有 API 都应有完整的 Swagger 注解
   - 导出统一格式的 paths 对象供主应用合并

3. **路由前缀管理**：
   - 在模块配置中统一定义路由前缀
   - 避免在多处硬编码路径前缀

4. **认证中间件统一应用**：
   - 使用 RouterConfig 工具类统一管理认证
   - 特殊认证需求应在路由注册时明确处理

5. **路由注册顺序**：
   - 先注册静态资源路由
   - 然后注册API路由
   - 最后注册错误处理中间件

## 八、模块化路由注册与排错指南

### 1. 模块路由注册方式

在项目中，有两种主要的模块路由注册方式：

#### 方式一：通过 ModuleManager 自动注册（推荐）

这种方式适用于符合标准模块结构的业务模块：

```javascript
// 模块入口文件 (index.js)
const BaseModule = require('../../../core/module/BaseModule');

class MyModule extends BaseModule {
  async initRoutes() {
    // 加载子模块路由
    const subRoutes = require('./sub/routes/SubRoute');
    
    // 注册到模块路由器
    this.router.use('/sub', subRoutes);
    
    console.log(`模块路由已初始化，前缀：${this.config.routes.prefix}`);
  }
}

module.exports = MyModule;
```

```json
// 模块配置文件 (module.json)
{
  "name": "myModule",
  "description": "我的模块",
  "enabled": true,
  "routes": {
    "prefix": "/api/v1/master/my-module"
  }
}
```

**优点**：
- 自动处理路由前缀
- 统一的模块生命周期管理
- 支持模块间依赖关系

#### 方式二：在主路由文件中手动注册

对于不符合标准模块结构或需要特殊处理的路由：

```javascript
// 在 apps/master/routes/index.js 中

// 直接引用子模块路由
const mySubRoutes = require('../myModule/sub/routes/SubRoute');

// 手动注册路由
router.use('/my-module/sub', mySubRoutes);
```

**优点**：
- 更灵活，可以处理特殊情况
- 适合临时解决方案或特殊需求

### 2. 常见问题与排错指南

#### 问题一：404 Not Found 错误

**可能原因**：
- 模块未被正确注册或加载
- 路由前缀配置错误
- 路由文件未正确导出路由实例

**排查步骤**：
1. 检查模块配置文件 `module.json` 是否存在且 `enabled` 设置为 `true`
2. 确认模块入口文件 `index.js` 是否正确继承 `BaseModule` 并实现 `initRoutes` 方法
3. 检查路由前缀是否与请求路径匹配
4. 查看服务器启动日志，确认模块是否被成功加载

**解决方案示例**：
如果模块结构正确但路由仍无法访问，可以尝试在主路由文件中直接注册：

```javascript
// 在 apps/master/routes/index.js 中
const channelRoutes = require('../platformManagement/channel/routes/ChannelRoute');
router.use('/platform/channel', channelRoutes);
```

#### 问题二：路由中间件错误

**可能原因**：
- 路由引用了未定义或为 `undefined` 的中间件
- 中间件函数未正确绑定 `this` 上下文

**排查步骤**：
1. 检查控制台错误信息，特别是 `Router.use() requires a middleware function but got undefined` 类型的错误
2. 确认所有引用的路由模块都正确导出了 Express 路由实例
3. 检查控制器方法是否正确绑定 `this` 上下文（使用 `.bind(controller)` 方法）

**解决方案示例**：
确保路由文件正确导出路由实例：

```javascript
const express = require('express');
const router = express.Router();

// 路由定义...

module.exports = router; // 确保导出的是路由实例
```

### 3. 最佳实践

1. **遵循标准模块结构**：
   - 每个模块有完整的 `index.js` 和 `module.json`
   - 模块入口类继承 `BaseModule`
   - 路由文件位于 `routes` 目录下

2. **路由调试技巧**：
   - 添加日志输出路由注册信息
   - 使用 `curl -v` 命令详细查看请求和响应
   - 检查服务器启动日志中的模块加载信息

3. **临时解决方案**：
   - 当标准模块加载机制出现问题时，可以临时在主路由文件中直接注册路由
   - 解决紧急问题后，应尽快回归到标准模块结构
