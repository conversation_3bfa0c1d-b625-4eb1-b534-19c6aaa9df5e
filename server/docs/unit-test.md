# 单元测试规范

## 1. 目录结构

每个模块都有独立的测试目录，例如：

```
server/
├── apps/
│   ├── master/
│   │   ├── __tests__/                    # 测试目录
│   │   │   ├── unit/                     # 单元测试
│   │   │   │   ├── controllers/          # 控制器测试
│   │   │   │   │   └── UserManagementController.test.js
│   │   │   │   ├── services/            # 服务层测试
│   │   │   │   │   └── UserManagementService.test.js
│   │   │   │   └── models/             # 模型层测试
│   │   │   │       └── UserModel.test.js
│   │   │   └── fixtures/               # 测试数据
│   │   │       └── users.json
│   ├── merchant/
│   │   ├── __tests__/
│   │   │   ├── unit/
│   │   │   └── integration/
│   └── supplier/
│       ├── __tests__/
│           ├── unit/
│           └── integration/
```

### 1.1 模块独立性
- 每个模块（master、merchant、supplier）的测试代码都位于其自己的 `__tests__` 目录下
- 可以单独运行某个模块的测试：
  ```bash
  # 运行 master 模块的测试
  npm run test:unit -- apps/master
  
  # 运行 merchant 模块的测试
  npm run test:unit -- apps/merchant
  ```

### 1.2 模块间依赖
- 每个模块的测试应该是独立的，不应该依赖其他模块的测试
- 如果模块间有依赖关系，应该使用 mock 模拟依赖

## 2. 命名规范

### 2.1 文件命名
- 测试文件必须以 `.test.js` 结尾
- 测试文件名应与被测试文件名保持一致
- 测试数据文件使用复数形式，如 `users.json`

### 2.2 测试用例命名
```javascript
describe('UserManagementController', () => {
  describe('list()', () => {
    it('应该返回用户列表', async () => {
      // 测试正常获取列表场景
    });
    
    it('应该处理查询错误', async () => {
      // 测试数据库查询失败场景
    });
  });

  describe('create()', () => {
    it('应该成功创建用户', async () => {
      // 测试正常创建用户场景
    });
    
    it('应该处理创建错误', async () => {
      // 测试用户已存在等错误场景
    });
  });

  describe('update()', () => {
    it('应该成功更新用户', async () => {
      // 测试正常更新用户场景
    });
  });

  describe('delete()', () => {
    it('应该成功删除用户', async () => {
      // 测试正常删除用户场景
    });
  });

  describe('login()', () => {
    it('应该成功登录', async () => {
      // 测试正常登录场景
    });
  });
});
```

## 3. 测试结构规范

### 3.1 基本结构
```javascript
const { describe, it, beforeEach, afterEach } = require('jest');
const UserController = require('../controllers/UserManagementController');
const UserService = require('../services/UserManagementService');

describe('模块名称', () => {
  // 1. 变量声明
  let controller;
  let mockService;
  
  // 2. 测试准备
  beforeEach(() => {
    // 初始化测试环境
  });
  
  // 3. 测试清理
  afterEach(() => {
    // 清理测试环境
  });
  
  // 4. 测试用例
  describe('方法名()', () => {
    it('应该完成特定功能', async () => {
      // 准备测试数据
      const testData = {};
      
      // 执行测试
      const result = await controller.method(testData);
      
      // 验证结果
      expect(result).toBe(expected);
    });
  });
});
```

### 3.2 测试分组
1. **正向测试**：验证正常流程
2. **异常测试**：验证错误处理
3. **边界测试**：验证边界条件
4. **性能测试**：验证性能要求

## 4. 测试覆盖率要求

### 4.1 覆盖率指标
- **语句覆盖率**：>= 85%
- **分支覆盖率**：>= 80%
- **函数覆盖率**：>= 90%
- **行覆盖率**：>= 85%

### 4.2 关键模块要求
- **控制器层**：100% 测试覆盖
- **服务层**：90% 测试覆盖
- **模型层**：85% 测试覆盖

## 5. Mock 规范

### 5.1 依赖注入和 Mock
```javascript
// 1. Mock 依赖模块
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn()
}));

jest.mock('../services/UserManagementService');

// 2. 初始化 Mock 对象
beforeEach(() => {
  // 清理所有 mock
  jest.clearAllMocks();
  
  // 初始化 mock prisma
  mockPrisma = {
    user: {
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      findUnique: jest.fn()
    }
  };
  PrismaClient.mockImplementation(() => mockPrisma);
  
  // 初始化 mock service
  mockService = {
    list: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    getById: jest.fn(),
    login: jest.fn()
  };
  UserService.mockImplementation(() => mockService);
  
  // 创建被测试的实例
  controller = new UserController(prisma);
});
```

### 5.2 Mock 数据和响应对象
```javascript
// 1. 测试数据定义
const mockUser = {
  id: 1,
  username: 'testuser',
  email: '<EMAIL>'
};

// 2. 错误数据定义
const mockError = new Error('测试错误');

// 3. Express 响应对象模拟
const mockRes = {
  status: jest.fn().mockReturnThis(),  // 支持链式调用
  json: jest.fn()
};

// 4. Express 请求对象模拟
const mockReq = {
  body: { username: 'testuser' },
  query: { page: 1, pageSize: 10 },
  params: { id: 1 }
};
```

## 6. 依赖注入测试规范

### 6.1 控制器测试
```javascript
// 1. 导出类而不是实例
class UserController {
  // ...
}
module.exports = UserController;  // 导出类

// 2. 在路由中创建实例
const UserController = require('../controllers/UserController');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const userController = new UserController(prisma);

// 3. 在测试中模拟依赖
jest.mock('@prisma/client');
const controller = new UserController(mockPrisma);
```

### 6.2 服务层测试
```javascript
// 1. 服务类定义
class UserService {
  constructor(prisma) {
    this.prisma = prisma;
  }
}

// 2. 测试设置
const mockPrisma = {
  user: {
    findMany: jest.fn(),
    create: jest.fn()
  }
};
const service = new UserService(mockPrisma);
```

### 6.3 依赖注入原则
1. **显式依赖**
   - 通过构造函数注入依赖
   - 避免在类内部直接创建依赖实例

2. **依赖隔离**
   - 使用接口而不是具体实现
   - 便于在测试中替换为 mock 对象

3. **单一职责**
   - 每个类只依赖必要的对象
   - 便于测试和维护

## 7. 异步测试规范

### 7.1 Promise 处理
```javascript
it('应该异步处理数据', async () => {
  // 准备
  const mockData = { id: 1 };
  mockService.findById.mockResolvedValue(mockData);
  
  // 执行
  const result = await controller.getById(1);
  
  // 验证
  expect(result).toEqual(mockData);
});
```

### 7.2 错误处理
```javascript
it('应该处理异步错误', async () => {
  // 准备
  const mockError = new Error('数据库错误');
  mockService.findById.mockRejectedValue(mockError);
  
  // 执行和验证
  await expect(controller.getById(1))
    .rejects
    .toThrow(mockError);
});
```

## 8. 测试运行

### 8.1 命令
```bash
# 运行所有测试
npm test

# 运行特定模块测试
npm test -- apps/master

# 运行带覆盖率报告
npm test -- --coverage
```

### 8.2 配置文件
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'node',
  testMatch: ['**/__tests__/**/*.test.js'],
  collectCoverageFrom: [
    'apps/**/controllers/**/*.js',
    'apps/**/services/**/*.js',
    'apps/**/models/**/*.js'
  ],
  coverageThreshold: {
    global: {
      statements: 85,
      branches: 80,
      functions: 90,
      lines: 85
    }
  }
};
```
