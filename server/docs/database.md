# 数据库设计规范

## 1. 模块化设计

### 1.1 Schema 隔离
使用 PostgreSQL 的 Schema 特性实现模块隔离，每个业务模块使用独立的 Schema：

- `master`: 系统管理模块
  - 用户管理
  - 角色权限
  - 部门组织
  - 系统配置

- `merchant`: 商户模块
  - 商户管理
  - 商品管理
  - 订单管理

- `supplier`: 供应商模块
  - 供应商管理
  - 采购管理
  - 库存管理

### 1.2 模型定义规范

#### 1.2.1 注释规范

1. **模型注释**
   ```prisma
   /// 用户信息表，存储系统用户数据
   model User {
     // 字段定义...
   }
   ```

2. **字段注释**
   ```prisma
   model User {
     // 主键
     id        BigInt    @id    /// @db.Comment('用户ID，16位雪花算法，系统自动生成')
     
     // 基本信息
     username  String    @unique /// @db.Comment('用户名，必填，唯一，用于登录')
     status    Int       @default(1) /// @db.Comment('状态：1-正常，0-禁用')
   }
   ```

3. **注释格式要求**
   - 模型注释：
     - 使用 `///` 开头
     - 包含表的中文名称和用途

   - 字段注释：
     - 使用 `/// @db.Comment('注释内容')`
     - 必须包含：
       - 中文名称
       - 类型说明
       - 是否必填
       - 约束说明（如果有）
       - 枚举值说明（如果有）
     - 外键字段必须注明关联的表和字段

   - 分组注释：
     - 使用 `// 分组名称` 对相关字段进行分组
     - 常见分组：主键、基本信息、状态信息、审计字段等

4. **注释同步到数据库**
   - 模型注释不会同步到数据库，只会保留在 Prisma Schema 中
   - 字段注释必须使用 `@db.Comment` 才会同步到数据库
   - 分组注释只用于代码组织，不会同步到数据库

#### 1.2.2 命名规范

1. **表命名**
   - 使用 PascalCase 命名法（首字母大写）
   - 使用单数形式表示实体
   - 表名应清晰表达业务含义

示例：
```sql
-- master schema（系统管理）
User                    -- 用户表
Role                    -- 角色表
Department              -- 部门表
Permission              -- 权限表
RolePermission         -- 角色权限关联表
UserRole               -- 用户角色关联表

-- merchant schema（商户模块）
Merchant               -- 商户表
Product                -- 商品表
Order                  -- 订单表
OrderItem              -- 订单项表

-- supplier schema（供应商模块）
Supplier               -- 供应商表
Purchase               -- 采购单表
Inventory              -- 库存表
```

#### 字段命名
- 使用 snake_case 命名法
- 主键统一使用 `id`，类型为 BigInt（16位雪花算法）
- 外键格式：`实体名称_id`，如 `dept_id`、`role_id`
- 状态字段：`status`，使用 Int 类型
- 时间戳字段：使用 BigInt 类型，如 `login_time`

示例：
```prisma
model User {
  // 主键 (16位雪花算法)
  id            BigInt    @id                           // 用户ID
  
  // 基本信息
  user_type     Int       @default(60)                 // 用户类型：100-主账户，60-子账号
  username      String    @unique                      // 用户名
  password      String                                 // 密码
  nickname      String?                                // 用户昵称
  phone         String?   @db.VarChar(11)             // 手机号
  email         String?   @unique                      // 邮箱
  avatar        String?                                // 头像地址
  
  // 组织架构
  dept_id       BigInt?                                // 部门ID
  role_id       BigInt?                                // 角色ID
  
  // 状态信息
  status        Int       @default(0)                 // 状态：0-正常，1-停用
  
  // 登录信息
  login_ip      String?   @db.VarChar(45)             // 最后登录IP
  login_time    BigInt?                               // 最后登录时间戳（毫秒）
  login_token   String?                                // 登录token
}
```

### 1.3 字段类型规范

#### 1.3.1 通用类型
- **ID**: 
  - 类型：`BigInt`
  - 说明：使用 16 位雪花算法
  - 注释示例：`// 用户ID，16位雪花算法，系统自动生成`

- **字符串**: 
  - 类型：`String`
  - 变体：
    - 常规文本: 默认 String
    - 定长文本: `@db.VarChar(n)`
  - 注释示例：`// 用户名，必填，唯一，用于登录`
- 数字: 
  - 整数: `Int`
  - 大整数: `BigInt`
  - 浮点数: `Float`
  - 精确数值: `Decimal`
- 布尔值: `Boolean`
- 时间戳: `BigInt` - 毫秒级时间戳
- 枚举值: `Int` - 使用整数常量

#### 字段约束
- 主键: `@id`
- 唯一约束: `@unique`
- 默认值: `@default(value)`
- 可空字段: `String?`、`Int?`、`BigInt?` 等
- 关联关系: `@relation`

### 1.4 实体关系

使用 Prisma 的关系映射来定义实体之间的关系：

```prisma
model User {
  id        BigInt     @id
  dept_id   BigInt?
  role_id   BigInt?
  department Department? @relation(fields: [dept_id], references: [id])
  role      Role?       @relation(fields: [role_id], references: [id])
}

model Department {
  id    BigInt  @id
  name  String
  users User[]
}

model Role {
  id    BigInt  @id
  name  String
  users User[]
}
```

### 1.5 数据完整性

1. **主键**
   - 使用 16 位雪花算法生成的 BigInt 作为主键
   - 在实体中通过 `@id` 标记

2. **外键**
   - 使用 `@relation` 定义关联关系
   - 外键字段可为空时使用可空类型（BigInt?）

3. **唯一约束**
   - 使用 `@unique` 标记唯一字段
   - 可以是单字段或多字段组合

## 2. 示例 Schema

### 2.1 系统管理模块

```prisma
// master schema

/// 用户信息表，存储系统用户数据
model User {
  // 主键
  id            BigInt      @id                         // 用户ID，16位雪花算法，系统自动生成
  
  // 基本信息
  username      String      @unique                    // 用户名，必填，唯一，用于登录
  password      String                                 // 密码，必填，加密存储
  email         String?     @unique                    // 邮箱，唯一，可用于找回密码
  
  // 状态信息
  status        Int         @default(1)                // 状态：1-正常，0-禁用
  
  // 组织架构
  dept_id       BigInt?                                // 部门ID，关联 Department 表的 id 字段
  role_id       BigInt?                                // 角色ID，关联 Role 表的 id 字段
  department    Department? @relation(fields: [dept_id], references: [id])
  role          Role?       @relation(fields: [role_id], references: [id])
  
  // 审计字段
  created_at    DateTime    @default(now())            // 创建时间
  updated_at    DateTime    @updatedAt                 // 更新时间
  created_by    String?                                // 创建人
  updated_by    String?                                // 更新人
  deleted_at    DateTime?                              // 删除时间，空表示未删除
}

/// 部门信息表，存储组织架构信息
model Department {
  // 主键
  id          BigInt    @id                         // 部门ID，16位雪花算法
  
  // 基本信息
  name        String                                // 部门名称，必填
  parent_id   BigInt?                               // 父部门ID，关联本表 id 字段，空表示一级部门
  order_num   Int       @default(0)                 // 显示顺序，默认 0
  
  // 关联字段
  users       User[]                                // 部门下的用户列表
  
  // 审计字段
  created_at  DateTime  @default(now())             // 创建时间
  updated_at  DateTime  @updatedAt                  // 更新时间
  created_by  String?                               // 创建人
  updated_by  String?                               // 更新人
  deleted_at  DateTime?                             // 删除时间，空表示未删除
}

/// 角色信息表，存储系统角色定义
model Role {
  // 主键
  id          BigInt    @id                         // 角色ID，16位雪花算法
  
  // 基本信息
  name        String                                // 角色名称，必填
  code        String    @unique                     // 角色编码，必填，唯一，用于标识角色
  status      Int       @default(1)                 // 状态：1-正常，0-禁用
  
  // 关联字段
  users       User[]                                // 拥有该角色的用户列表
  
  // 审计字段
  created_at  DateTime  @default(now())             // 创建时间
  updated_at  DateTime  @updatedAt                  // 更新时间
  created_by  String?                               // 创建人
  updated_by  String?                               // 更新人
  deleted_at  DateTime?                             // 删除时间，空表示未删除
}
```

### 2.2 商户模块

```prisma
// merchant schema

model Merchant {
  id            BigInt    @id
  name          String
  code          String    @unique
  status        Int       @default(0)
  products      Product[]
  orders        Order[]
}

model Product {
  id            BigInt    @id
  name          String
  price         Decimal
  merchant_id   BigInt
  merchant      Merchant  @relation(fields: [merchant_id], references: [id])
  order_items   OrderItem[]
}

model Order {
  id            BigInt      @id
  order_no      String      @unique
  merchant_id   BigInt
  status        Int         @default(0)
  total_amount  Decimal
  merchant      Merchant    @relation(fields: [merchant_id], references: [id])
  items         OrderItem[]
}

model OrderItem {
  id            BigInt    @id
  order_id      BigInt
  product_id    BigInt
  quantity      Int
  price         Decimal
  order         Order     @relation(fields: [order_id], references: [id])
  product       Product   @relation(fields: [product_id], references: [id])
}
```
```
