const fs = require('fs');
const path = require('path');

// 读取各部分 JSON 文件
const part1 = require('./spider-api-part1.json');
const part2 = require('./spider-api-part2.json');
const part3 = require('./spider-api-part3.json');
const part4 = require('./spider-api-part4.json');

// 合并 paths
const mergedPaths = {
  ...part1.paths,
  ...part2.paths,
  ...part3.paths,
  ...part4.paths
};

// 创建完整的 API 文档
const mergedApi = {
  openapi: part1.openapi,
  info: part1.info,
  servers: part1.servers,
  paths: mergedPaths,
  components: part4.components
};

// 写入合并后的 JSON 文件
fs.writeFileSync(
  path.join(__dirname, 'spider-api.json'),
  JSON.stringify(mergedApi, null, 2)
);

console.log('API 文档合并完成，已保存到 spider-api.json');
