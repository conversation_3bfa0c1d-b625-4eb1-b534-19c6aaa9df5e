// https://nuxt.com/docs/api/configuration/nuxt-config
const proxyPrefix = process.env.VITE_APP_PROXY_PREFIX
const tokenPrefix =  process.env.VITE_APP_TOKEN_PREFIX
export default defineNuxtConfig({
  compatibilityDate: '2024-11-01',
  devServer: {
    host: '0.0.0.0', // 绑定到所有网络接口
    port: 3000 // 指定端口
  },
  // 开发环境性能优化
  experimental: {
    payloadExtraction: false, // 禁用 payload 提取以提高开发性能
    inlineSSRStyles: false, // 禁用内联 SSR 样式
  },
  css: ['./style/skin.less', './style/index.css', './style/global.less', './assets/css/iconfont.css'],
  alias: {
    "@cps": "/components",
  },
  runtimeConfig:{
    public:{
      VITE_APP_TOKEN_PREFIX: tokenPrefix,
      VITE_APP_PROXY_PREFIX: proxyPrefix,
      // 添加API基础URL配置
      VITE_API_BASE_URL: process.env.VITE_API_BASE_URL || 'https://v4api.ioa.8080bl.com',
      // 添加应用标题
      NUXT_APP_TITLE: process.env.NUXT_APP_TITLE || '默认标题',
      // 添加应用基础路径
      NUXT_APP_BASE: process.env.NUXT_APP_BASE || '',
      NUXT_PUBLIC_TEST: process.env.NUXT_PUBLIC_TEST || '',
    }
  },
  ssr: false,
  devtools: {enabled: false},
  modules: [
    "nuxt-lodash",
    "dayjs-nuxt",
    // "nuxt-echarts", // 临时注释掉，稍后手动集成
    "nuxt-monaco-editor",
    "@pinia/nuxt",
    "@nuxtjs/tailwindcss",
  ],
  plugins: [
    '~/plugins/arco-design.js',
    '~/plugins/message.js',
    '~/plugins/global-components.ts',
    '~/plugins/directives.js',
    '~/plugins/api-config.js' // 添加API配置插件
  ],
  components: {
    global: true,
    dirs: [
      '~/components/base',
      '~/components/master'
    ]
  },
  dayjs: {
    defaultLocale: 'zh-cn',
    plugins: ['relativeTime'],
  },
  build: {
    transpile: ['jsencrypt'],
  },
  nitro: {
    esbuild: {
      options: {
        target: 'esnext',
      },
    },
    externals: {
      inline: ['jsencrypt'],
    },
  },
  vite: {
    define: {
      'process.env.DEBUG': false,
      'window.global': 'window',
    },
    resolve: {
      alias: {
        jsencrypt: 'jsencrypt/lib/index.js',
      },
    },
    server: {
      // 优化文件监听 - 关闭轮询，使用原生文件系统事件
      watch: {
        usePolling: false, // 改为 false，使用原生文件系统事件
        ignored: [
          '**/node_modules/**',
          '**/.git/**',
          '**/dist/**',
          '**/coverage/**',
          '**/logs/**',
          '**/public/**',
          '**/server/node_modules/**', // 忽略服务端 node_modules
          '**/server/logs/**',
          '**/server/backups/**'
        ]
      },
      // 启用 HMR 优化
      hmr: {
        overlay: false // 关闭错误覆盖层以提高性能
      },
      proxy: {
        '/api': {
          target: process.env.VITE_APP_PROXY_PREFIX || 'https://v4api.ioa.8080bl.com',
          changeOrigin: true,
          ws: true,
          rewrite: path => path
        }
      }
    },
    // 优化依赖预构建
    optimizeDeps: {
      include: [
        '@iconify/vue',
        'echarts',
        'echarts/core',
        'echarts/charts',
        'echarts/components',
        'echarts/renderers',
        'vue',
        'vue-router',
        '@arco-design/web-vue',
        'axios',
        'dayjs',
        'lodash-es',
        'pinia'
      ],
      exclude: ['jsencrypt'],
      // 强制预构建某些依赖
      force: true
    },
    build: {
      // 开发环境构建优化
      target: 'esnext',
      minify: false, // 开发环境不压缩
      rollupOptions: {
        external: ['@iconify/vue'],
        onwarn(warning, warn) {
          if (warning.code === 'EVAL' && warning.id?.includes('jsencrypt')) {
            return;
          }
          warn(warning);
        },
      },
    },
    // 添加缓存配置
    cacheDir: 'node_modules/.vite'
  }
})