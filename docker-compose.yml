version: '3.8'

services:
  # 前端服务 - 完全独立的容器
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: julingcloud-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - API_BASE_URL=http://backend:4000
    networks:
      - julingcloud-network
    volumes:
      - frontend_uploads:/app/public/uploads

  # 后端服务 - 完全独立的容器
  backend:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: julingcloud-backend
    restart: unless-stopped
    ports:
      - "4000:4000"
    environment:
      - NODE_ENV=production
    networks:
      - julingcloud-network
    volumes:
      - backend_uploads:/app/uploads
      - backend_public:/app/public

networks:
  julingcloud-network:
    driver: bridge

volumes:
  frontend_uploads:
  backend_uploads:
  backend_public:
