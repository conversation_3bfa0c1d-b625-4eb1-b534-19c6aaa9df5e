# 聚灵云4.0开发标准

## 技术栈
- **UI框架**: mineadmin
- **前后端**: nuxtjs
- **数据库**: PostgreSQL
- **API文档**: OpenAPI/Swagger

## 服务端设计
- [服务端架构设计](server/docs/architecture.md)
- [Swagger文档设计](server/docs/swagger.md)
- [单元测试标准](server/docs/unit-test.md)

## 单元测试

### 模块化测试
每个模块（master、merchant、supplier）都有独立的测试目录，可以单独运行和维护。

### 技术栈
- **框架**: Jest
- **目录结构**: 每个模块下的 `__tests__/unit/` 和 `__tests__/integration/`
- **依赖注入**: 使用构造函数注入依赖，便于测试和维护
- **Mock**: 使用 Jest 的 mock 功能模拟外部依赖

### 测试覆盖率要求
- 控制器层: 100%
- 服务层: 90%
- 模型层: 85%

### 运行测试
```bash
# 运行所有测试
npm run test:unit

# 运行特定模块的测试
npm run test:unit -- apps/master    # master 模块测试
npm run test:unit -- apps/merchant  # merchant 模块测试
npm run test:unit -- apps/supplier  # supplier 模块测试

# 运行带覆盖率的测试
npm run test:unit -- --coverage
```

### 测试文档
详细的测试规范和示例请参考 [server/docs/unit-test.md](server/docs/unit-test.md)

## 产品结构图
https://www.kdocs.cn/view/l/cjus4BfABsGS

---

## 文件目录结构

### 前端目录:
- **pages/**: 包含所有页面, 如 `index.vue` (官网和H5)、`merchant/` (商家入口)、`master/` (后台入口)。
- **components/**: UI组件, 分为 `base/` (通用)、`merchant/` (商家专用)、`master/` (后台专用)。
- **layouts/**: 页面布局文件。
- **store/**: 状态管理, 如 `merchant.js` (商家状态)、`master.js` (后台状态)。 *(建议使用 Pinia，并按模块划分，如 `store/merchant/orders.ts`)*
- **assets/**: 静态资源如图片和样式。
- **composables/**: Vue 3 可组合函数 (Hooks)，用于封装可复用逻辑 (如 `useAuth.ts`, `useDataTable.ts`)。
- **utils/**: 通用工具函数 (日期格式化, 校验等)。
- **plugins/**: Nuxt 插件 (初始化库, 全局指令/组件注册)。
- **middleware/**: 路由中间件 (认证, 权限)。

### 后端目录:
- **server/routes/**: API路由, 如 `api/merchant.js` (商家API)、`api/master.js` (后台API)。 *(建议按资源组织，如 `server/api/merchant/orders.ts`)*
- **server/lib/db/**: 数据库操作, 包含 `models/` 如 `user.js` (用户模型)。
- **server/lib/thirdparty/**: 第三方服务, 如 `oss.js` (阿里云OSS)、`wechat.js` (微信登录)。
- **server/api/**: API 路由层 (保持不变或按资源细化)。
- **server/services/** (或 `server/modules/`): **新增业务逻辑层**。这是处理核心业务逻辑的地方，将逻辑从路由处理程序中分离出来，提高可维护性。例如 `server/services/orders/orders.service.ts`。
- **server/database/** (或保留 `server/lib/db/`): 数据库交互层。包含模型定义 (如果使用 ORM 如 Prisma，则可能是 `prisma/schema.prisma`)、数据库客户端实例、迁移脚本等。
- **server/utils/**: 后端通用工具函数。
- **server/middleware/**: 服务器端中间件 (日志, 错误处理, CORS)。
- **server/config/**: 配置管理。
- **server/types/** (或 `server/interfaces/`): 共享的类型定义。

---

## 命名规范

- **函数命名**: camelCase, 描述性强, 如 `fetchMerchantData` (前端API调用)、`getUserById` (数据库操作)。
- **变量命名**: camelCase, 如 `merchantList`、`userData`, 便于阅读。
- **组件命名**: PascalCase, 如 `MerchantList`、`AdminDashboard`, 反映功能。
- **文件名**: 小写, 描述性命名, 如 `merchant-list.vue`、`user.js`, 扩展名明确。 *(建议使用 kebab-case (短横线分隔) 或 camelCase/PascalCase 保持一致性，例如 `merchant-list.vue` 或 `MerchantList.vue`，`user.model.ts`)*

---

## 数据库设计

**核心原则:** 清晰、一致、模块化、符合 Prisma 规范。

1. **Schema 隔离**
   - 使用 PostgreSQL 的 Schema 特性实现模块隔离
   - 每个业务模块使用独立的 Schema（master、merchant、supplier）

2. **命名规范**
   - 表名：使用 PascalCase，如 `User`、`Department`
   - 字段名：使用 snake_case，如 `user_id`、`login_time`
   - ID 字段：统一使用 `id`，类型为 BigInt（16位雪花算法）

详细的数据库设计规范和示例请参考 [数据库设计文档](server/docs/database.md)



---

## **API 接口标准 (基于 OpenAPI/Swagger)

**核心原则:** RESTful, 清晰路径, 标准方法, 统一格式, OpenAPI 规范。

1.  **规范工具:** 使用 OpenAPI 3.0+ (Swagger) 编写 `openapi.yaml` 或 `openapi.json`。
2.  **基本路径:** `/api/v[版本号]` (e.g., `/api/v1`)
3.  **资源路径 (Paths):**
  *   **格式:** `/模块名/资源名词复数/{路径参数}`
  *   **模块化:**
    *   商家: `/api/v1/merchant/orders`, `/api/v1/merchant/orders/{orderId}`
    *   后台: `/api/v1/master/products`, `/api/v1/master/products/{productId}`
    *   核心/共享: `/api/v1/users`, `/api/v1/files/upload`
  *   **关联资源:** `/api/v1/merchant/orders/{orderId}/items`
  *   **避免在 URL 中使用动词**，除非无法用 HTTP 方法表达 (少用)。
4.  **HTTP 方法:**
  *   `GET`: 查询 (单个/列表)
  *   `POST`: 创建
  *   `PUT`: 完整更新/替换
  *   `PATCH`: 部分更新
  *   `DELETE`: 删除
5.  **请求 (Request):**
  *   **参数:** 路径参数 (`{orderId}`), 查询参数 (`?status=pending&page=1&pageSize=20`)
  *   **请求体:** `POST`, `PUT`, `PATCH` 使用 JSON (`Content-Type: application/json`)，字段为 `camelCase`。
6.  **响应 (Response):**
  *   **状态码:** 标准 HTTP 状态码 (200, 201, 204, 400, 401, 403, 404, 500等)。
  *   **响应体:**
    *   **统一结构 (推荐):**
        ```json
        {
          "success": true | false,
          "code": 200, // 可选的业务码
          "message": "操作成功 | 错误信息",
          "data": { ... } | [ ... ] | null, // 主要数据
          "pagination": { "total": 100, "page": 1, "pageSize": 20 } // (列表时可选)
        }
        ```
    *   字段为 `camelCase`。
7.  **OpenAPI/Swagger 定义:**
  *   使用 `tags` 对 API 分组 (e.g., "商家订单", "后台用户")。
  *   使用 `components/schemas` 定义可复用的数据模型 (DTOs)，使用 `PascalCase` (e.g., `MerchantOrder`, `UserInput`)。
  *   清晰定义每个接口的 `summary`, `description`, `parameters`, `requestBody`, `responses`。
  *   定义 `securitySchemes` (如 JWT Bearer) 并应用到需要认证的接口。

---

## 第三方模块
- **阿里云OSS**: 用于文件存储, 代码在 `server/lib/thirdparty/oss.js`。 *(建议移至 `server/services/thirdparty/oss.service.ts` 或类似位置)*
- **微信登录**: 用于用户认证, 代码在 `server/lib/thirdparty/wechat.js`。 *(建议移至 `server/services/thirdparty/wechat.service.ts` 或类似位置)*

---

## 模块功能 (后台与商家)

### 后台模块:
- **订单管理**: 通过 `pages/master/orders.vue` 和 `/api/master/orders` 处理, 管理员可查看、创建、更新、删除所有订单。
- **商品管理**: 通过 `pages/master/products.vue` 和 `/api/master/products` 处理, 管理商品信息。
- **客户管理**: 通过 `pages/master/customers.vue` 和 `/api/master/customers` 处理, 管理客户信息。
- **财务管理**: 通过 `pages/master/finances.vue` 和 `/api/master/finances` 处理, 处理财务数据。
- **权限管理**: 通过 `pages/master/permissions.vue` 和 `/api/master/permissions` 处理, 管理用户角色和权限。

### 商家模块:
- **订单管理**: 通过 `pages/merchant/orders.vue` 和 `/api/merchant/orders` 处理, 商家可查看和管理自身订单。
- **财务管理**: 通过 `pages/merchant/finances.vue` 和 `/api/merchant/finances` 处理, 管理财务数据。
- **商品管理**: 通过 `pages/merchant/products.vue` 和 `/api/merchant/products` 处理, 管理商品信息。
- **客户管理**: 通过 `pages/merchant/customers.vue` 和 `/api/merchant/customers` 处理, 管理客户信息。
- **权限管理**: 通过 `pages/merchant/permissions.vue` 和 `/api/merchant/permissions` 处理, 管理商家权限。

---

## 性能压测标准

| 数据规模 | 并发线程 | p50 (ms) | p95 (ms) | p99 (ms) | 目标 RPS | 错误率上限 |
|----------|-----------|-----------|-----------|-----------|------------|------------|
| 1 万     | 50        | 90        | 250       | 400       | 180        | 0.20%      |
| 10 万    | 100       | 160       | 400       | 650       | 140        | 0.20%      |
| 100 万   | 200       | 280       | 700       | 1 050     | 100        | 0.30%      |
| 500 万   | 300       | 450       | 1 200     | 1 600     | 75         | 0.30%      |