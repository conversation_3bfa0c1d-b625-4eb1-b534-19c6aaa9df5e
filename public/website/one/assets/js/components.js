// 导航栏组件
const navbarTemplate = `
  <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container d-flex">
            <a class="navbar-brand" href="index.html">
                <img src="assets/images/logo.svg" alt="八灵logo">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" data-page="profile" href="#">关于我们</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-page="news" href="#">新闻动态</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-page="responsibility" href="#">企业社会责任</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-page="joinus" href="#">加入我们</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
`;

// 页脚组件
const footerTemplate = `
 <footer class="footer py-4">
        <div class="container py-4 py-md-5 px-4 px-md-3 text-body-secondary">
            <div class="row align-items-center mb-4">
                <div class="col-md-6">
                    <img src="assets/images/logo2.svg" alt="八灵logo" class="footer-logo">
                </div>
                <div class="col-md-6 text-end">
                    <h5 class="mb-0">关注我们</h5>
                    <div class="social-icons mt-2">
                        <span class="iconfont icon-weixin social-icon">
                            <div class="qr-code">
                                <img src="assets/images/weixin-qr.png" alt="微信二维码">
                                <p>扫码关注</p>
                            </div>
                        </span>
                        <span class="iconfont icon-douyin social-icon">
                            <div class="qr-code">
                                <img src="assets/images/weixin-qr.png" alt="抖音二维码">
                                <p>扫码关注</p>
                            </div>
                        </span>
                    </div>
                </div>
            </div>
            <hr class="my-6">
            <div class="row" style="padding-top: 1.5rem;">
                <div class="col-6 col-md-4 col-lg-2 mb-4">
                    <h5 class="mb-3">关于我们</h5>
                    <ul class="list-unstyled">
                        <li><a href="#">公司简介</a></li>
                        <li><a href="#">愿景及使命</a></li>
                        <li><a href="#">业务架构</a></li>
                        <li><a href="#">管理团队</a></li>
                        <li><a href="#">企业文化</a></li>
                        <li><a href="#">办公地点</a></li>
                    </ul>
                </div>
                <div class="col-6 col-md-4 col-lg-2 mb-4">
                    <h5 class="mb-3">新闻动态</h5>
                    <ul class="list-unstyled">
                        <li><a href="#">企业新闻</a></li>
                        <li><a href="#">行业新闻</a></li>
                    </ul>
                </div>
                <div class="col-6 col-md-4 col-lg-2 mb-4">
                    <h5 class="mb-3">企业社会责任</h5>
                    <ul class="list-unstyled">
                        <li><a href="#">公益慈善</a></li>
                        <li><a href="#">校企合作</a></li>
                        <li><a href="#">党建共建</a></li>
                    </ul>
                </div>
                <div class="col-6 col-md-4 col-lg-2 mb-4">
                    <h5 class="mb-3">服务支持</h5>
                    <ul class="list-unstyled">
                        <li><a href="#">服务协议</a></li>
                        <li><a href="#">隐私政策</a></li>
                        <li><a href="#">知识产权</a></li>
                        <li><a href="#">成功案例</a></li>
                        <li><a href="#">合作品牌</a></li>
                    </ul>
                </div>
                <div class="col-6 col-md-4 col-lg-2 mb-4">
                    <h5 class="mb-3">联系我们</h5>
                    <ul class="list-unstyled">
                        <li><a href="#">服务网点</a></li>
                        <li><a href="#">官方商城</a></li>
                        <li><a href="#">聚灵云平台</a></li>
                        <li><a href="#">供应商自荐</a></li>
                    </ul>
                </div>
                <div class="col-6 col-md-4 col-lg-2 mb-4">
                    <h5 class="mb-3">加入我们</h5>
                    <ul class="list-unstyled">
                        <li><a href="#" data-page="joinus">社会招聘</a></li>
                        <li><a href="#" data-page="joinus">校园招聘</a></li>
                    </ul>
                </div>
            </div>
            <div class="row mt-4">
                <div class="col-12 text-center">
                    <p class="mb-1">Copyright 2012 - 2025 Baling. All Rights Reserved. 八灵科技 版权所有</p>
                    <p class="mb-1">增值电信业务经营许可证：粤B2-20191630</p>
                    <p class="mb-0">粤ICP备19062272号</p>
                </div>
            </div>
        </div>
    </footer>
`;

// 加载组件的函数
function loadComponents() {
    // 加载导航栏
    const navPlaceholder = document.getElementById('navbar-placeholder');
    if (navPlaceholder) {
        navPlaceholder.innerHTML = navbarTemplate;
    }

    // 加载页脚
    const footerPlaceholder = document.getElementById('footer-placeholder');
    if (footerPlaceholder) {
        footerPlaceholder.innerHTML = footerTemplate;
    }

    // 获取当前页面路径
    const currentPath = window.location.pathname;
    const isHomePage = currentPath === '/' || currentPath.includes('index.html') || currentPath.endsWith('/');
    
    // 如果不是首页，修改导航栏样式
    if (!isHomePage) {
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            navbar.style.backgroundColor = 'white';
            navbar.classList.add('not-homepage');
        }
        
        // 更换Logo
        const logo = document.querySelector('.navbar-brand img');
        if (logo) {
            logo.src = 'assets/images/logo2.svg';
        }
    } else {
        // 如果是首页，添加滚动事件监听器
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            const logo = document.querySelector('.navbar-brand img');
            
            if (window.scrollY >= 56) {
                // 当滚动超过56px时，改变导航栏样式
                navbar.style.backgroundColor = 'white';
                navbar.classList.add('not-homepage');
                
                // 更换Logo
                if (logo) {
                    logo.src = 'assets/images/logo2.svg';
                }
            } else {
                // 当滚动小于56px时，恢复原始样式
                navbar.style.backgroundColor = 'rgba(0, 0, 0, 0.4)';
                navbar.classList.remove('not-homepage');
                
                // 恢复原始Logo
                if (logo) {
                    logo.src = 'assets/images/logo.svg';
                }
            }
        });
    }

    // 添加页面导航事件
    document.addEventListener('click', function(e) {
        const target = e.target;
        if (target.tagName === 'A' && target.dataset.page) {
            e.preventDefault();
            const page = target.dataset.page;
            
            // 移除所有导航项的高亮样式
            document.querySelectorAll('.nav-link').forEach(item => {
                item.classList.remove('active');
            });
            
            // 添加当前点击项的高亮样式
            target.classList.add('active');
            
            if (page === 'joinus') {
                window.location.href = 'joinus.html';
            } else if (page === 'profile') {
                window.location.href = 'profile.html';
            } else if (page === 'news') {
                window.location.href = 'news.html';
            } else if (page === 'responsibility') {
                window.location.href = 'responsibility.html';
            }
        }
    });
    
    // 根据当前页面URL高亮对应的导航项
    let currentPage = '';
    
    if (currentPath.includes('profile.html')) {
        currentPage = 'profile';
    } else if (currentPath.includes('news.html')) {
        currentPage = 'news';
    } else if (currentPath.includes('responsibility.html')) {
        currentPage = 'responsibility';
    } else if (currentPath.includes('joinus.html')) {
        currentPage = 'joinus';
    }
    
    if (currentPage) {
        const navLink = document.querySelector(`.nav-link[data-page="${currentPage}"]`);
        if (navLink) {
            navLink.classList.add('active');
        }
    }
}

// 当DOM加载完成后执行
document.addEventListener('DOMContentLoaded', loadComponents);

// 为移动端添加供应链模块和公司信息模块的点击交互支持
document.addEventListener('DOMContentLoaded', function() {
    // 判断是否为移动设备
    const isMobile = window.matchMedia('(max-width: 992px)').matches;
    
    if (isMobile) {
        // 为供应链模块添加点击事件
        const supplyItems = document.querySelectorAll('.supply-item');
        supplyItems.forEach(item => {
            item.addEventListener('click', function() {
                // 先移除所有其他项的active类
                supplyItems.forEach(otherItem => {
                    if (otherItem !== item) {
                        otherItem.classList.remove('active');
                    }
                });
                
                // 切换当前项的active类
                item.classList.toggle('active');
            });
        });
        
        // 为公司信息模块添加点击事件
        const companyCards = document.querySelectorAll('.company-info-card');
        companyCards.forEach(card => {
            card.addEventListener('click', function() {
                // 先移除所有其他卡片的active类
                companyCards.forEach(otherCard => {
                    if (otherCard !== card) {
                        otherCard.classList.remove('active');
                    }
                });
                
                // 切换当前卡片的active类
                card.classList.toggle('active');
            });
        });
        
        // 点击其他区域关闭active状态
        document.addEventListener('click', function(event) {
            // 关闭供应链模块的active状态
            if (!event.target.closest('.supply-item')) {
                supplyItems.forEach(item => {
                    item.classList.remove('active');
                });
            }
            
            // 关闭公司信息模块的active状态
            if (!event.target.closest('.company-info-card')) {
                companyCards.forEach(card => {
                    card.classList.remove('active');
                });
            }
        }, true);
    }
    
    // 屏幕尺寸改变时重新加载页面以应用正确的交互模式
    window.addEventListener('resize', function() {
        const wasMobile = isMobile;
        const nowMobile = window.matchMedia('(max-width: 992px)').matches;
        
        // 只有在移动状态发生变化时才重新加载
        if (wasMobile !== nowMobile) {
            location.reload();
        }
    });
});
