{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "node --max-old-space-size=4096 ./node_modules/.bin/nuxt build", "dev": "node --max-old-space-size=4096 ./node_modules/.bin/nuxt dev", "dev:fast": "node dev-optimized.js", "dev:simple": "NODE_OPTIONS='--max-old-space-size=4096' nuxt dev", "dev:original": "nuxt dev", "dev:all": "node dev.js", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "start": "node start.js", "perf:check": "node quick-check.js"}, "dependencies": {"@arco-design/color": "^0.4.0", "@arco-design/web-vue": "^2.55.3", "@arco-themes/vue-mine-admin-v2": "^0.0.3", "@elastic/elasticsearch": "^8.13.0", "@iconify/vue": "^4.3.0", "@nuxtjs/tailwindcss": "^6.13.2", "@pinia/nuxt": "^0.10.1", "@prisma/client": "^6.6.0", "@tinymce/tinymce-vue": "^6.1.0", "@types/express": "^5.0.1", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@vueuse/core": "^13.1.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "ace-builds": "^1.39.1", "autoprefixer": "^10.4.17", "axios": "^1.9.0", "crypto-js": "^4.2.0", "dayjs-nuxt": "^2.1.11", "decimal.js": "^10.5.0", "dompurify": "^3.2.5", "dotenv": "^16.5.0", "echarts": "^5.6.0", "echarts-wordcloud": "^2.1.0", "express-validator": "^7.2.1", "file2md5": "^1.3.0", "joi": "^17.13.3", "jsencrypt": "^3.3.2", "marked": "^15.0.8", "md-editor-v3": "^4.13.5", "monaco-editor": "^0.52.2", "node-cron": "^4.1.0", "nodemailer": "^7.0.3", "nprogress": "^0.2.0", "nuxt": "^3.16.0", "nuxt-echarts": "^0.2.6", "nuxt-lodash": "^2.5.3", "nuxt-monaco-editor": "^1.3.1", "overlayscrollbars": "^2.11.1", "overlayscrollbars-vue": "^0.5.9", "pinia": "^3.0.1", "postcss-import": "^14.1.0", "qrcode": "^1.5.4", "qs": "^6.10.3", "radash": "^12.1.0", "redis": "^4.7.0", "resize-observer-polyfill": "^1.5.1", "slugify": "^1.6.6", "sortablejs": "^1.15.0", "svg-captcha": "^1.4.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tinymce": "^7.8.0", "ts-node": "^10.9.2", "version-rocket": "^1.6.2", "vue": "^3.5.13", "vue-clipboard3": "^2.0.0", "vue-color-kit": "^1.0.5", "vue-echarts": "^6.0.2", "vue-i18n": "^9.1.10", "vue-m-message": "^4.0.2", "vue-router": "^4.5.0", "vue3-ace-editor": "^2.2.4", "vuedraggable": "^4.1.0", "zod": "^3.24.2"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/nprogress": "^0.2.3", "less": "^4.2.2", "prisma": "^6.6.0"}}