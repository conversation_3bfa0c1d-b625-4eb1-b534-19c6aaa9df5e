<!--
 - Mine<PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -->
<template>
  <div class="ma-content-block p-4">
    <!-- 内容区域 -->
    <div class="flex">
      <!-- 左侧导航区域 -->
      <div class="w-2/12 mr-4" >
        <!-- 渠道列表标签页 -->
        <div class="bg-white rounded shadow-sm mb-4">
          <div class="tab-container-wrapper">
            <div class="tab-container flex border-b border-gray-200 overflow-x-auto">
              <div 
                v-for="(item, index) in channelList" 
                :key="index"
                class="category-tab cursor-pointer text-sm font-medium whitespace-nowrap flex-shrink-0"
                :class="{'text-blue-500 border-b-2 border-blue-500': currentCategory === item.id}"
                @click="switchCategory(item.id)"
              >
                <span class="tab-text">{{ item.name }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 平台列表 -->
        <div class="bg-white rounded shadow-sm">
          <div class="platform-list">
            <template v-if="currentCategory && platformCategories[currentCategory]">
              <div 
                v-for="platform in platformCategories[currentCategory].platforms" 
                :key="platform.id"
                class="platform-item flex items-center h-14 px-4 py-2 cursor-pointer hover:bg-gray-100 border-b border-gray-200"
                :class="{'bg-blue-50 border-l-4 border-blue-500 pl-3': currentPlatform === platform.id}"
                @click="selectPlatform(platform.id)"
              >
                <div class="platform-icon flex items-center justify-center w-8 h-8 mr-4 flex-shrink-0">
                  <i class="iconfont text-xl" :class="platform.icon"></i>
                </div>
                <div class="platform-name flex-grow text-base whitespace-nowrap overflow-hidden text-ellipsis">{{ platform.name }}</div>
                <div class="platform-count text-sm font-medium text-gray-500 ml-3">{{ platform.count }}</div>
              </div>
            </template>
            <template v-else-if="platformCategories['default']">
              <div 
                v-for="platform in platformCategories['default'].platforms" 
                :key="platform.id"
                class="platform-item flex items-center h-14 px-4 py-2 cursor-pointer hover:bg-gray-100 border-b border-gray-200"
                :class="{'bg-blue-50 border-l-4 border-blue-500 pl-3': currentPlatform === platform.id}"
                @click="selectPlatform(platform.id)"
              >
                <div class="platform-icon flex items-center justify-center w-8 h-8 mr-4 flex-shrink-0">
                  <i class="iconfont text-xl" :class="platform.icon"></i>
                </div>
                <div class="platform-name flex-grow text-base whitespace-nowrap overflow-hidden text-ellipsis">{{ platform.name }}</div>
                <div class="platform-count text-sm font-medium text-gray-500 ml-3">{{ platform.count }}</div>
              </div>
            </template>
            <template v-else>
              <div class="p-4 text-center text-gray-500">暂无平台数据</div>
            </template>
          </div>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="flex-grow bg-white rounded shadow-sm p-4" style="width: 1200px;">
        <!-- CRUD 组件 -->
        <ma-crud :options="crud" :columns="columns" ref="crudRef">
          <!-- 状态列 -->
          <template #status="{ record }">
            <a-tag :color="record.status == 1 ? 'green' : 'red'">
              {{ record.status == 1 ? '启用' : '禁用' }}
            </a-tag>
          </template>
          
          <!-- 授权状态列 -->
          <template #auth_status="{ record }">
            <a-tag :color="record.auth_status == 1 ? 'green' : 'red'">
              {{ record.auth_status == 1 ? '已授权' : '未授权' }}
            </a-tag>
          </template>

          <!-- 操作列 -->
          <template #operationBeforeExtend="{ record }">
            <a-button type="text" size="small" @click="openDetails(record)">
              <template #icon>
                <icon-eye />
              </template>
              详情
            </a-button>
            <a-button type="text" size="small" @click="openEdit(record)">
              <template #icon>
                <icon-edit />
              </template>
              编辑
            </a-button>
          </template>
        </ma-crud>
      </div>
    </div>

    <!-- 店铺详情抽屉 -->
    <DetailDrawer 
      :visible="detailVisible" 
      :record="detailRecord"
      @update:visible="detailVisible = $event"
      @edit="handleDetailEdit"
    />
    
    <!-- 店铺编辑抽屉 -->
    <EditDrawer
      :visible="editVisible"
      :record="editRecord"
      @update:visible="editVisible = $event"
      @submit="handleEditSave"

    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from "vue";
import { Message, Modal } from "@arco-design/web-vue";
import { IconUser, IconEye, IconSearch, IconRefresh, IconPlus } from '@arco-design/web-vue/es/icon';
import upstreamApi from "@/api/master/upstream"; // 导入上游管理API
import orderApi from "@/api/master/order"; // 导入订单API
import DetailDrawer from './components/DetailDrawer.vue';
import EditDrawer from './components/EditDrawer.vue';

// 定义页面元数据
definePageMeta({
  name: "master-upstream-authorization",
  path: "/master/upstream/authorization"
});

// 店铺列表数据
const storeList = ref([]);
const storePagination = ref({
  page: 1,
  pageSize: 10,
  total: 0
});

// 渠道列表数据
const channelList = ref([]);
const channelPagination = ref({
  page: 1,
  pageSize: 10,
  total: 0
});

// 保存渠道列表API响应数据
const channelApiResponse = ref(null);


// 获取渠道列表数据
const fetchChannelList = async () => {
  try {
    const res = await orderApi.getChannelList({
      page: channelPagination.value.page,
      pageSize: channelPagination.value.pageSize
    });
    
    if (res.code === 200) {
      // 检查返回的数据结构是否符合预期
      if (res.data && res.data.items ) {
        // 保存完整的API响应数据
        channelApiResponse.value = {
          code: res.code,
          message: res.message,
          data: {
            list: res.data.items,
            pagination: res.data.pageInfo
          }
        };
        
        // 更新渠道列表和分页数据
        channelList.value = res.data.items;
        channelPagination.value = res.data.pageInfo;
        console.log('渠道列表数据已保存:', channelApiResponse.value);
        
        // 如果有渠道数据，自动选中第一个渠道
        if (channelList.value.length > 0) {
          const firstChannelId = channelList.value[0].id;
          switchCategory(firstChannelId);
          console.log('自动选中第一个渠道:', firstChannelId);
        }
      } else {
        // 数据结构不符合预期，使用模拟数据
        console.warn('返回的数据结构不符合预期，使用模拟数据');
        useChannelMockData();
      }
    } else {
      // API调用失败，使用模拟数据
      Message.error(res.message || '获取渠道列表失败，使用模拟数据');
      useChannelMockData();
    }
  } catch (error) {
    console.error('获取渠道列表出错:', error);
    Message.error('获取渠道列表出错，使用模拟数据');
    useChannelMockData();
  }
};


// 渠道平台映射数据
const platformCategories = reactive({});

// 平台数据加载状态
const platformLoading = ref(false);

// 获取平台列表数据
const fetchPlatformList = async (channelId) => {
  if (!channelId) {
    console.warn('渠道ID为空，无法获取平台数据');
    return;
  }
  
  platformLoading.value = true;
  try {
    // 调用API获取平台列表数据
    const res = await upstreamApi.platform.getList({
      channelId: channelId, // 传递渠道ID作为参数
      page: 1,
      pageSize: 100 // 一次获取足够多的平台数据
    });
    
    if (res.code === 200 && res.data && res.data.items) {
      // 处理平台数据
      const platforms = res.data.items.map(item => ({
        id: item.id,
        name: item.name,
        icon: item.icon || 'icon-default', // 使用默认图标如果没有提供
        count: item.authCount || '0/0' // 授权数量
      }));
      
      // 保存到渠道平台映射中
      platformCategories[channelId] = {
        name: channelList.value.find(c => c.id === channelId)?.name || '未知渠道',
        platforms: platforms
      };
      
      console.log(`成功获取渠道 ${channelId} 的平台数据:`, platforms);
      
      // 如果有平台数据，默认选择第一个
      if (platforms.length > 0) {
        currentPlatform.value = platforms[0].id;
        // 加载该平台下的授权数据
        filterDataByPlatform(platforms[0].id);
      }
    } else {
      console.warn('获取平台数据失败或数据格式不正确:', res);
      // 初始化空数据
      platformCategories[channelId] = {
        name: channelList.value.find(c => c.id === channelId)?.name || '未知渠道',
        platforms: []
      };
    }
  } catch (error) {
    console.error('获取平台数据出错:', error);
    Message.error('获取平台数据失败');
    // 初始化空数据
    platformCategories[channelId] = {
      name: channelList.value.find(c => c.id === channelId)?.name || '未知渠道',
      platforms: []
    };
  } finally {
    platformLoading.value = false;
  }
};

// 默认渠道数据，如果API调用失败时使用
const initDefaultPlatformData = () => {
  // 默认渠道数据
  platformCategories['default'] = {
    name: '默认渠道',
    platforms: [
      { id: 'default1', name: '默认平台', icon: 'icon-default', count: '0/0' }
    ]
  };
};

// 初始化平台数据
initDefaultPlatformData();

// 当前主标签页，平台或ERP
const mainTab = ref('platform');

// 当前选中的渠道ID
const currentCategory = ref(''); // 初始时不选中任何渠道，将在数据加载后设置

// 当前选中的平台
const currentPlatform = ref('');

// 切换渠道分类
async function switchCategory(channelId) {
  console.log('切换到渠道:', channelId);
  currentCategory.value = channelId;
  
  // 如果该渠道的平台数据还没有加载，则调用API获取
  if (!platformCategories[channelId] || !platformCategories[channelId].platforms) {
    console.log('正在获取渠道平台数据...');
    await fetchPlatformList(channelId);
  }
  
  // 获取渠道对应的平台数据
  const platformData = platformCategories[channelId] || platformCategories['default'];
  
  if (platformData && platformData.platforms && platformData.platforms.length > 0) {
    // 如果当前渠道有平台，默认选中第一个
    currentPlatform.value = platformData.platforms[0].id;
    console.log('选中平台:', currentPlatform.value);
    
    // 加载该平台下的授权数据
    filterDataByPlatform(currentPlatform.value);
  } else {
    console.warn('渠道没有对应的平台数据:', channelId);
    currentPlatform.value = '';
    
    // 清空数据，使用CRUD组件的刷新机制
    if (crudRef.value) {
      // 设置加载状态
      crud.loading = true;
      
      try {
        // 调用ma-crud的刷新方法
        crudRef.value.refresh();
      } finally {
        // 关闭加载状态
        crud.loading = false;
      }
    }
  }
}

// 选择平台
function selectPlatform(platformId) {
  console.log('选择平台:', platformId);
  currentPlatform.value = platformId;
  
  // 根据平台ID过滤数据
  filterDataByPlatform(platformId);
}

// 根据平台ID过滤数据
async function filterDataByPlatform(platformId) {
  if (!platformId) {
    console.warn('未提供平台ID');
    return;
  }
  
  console.log(`开始根据平台ID ${platformId} 过滤店铺数据`);
  
  // 更新当前平台ID
  currentPlatform.value = platformId;
  
  try {
    // 调用API获取平台下的店铺数据
    // 使用新的店铺 API接口
    const res = await upstreamApi.store.getList({
      platformId: platformId,
      page: 1,
      pageSize: 20
    });
    
    if (res.code === 200 && res.data && res.data.items) {
      // 更新数据源
      storeList.value = res.data.items;
      console.log(`成功获取平台 ${platformId} 的店铺数据:`, res.data.items);
    } else {
      console.warn('获取店铺数据失败或数据格式不正确:', res);
      // 如果获取失败，使用空数组
      storeList.value = [];
    }
  } catch (error) {
    console.error('加载店铺数据出错:', error);
    Message.error('加载店铺数据失败');
    storeList.value = [];
  } finally {
    // 关闭加载状态
    crud.loading = false;
    
    // 刷新CRUD组件
    if (crudRef.value) {
      crudRef.value.refresh();
    } else {
      console.warn('CRUD组件引用不可用');
      Message.warning('加载数据失败，请刷新页面');
    }
  }
}

// 详情弹窗相关状态
const detailVisible = ref(false);
const detailRecord = ref({});

// 编辑弹窗相关状态
const editVisible = ref(false);
const editRecord = ref({});

// 过滤掉 hide、序号和操作相关字段的列用于详情展示
const detailColumns = computed(() =>
  columns.filter(
    col =>
      !col.hide &&
      col.dataIndex !== "__index" &&
      col.dataIndex !== "__operation"
  )
);

// 打开详情弹窗
async function openDetails(record) {
  try {
    // 显示加载状态
    const loadingMessage = Message.loading('正在获取店铺详情...', 0);

    // 通过店铺ID调用API获取详情数据
    const res = await upstreamApi.store.getById(record.id);

    // 关闭加载提示
    loadingMessage.close();

    if (res.code === 200 && res.data) {
      // 转换API返回的数据格式，适配DetailDrawer组件
      const apiData = res.data;
      const formattedRecord = {
        id: apiData.id,
        name: apiData.name,
        code: apiData.code,
        platformId: apiData.platformId,
        platformName: apiData.platformName,
        channelId: apiData.channelId,
        channelName: apiData.channelName,
        accountConfig: apiData.accountConfig || {},
        cookies: apiData.cookies || '',
        status: apiData.status,
        remark: apiData.remark || '',
        principalName: apiData.principalName || '',
        invoiceTitle: apiData.invoiceTitle || '',
        createdAt: apiData.createdAt,
        updatedAt: apiData.updatedAt,
        // 从API数据中获取爬虫任务和爬虫信息
        spiderTasks: apiData.spiderTasks || [],
        spiders: apiData.spiders || []
      };

      detailRecord.value = formattedRecord;
      detailVisible.value = true;
    } else {
      Message.error(res.message || '获取店铺详情失败');
    }
  } catch (error) {
    console.error('获取店铺详情出错:', error);
    Message.error('获取店铺详情出错');
  }
}

// 打开编辑弹窗
const openEdit = async (record) => {
  try {
    // 显示加载状态
    const loadingMessage = Message.loading('正在获取店铺详情...', 0);

    // 通过店铺ID调用API获取详情数据
    const res = await upstreamApi.store.getById(record.id);

    // 关闭加载提示
    loadingMessage.close();

    if (res.code === 200 && res.data) {
      // 转换API返回的数据格式，适配EditDrawer组件
      const apiData = res.data;
      const formattedRecord = {
        id: apiData.id,
        storeName: apiData.name,
        storeCode: apiData.code,
        invoiceTitle: apiData.invoiceTitle || '',
        principalName: apiData.principalName || '',
        channelId: apiData.channelId || '',
        isAutoSync: apiData.isAutoSync || false,
        orderSyncInterval: apiData.orderSyncInterval || 300,
        productSyncInterval: apiData.productSyncInterval || 600,
        invoiceSyncInterval: apiData.invoiceSyncInterval || 900,
        reportSyncInterval: apiData.reportSyncInterval || 1200,
        account: apiData.accountConfig?.username || '',
        password: apiData.accountConfig?.password || '',
        loginUrl: apiData.loginUrl || '',
        cookies: apiData.cookies || '',
        remark: apiData.remark || '',
        createdAt: apiData.createdAt,
        updatedAt: apiData.updatedAt,
        platformId: apiData.platformId || currentPlatform.value,
        // 添加爬虫信息
        spiders: apiData.spiders || [],
        spiderTasks: apiData.spiderTasks || []
      };

      // 设置编辑记录并打开编辑抽屉
      editRecord.value = formattedRecord;
      editVisible.value = true;
    } else {
      Message.error(res.message || '获取店铺详情失败');
    }
  } catch (error) {
    console.error('获取店铺详情出错:', error);
    Message.error('获取店铺详情出错');
  }
}

// 关闭详情抽屉
function closeDetail() {
  detailVisible.value = false;
}

// 确认按钮处理函数
function handleOk() {
  detailVisible.value = false;
}

// 日期格式化函数
function formatDate(dateValue) {
  console.log('日期值:', dateValue);
  
  if (!dateValue) return '-';
  
  try {
    // 处理时间戳（数字或数字字符串）
    let date;
    if (typeof dateValue === 'number' || !isNaN(Number(dateValue))) {
      // 如果是毫秒时间戳
      date = new Date(Number(dateValue));
    } else {
      // 如果是日期字符串
      date = new Date(dateValue);
    }
    
    console.log('解析后的日期:', date);
    
    // 验证日期是否有效
    if (isNaN(date.getTime())) {
      console.error('无效的日期值:', dateValue);
      return '-';
    }
    
    // 格式化日期为 YYYY-MM-DD HH:MM:SS
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (e) {
    console.error('日期格式化错误:', e);
    return '-';
  }
}

// 详情页编辑按钮处理函数
function handleDetailEdit(record) {
  // 关闭详情抽屉
  detailVisible.value = false;
  
  // 转换数据格式，适配EditDrawer组件
  const formattedRecord = {
    id: record.id,
    name: record.name || '',
    code: record.code || '',
    invoiceTitle: record.invoiceTitle || '',
    channelId: record.channelId || '',
    channelName: record.channelName || '',
    platformId: record.platformId || '',
    platformName: record.platformName || '',
    principalName: record.principalName || '',
    isAutoSync: record.isAutoSync || false, // 添加isAutoSync字段
    orderSyncInterval: record.orderSyncInterval || 300,
    productSyncInterval: record.productSyncInterval || 600,
    invoiceSyncInterval: record.invoiceSyncInterval || 900,
    reportSyncInterval: record.reportSyncInterval || 1200,
    account: record.account || '',
    password: record.password || '',
    loginUrl: record.loginUrl || '',
    cookies: record.cookies || '',
    remark: record.remark || '',
    status: record.status || 1,
    // 传递爬虫和爬虫任务信息
    spiders: record.spiders || [],
    spiderTasks: record.spiderTasks || []
  };
  
  // 设置编辑记录并打开编辑抽屉
  editRecord.value = formattedRecord;
  editVisible.value = true;
}

// 处理编辑保存
function handleEditSave(saveData) {
    // 直接调用API
    upstreamApi.store.update(saveData.id,saveData).then(res => {
      if (res.code === 200) {
        Message.success('保存成功');
        // 刷新数据
        filterDataByPlatform(currentPlatform.value);
      } else {
        Message.error(res.message || '保存失败');
      }
    }).catch(err => {
      console.error('保存数据出错:', err);
      Message.error('保存数据出错');
    });

}

const crudRef = ref();
const storeApi = upstreamApi.store; // 使用商铺API


// 页面初始化
onMounted(async () => {
  try {
    // 获取渠道列表数据
    await fetchChannelList();
    
    // 如果有当前选中的渠道，获取平台数据
    if (currentCategory.value) {
      await fetchPlatformList(currentCategory.value);
      
      // 如果有当前选中的平台，获取授权数据
      if (currentPlatform.value) {
        await filterDataByPlatform(currentPlatform.value);
      }
    }
  } catch (error) {
    console.error('页面初始化出错:', error);
    Message.error('页面初始化出错');
  }
});

// CRUD配置
const crud = reactive({
  // 设置API请求方法
  api: storeApi.getList,
  showIndex: false,
  pageLayout: "fixed",
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 300,
  // 表单配置
  formOption: {
    // 显示方式支持模态框和抽屉: modal drawer
    viewType: 'modal',
    // 显示宽度
    width: 700,
    // 是否全屏，只有modal有效
    isFull: false
  },
  // 在请求前处理参数
  beforeRequest: (params) => {
    // 获取当前平台ID
    const platformId = currentPlatform.value;
    console.log('请求前处理，当前平台ID:', platformId);
    
    // 确保params是对象类型
    if (params === null || typeof params !== 'object') {
      params = {};
    }
    
    // 添加平台ID参数
    if (platformId) {
      // 确保platformId为字符串类型
      params.platformId = String(platformId);
    } else {
      console.warn('当前没有选中平台，可能会导致数据加载失败');
    }
    
    return params;
  },
  // 初始化时的回调
  onInit: (params) => {
    console.log('CRUD初始化', params);
  },
  // 新增打开前方法
  beforeOpenAdd: () => {
    // 确保有选中的平台
    if (!currentPlatform.value) {
      Message.warning('请先选择一个平台');
      return false;
    }
    return true;
  },
  // 新增提交前方法
  beforeAdd: (formData) => {
    // 添加平台ID
    if (!currentPlatform.value) {
      Message.error('请先选择一个平台');
      return false;
    }
    
    // 直接在表单数据中添加platformId
    formData.platformId = String(currentPlatform.value);
    
    // 处理账号配置
    if (typeof formData.accountConfig === 'string') {
      try {
        formData.accountConfig = JSON.parse(formData.accountConfig);
      } catch (e) {
        console.error('解析accountConfig失败:', e);
        Message.error('账号配置格式错误');
        return false;
      }
    } else if (!formData.accountConfig) {
      formData.accountConfig = {};
    }
    
    console.log('新增提交的表单数据:', formData);
    return formData;
  },
  // 新增提交后方法
  afterAdd: (response, formData) => {
    console.log('新增提交后响应:', response, '提交数据:', formData);
    if (response.code === 200) {
      // 刷新平台数据
      fetchPlatformList(currentCategory.value);
    } else {
      Message.error(response.message || '添加店铺失败');
    }
  },
  // 编辑打开前方法
  beforeOpenEdit: (record) => {
    console.log('编辑打开前处理记录:', record);
    
    // 确保有选中的平台
    if (!currentPlatform.value) {
      Message.warning('请先选择一个平台');
      return false;
    }
    
    // 确保记录中包含平台ID且为字符串类型
    record.platformId = String(currentPlatform.value);
    
    // 如果accountConfig是字符串，尝试解析成对象
    if (record.accountConfig && typeof record.accountConfig === 'string') {
      try {
        record.accountConfig = JSON.parse(record.accountConfig);
      } catch (e) {
        // 如果解析失败，保持原样
        console.warn('解析accountConfig失败，保持原样:', e);
      }
    }
    
    return record;
  },
  // 编辑提交前方法
  beforeEdit: (formData) => {
    console.log('编辑提交前处理表单:', formData);
    
    // 确保有选中的平台
    if (!currentPlatform.value) {
      Message.error('请先选择一个平台');
      return false;
    }
    
    // 确保表单数据中包含平台ID且为字符串类型
    formData.platformId = String(currentPlatform.value);
    
    // 确保ID字段存在且有效
    if (!formData.id) {
      Message.error('编辑数据缺少ID字段');
      console.error('编辑数据缺少ID字段:', formData);
      return false;
    }
    
    // 处理账号配置字段
    // 如果是分开的字段，合并成一个对象
    const accountConfig = {
      username: formData['accountConfig.username'] || '',
      password: formData['accountConfig.password'] || '',
      apiKey: formData['accountConfig.apiKey'] || ''
    };
    
    // 删除原来的分散字段
    delete formData['accountConfig.username'];
    delete formData['accountConfig.password'];
    delete formData['accountConfig.apiKey'];
    
    // 设置accountConfig对象
    formData.accountConfig = accountConfig;
    
    // 确保状态是数字类型
    if (formData.status !== undefined) {
      formData.status = Number(formData.status);
    }
    
    console.log('处理后的提交数据:', formData);
    return formData;
  },
  // 删除后方法
  afterDelete: (response) => {
    if (response.code === 200) {
      // 刷新平台数据
      fetchPlatformList(currentCategory.value);
    } else {
      Message.error(response.message || '删除失败');
    }
  },
  add: { 
    show: true, 
    text: '添加店铺',
    // 设置添加API
    api: storeApi.create,
    // 添加前检查是否选择了平台
    disabled: () => !currentPlatform.value,
    // 自定义请求参数
    params: () => {
      return {
        platformId: String(currentPlatform.value)
      };
    },
  },
  edit: { 
    show: false, 
    text: '编辑店铺',
    // 设置编辑API
    api: storeApi.update,
    // 注意：ma-crud 组件不支持 params 配置项
    // 所有数据处理都在 beforeEdit 方法中进行
  },
  delete: {
    show: true,
    text: '删除店铺',
    // 设置删除API
    api: storeApi.delete,
    // 是否显示批量处理按钮
    batch: true,
  },
});


// 列定义
const columns = ref([
  {
    title: "平台ID",
    dataIndex: "platformId",
    hide: true,
    search: false,
    formType: "input",
    editDisplay: false,
    addDisplay: false,
    commonRules: [{ required: true, message: "平台ID必填" }]
  },
  {
    title: "店铺名称",
    dataIndex: "name",
    search: true,
    formType: "input",
    editDisplay: true,
    addDisplay: true,
    commonRules: [{ required: true, message: "店铺名称必填" }]
  },
  {
    title: "店铺代码",
    dataIndex: "code",
    search: true,
    commonRules: [{ required: true, message: "店铺代码必填" }]
  },
  {
    title: "平台名称",
    dataIndex: "platformName",
    search: false,
    formType: "input",
    editDisplay: false,
    addDisplay: false
  },
  {
    title: "渠道名称",
    dataIndex: "channelName",
    search: false,
    formType: "input",
    editDisplay: false,
    addDisplay: false
  },
  {
    title: "账号配置",
    dataIndex: "accountConfig",
    search: false,
    hide: true,
    formType: "textarea",
    component: 'a-textarea',
    componentProps: {
      rows: 6,
      placeholder: '请输入JSON格式的账号配置',
    },
    editDisplay: false,
    addDisplay: false,
    commonRules: [{ required: true, message: "账号配置必填" }]
  },
  {
    title: "API密钥",
    dataIndex: "accountConfig.apiKey",
    formType: "input",
    search: false,
    hide: true,
    editDisplay: true,
    addDisplay: true
  },
  {
    title: "用户名",
    dataIndex: "accountConfig.username",
    formType: "input",
    search: false,
    hide: true,
    editDisplay: true,
    addDisplay: true
  },
  {
    title: "密码",
    dataIndex: "accountConfig.password",
    formType: "input-password",
    search: false,
    hide: true,
    editDisplay: true,
    addDisplay: true,
    component: 'a-input-password'
  },
  {
    title: "Cookies",
    dataIndex: "cookies",
    search: false,
    hide: true,
    formType: "textarea",
    editDisplay: true,
    addDisplay: true
  },

  {
    title: "店铺状态",
    dataIndex: "status",
    search: true,
    formType: "select",
    addDefaultValue: 1,
    dict: {
      data: [
        { label: "启用", value: 1 },
        { label: "禁用", value: 0 }
      ]
    }
  },

  {
    title: "创建时间",
    dataIndex: "createdAt",
    search: false,
    formType: "date-picker",
    component: 'a-date-picker',
    editDisplay: false,
    addDisplay: false,
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss'
    },
    customRender: ({ record }) => {
      return formatDate(record.createdAt);
    }
  },
  {
    title: "更新时间",
    dataIndex: "updatedAt",
    search: false,
    formType: "date-picker",
    component: 'a-date-picker',
    editDisplay: false,
    addDisplay: false,
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss'
    },
    customRender: ({ record }) => {
      return formatDate(record.updatedAt);
    }
  }
]);
</script>

<script>
export default { name: "master-upstream-authorization" };
</script>

<style scoped>
/* 左侧导航样式 */
.left-sidebar {
  border: 1px solid #ebedf0;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.tab-item {
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
  position: relative;
}

.tab-item:hover {
  background-color: #f5f5f5;
}

.category-tab {
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
  padding-left: 1.5rem;
}

.category-tab:hover {
  background-color: #f5f5f5;
}

.platform-item {
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.platform-item:hover {
  background-color: #f5f5f5;
}

.platform-item.active {
  background-color: #f0f7ff;
  border-left-color: #1890ff;
}

/* 详情抽屉样式 */
.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item .text-gray-500 {
  width: 100px;
  flex-shrink: 0;
}

/* 平台图标样式 */
.platform-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.platform-icon img {
  max-width: 100%;
  max-height: 100%;
}

/* 平台项样式 */
.platform-item {
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
  height: 56px;
  display: flex;
  align-items: center;
}

.platform-item:hover {
  background-color: #f5f5f5;
}

/* 平台名称样式 */
.platform-name {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 平台计数样式 */
.platform-count {
  font-weight: 500;
}

/* 平台图标样式 */
.platform-icon .iconfont {
  font-size: 20px;
}

/* 平台列表样式 */
.platform-list {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

/* 标签容器包裹器样式 */
.tab-container-wrapper {
  width: 100%;
  position: relative;
  overflow: hidden;
}

/* 标签容器样式 */
.tab-container {
  display: flex;
  width: 100%;
  overflow-x: auto;
  scrollbar-width: thin; /* Firefox */
  -ms-overflow-style: auto; /* IE and Edge */
}

/* 自定义Chrome的滚动条 */
.tab-container::-webkit-scrollbar {
  height: 6px;
}

.tab-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.tab-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.tab-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 标签页样式 */
.category-tab {
  flex: 0 0 auto;
  min-width: 100px; /* 每个标签的最小宽度 */
  max-width: 150px; /* 每个标签的最大宽度 */
  padding: 10px 15px; /* 增加内边距，使标签更易点击 */
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  white-space: nowrap;
  box-sizing: border-box;
  position: relative;
  transition: all 0.3s ease;
}

/* 确保标签页内容在切换时不会改变布局 */
.category-tab::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: transparent;
}

/* 标签页文字样式 */
.tab-text {
  display: block;
  width: 100%;
  text-align: center;
  margin: 0 auto;
}
</style>