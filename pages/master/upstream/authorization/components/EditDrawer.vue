<!--
 - MineAdmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -->
<template>
  <!-- 授权编辑抽屉 -->
  <a-drawer 
    :width="650" 
    :visible="visible" 
    @cancel="closeDrawer"
    :unmount-on-close="true"
    @ok="handleSubmit"
  >
    <template #title>
      编辑授权信息
    </template>
    
    <div class="auth-edit-container">
      <a-form :model="formData" layout="vertical">
        <!-- 基础信息 -->
        <div class="section mb-4 pb-4 border-b border-gray-200">
          <h4 class="text-base font-medium mb-4 text-blue-500">基础信息</h4>

          <a-form-item field="storeName" label="店铺名称">
            <a-input v-model="formData.storeName" placeholder="请输入店铺名称" />
          </a-form-item>

          <a-form-item field="principalName" label="主体名称">
            <a-input v-model="formData.principalName" placeholder="请输入主体名称" />
          </a-form-item>

          <a-form-item field="invoiceTitle" label="发票抬头">
            <a-input v-model="formData.invoiceTitle" placeholder="请输入发票抬头" />
          </a-form-item>
        </div>

        <!-- 同步设置 -->
        <div class="section mb-4 pb-4 border-b border-gray-200">
          <h4 class="text-base font-medium mb-4 text-blue-500">同步设置</h4>

          <!-- 与详情页保持一致的渲染逻辑 -->
          <template v-if="props.record.spiders && props.record.spiders.length > 0">
            <a-form-item field="isAutoSync" label="自动同步">
              <a-switch v-model="formData.isAutoSync" />
            </a-form-item>

            <div class="grid grid-cols-2 gap-4">
              <a-form-item
                v-for="spider in props.record.spiders"
                :key="spider.id"
                :field="`spider_${spider.id}_interval`"
                :label="`${getSpiderTypeName(spider.spiderType)}同步(同步间隔秒)`"
              >
                <div class="flex flex-col">
                  <a-input-number
                      v-model="formData[`spider_${spider.id}_interval`]"
                      :min="0"
                      :step="1"
                      :placeholder="`${spider.name} - 输入0代表不同步`"
                      :disabled="!formData.isAutoSync"
                  />
                  <div class="text-xs text-gray-400 mt-1">{{ spider.remark }}</div>
                </div>
              </a-form-item>
            </div>
          </template>
          <template v-else>
            <div class="text-center py-8">
              <div class="text-gray-500 text-base">没有同步任务配置</div>
            </div>
          </template>
        </div>
        
        <!-- 交互信息 -->
        <div class="section mb-4 pb-4 border-b border-gray-200">
          <h4 class="text-base font-medium mb-4 text-blue-500">交互信息</h4>
          
          <a-form-item field="account" label="账号">
            <a-input v-model="formData.account" placeholder="请输入账号" />
          </a-form-item>
          
          <a-form-item field="password" label="密码">
            <a-input-password v-model="formData.password" placeholder="请输入密码" />
          </a-form-item>
          
          <!-- <a-form-item field="loginUrl" label="登录网址">
            <a-input v-model="formData.loginUrl" placeholder="请输入登录网址" />
          </a-form-item> -->
          
          <a-form-item field="cookies" label="Cookies">
            <a-textarea v-model="formData.cookies" placeholder="请输入Cookies" :auto-size="{ minRows: 3, maxRows: 5 }" />
          </a-form-item>
        </div>

      </a-form>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits, watch, onMounted } from 'vue';
import { Message, Modal } from '@arco-design/web-vue';
import { IconUser } from '@arco-design/web-vue/es/icon';
import orderApi from '@/api/master/order.js';
import spiderTasksApi from '@/api/spider/tasks.js';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  record: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'save']);

// 表单数据
const formData = reactive({
  storeName: '',
  principalName: '',
  invoiceTitle: '',
  channelId: '',
  isAutoSync: false,
  orderSyncInterval: 300,
  productSyncInterval: 600,
  invoiceSyncInterval: 900,
  reportSyncInterval: 1200,
  account: '',
  password: '',
  loginUrl: '',
  cookies: ''
});

// 渠道选项
const channelOptions = reactive([]);
const channelLoading = ref(false);

// 获取渠道列表
const fetchChannelList = async () => {
  channelLoading.value = true;
  try {
    const res = await orderApi.getChannelList();
    if (res.code === 200 && res.data && res.data.items) {
      channelOptions.length = 0; // 清空原有数据
      res.data.items.forEach(item => {
        channelOptions.push({
          id: item.id,
          name: item.name,
          iconUrl: item.iconUrl || ''
        });
      });
    } else {
      Message.error('获取渠道列表失败');
    }
  } catch (error) {
    console.error('获取渠道列表出错:', error);
    Message.error('获取渠道列表出错');
  } finally {
    channelLoading.value = false;
  }
};

// 组件挂载时获取渠道列表
onMounted(() => {
  // fetchChannelList();
});

// 获取爬虫类型中文名称（与详情页保持一致）
const getSpiderTypeName = (spiderType) => {
  switch(spiderType) {
    case 'order': return '订单';
    case 'goods': return '商品';
    case 'product': return '商品'; // 兼容product类型
    case 'invoice': return '发票';
    case 'report': return '报备';
    case 'inventory': return '库存';
    case 'price': return '价格';
    default: return '未知';
  }
};

// 监听record变化，更新表单数据
watch(() => props.record, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 复制基础字段
    formData.storeName = newVal.storeName || newVal.name || '';
    formData.principalName = newVal.principalName || '';
    formData.invoiceTitle = newVal.invoiceTitle || '';
    formData.channelId = newVal.channelId || '';

    // 复制同步设置字段，如果不存在则使用默认值
    // 处理isAutoSync字段，兼容1、0和布尔值
    formData.isAutoSync = newVal.isAutoSync === 1 || newVal.isAutoSync === true ? true : false;
    formData.orderSyncInterval = newVal.orderSyncInterval || 300;
    formData.productSyncInterval = newVal.productSyncInterval || 600;
    formData.invoiceSyncInterval = newVal.invoiceSyncInterval || 900;
    formData.reportSyncInterval = newVal.reportSyncInterval || 1200;

    // 如果有爬虫数据，初始化爬虫同步间隔
    if (newVal.spiders && newVal.spiders.length > 0) {
      newVal.spiders.forEach(spider => {
        const fieldName = `spider_${spider.id}_interval`;

        // 查找对应的 spiderTask，通过 spiderId 关联
        const relatedTask = newVal.spiderTasks?.find(task => task.spiderId === spider.id);

        // 如果找到对应的任务，使用任务中的 runInterval，否则默认为0
        // 如果 spiderTasks 为空或未找到匹配项，输入框回显默认为0
        formData[fieldName] = relatedTask ? relatedTask.runInterval : 0;
      });
    }

    // 复制交互信息字段
    formData.account = newVal.account || '';
    formData.password = newVal.password || '';
    formData.loginUrl = newVal.loginUrl || '';
    formData.cookies = newVal.cookies || '';
  }
}, { immediate: true, deep: true });

// 关闭抽屉
function closeDrawer() {
  emit('update:visible', false);
}

// 创建或更新爬虫任务
async function createOrUpdateSpiderTasks() {
  const tasks = [];

  // 遍历所有爬虫，为每个爬虫创建或更新任务
  for (const spider of props.record.spiders) {
    const intervalFieldName = `spider_${spider.id}_interval`;
    const runInterval = formData[intervalFieldName];

    // 如果同步间隔为0，跳过创建任务
    if (!runInterval || runInterval === 0) {
      continue;
    }

    // 检查是否已存在对应的任务（通过 spiderId 关联）
    const existingTask = props.record.spiderTasks?.find(task => task.spiderId === spider.id);

    const taskData = {
      spider_id: spider.id,
      platform_id: spider.platformId,
      store_id: props.record.id,
      spider_type: spider.spiderType,
      run_interval: runInterval
    };

    try {
      if (existingTask) {
        // 更新现有任务 - 调用 PUT /api/v1/spider/tasks/{id}
        console.log(`更新爬虫任务 (${spider.name}):`, taskData);
        const result = await spiderTasksApi.update(existingTask.id, taskData);
        if (result.code === 200) {
          tasks.push({ action: 'updated', spider: spider.name, result });
        } else {
          tasks.push({ action: 'failed', spider: spider.name, error: result.message || '更新失败' });
        }
      } else {
        // 创建新任务 - 调用 POST /api/v1/spider/tasks
        console.log(`创建爬虫任务 (${spider.name}):`, taskData);
        const result = await spiderTasksApi.create(taskData);
        if (result.code === 200) {
          tasks.push({ action: 'created', spider: spider.name, result });
        } else {
          tasks.push({ action: 'failed', spider: spider.name, error: result.message || '创建失败' });
        }
      }
    } catch (error) {
      console.error(`处理爬虫任务失败 (${spider.name}):`, error);
      // 如果某个任务创建/更新失败，记录错误但继续处理其他任务
      tasks.push({ action: 'failed', spider: spider.name, error: error.message || error.toString() });
    }
  }

  // 显示任务处理结果
  const successTasks = tasks.filter(t => t.action === 'created' || t.action === 'updated');
  const failedTasks = tasks.filter(t => t.action === 'failed');

  if (successTasks.length > 0) {
    Message.success(`成功处理 ${successTasks.length} 个爬虫任务`);
  }

  if (failedTasks.length > 0) {
    Message.warning(`${failedTasks.length} 个爬虫任务处理失败`);
  }
}

// 提交表单
function handleSubmit() {
  // 显示确认弹窗
  Modal.confirm({
    title: '确认提交',
    content: '确定要保存当前的编辑内容吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 构建要保存的数据对象，按照指定格式返回
        const saveData = {
          id: props.record.id,
          name: formData.storeName,
          code: props.record.code || props.record.storeCode,
          platformId: props.record.platformId,
          status: props.record.status,
          accountConfig: {
            username: formData.account,
            password: formData.password,
            apiKey: props.record.accountConfig?.apiKey || ""
          },
          cookies: formData.cookies,
          remark: props.record.remark || "",
          principalName: formData.principalName,
          invoiceTitle: formData.invoiceTitle,
          isAutoSync: formData.isAutoSync // 添加isAutoSync字段
        };

        // 如果开启了自动同步，需要创建或更新爬虫任务
        if (formData.isAutoSync && props.record.spiders && props.record.spiders.length > 0) {
          await createOrUpdateSpiderTasks();
        }

        // 发送保存事件，传递完整的数据
        emit('submit', saveData);


        // 关闭抽屉
        closeDrawer();
      } catch (error) {
        console.error('保存失败:', error);
        Message.error('保存失败: ' + (error.message || '未知错误'));
      }
    },
    onCancel: () => {
      // 用户取消，不执行任何操作
      console.log('用户取消了提交操作');
    }
  });
}
</script>

<style scoped>
/* 编辑抽屉样式 */
.section {
  margin-bottom: 20px;
}
</style>
