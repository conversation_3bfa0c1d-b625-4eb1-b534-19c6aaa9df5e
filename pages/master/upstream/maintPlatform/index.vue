
<template>
    <div class="ma-content-block lg:flex justify-between p-4 gap-4">
        <div
            class="lg:w-4/12 w-full mb-4 lg:mb-0 border rounded-md p-3 bg-white dark:bg-blackdark-5 dark:border-blackdark-3 shadow">
            <div class="flex items-center justify-between mb-3">
                <h3 class="text-lg font-semibold">平台分类</h3>
                <a-button type="primary" size="small" @click="handleAddRoot">
                    <template #icon><icon-plus /></template> 添加根分类
                </a-button>
            </div>
            <a-input v-model="filterText" placeholder="搜索分类名称" allow-clear class="mb-2">
                <template #suffix>
                    <icon-search />
                </template>
            </a-input>
            <div class="category-container customer-scrollbar">
                <!-- 根分类列表 -->
                <div class="mb-4">
                    <div v-for="category in filteredTreeData" :key="category.id" 
                         :class="['category-item', selectedCategory && selectedCategory.id === category.id ? 'selected-category' : '']" 
                         @click="selectCategory(category)">
                        <div class="flex items-center justify-between p-2 hover:bg-gray-100 dark:hover:bg-blackdark-4 rounded">
                            <div class="flex items-center">
                                <i class="iconfont mr-2 text-lg" :class="category.icon"></i>
                                <span class="truncate font-medium">{{ category.name || '未命名分类' }}</span>
                            </div>
                            <a-space size="mini">
                                <a-button type="text" size="mini" @click.stop="handleEdit(category)">
                                    <template #icon><icon-edit /></template>
                                </a-button>
                                <a-button type="text" size="mini" @click.stop="handleAddChild(category)">
                                    <template #icon><icon-plus /></template>
                                </a-button>
                                <a-popconfirm content="确定要删除此分类及其子分类吗?" @ok="handleDelete(category)">
                                    <a-button type="text" status="danger" size="mini" @click.stop>
                                        <template #icon><icon-delete /></template>
                                    </a-button>
                                </a-popconfirm>
                            </a-space>
                        </div>
                        
                        <!-- 子分类列表 -->
                        <div v-if="category.children && category.children.length && (selectedCategory && selectedCategory.id === category.id)" 
                             class="ml-6 mt-1 border-l-2 border-gray-200 dark:border-blackdark-3 pl-2">
                            <div v-for="child in category.children" :key="child.id" 
                                 :class="['subcategory-item', selectedCategory && selectedSubcategory && selectedSubcategory.id === child.id ? 'selected-subcategory' : '']" 
                                 @click.stop="selectSubcategory(child, $event)">
                                <div class="flex items-center justify-between p-2 hover:bg-gray-100 dark:hover:bg-blackdark-4 rounded">
                                    <div class="flex items-center">
                                        <i class="iconfont mr-2" :class="child.icon"></i>
                                        <span class="truncate">{{ child.name || '未命名子分类' }}</span>
                                    </div>
                                    <a-space size="mini">
                                        <a-button type="text" size="mini" @click.stop="handleEdit(child)">
                                            <template #icon><icon-edit /></template>
                                        </a-button>
                                        <a-popconfirm content="确定要删除此分类吗?" @ok="handleDelete(child)">
                                            <a-button type="text" status="danger" size="mini" @click.stop>
                                                <template #icon><icon-delete /></template>
                                            </a-button>
                                        </a-popconfirm>
                                    </a-space>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="lg:w-8/12 w-full border rounded-md p-4 bg-white dark:bg-blackdark-5 dark:border-blackdark-3 shadow">
            <div class="text-lg font-semibold mb-4">
                {{ formMode === 'add' ? '新增分类详情' : formMode === 'edit' ? '编辑分类详情' : '分类详情' }}
            </div>
            <ma-form ref="maFormRef" v-model="formModel" :columns="columns" :options="formOptions" auto-label-width
                @submit="handleSubmit">
                <template #form-parent_id>
                    <a-select v-model="formModel.parent_id" :disabled="formMode === 'edit'"
                        placeholder="选择父级分类（不选则为根分类）" allow-clear allow-search>
                        <a-option :value="0" key="0">根分类</a-option>
                        <template v-for="item in classData" :key="item.id">
                            <a-option :value="item.id" :label="item.name">{{ item.name }}</a-option>
                        </template>
                    </a-select>
                </template>

                 <!-- 关联属性模板 -->
                 <template #form-template_id>
                    <a-select v-model="formModel.template_id" placeholder="请选择关联属性模板" allow-clear allow-search>
                        <a-option value="0">无</a-option>
                        <a-option value="1">手机属性</a-option>
                        <a-option value="2">服装属性</a-option>
                        <a-option value="3">家具属性</a-option>
                        <a-option value="4">电脑属性</a-option>
                    </a-select>
                </template>

                
            </ma-form>
            <div v-if="formMode === 'view'" class="p-4 text-center text-gray-500">
                请在左侧选择产品分类进行编辑，或点击按钮添加新分类。
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconPlus, IconEdit, IconDelete, IconSearch } from '@arco-design/web-vue/es/icon';
import tool from '@/utils/tool';

// 导入假数据（仅用于演示）
const platformCategoriesData = {
    code: 200,
    data: [
        {
            id: '1',
            name: '军政企',
            parent_id: '0',
            is_enabled: '1',
            icon: 'icon-gouwu',
            sort: 1,
            children: [
                {
                    id: '11',
                    name: '淘宝',
                    parent_id: '1',
                    is_enabled: '1',
                    icon: 'icon-taobao',
                    sort: 1,
                },
                {
                    id: '12',
                    name: '京东',
                    parent_id: '1',
                    is_enabled: '1',
                    icon: 'icon-jingdong',
                    sort: 2,
                },
                {
                    id: '13',
                    name: '拼多多',
                    parent_id: '1',
                    is_enabled: '1',
                    icon: 'icon-pinduoduo',
                    sort: 3,
                },
                {
                    id: '14',
                    name: '抖店',
                    parent_id: '1',
                    is_enabled: '1',
                    icon: 'icon-doudian',
                    sort: 4,
                }
            ]
        },
        {
            id: '2',
            name: '主流电商',
            parent_id: '0',
            is_enabled: '1',
            icon: 'icon-jine',
            sort: 2,
            children: [
                {
                    id: '21',
                    name: '用友',
                    parent_id: '2',
                    is_enabled: '1',
                    icon: 'icon-yongyou',
                    sort: 1,
                },
                {
                    id: '22',
                    name: '金蝶',
                    parent_id: '2',
                    is_enabled: '1',
                    icon: 'icon-jine',
                    sort: 2,
                }
            ]
        },
        {
            id: '3',
            name: 'ERP',
            parent_id: '0',
            is_enabled: '1',
            icon: 'icon-zhifu',
            sort: 3,
            children: [
                {
                    id: '31',
                    name: '支付宝',
                    parent_id: '3',
                    is_enabled: '1',
                    icon: 'icon-alipay',
                    sort: 1,
                },
                {
                    id: '32',
                    name: '微信支付',
                    parent_id: '3',
                    is_enabled: '1',
                    icon: 'icon-weixinzhifu',
                    sort: 2,
                }
            ]
        }
    ]
};

const crawlerModulesData = {
  code: 200,
  message: '获取成功',
  data: {
    total: 10,
    items: [
      {
        id: 1,
        name: '电商通用爬虫',
        description: '适用于各大电商平台的通用爬虫模块',
        status: 1
      },
      {
        id: 11,
        name: '淘宝专用爬虫',
        description: '专门针对淘宝平台优化的爬虫模块',
        status: 1
      },
      {
        id: 12,
        name: '京东专用爬虫',
        description: '专门针对京东平台优化的爬虫模块',
        status: 1
      },
      {
        id: 13,
        name: '拼多多专用爬虫',
        description: '专门针对拼多多平台优化的爬虫模块',
        status: 1
      },
      {
        id: 2,
        name: '社交媒体通用爬虫',
        description: '适用于各大社交媒体平台的通用爬虫模块',
        status: 1
      },
      {
        id: 21,
        name: '微博专用爬虫',
        description: '专门针对微博平台优化的爬虫模块',
        status: 1
      },
      {
        id: 22,
        name: '抖音专用爬虫',
        description: '专门针对抖音平台优化的爬虫模块',
        status: 1
      }
    ]
  }
};

// 页面元数据定义
definePageMeta({
    name: 'master-upstream-maintPlatform',
    path: '/master/upstream/maintPlatform'
});

const maFormRef = ref();
const filterText = ref('');
const formMode = ref('view');
const classData = ref([]);
const filteredTreeData = ref([]);
const crawlerModules = ref([]);
const selectedCategory = ref(null);
const selectedSubcategory = ref(null);

const initialForm = {
    id: undefined,
    parent_id: 0, 
    name: '',
    icon: '',
    crawler_module_id: undefined,
    sort_order: 1,
    is_enabled: '1', 
    description: '',
};
const formModel = ref({ ...initialForm });
formMode.value = 'add';

const formOptions = reactive({
    labelWidth: '130px',
    showButtons: true, 
    submitText: '保存',
    resetText: '重置',
});

const columns = reactive([
    {
        title: '分类ID',
        dataIndex: 'id',
        formType: 'input',
        disabled: true,
        hide: computed(() => formMode.value === 'add'),
        labelWidth: '135px'
    },
    {
        title: '分类名称',
        dataIndex: 'name',
        formType: 'input',
        rules: [{ required: true, message: '分类名称必填' }],
        labelWidth: '135px'
    },
    { 
        title: '上级分类', 
        dataIndex: 'parent_id', 
        formType: 'select', 
        hide: false, 
        addDisplay: true, 
        editDisplay: true,
        disabled: computed(() => formMode.value === 'edit'), 
        labelWidth: '135px' 
    },
    {
        title: '分类图标',
        dataIndex: 'icon',
        formType: 'select',
        placeholder: '请选择图标',
        labelWidth: '135px',
        options: [
            { label: '购物', value: 'icon-gouwu' },
            { label: '淘宝', value: 'icon-taobao' },
            { label: '京东', value: 'icon-jingdong' },
            { label: '拼多多', value: 'icon-pinduoduo' },
            { label: '抖店', value: 'icon-doudian' },
            { label: '用友', value: 'icon-yongyou' },
            { label: '金额', value: 'icon-jine' },
            { label: '支付宝', value: 'icon-alipay' },
            { label: '微信支付', value: 'icon-weixinzhifu' },
            { label: '支付', value: 'icon-zhifu' }
        ]
    },
    {
        title: '爬虫模块绑定',
        dataIndex: 'crawler_module_id',
        formType: 'select',
        labelWidth: '135px',
        dict: { 
            data: computed(() => {
                return crawlerModules.value.map(item => ({
                    label: item.name,
                    value: item.id
                }));
            })
        }
    },
    { 
        title: '分类描述', 
        dataIndex: 'description', 
        formType: 'textarea',
        labelWidth: '135px' 
    },
    {
        title: '排序',
        dataIndex: 'sort_order',
        formType: 'input-number',
        min: 0,
        max: 999,
        addDefaultValue: 100,
        labelWidth: '135px'
    },
    {
        title: '状态',
        dataIndex: 'is_enabled',
        formType: 'radio',
        addDefaultValue: '1',
        dict: { data: [{ label: '启用', value: '1' }, { label: '禁用', value: '0' }] },
        labelWidth: '135px',
    }
]);

// 获取父分类选项
const getParentOptions = computed(() => {
    const options = [];
    options.push({ value: '0', label: '根分类' });
    
    // 只有在添加模式下，才需要获取可选的父分类
    if (formMode.value === 'add') {
        const addParentOptions = (items) => {
            if (!items) return;
            items.forEach(item => {
                // 排除当前编辑的节点及其子节点作为父节点选项
                if (formModel.value.id !== item.id) {
                    options.push({
                        value: String(item.id),
                        label: item.name
                    });
                }
                if (item.children && item.children.length > 0) {
                    addParentOptions(item.children);
                }
            });
        };
        addParentOptions(classData.value);
    }
    return options;
});

// 初始化数据
const initData = () => {
    // 模拟API调用，实际项目中应该替换为真实API
    // 获取分类数据
    classData.value = platformCategoriesData.data || [];
    filteredTreeData.value = [...classData.value];
    
    // 获取爬虫模块数据
    crawlerModules.value = crawlerModulesData.data.items || [];
};

// 获取平台分类列表
const getList = () => {
    // 实际环境中应该调用API
    // productManagementApi.getList().then(res => {
    //     classData.value = res.data.items;
    //     filteredTreeData.value = res.data.items;
    // });
    
    // 使用假数据
    initData();
}

// 获取爬虫模块列表
const getCrawlerModules = () => {
    // 实际环境中应该调用API
    // 使用假数据
    crawlerModules.value = crawlerModulesData.data.items;
}

onMounted(() => {
    getList();
    getCrawlerModules();
});

watch(filterText, (val) => {
    if (!val) {
        filteredTreeData.value = [...classData.value];
        return;
    }
    
    // 过滤树数据，保留匹配的节点及其祖先节点
    const filterTree = (tree, keyword) => {
        const result = [];
        
        for (const node of tree) {
            // 创建节点副本，避免修改原始数据
            const newNode = { ...node };
            
            // 检查当前节点是否匹配
            const isMatch = node.name && node.name.toLowerCase().includes(keyword.toLowerCase());
            
            // 递归过滤子节点
            if (node.children && node.children.length) {
                newNode.children = filterTree(node.children, keyword);
            }
            
            // 如果当前节点匹配或者有匹配的子节点，则保留该节点
            if (isMatch || (newNode.children && newNode.children.length)) {
                result.push(newNode);
            }
        }
        
        return result;
    };
    
    filteredTreeData.value = filterTree(classData.value, val);
});

// 选择根分类
const selectCategory = (category) => {
    if (selectedCategory.value && selectedCategory.value.id === category.id) {
        // 如果点击的是当前选中的分类，则取消选择
        selectedCategory.value = null;
        selectedSubcategory.value = null;
        resetFormView();
    } else {
        // 选择新的分类
        selectedCategory.value = category;
        selectedSubcategory.value = null;
        
        // 编辑该分类
        const nodeData = { ...category };
        if (nodeData.is_enabled !== undefined) {
            nodeData.is_enabled = String(nodeData.is_enabled);
        }
        formModel.value = nodeData;
        formMode.value = 'edit';
        maFormRef.value?.clearValidate && maFormRef.value.clearValidate();
    }
};

// 选择子分类
const selectSubcategory = (subcategory, event) => {
    if (selectedSubcategory.value && selectedSubcategory.value.id === subcategory.id) {
        // 如果点击的是当前选中的子分类，则保持选择根分类但取消选择子分类
        selectedSubcategory.value = null;
        // 显示根分类的信息
        const nodeData = { ...selectedCategory.value };
        if (nodeData.is_enabled !== undefined) {
            nodeData.is_enabled = String(nodeData.is_enabled);
        }
        formModel.value = nodeData;
    } else {
        // 选择新的子分类
        selectedSubcategory.value = subcategory;
        
        // 编辑该子分类
        const nodeData = { ...subcategory };
        if (nodeData.is_enabled !== undefined) {
            nodeData.is_enabled = String(nodeData.is_enabled);
        }
        formModel.value = nodeData;
        formMode.value = 'edit';
        maFormRef.value?.clearValidate && maFormRef.value.clearValidate();
    }
    
    // 阻止事件冒泡
    if (event) {
        event.stopPropagation();
    }
};

const handleAddRoot = () => {
    resetFormView(); 
    formModel.value.parent_id = 0; 
    formMode.value = 'add';
};

const handleAddChild = (parentData) => {
    resetFormView();
    formModel.value.parent_id = parentData.id;
    formMode.value = 'add';
};

const resetFormView = () => {
    formModel.value = { ...initialForm };
    formMode.value = 'view';
    // 如果组件提供了重置方法，则调用
    maFormRef.value?.resetFields && maFormRef.value.resetFields();
    maFormRef.value?.clearValidate && maFormRef.value.clearValidate();
};

const handleSubmit = (formData) => {
    // 确保is_enabled是数字类型
    if (formData.is_enabled !== undefined) {
        formData.is_enabled = Number(formData.is_enabled);
    }

    // 为演示目的，我们直接在本地数据中更新
    if (formMode.value === 'add') {
        // 生成一个新的唯一ID
        const newId = Math.max(...classData.value.map(item => item.id), 0) + 1;
        formData.id = newId;
        
        if (formData.parent_id === 0) {
            // 添加根分类
            formData.children = [];
            classData.value.push(formData);
        } else {
            // 添加子分类
            const parentNode = findNodeById(classData.value, formData.parent_id);
            if (parentNode) {
                if (!parentNode.children) {
                    parentNode.children = [];
                }
                parentNode.children.push(formData);
            }
        }
        
        Message.success('分类添加成功');
    } else if (formMode.value === 'edit') {
        // 更新现有分类
        const node = findNodeById(classData.value, formData.id);
        if (node) {
            Object.assign(node, formData);
            Message.success('分类更新成功');
        } else {
            Message.error('找不到要更新的分类');
        }
    }
    
    filteredTreeData.value = [...classData.value];
    resetFormView();
};

const handleDelete = (data) => {
    // 模拟删除成功
    Message.success(`分类 "${data.name}" 及其子分类已删除`);
    
    // 为演示目的，我们直接从本地数据中删除
    if (data.parent_id === 0) {
        // 删除根分类
        const index = classData.value.findIndex(item => item.id === data.id);
        if (index !== -1) {
            classData.value.splice(index, 1);
        }
    } else {
        // 删除子分类
        const parentNode = findNodeById(classData.value, data.parent_id);
        if (parentNode && parentNode.children) {
            const index = parentNode.children.findIndex(item => item.id === data.id);
            if (index !== -1) {
                parentNode.children.splice(index, 1);
            }
        }
    }
    
    filteredTreeData.value = [...classData.value];
    resetFormView();
};

const findNodeById = (tree, id) => {
    for (const node of tree) {
        if (node.id === id) {
            return node;
        }
        if (node.children) {
            const found = findNodeById(node.children, id);
            if (found) {
                return found;
            }
        }
    }
    return null;
};

const handleEdit = (nodeData) => {
    if (nodeData) {
        nodeData.is_enabled = String(nodeData.is_enabled);
        formModel.value = { ...nodeData };
        formMode.value = 'edit';
        
        // 如果组件提供了清除验证方法，则调用
        maFormRef.value?.clearValidate && maFormRef.value.clearValidate();
    }
};
</script>

<style scoped>
.category-container {
    height: 100%;
    max-height: calc(100vh - 240px);
    overflow: auto;
}

.category-item {
    margin-bottom: 0.5rem;
    border-radius: 0.375rem;
    cursor: pointer;
}

.selected-category {
    background-color: rgb(var(--primary-1));
}

.selected-category > div:first-child {
    color: rgb(var(--primary-6));
}

.subcategory-item {
    margin-top: 0.25rem;
    border-radius: 0.25rem;
    cursor: pointer;
}

.selected-subcategory {
    background-color: rgb(var(--primary-1));
}

.selected-subcategory > div {
    color: rgb(var(--primary-6));
}
</style>