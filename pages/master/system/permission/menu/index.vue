<!--
 - <PERSON><PERSON><PERSON><PERSON> is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div class="ma-content-block lg:flex justify-between p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef">
      <!-- 排序列 -->
      <!-- <template #sort="{ record }">
        <a-input-number :default-value="record.sort" mode="button" @change="changeSort($event, record.id)" :min="0"
          :max="1000" />
      </template>-->
      <!-- 图标列 -->
      <!-- <template #icon="{ record }">
        <component :is="record.icon" v-if="record.icon" />
      </template>-->
      <!-- 状态列 -->
      <template #status="{ record }">{{ record.status == 1 ? '正常' : '停用' }}</template>
      <!-- 隐藏列 -->
      <template #is_hidden="{ record }">{{ record.is_hidden == 1 ? '是' : '否' }}</template>
      <!-- 自定义列 - 创建时间 -->
      <template #created_at="{ record }">
        <div v-time="record.created_at"></div>
      </template>

      <!-- 操作前置扩展 -->
      <template #operationBeforeExtend="{ record }">
        <a-link @click="openAdd(record.id)" v-if="record.type === 'M' && !isRecovery">
          <icon-plus />新增
        </a-link>
      </template>
    </ma-crud>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from "vue";
import { Message } from "@arco-design/web-vue";
import systemApi from "@/api/master/system";
const menuApi = systemApi.menu;
const crudRef = ref();
const currentParentId = ref();

let isRecovery = computed(() =>
  crudRef.value ? crudRef.value.isRecovery : false
);

const menuType = [
  { label: "菜单", value: "M" },
  { label: "按钮", value: "B" },
  { label: "外链", value: "L" },
  { label: "iFrame", value: "I" }
];

const openAdd = id => {
  columns[1].addDefaultValue = id;
  crudRef.value.crudFormRef.add();
};

const crud = reactive({
  api: menuApi.tree,
  showIndex: false,
  pageLayout: "fixed",
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 200,
  add: { show: true, api: menuApi.create },
  edit: { show: true, api: menuApi.update },
  delete: {
    show: true,
    api: menuApi.delete
  },
  formOption: { viewType: "drawer", width: 600 },
  isExpand: true,
  beforeAdd: params => {
    if (params.parent_id) {
      params.parentId = params.parent_id;
    }
    params.isHidden = params.is_hidden;
    delete params.is_hidden;
    delete params.parent_id;

    Object.keys(params).forEach(key => {
      if (
        params[key] === null ||
        params[key] === undefined ||
        params[key] === ""
      ) {
        delete params[key];
      }
    });
    console.log("编辑前处理后参数:", params);

    return params;
  },
  beforeEdit: params => {
    console.log("编辑前原始参数:", params);
    if (params.parent_id) {
      params.parentId = params.parent_id;
    }
    params.isHidden = params.is_hidden;
    delete params.is_hidden;
    delete params.parent_id;

    return params;
  },
  // 搜索前处理参数
  beforeSearch: params => {
    // 如果有创建时间参数，转换为时间戳
    if (params.created_at) {
      params.startTime = new Date(params.created_at[0]).getTime() / 1000;
      params.endTime = new Date(params.created_at[1]).getTime() / 1000;
      delete params.created_at;
    } else {
      delete params.startTime;
      delete params.endTime;
    }
    return params;
  }
});

const columns = reactive([
  {
    title: "ID",
    dataIndex: "id",
    addDisplay: false,
    editDisplay: false,
    width: 50,
    hide: true
  },
  {
    title: "上级菜单",
    dataIndex: "parent_id",
    hide: true,
    formType: "tree-select",
    dict: {
      data: async () => {
        const res = await menuApi.tree();
        const transform = item => {
          return {
            value: item.id,
            label: item.name,
            children: item.children ? item.children.map(transform) : undefined
          };
        };
        return res.data.map(transform);
      }
    },
    addDefaultValue: 0,
    editDefaultValue: record => {
      return record.parent_id == 0 ? undefined : record.parent_id;
    }
  },
  {
    title: "菜单名称",
    dataIndex: "name",
    search: true,
    commonRules: [{ required: true, message: "菜单名称必填" }],
    width: 180
  },
  {
    title: "菜单类型",
    dataIndex: "type",
    formType: "radio",
    addDefaultValue: "M",
    width: 100,
    dict: {
      data: menuType,
      translation: true,
      tagColors: { M: "blue", B: "green", L: "orangered", I: "pinkpurple" }
    },
    // 该方法是对菜单类型的控制，根据菜单类型的值，来控制其他字段的显示隐藏
    // value 是菜单类型的值，maFormObject 是当前表单对象
    onControl: (value, maFormObject) => {
      if (!maFormObject) return;

      const service = maFormObject.getColumnService();
      if (!service) return;

      const dataIndexList = [
        "icon",
        "route",
        "component",
        "redirect",
        "sort",
        "is_hidden",
        "restful"
      ];

      if (value === "B") {
        dataIndexList.forEach(name => {
          const field = service.get(name);
          if (field && typeof field.setAttr === "function") {
            field.setAttr("display", false);
          }
        });
        return;
      }

      if (["I", "L"].includes(value)) {
        dataIndexList.forEach(name => {
          const field = service.get(name);
          if (field && typeof field.setAttr === "function") {
            field.setAttr(
              "display",
              ["icon", "route", "sort", "is_hidden"].includes(name)
            );
          }
        });
        return;
      }

      dataIndexList.forEach(name => {
        const field = service.get(name);
        if (field && typeof field.setAttr === "function") {
          field.setAttr("display", true);
        }
      });
    }
  },
  {
    title: "图标",
    dataIndex: "icon",
    width: 80,
    formType: "icon-picker",
    style: { width: "100%" },
    formProps: {
      showTabs: ["arco"] // 只显示arco图标
    }
  },
  {
    title: "菜单标识",
    dataIndex: "code",
    commonRules: [{ required: true, message: "菜单标识必填" }],
    width: 150
  },
  { title: "路由地址", dataIndex: "route", width: 150 },
  { title: "视图组件", dataIndex: "component", width: 200 },
  { title: "重定向", dataIndex: "redirect", hide: true },
  {
    title: "排序",
    dataIndex: "sort",
    formType: "input-number",
    addDefaultValue: 1,
    width: 180,
    min: 0,
    max: 1000
  },
  {
    title: "隐藏",
    dataIndex: "is_hidden",
    formType: "radio",
    dict: {
      data: [
        { label: "否", value: 0 },
        { label: "是", value: 1 }
      ]
    },
    addDefaultValue: 0,
    width: 80
  },
  {
    title: "状态",
    dataIndex: "status",
    search: true,
    formType: "radio",
    dict: {
      data: [
        { label: "启用", value: 1 },
        { label: "禁用", value: 0 }
      ]
    },
    addDefaultValue: 1,
    width: 120
  },
  // {
  //   title: '生成按钮', dataIndex: 'restful', hide: true, formType: 'radio',
  //   dict: {
  //     data: [{ label: '是', value: '1' }, { label: '否', value: '2' }]
  //   },
  //   addDefaultValue: '2', editDisplay: false
  // },
  {
    title: "备注",
    dataIndex: "remark",
    hide: true,
    formType: "textarea"
  },
  {
    title: "创建时间",
    dataIndex: "created_at",
    addDisplay: false,
    editDisplay: false,
    search: true,
    formType: "range",
    width: 180,
    form: false
  }
]);
</script>

<script>
export default { name: "system:menu" };
</script>

<style scoped>
.icon {
  width: 1em;
}
</style>