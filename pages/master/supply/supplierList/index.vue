<template>
  <div class="supplier-list">
    <ma-crud
      ref="crudRef"
      :options="crudOptions"
      :columns="columns"
      :data="mockSuppliers"
    >
      <!-- 供应商信息 -->
      <template #supplier_info="{ record }">
        <div class="supplier-info" >
          <div style="display: flex; align-items: center;">
            <div><span>供应商名称：</span><span>{{ record.supplier_name }}</span></div>
            <div style="margin-left: 10px;"><span>代码：</span>{{ record.supplier_code }}</div>
          </div>
          <div style="display: flex; align-items: center;">
            <div><span>联系人：</span>{{ record.contact_person }}</div>
            <div style="margin-left: 10px;"> {{ record.position }}</div>
            <div style="margin-left: 10px;">  {{ record.phone }}</div>
          </div>
          <div><span>公司性质：</span>{{ record.company_nature }}</div>
          <div><span>通信地址：</span>{{ record.address }}</div>
        </div>
      </template>
      <!-- 操作信息 -->
      <template #operation_info="{ record }">
        <div class="operation-info">
          <div><span>部门：</span>{{ record.department }}</div>
          <div><span>提交人：</span>{{ record.submitter }}</div>
          <div><span>更新人：</span>{{ record.updater || '-' }}</div>
          <div><span>复审人：</span>{{ record.reviewer || '-' }}</div>
          <div><span>提交日期：</span>{{ record.submit_date }}</div>
          <div><span>更新时间：</span>{{ record.update_date || '-' }}</div>
        </div>
      </template>
      <!-- 主营产品 -->
      <template #main_products="{ record }">
        <div class="products">
          <a-tag v-for="product in record.main_products" :key="product" color="blue">
            {{ product }}
          </a-tag>
        </div>
      </template>

      <!-- 合作信息 -->
      <template #cooperation_info="{ record }">
        <div class="cooperation-info">
          <div class="cooperation-item">
            <span class="cooperation-label">合作方式：</span>
            <a-tag :color="getCooperationTypeColor(record.cooperation_type)">
              {{ record.cooperation_type }}
            </a-tag>
          </div>
          <div class="cooperation-item">
            <span class="cooperation-label">合作状态：</span>
            <a-tag :color="getCooperationStatusColor(record.cooperation_status)">
              {{ record.cooperation_status }}
            </a-tag>
          </div>
        </div>
      </template>

      <!-- 品牌授权 -->
      <template #brand_authorization="{ record }">
        <div class="brand-auth">
          <div v-for="brand in record.brands" :key="brand.name" class="brand-item">
            <a-tag :color="brand.authorized ? 'green' : 'orange'">
              {{ brand.name }}{{ brand.authorized ? '(已授权)' : '(未授权)' }}
            </a-tag>
          </div>
        </div>
      </template>


      <!-- 自定义操作按钮 -->
      <template #operationCell="{ record }">
        <a-space direction="vertical" fill>
          <a-button type="text" size="small" @click="handleView(record)">
            <template #icon><icon-eye /></template>
            详情
          </a-button>
          <a-button type="text" size="small" @click="handleEdit(record)">
            <template #icon><icon-edit /></template>
            编辑
          </a-button>
          <a-button type="text" size="small" @click="handleChangeReviewer(record)">
            <template #icon><icon-user /></template>
            更换复核人
          </a-button>
        </a-space>
      </template>

      <!-- 合作信息搜索自定义 -->
      <template #search-cooperation_info="{ searchForm, component }">
        <a-select
          v-model="searchForm.cooperation_info"
          placeholder="请选择合作方式或状态"
          allow-clear
        >
          <a-optgroup label="合作方式">
            <a-option value="长期合作">长期合作</a-option>
            <a-option value="项目合作">项目合作</a-option>
            <a-option value="临时采购">临时采购</a-option>
          </a-optgroup>
          <a-optgroup label="合作状态">
            <a-option value="正常合作">正常合作</a-option>
            <a-option value="暂停合作">暂停合作</a-option>
            <a-option value="待审核">待审核</a-option>
            <a-option value="已终止">已终止</a-option>
          </a-optgroup>
        </a-select>
      </template>
    </ma-crud>

    <!-- 供应商详情组件 -->
    <SupplierDetail
      v-model:visible="detailVisible"
      :supplier-data="currentSupplier"
      @edit-success="handleEditSuccess"
    />

    <!-- 供应商编辑组件 -->
    <SupplierEdit
      v-model:visible="editVisible"
      :supplier-data="currentSupplier"
      @save-success="handleEditSuccess"
    />

    <!-- 更换复核人弹框 -->
    <a-modal
      v-model:visible="changeReviewerVisible"
      title="更换复核人"
      width="400px"
      @ok="handleConfirmChangeReviewer"
      @cancel="handleCancelChangeReviewer"
      ok-text="确认"
      cancel-text="取消"
    >
      <a-form :model="reviewerForm" layout="vertical">
        <a-form-item label="选择复核人：" required>
          <a-select
            v-model="reviewerForm.reviewer"
            placeholder="请选择"
            allow-clear
            :options="reviewerOptions"
          />
        </a-form-item>
        <div class="reviewer-note">
          <span style="color: #ff4d4f;">注：一旦更换，原来的复核人将不再具有该供应商的复核权限。</span>
        </div>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, h } from "vue";
import { Message } from "@arco-design/web-vue";
import SupplierDetail from './detail/index.vue';
import SupplierEdit from './edit/index.vue';

// 定义页面路由元信息
definePageMeta({
  name: "supplierList",
  path: "/master/supply/supplierList",
});

const crudRef = ref();

// 详情相关
const detailVisible = ref(false);
const editVisible = ref(false);
const currentSupplier = ref({});

// 更换复核人相关
const changeReviewerVisible = ref(false);
const reviewerForm = reactive({
  reviewer: ''
});

// 复核人选项
const reviewerOptions = [
  { label: '供应链-胡晓梅', value: '供应链-胡晓梅' },
  { label: '陈伟锋', value: '陈伟锋' },
  { label: '供应链-陈小娇', value: '供应链-陈小娇' },
  { label: '供应链-吴诗君', value: '供应链-吴诗君' },
  { label: '供应链-匡晓西', value: '供应链-匡晓西' }
];

// 模拟API函数
const mockApi = {
  // 获取列表
  getList: async (params = {}) => {
    console.log('搜索参数:', params);

    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    let filteredData = [...mockSuppliers];

    // 根据Tab过滤
    if (params.cooperation_status === "待审核") {
      filteredData = filteredData.filter(item => item.cooperation_status === "待审核");
    }

    // 根据搜索条件过滤
    if (params.department) {
      filteredData = filteredData.filter(item => item.department.includes(params.department));
    }
    if (params.contact_person) {
      filteredData = filteredData.filter(item => item.contact_person.includes(params.contact_person));
    }
    if (params.supplier_name) {
      filteredData = filteredData.filter(item => item.supplier_name.includes(params.supplier_name));
    }
    if (params.brand) {
      filteredData = filteredData.filter(item =>
        item.brands.some(brand => brand.name.includes(params.brand))
      );
    }
    if (params.main_products) {
      filteredData = filteredData.filter(item =>
        item.main_products.includes(params.main_products)
      );
    }
    if (params.company_nature) {
      filteredData = filteredData.filter(item => item.company_nature === params.company_nature);
    }
    if (params.cooperation_info) {
      filteredData = filteredData.filter(item =>
        item.cooperation_type === params.cooperation_info ||
        item.cooperation_status === params.cooperation_info
      );
    }
    if (params.payment_terms) {
      filteredData = filteredData.filter(item => item.payment_terms.includes(params.payment_terms));
    }

    // 新增的搜索字段过滤逻辑
    if (params.submitter) {
      filteredData = filteredData.filter(item => item.submitter.includes(params.submitter));
    }
    if (params.supplier_relation) {
      filteredData = filteredData.filter(item => item.supplier_relation.includes(params.supplier_relation));
    }
    if (params.reviewer) {
      filteredData = filteredData.filter(item => item.reviewer && item.reviewer.includes(params.reviewer));
    }
    if (params.cooperation_type_search) {
      filteredData = filteredData.filter(item => item.cooperation_type === params.cooperation_type_search);
    }
    if (params.cooperation_status_search) {
      filteredData = filteredData.filter(item => item.cooperation_status === params.cooperation_status_search);
    }
    if (params.authorization_status) {
      filteredData = filteredData.filter(item => {
        const hasAuthorization = item.brands.some(brand => brand.authorized);
        return params.authorization_status === '有' ? hasAuthorization : !hasAuthorization;
      });
    }
    if (params.address) {
      filteredData = filteredData.filter(item => item.address.includes(params.address));
    }
    if (params.updater) {
      filteredData = filteredData.filter(item => item.updater && item.updater.includes(params.updater));
    }

    // 模拟分页
    const page = params.page || 1;
    const pageSize = params.pageSize || 10;
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const paginatedData = filteredData.slice(start, end);

    return {
      code: 200,
      message: '获取成功',
      data: {
        items: paginatedData,
        total: filteredData.length
      }
    };
  },

  // 新增
  create: async (data) => {
    console.log('新增供应商:', data);
    await new Promise(resolve => setTimeout(resolve, 300));
    Message.success('新增供应商成功');
    return { code: 200, message: '新增成功' };
  },

  // 更新
  update: async (id, data) => {
    console.log('更新供应商:', id, data);
    await new Promise(resolve => setTimeout(resolve, 300));
    Message.success('更新供应商成功');
    return { code: 200, message: '更新成功' };
  },

  // 删除
  delete: async (id) => {
    console.log('删除供应商:', id);
    await new Promise(resolve => setTimeout(resolve, 300));
    Message.success('删除供应商成功');
    return { code: 200, message: '删除成功' };
  }
};

// 模拟数据
const mockSuppliers = [
  {
    id: 1,
    department: "采购部",
    submitter: "张三",
    reviewer: "李经理",
    submit_date: "2024-01-15",
    updater: "王经理",
    update_date: "2024-01-20",
    supplier_name: "深圳市科技有限公司",
    supplier_code: "SUP001",
    contact_person: "王五",
    position: "销售经理",
    phone: "***********",
    company_nature: "有限责任公司",
    address: "深圳市南山区科技园南区",
    brands: [
      {
        id: 1,
        name: "华为",
        authorized: true,
        authorization_date: "2023-01-15",
        expiry_date: "2025-01-15",
        authorization_scope: "智能手机、平板电脑",
        remark: "核心合作品牌"
      },
      {
        id: 2,
        name: "小米",
        authorized: false,
        remark: "正在申请授权中"
      }
    ],
    main_products: ["智能手机", "平板电脑"],
    cooperation_type: "授权",
    cooperation_status: "启用",
    payment_terms: "月结30天",
    // 基础信息扩展
    order_count: 156,
    cooperation_agreement: "长期供货协议2024",
    supplier_group: "A类供应商",
    register_time: "2020-03-15",
    credit_code: "91440300123456789X",
    supplier_relation: "独立供应商",
    detailed_address: "深圳市南山区科技园南区高新中一道9号软件大厦A座15楼",
    production_address: "深圳市宝安区西乡街道固戍工业区",
    // 账户信息
    invoice_type: "增值税专用发票",
    tax_rate: 13,
    account_name: "深圳市科技有限公司",
    settlement_method: "银行转账",
    account_number: "****************",
    bank_name: "中国银行深圳分行",
    registered_capital: 1000,
    // 联系人信息
    contacts: [
      {
        id: 1,
        name: "王五",
        phone: "***********",
        position: "销售经理",
        email: "<EMAIL>",
        department: "销售部",
        is_primary: true,
        remark: "主要业务联系人"
      },
      {
        id: 2,
        name: "李六",
        phone: "***********",
        position: "技术总监",
        email: "<EMAIL>",
        department: "技术部",
        is_primary: false,
        remark: "技术支持联系人"
      }
    ],
    // 协议信息
    agreements: [
      {
        id: 1,
        agreement_type: "供货协议",
        agreement_name: "2024年度供货协议",
        start_date: "2024-01-01",
        end_date: "2024-12-31",
        duration: "12个月",
        status: "active",
        sign_date: "2023-12-15",
        file_url: "/files/agreement1.pdf"
      },
      {
        id: 2,
        agreement_type: "保密协议",
        agreement_name: "商业保密协议",
        start_date: "2023-01-01",
        end_date: "2026-12-31",
        duration: "4年",
        status: "active",
        sign_date: "2022-12-20",
        file_url: "/files/agreement2.pdf"
      }
    ],
    // 附件信息
    attachments: {
      business_license: [
        { url: "https://via.placeholder.com/300x200/4A90E2/FFFFFF?text=营业执照", name: "营业执照" }
      ],
      bank_proof: [
        { url: "https://via.placeholder.com/300x200/50C878/FFFFFF?text=开户证明", name: "开户证明" }
      ],
      id_card: [
        { url: "https://via.placeholder.com/300x200/FF6B6B/FFFFFF?text=身份证正面", type: "front", name: "法人身份证正面" },
        { url: "https://via.placeholder.com/300x200/FFD93D/FFFFFF?text=身份证反面", type: "back", name: "法人身份证反面" }
      ],
      others: [
        { url: "https://via.placeholder.com/300x200/9B59B6/FFFFFF?text=资质证书", name: "资质证书" },
        { url: "https://via.placeholder.com/300x200/E67E22/FFFFFF?text=授权书", name: "品牌授权书" }
      ],
      remark: "所有证件均已通过审核，有效期内。"
    }
  },
  {
    id: 2,
    department: "技术部",
    submitter: "李四",
    reviewer: null,
    submit_date: "2024-01-20",
    updater: "陈主管",
    update_date: "2024-01-25",
    supplier_name: "广州电子设备公司",
    supplier_code: "SUP002",
    contact_person: "赵六",
    position: "技术总监",
    phone: "***********",
    company_nature: "股份有限公司",
    address: "广州市天河区珠江新城",
    brands: [
      { name: "联想", authorized: true },
      { name: "戴尔", authorized: true }
    ],
    main_products: ["笔记本电脑", "服务器"],
    cooperation_type: "代加工",
    cooperation_status: "待启用",
    payment_terms: "预付30%，发货前70%",
    supplier_relation: "战略合作伙伴"
  },
  {
    id: 3,
    department: "生产部",
    submitter: "王五",
    reviewer: "陈总监",
    submit_date: "2024-01-18",
    updater: "刘副总",
    update_date: "2024-01-22",
    supplier_name: "上海机械制造厂",
    supplier_code: "SUP003",
    contact_person: "孙七",
    position: "业务经理",
    phone: "13800138003",
    company_nature: "外商独资企业",
    address: "上海市浦东新区张江高科技园区",
    brands: [
      { name: "西门子", authorized: true },
      { name: "ABB", authorized: false }
    ],
    main_products: ["机械设备", "自动化设备"],
    cooperation_type: "授权",
    cooperation_status: "启用",
    payment_terms: "货到付款",
    supplier_relation: "授权代理商"
  },
  {
    id: 4,
    department: "采购部",
    submitter: "赵六",
    reviewer: null,
    submit_date: "2024-01-22",
    updater: null,
    update_date: null,
    supplier_name: "北京化工材料有限公司",
    supplier_code: "SUP004",
    contact_person: "钱八",
    position: "销售代表",
    phone: "13800138004",
    company_nature: "国有企业",
    address: "北京市朝阳区CBD商务区",
    brands: [
      { name: "巴斯夫", authorized: true },
      { name: "杜邦", authorized: true }
    ],
    main_products: ["化工原料", "塑料制品"],
    cooperation_type: "代加工+授权",
    cooperation_status: "黑名单",
    payment_terms: "信用证付款",
    supplier_relation: "临时供应商"
  },
  {
    id: 5,
    department: "工程部",
    submitter: "孙七",
    reviewer: "刘工程师",
    submit_date: "2024-01-25",
    updater: "张总工",
    update_date: "2024-01-28",
    supplier_name: "杭州建材集团",
    supplier_code: "SUP005",
    contact_person: "周九",
    position: "区域经理",
    phone: "13800138005",
    company_nature: "有限责任公司",
    address: "杭州市西湖区文三路",
    brands: [
      { name: "海螺水泥", authorized: true },
      { name: "中国建材", authorized: true }
    ],
    main_products: ["建筑材料", "水泥制品"],
    cooperation_type: "独家代理",
    cooperation_status: "不启用",
    payment_terms: "月结60天",
    supplier_relation: "独家代理商"
  }
];

// ma-crud 配置选项
const crudOptions = reactive({
  api: mockApi.getList,
  title: "供应商列表管理",
  searchLabelWidth: "100px",
  searchColNumber: 4,
  rowSelection: false,
  operationColumn: true,
  showIndex: false,
  indexLabel: "序号",
  pageSize: 5,
  pageSizeOption: [5, 10, 20, 50, 100],
  tablePagination: {
    showTotal: true,
    showJumper: true,
    showPageSize: true,
  },
  export: {
    show: false
  },
  // Tab配置
  tabs: {
    type: 'line',
    trigger: 'click',
    data: [
      { label: '全部', value: 'all' },
      { label: '待处理', value: 'pending' }
    ],
    defaultKey: 'all',
    searchKey: 'cooperation_status',
    onChange: (value) => {
      console.log('Tab切换:', value);
      if (value === 'pending') {
        crudRef.value.requestParams.cooperation_status = '待审核';
      } else {
        delete crudRef.value.requestParams.cooperation_status;
      }
      crudRef.value.refresh();
    }
  },

  // 表单配置
  formOption: {
    width: 900,
    viewType: 'modal'
  },

  // 搜索前处理参数
  beforeSearch: (params) => {
    return params;
  },

  // 新增配置
  add: {
    show: false,
    api: mockApi.create,
    text: "新增供应商"
  },

  // 编辑配置
  edit: {
    show: false,
    api: mockApi.update,
  },

  // 删除配置
  delete: {
    show: false,
    api: mockApi.delete,
  },
});

// 表格列配置
const columns = [
  {
    title: "供应商信息",
    dataIndex: "supplier_info",
    width: 300,
    search: false,
  },
  {
    title: "部门",
    dataIndex: "department",
    width: 100,
    search: true,
    formType: "input",
    searchPlaceholder: "请输入部门",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "联系人",
    dataIndex: "contact_person",
    width: 100,
    search: true,
    formType: "input",
    searchPlaceholder: "请输入联系人姓名",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "供应商名称",
    dataIndex: "supplier_name",
    width: 150,
    search: true,
    formType: "input",
    searchPlaceholder: "请输入供应商名称",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "品牌",
    dataIndex: "brand",
    width: 100,
    search: true,
    formType: "input",
    searchPlaceholder: "请输入品牌名称",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "主营产品",
    dataIndex: "main_products",
    width: 80,
    search: true,
    formType: "select",
    dict: {
      data: [
        { label: "电子产品", value: "电子产品" },
        { label: "机械设备", value: "机械设备" },
        { label: "化工原料", value: "化工原料" },
        { label: "建筑材料", value: "建筑材料" }
      ]
    },
    searchPlaceholder: "请选择主营产品",
    customRender: ({ record }) => {
      return h('div', { class: 'products' },
        record.main_products.map(product =>
          h('a-tag', { color: 'blue', style: 'margin-bottom: 4px; display: block;' }, product)
        )
      );
    }
  },
  {
    title: "公司性质",
    dataIndex: "company_nature",
    width: 120,
    search: true,
    formType: "select",
    dict: {
      data: [
        { label: "有限责任公司", value: "有限责任公司" },
        { label: "股份有限公司", value: "股份有限公司" },
        { label: "外商独资企业", value: "外商独资企业" },
        { label: "国有企业", value: "国有企业" }
      ]
    },
    searchPlaceholder: "请选择公司性质",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "合作信息",
    dataIndex: "cooperation_info",
    width: 150,
    search: true,
    formType: "select", // 将通过自定义模板实现
    searchPlaceholder: "请选择合作方式或状态",
    hide: true, // 隐藏列，只用于搜索，通过自定义模板显示
  },
  {
    title: "付款条件",
    dataIndex: "payment_terms",
    width: 150,
    search: true,
    formType: "input",
    searchPlaceholder: "请输入付款条件",
  },
  {
    title: "审核时间",
    dataIndex: "review_date_range",
    width: 150,
    search: true,
    formType: "range",
    searchPlaceholder: "请选择审核时间",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "提交日期",
    dataIndex: "submit_date_range",
    width: 150,
    search: true,
    formType: "range",
    searchPlaceholder: "请选择提交日期",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "提交人搜索",
    dataIndex: "submitter",
    width: 100,
    search: true,
    formType: "input",
    searchPlaceholder: "请输入提交人",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "更新人搜索",
    dataIndex: "updater",
    width: 100,
    search: true,
    formType: "input",
    searchPlaceholder: "请输入更新人",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "更新时间",
    dataIndex: "update_date_range",
    width: 150,
    search: true,
    formType: "range",
    searchPlaceholder: "请选择更新时间",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "供应商关联搜索",
    dataIndex: "supplier_relation",
    width: 120,
    search: true,
    formType: "input",
    searchPlaceholder: "请输入供应商关联",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "复核人搜索",
    dataIndex: "reviewer",
    width: 100,
    search: true,
    formType: "input",
    searchPlaceholder: "请输入复核人",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "合作方式搜索",
    dataIndex: "cooperation_type_search",
    width: 120,
    search: true,
    formType: "select",
    dict: {
      data: [
        { label: "代加工", value: "代加工" },
        { label: "代加工+授权", value: "代加工+授权" },
        { label: "授权", value: "授权" },
        { label: "独家代理", value: "独家代理" }
      ]
    },
    searchPlaceholder: "请选择合作方式",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "合作状态搜索",
    dataIndex: "cooperation_status_search",
    width: 120,
    search: true,
    formType: "select",
    dict: {
      data: [
        { label: "启用", value: "启用" },
        { label: "待启用", value: "待启用" },
        { label: "黑名单", value: "黑名单" },
        { label: "不启用", value: "不启用" }
      ]
    },
    searchPlaceholder: "请选择合作状态",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "授权情况搜索",
    dataIndex: "authorization_status",
    width: 100,
    search: true,
    formType: "select",
    dict: {
      data: [
        { label: "有", value: "有" },
        { label: "无", value: "无" }
      ]
    },
    searchPlaceholder: "请选择授权情况",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "通讯地址搜索",
    dataIndex: "address",
    width: 150,
    search: true,
    formType: "input",
    searchPlaceholder: "请输入通讯地址",
    hide: true, // 隐藏列，只用于搜索
  },
  {
    title: "品牌-授权",
    dataIndex: "brand_authorization",
    width: 100,
    search: false,
  },
  {
    title: "合作信息",
    dataIndex: "cooperation_info",
    width: 100,
    search: false,
  },
  {
    title: "操作人信息",
    dataIndex: "operation_info",
    width: 150,
    search: false,
  },
];

// 获取合作方式颜色
const getCooperationTypeColor = (type) => {
  const colorMap = {
    "长期合作": "blue",
    "项目合作": "green",
    "临时采购": "orange"
  };
  return colorMap[type] || "gray";
};

// 获取合作状态颜色
const getCooperationStatusColor = (status) => {
  const colorMap = {
    "正常合作": "green",
    "暂停合作": "orange",
    "待审核": "blue",
    "已终止": "red"
  };
  return colorMap[status] || "gray";
};

// 事件处理方法
const handleView = (record) => {
  currentSupplier.value = record;
  detailVisible.value = true;
};

const handleEdit = (record) => {
  currentSupplier.value = record;
  editVisible.value = true;
};

// 更换复核人相关方法
const handleChangeReviewer = (record) => {
  currentSupplier.value = record;
  reviewerForm.reviewer = record.reviewer || '';
  changeReviewerVisible.value = true;
};

const handleConfirmChangeReviewer = async () => {
  if (!reviewerForm.reviewer) {
    Message.error('请选择复核人');
    return;
  }

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300));

    // 更新当前供应商的复核人信息
    const supplierIndex = mockSuppliers.findIndex(item => item.id === currentSupplier.value.id);
    if (supplierIndex !== -1) {
      mockSuppliers[supplierIndex].reviewer = reviewerForm.reviewer;
      mockSuppliers[supplierIndex].updater = '系统管理员'; // 记录更换操作人
      mockSuppliers[supplierIndex].update_date = new Date().toISOString().split('T')[0]; // 更新时间
    }

    Message.success(`已成功更换复核人为：${reviewerForm.reviewer}`);
    changeReviewerVisible.value = false;
    crudRef.value.refresh();
  } catch (error) {
    Message.error('更换复核人失败，请重试');
  }
};

const handleCancelChangeReviewer = () => {
  changeReviewerVisible.value = false;
  reviewerForm.reviewer = '';
};

const handleEditSuccess = (updatedData) => {
  Message.success('供应商信息更新成功');
  // 刷新列表数据
  crudRef.value.refresh();
  // 更新当前供应商数据
  currentSupplier.value = updatedData;
};
</script>

<style scoped>
.supplier-list {
  padding: 20px;
  background-color: var(--color-bg-1);
}

.operation-info {
  line-height: 1.6;
}

.operation-info div {
  margin-bottom: 4px;
}

.supplier-info {
  line-height: 1.6;
}

.supplier-info div {
  margin-bottom: 4px;
}

.brand-auth {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.brand-item {
  margin-bottom: 4px;
}

.products {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.products .arco-tag {
  margin-bottom: 4px;
}

.cooperation-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.cooperation-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.cooperation-label {
  font-size: 12px;
  color: var(--color-text-3);
  min-width: 32px;
}

.reviewer-note {
  margin-top: 12px;
  padding: 8px;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  font-size: 12px;
}
</style>