<template>
  <div class="agreement-info-add">
    <div class="add-header">
      <h4>协议信息</h4>
    </div>
    <div class="add-content">
      <div class="agreement-list">
        <div v-for="(agreement, index) in formData" :key="index" class="agreement-item">
          <a-card>
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="协议类型">
                  <a-select 
                    v-model="agreement.agreement_type" 
                    placeholder="请选择协议类型"
                    allow-clear
                  >
                    <a-option value="供货协议">供货协议</a-option>
                    <a-option value="保密协议">保密协议</a-option>
                    <a-option value="质量协议">质量协议</a-option>
                    <a-option value="服务协议">服务协议</a-option>
                    <a-option value="框架协议">框架协议</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="协议名称">
                  <a-input 
                    v-model="agreement.agreement_name" 
                    placeholder="请输入协议名称"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="开始日期">
                  <a-date-picker 
                    v-model="agreement.start_date" 
                    placeholder="请选择开始日期"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="结束日期">
                  <a-date-picker 
                    v-model="agreement.end_date" 
                    placeholder="请选择结束日期"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="2">
                <a-form-item label=" ">
                  <a-button 
                    type="text" 
                    status="danger" 
                    @click="removeAgreement(index)"
                    :disabled="formData.length <= 1"
                  >
                    <template #icon><icon-delete /></template>
                  </a-button>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="协议状态">
                  <a-select 
                    v-model="agreement.status" 
                    placeholder="请选择协议状态"
                    allow-clear
                  >
                    <a-option value="active">生效中</a-option>
                    <a-option value="pending">待生效</a-option>
                    <a-option value="expired">已过期</a-option>
                    <a-option value="terminated">已终止</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="签署日期">
                  <a-date-picker 
                    v-model="agreement.sign_date" 
                    placeholder="请选择签署日期"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="协议期限">
                  <a-input 
                    v-model="agreement.duration" 
                    placeholder="如：12个月"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="附件文件">
                  <a-upload
                    :file-list="agreement.fileList || []"
                    :show-file-list="true"
                    :auto-upload="false"
                    @change="handleFileChange(index, $event)"
                  >
                    <template #upload-button>
                      <a-button type="outline" size="small">
                        <template #icon><icon-upload /></template>
                        上传文件
                      </a-button>
                    </template>
                  </a-upload>
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </div>
      </div>

      <div class="add-agreement-section">
        <a-button type="dashed" @click="addAgreement" style="width: 100%">
          <template #icon><icon-plus /></template>
          添加协议
        </a-button>
      </div>

      <!-- 保存按钮 -->
      <div class="save-section">
        <a-button type="primary" @click="handleSave" :loading="saving">
          <template #icon><icon-save /></template>
          保存协议信息
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';

const props = defineProps({
  formData: {
    type: Array,
    default: () => []
  },
  errors: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:formData', 'save']);

const saving = ref(false);

// 表单数据
const formData = reactive([...props.formData]);

// 如果没有协议数据，添加一个默认项
if (formData.length === 0) {
  formData.push({
    id: Date.now(),
    agreement_type: '',
    agreement_name: '',
    start_date: '',
    end_date: '',
    status: 'pending',
    sign_date: '',
    duration: '',
    file_url: '',
    fileList: []
  });
}

// 添加协议
const addAgreement = () => {
  formData.push({
    id: Date.now(),
    agreement_type: '',
    agreement_name: '',
    start_date: '',
    end_date: '',
    status: 'pending',
    sign_date: '',
    duration: '',
    file_url: '',
    fileList: []
  });
};

// 删除协议
const removeAgreement = (index) => {
  if (formData.length > 1) {
    formData.splice(index, 1);
  }
};

// 处理文件上传
const handleFileChange = (index, fileList) => {
  formData[index].fileList = fileList;
};

// 监听表单数据变化
watch(formData, (newVal) => {
  emit('update:formData', newVal);
}, { deep: true });

// 监听props变化
watch(() => props.formData, (newVal) => {
  formData.splice(0, formData.length, ...newVal);
  if (formData.length === 0) {
    addAgreement();
  }
}, { deep: true });

// 验证表单
const validate = async () => {
  try {
    // 简单验证：检查是否有协议名称
    const hasValidAgreement = formData.some(agreement => 
      agreement.agreement_name && agreement.agreement_name.trim()
    );
    if (!hasValidAgreement) {
      throw new Error('请至少添加一个协议名称');
    }
    return true;
  } catch (error) {
    console.error('协议信息验证失败:', error);
    return false;
  }
};

// 保存处理
const handleSave = async () => {
  saving.value = true;
  try {
    emit('save');
  } finally {
    saving.value = false;
  }
};

// 暴露验证方法
defineExpose({
  validate
});
</script>

<style scoped>
.agreement-info-add {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.add-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border-2);
}

.add-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-1);
}

.add-content {
  margin-top: 16px;
}

.agreement-list {
  margin-bottom: 16px;
}

.agreement-item {
  margin-bottom: 16px;
}

.add-agreement-section {
  margin-bottom: 24px;
}

:deep(.arco-form-item-label) {
  font-weight: 500;
  color: var(--color-text-1);
}

:deep(.arco-form-item) {
  margin-bottom: 16px;
}

.save-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--color-border-2);
  display: flex;
  justify-content: flex-end;
}
</style>
