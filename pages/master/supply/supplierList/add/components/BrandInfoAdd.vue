<template>
  <div class="brand-info-add">
    <div class="add-header">
      <h4>品牌信息</h4>
    </div>
    <div class="add-content">
      <div class="brand-list">
        <div v-for="(brand, index) in formData" :key="index" class="brand-item">
          <a-card>
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="品牌名称">
                  <a-input 
                    v-model="brand.name" 
                    placeholder="请输入品牌名称"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="是否授权">
                  <a-switch 
                    v-model="brand.authorized"
                    checked-text="已授权"
                    unchecked-text="未授权"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="授权范围">
                  <a-input 
                    v-model="brand.authorization_scope" 
                    placeholder="请输入授权范围"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="备注">
                  <a-input 
                    v-model="brand.remark" 
                    placeholder="请输入备注"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="2">
                <a-form-item label=" ">
                  <a-button 
                    type="text" 
                    status="danger" 
                    @click="removeBrand(index)"
                    :disabled="formData.length <= 1"
                  >
                    <template #icon><icon-delete /></template>
                  </a-button>
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </div>
      </div>

      <div class="add-brand-section">
        <a-button type="dashed" @click="addBrand" style="width: 100%">
          <template #icon><icon-plus /></template>
          添加品牌
        </a-button>
      </div>

      <!-- 保存按钮 -->
      <div v-if="showSaveButton" class="save-section">
        <a-button type="primary" @click="handleSave" :loading="saving">
          <template #icon><icon-save /></template>
          保存品牌信息
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';

const props = defineProps({
  formData: {
    type: Array,
    default: () => []
  },
  errors: {
    type: Object,
    default: () => ({})
  },
  showSaveButton: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:formData', 'save']);

const saving = ref(false);

// 表单数据
const formData = reactive([...props.formData]);

// 如果没有品牌数据，添加一个默认项
if (formData.length === 0) {
  formData.push({
    id: Date.now(),
    name: '',
    authorized: false,
    authorization_scope: '',
    remark: ''
  });
}

// 添加品牌
const addBrand = () => {
  formData.push({
    id: Date.now(),
    name: '',
    authorized: false,
    authorization_scope: '',
    remark: ''
  });
};

// 删除品牌
const removeBrand = (index) => {
  if (formData.length > 1) {
    formData.splice(index, 1);
  }
};

// 监听表单数据变化
watch(formData, (newVal) => {
  emit('update:formData', newVal);
}, { deep: true });

// 监听props变化
watch(() => props.formData, (newVal) => {
  formData.splice(0, formData.length, ...newVal);
  if (formData.length === 0) {
    addBrand();
  }
}, { deep: true });

// 验证表单
const validate = async () => {
  try {
    // 简单验证：检查是否有品牌名称
    const hasValidBrand = formData.some(brand => brand.name && brand.name.trim());
    if (!hasValidBrand) {
      throw new Error('请至少添加一个品牌');
    }
    return true;
  } catch (error) {
    console.error('品牌信息验证失败:', error);
    return false;
  }
};

// 保存处理
const handleSave = async () => {
  saving.value = true;
  try {
    emit('save');
  } finally {
    saving.value = false;
  }
};

// 暴露验证方法
defineExpose({
  validate
});
</script>

<style scoped>
.brand-info-add {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.add-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border-2);
}

.add-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-1);
}

.add-content {
  margin-top: 16px;
}

.brand-list {
  margin-bottom: 16px;
}

.brand-item {
  margin-bottom: 16px;
}

.add-brand-section {
  margin-bottom: 24px;
}

:deep(.arco-form-item-label) {
  font-weight: 500;
  color: var(--color-text-1);
}

:deep(.arco-form-item) {
  margin-bottom: 16px;
}

.save-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--color-border-2);
  display: flex;
  justify-content: flex-end;
}
</style>
