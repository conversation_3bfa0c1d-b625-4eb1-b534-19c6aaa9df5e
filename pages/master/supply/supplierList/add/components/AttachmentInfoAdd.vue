<template>
  <div class="attachment-info-add">
    <div class="add-header">
      <h4>附件信息</h4>
    </div>
    <div class="add-content">
      <a-row :gutter="24">
        <a-col :span="12">
          <a-card title="营业执照" size="small">
            <a-upload
              :file-list="formData.business_license || []"
              :show-file-list="true"
              :auto-upload="false"
              accept="image/*,.pdf"
              @change="handleFileChange('business_license', $event)"
            >
              <template #upload-button>
                <a-button type="outline">
                  <template #icon><icon-upload /></template>
                  上传营业执照
                </a-button>
              </template>
            </a-upload>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="开户证明" size="small">
            <a-upload
              :file-list="formData.bank_proof || []"
              :show-file-list="true"
              :auto-upload="false"
              accept="image/*,.pdf"
              @change="handleFileChange('bank_proof', $event)"
            >
              <template #upload-button>
                <a-button type="outline">
                  <template #icon><icon-upload /></template>
                  上传开户证明
                </a-button>
              </template>
            </a-upload>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="24" style="margin-top: 16px;">
        <a-col :span="12">
          <a-card title="法人身份证" size="small">
            <div class="id-card-upload">
              <div class="id-card-item">
                <label>身份证正面：</label>
                <a-upload
                  :file-list="getIdCardFiles('front')"
                  :show-file-list="true"
                  :auto-upload="false"
                  accept="image/*"
                  @change="handleIdCardChange('front', $event)"
                >
                  <template #upload-button>
                    <a-button type="outline" size="small">
                      <template #icon><icon-upload /></template>
                      上传正面
                    </a-button>
                  </template>
                </a-upload>
              </div>
              <div class="id-card-item">
                <label>身份证反面：</label>
                <a-upload
                  :file-list="getIdCardFiles('back')"
                  :show-file-list="true"
                  :auto-upload="false"
                  accept="image/*"
                  @change="handleIdCardChange('back', $event)"
                >
                  <template #upload-button>
                    <a-button type="outline" size="small">
                      <template #icon><icon-upload /></template>
                      上传反面
                    </a-button>
                  </template>
                </a-upload>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="其他附件" size="small">
            <a-upload
              :file-list="formData.others || []"
              :show-file-list="true"
              :auto-upload="false"
              multiple
              @change="handleFileChange('others', $event)"
            >
              <template #upload-button>
                <a-button type="outline">
                  <template #icon><icon-upload /></template>
                  上传其他附件
                </a-button>
              </template>
            </a-upload>
          </a-card>
        </a-col>
      </a-row>

      <a-row style="margin-top: 16px;">
        <a-col :span="24">
          <a-form-item label="备注说明">
            <a-textarea 
              v-model="formData.remark" 
              placeholder="请输入备注说明"
              :auto-size="{ minRows: 3, maxRows: 6 }"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 保存按钮 -->
      <div v-if="showSaveButton" class="save-section">
        <a-button type="primary" @click="handleSave" :loading="saving">
          <template #icon><icon-save /></template>
          保存附件信息
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({})
  },
  errors: {
    type: Object,
    default: () => ({})
  },
  showSaveButton: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:formData', 'save']);

const saving = ref(false);

// 表单数据
const formData = reactive({
  business_license: [],
  bank_proof: [],
  id_card: [],
  others: [],
  remark: '',
  ...props.formData
});

// 处理文件上传
const handleFileChange = (type, fileList) => {
  formData[type] = fileList;
};

// 处理身份证文件上传
const handleIdCardChange = (side, fileList) => {
  if (!formData.id_card) {
    formData.id_card = [];
  }
  
  // 移除同类型的旧文件
  formData.id_card = formData.id_card.filter(file => file.type !== side);
  
  // 添加新文件
  if (fileList.length > 0) {
    const newFile = { ...fileList[0], type: side };
    formData.id_card.push(newFile);
  }
};

// 获取身份证文件
const getIdCardFiles = (side) => {
  if (!formData.id_card) return [];
  return formData.id_card.filter(file => file.type === side);
};

// 监听表单数据变化
watch(formData, (newVal) => {
  emit('update:formData', newVal);
}, { deep: true });

// 监听props变化
watch(() => props.formData, (newVal) => {
  Object.assign(formData, {
    business_license: [],
    bank_proof: [],
    id_card: [],
    others: [],
    remark: '',
    ...newVal
  });
}, { deep: true });

// 验证表单
const validate = async () => {
  try {
    // 附件信息验证相对宽松，只要有任何一个附件即可
    return true;
  } catch (error) {
    console.error('附件信息验证失败:', error);
    return false;
  }
};

// 保存处理
const handleSave = async () => {
  saving.value = true;
  try {
    emit('save');
  } finally {
    saving.value = false;
  }
};

// 暴露验证方法
defineExpose({
  validate
});
</script>

<style scoped>
.attachment-info-add {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.add-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border-2);
}

.add-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-1);
}

.add-content {
  margin-top: 16px;
}

.id-card-upload {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.id-card-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.id-card-item label {
  min-width: 80px;
  font-size: 12px;
  color: var(--color-text-2);
}

:deep(.arco-form-item-label) {
  font-weight: 500;
  color: var(--color-text-1);
}

:deep(.arco-form-item) {
  margin-bottom: 16px;
}

.save-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--color-border-2);
  display: flex;
  justify-content: flex-end;
}
</style>
