<template>
  <div class="contact-info-add">
    <div class="add-header">
      <h4>联系人信息</h4>
    </div>
    <div class="add-content">
      <div class="contact-list">
        <div v-for="(contact, index) in formData" :key="index" class="contact-item">
          <a-card>
            <a-row :gutter="16">
              <a-col :span="4">
                <a-form-item label="姓名">
                  <a-input 
                    v-model="contact.name" 
                    placeholder="请输入姓名"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="手机号">
                  <a-input 
                    v-model="contact.phone" 
                    placeholder="请输入手机号"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="职位">
                  <a-input 
                    v-model="contact.position" 
                    placeholder="请输入职位"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="邮箱">
                  <a-input 
                    v-model="contact.email" 
                    placeholder="请输入邮箱"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="3">
                <a-form-item label="部门">
                  <a-input 
                    v-model="contact.department" 
                    placeholder="请输入部门"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="3">
                <a-form-item label="主要联系人">
                  <a-switch 
                    v-model="contact.is_primary"
                    checked-text="是"
                    unchecked-text="否"
                    @change="handlePrimaryChange(index)"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="2">
                <a-form-item label=" ">
                  <a-button 
                    type="text" 
                    status="danger" 
                    @click="removeContact(index)"
                    :disabled="formData.length <= 1"
                  >
                    <template #icon><icon-delete /></template>
                  </a-button>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="22">
                <a-form-item label="备注">
                  <a-textarea 
                    v-model="contact.remark" 
                    placeholder="请输入备注"
                    :auto-size="{ minRows: 1, maxRows: 3 }"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </div>
      </div>

      <div class="add-contact-section">
        <a-button type="dashed" @click="addContact" style="width: 100%">
          <template #icon><icon-plus /></template>
          添加联系人
        </a-button>
      </div>

      <!-- 保存按钮 -->
      <div v-if="showSaveButton" class="save-section">
        <a-button type="primary" @click="handleSave" :loading="saving">
          <template #icon><icon-save /></template>
          保存联系人信息
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';

const props = defineProps({
  formData: {
    type: Array,
    default: () => []
  },
  errors: {
    type: Object,
    default: () => ({})
  },
  showSaveButton: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:formData', 'save']);

const saving = ref(false);

// 表单数据
const formData = reactive([...props.formData]);

// 如果没有联系人数据，添加一个默认项
if (formData.length === 0) {
  formData.push({
    id: Date.now(),
    name: '',
    phone: '',
    position: '',
    email: '',
    department: '',
    is_primary: true,
    remark: ''
  });
}

// 添加联系人
const addContact = () => {
  formData.push({
    id: Date.now(),
    name: '',
    phone: '',
    position: '',
    email: '',
    department: '',
    is_primary: false,
    remark: ''
  });
};

// 删除联系人
const removeContact = (index) => {
  if (formData.length > 1) {
    formData.splice(index, 1);
  }
};

// 处理主要联系人变更
const handlePrimaryChange = (index) => {
  if (formData[index].is_primary) {
    // 如果设置为主要联系人，取消其他联系人的主要状态
    formData.forEach((contact, i) => {
      if (i !== index) {
        contact.is_primary = false;
      }
    });
  }
};

// 监听表单数据变化
watch(formData, (newVal) => {
  emit('update:formData', newVal);
}, { deep: true });

// 监听props变化
watch(() => props.formData, (newVal) => {
  formData.splice(0, formData.length, ...newVal);
  if (formData.length === 0) {
    addContact();
  }
}, { deep: true });

// 验证表单
const validate = async () => {
  try {
    // 简单验证：检查是否有联系人姓名和手机号
    const hasValidContact = formData.some(contact => 
      contact.name && contact.name.trim() && 
      contact.phone && contact.phone.trim()
    );
    if (!hasValidContact) {
      throw new Error('请至少添加一个有效的联系人（姓名和手机号必填）');
    }
    return true;
  } catch (error) {
    console.error('联系人信息验证失败:', error);
    return false;
  }
};

// 保存处理
const handleSave = async () => {
  saving.value = true;
  try {
    emit('save');
  } finally {
    saving.value = false;
  }
};

// 暴露验证方法
defineExpose({
  validate
});
</script>

<style scoped>
.contact-info-add {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.add-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border-2);
}

.add-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-1);
}

.add-content {
  margin-top: 16px;
}

.contact-list {
  margin-bottom: 16px;
}

.contact-item {
  margin-bottom: 16px;
}

.add-contact-section {
  margin-bottom: 24px;
}

:deep(.arco-form-item-label) {
  font-weight: 500;
  color: var(--color-text-1);
}

:deep(.arco-form-item) {
  margin-bottom: 16px;
}

.save-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--color-border-2);
  display: flex;
  justify-content: flex-end;
}
</style>
