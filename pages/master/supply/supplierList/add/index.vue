<template>
  <a-drawer
    v-model:visible="visible"
    title="新增供应商信息"
    :width="1200"
    :footer="false"
    unmount-on-close
    :mask-closable="false"
    :esc-to-close="true"
    @close="handleClose"
    placement="right"
  >
    <div class="supplier-add">
      <!-- 步骤指示器 -->
      <div class="steps-section">
        <a-steps :current="currentStep" size="small">
          <a-step title="基础信息" />
          <a-step title="账户信息" />
          <a-step title="品牌信息" />
          <a-step title="联系人信息" />
          <a-step title="协议信息" />
          <a-step title="附件信息" />
        </a-steps>
      </div>

      <!-- 步骤内容区域 -->
      <div class="step-content-section">
        <!-- 基础信息 -->
        <div v-show="currentStep === 0" class="step-content">
          <BasicInfoAdd
            ref="basicInfoRef"
            v-model:form-data="formData.basicInfo"
            :errors="errors.basicInfo"
            :show-save-button="false"
          />
        </div>

        <!-- 账户信息 -->
        <div v-show="currentStep === 1" class="step-content">
          <AccountInfoAdd
            ref="accountInfoRef"
            v-model:form-data="formData.accountInfo"
            :errors="errors.accountInfo"
            :show-save-button="false"
          />
        </div>

        <!-- 品牌信息 -->
        <div v-show="currentStep === 2" class="step-content">
          <BrandInfoAdd
            ref="brandInfoRef"
            v-model:form-data="formData.brandInfo"
            :errors="errors.brandInfo"
            :show-save-button="false"
          />
        </div>

        <!-- 联系人信息 -->
        <div v-show="currentStep === 3" class="step-content">
          <ContactInfoAdd
            ref="contactInfoRef"
            v-model:form-data="formData.contactInfo"
            :errors="errors.contactInfo"
            :show-save-button="false"
          />
        </div>

        <!-- 协议信息 -->
        <div v-show="currentStep === 4" class="step-content">
          <AgreementInfoAdd
            ref="agreementInfoRef"
            v-model:form-data="formData.agreementInfo"
            :errors="errors.agreementInfo"
            :show-save-button="false"
          />
        </div>

        <!-- 附件信息 -->
        <div v-show="currentStep === 5" class="step-content">
          <AttachmentInfoAdd
            ref="attachmentInfoRef"
            v-model:form-data="formData.attachmentInfo"
            :errors="errors.attachmentInfo"
            :show-save-button="false"
          />
        </div>
      </div>

      <!-- 步骤操作按钮 -->
      <div class="step-actions">
        <a-button
          v-if="currentStep > 0"
          @click="handlePrevStep"
          :disabled="saving"
        >
          上一步
        </a-button>
        <a-button
          v-if="currentStep < 5"
          type="primary"
          @click="handleNextStep"
          :disabled="saving"
        >
          下一步
        </a-button>
        <a-button
          v-if="currentStep === 5"
          type="primary"
          @click="handleSaveAll"
          :loading="saving"
        >
          <template #icon><icon-save /></template>
          保存新增
        </a-button>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue';
import { Message } from '@arco-design/web-vue';
import BasicInfoAdd from './components/BasicInfoAdd.vue';
import AccountInfoAdd from './components/AccountInfoAdd.vue';
import BrandInfoAdd from './components/BrandInfoAdd.vue';
import ContactInfoAdd from './components/ContactInfoAdd.vue';
import AgreementInfoAdd from './components/AgreementInfoAdd.vue';
import AttachmentInfoAdd from './components/AttachmentInfoAdd.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'save-success']);

const visible = ref(props.visible);
const currentStep = ref(0); // 当前步骤，从0开始
const saving = ref(false);

// 表单引用
const basicInfoRef = ref();
const accountInfoRef = ref();
const brandInfoRef = ref();
const contactInfoRef = ref();
const agreementInfoRef = ref();
const attachmentInfoRef = ref();

// 表单数据
const formData = reactive({
  basicInfo: {},
  accountInfo: {},
  brandInfo: [],
  contactInfo: [],
  agreementInfo: [],
  attachmentInfo: {}
});

// 错误信息
const errors = reactive({
  basicInfo: {},
  accountInfo: {},
  brandInfo: {},
  contactInfo: {},
  agreementInfo: {},
  attachmentInfo: {}
});

// 监听visible变化
watch(() => props.visible, (newVal) => {
  visible.value = newVal;
  if (newVal) {
    initFormData();
  }
});

watch(visible, (newVal) => {
  emit('update:visible', newVal);
  if (newVal) {
    currentStep.value = 0; // 重置到第一步
  }
});

// 初始化表单数据（新增模式，设置默认值）
const initFormData = () => {
  // 基础信息默认值
  formData.basicInfo = {
    supplier_name: '',
    supplier_code: '',
    company_nature: '',
    main_products: [],
    cooperation_type: '',
    cooperation_status: '待审核',
    cooperation_agreement: '',
    supplier_group: '',
    address: '',
    detailed_address: '',
    production_address: '',
    register_time: '',
    credit_code: '',
    supplier_relation: ''
  };

  // 账户信息默认值
  formData.accountInfo = {
    payment_terms: '',
    invoice_type: '',
    tax_rate: '',
    account_name: '',
    settlement_method: '',
    account_number: '',
    bank_name: '',
    registered_capital: ''
  };

  // 品牌信息默认值
  formData.brandInfo = [];

  // 联系人信息默认值
  formData.contactInfo = [];

  // 协议信息默认值
  formData.agreementInfo = [];

  // 附件信息默认值
  formData.attachmentInfo = {};
};

// 步骤引用映射
const stepRefs = {
  0: () => basicInfoRef.value,
  1: () => accountInfoRef.value,
  2: () => brandInfoRef.value,
  3: () => contactInfoRef.value,
  4: () => agreementInfoRef.value,
  5: () => attachmentInfoRef.value
};

// 下一步
const handleNextStep = async () => {
  // 验证当前步骤
  const currentRef = stepRefs[currentStep.value]();
  if (currentRef?.validate) {
    const isValid = await currentRef.validate();
    if (!isValid) {
      Message.error('请检查当前步骤信息填写是否正确');
      return;
    }
  }

  // 进入下一步
  if (currentStep.value < 5) {
    currentStep.value++;
  }
};

// 上一步
const handlePrevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
};

// 保存所有信息
const handleSaveAll = async () => {
  saving.value = true;
  try {
    // 验证最后一步
    const currentRef = stepRefs[currentStep.value]();
    if (currentRef?.validate) {
      const isValid = await currentRef.validate();
      if (!isValid) {
        Message.error('请检查附件信息填写是否正确');
        return;
      }
    }

    // 组装完整的供应商数据
    const completeSupplierData = {
      // 基础信息
      ...formData.basicInfo,
      // 账户信息
      ...formData.accountInfo,
      // 品牌信息
      brands: formData.brandInfo,
      // 联系人信息
      contacts: formData.contactInfo,
      // 协议信息
      agreements: formData.agreementInfo,
      // 附件信息
      attachments: formData.attachmentInfo,
      // 系统信息
      id: Date.now(), // 模拟生成ID
      department: '采购部',
      submitter: '当前用户',
      submit_date: new Date().toISOString().split('T')[0],
      updater: null,
      update_date: null,
      reviewer: null
    };

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));

    Message.success('供应商新增成功');
    emit('save-success', completeSupplierData);

    // 重置表单和步骤
    currentStep.value = 0;
    initFormData();

  } catch (error) {
    console.error('供应商新增失败:', error);
    Message.error('供应商新增失败，请重试');
  } finally {
    saving.value = false;
  }
};

// 关闭处理
const handleClose = () => {
  visible.value = false;
};
</script>

<style scoped>
.supplier-add {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.steps-section {
  padding: 20px 20px 0 20px;
  background-color: var(--color-bg-1);
  border-bottom: 1px solid var(--color-border-2);
}

.step-content-section {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.step-content {
  height: 100%;
}

.step-actions {
  padding: 16px 20px;
  background-color: var(--color-bg-1);
  border-top: 1px solid var(--color-border-2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-actions .arco-btn + .arco-btn {
  margin-left: 12px;
}

:deep(.arco-drawer-body) {
  padding: 0;
  height: 100%;
  overflow: hidden;
}
</style>
