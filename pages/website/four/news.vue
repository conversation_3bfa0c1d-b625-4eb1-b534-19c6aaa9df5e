<template>
  <div class="website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl2" />
    <!-- 面包屑导航 -->
    <div class="breadcrumb-container">
      <div class="container">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="/website/four/index">首页</a>
            </li>
            <li class="breadcrumb-item">
              <a href="/website/four/news">新闻中心</a>
            </li>
          </ol>
        </nav>
      </div>
    </div>
    <!-- 新闻列表 -->
    <div class="news-info-section">
      <div class="container">
        <div class="news-info-grid">
          <div
            class="news-info-item"
            @click="handleViewMore(`/website/four/newsdetail/${item.id}`)"
            v-for="(item, index) in newsList"
            :key="index"
          >
            <div class="news-info-date">
              <span class="day" v-time="item.created_at" format="dd"></span>
              <span class="month-year" v-time="item.created_at" format="yyyy-MM"></span>
            </div>
            <div class="news-info-image">
              <a-image
                :preview="false"
                :src="item.image"
                :alt="item.title"
                :width="'100%'"
                :height="200"
                fit="cover"
              />
            </div>
            <div class="news-info-content">
              <h3 class="news-info-title">{{ item.title }}</h3>
              <p class="news-info-desc">{{ item.summary }}</p>
              <a class="news-info-link" :href="`/website/four/newsdetail/${item.id}`">查看详情</a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加页脚组件 -->
    <Footer :base-url="baseUrl2" />
  </div>
</template>
  
<script setup>
import officialApi from "@/api/master/officialWebsiteModule";
import { ref, onMounted } from "vue";
import NavBar from "@/components/website/four/NavBar.vue";
import Footer from "@/components/website/four/Footer.vue";

// 定义页面元信息
definePageMeta({
  layout: false,
  path: "/website/four/news"
});

// 基础URL
const baseUrl = "/website/one/assets";
const baseUrl2 = "/website/two/assets";

//获取新闻列表
const newsList = ref([]);

const getNewsList = () => {
  officialApi.newsManagement.list
    .getList({ page: 1, pageSize: 6 })
    .then(res => {
      if (res.code == 200) {
        newsList.value = res.data.items;
      }
    });
};

// 分页数据
const currentPage = ref(1);
const totalItems = ref(20); // 假设总共有20条新闻
const pageSize = ref(6);

const handleViewMore = url => {
  navigateTo(url);
};
// 切换页面
const changePage = page => {
  currentPage.value = page;
  // 这里可以添加获取对应页面数据的逻辑
  console.log("切换到页面:", page);
};

const goToProductDetail = id => {
  navigateTo(`/website/two/newsdetail/${id}`);
};
// 页面加载完成后执行的逻辑
onMounted(() => {
  getNewsList();
  // 加载CSS
  function loadCSS(href, id) {
    if (document.getElementById(id)) return;

    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, "style-css");
  // 加载Bootstrap CSS
  loadCSS(`${baseUrl}/vendor/bootstrap/bootstrap.min.css`, "bootstrap-css");
});

// 添加页面元信息
useHead({
  link: [
    {
      rel: "stylesheet",
      href: `${baseUrl}/vendor/bootstrap/bootstrap.min.css`
    },
    {
      rel: "stylesheet",
      href:
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
    }
  ]
});
</script>
<style scoped lang="less">
.website-container {
  width: 100%;
  overflow-x: hidden;
  background-color: white;
  padding-top: 80px; /* 为固定导航栏留出空间 */
}
/* 面包屑导航样式 */
.breadcrumb-container {
  margin-top: -24px;
  background-color: #f8f8f8;
  padding: 15px 0;
  border-bottom: 1px solid #e1e5eb;
}

.breadcrumb {
  margin-bottom: 0;
  background-color: transparent;
  padding: 0;
}

.breadcrumb-item a {
  color: #666 !important;
  text-decoration: none;
}

.breadcrumb-item.active {
  color: #666 !important;
}

/* 新闻资讯模块样式 */
.news-info-section {
  .container {
    background-color: #f8f9fa;
    padding-bottom: 40px;
  }
}

.news-info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-top: 40px;
}

.news-info-item {
  cursor: pointer;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.news-info-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.news-info-date {
  position: absolute;
  left: 15px;
  background-color: #f44217;
  color: #fff;
  padding: 10px;
  text-align: center;
  z-index: 2;
}

.news-info-date .day {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
}

.news-info-date .month-year {
  display: block;
  font-size: 0.8rem;
}

.news-info-image {
  height: 200px;
  overflow: hidden;
  position: relative;
}

.news-info-image :deep(img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.news-info-item:hover .news-info-image :deep(img) {
  transform: scale(1.1);
}

.news-info-content {
  padding: 20px;
}

.news-info-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 3rem;
}

.news-info-desc {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 4rem;
}

.news-info-link {
  display: inline-block;
  padding: 8px 20px;
  border: 1px solid #ccc;
  border-radius: 20px;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  background-color: #fff;
}

.news-info-link:hover {
  background-color: #ff6b00;
  color: #fff;
  border-color: #ff6b00;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .news-info-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .news-info-section {
    padding: 60px 0;
  }

  .news-info-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .news-info-title {
    font-size: 1.1rem;
  }
}

@media (max-width: 576px) {
  .news-info-section {
    padding: 40px 0;
  }

  .news-info-date {
    padding: 8px;
  }

  .news-info-date .day {
    font-size: 1.2rem;
  }

  .news-info-date .month-year {
    font-size: 0.7rem;
  }

  .news-info-image {
    height: 180px;
  }

  .news-info-content {
    padding: 15px;
  }

  .news-info-title {
    font-size: 1rem;
    margin-bottom: 8px;
  }

  .news-info-desc {
    font-size: 0.85rem;
    margin-bottom: 10px;
  }
}
</style>
