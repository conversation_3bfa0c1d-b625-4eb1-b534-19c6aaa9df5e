<template>
  <div class="website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl2" />
    <!-- 面包屑导航 -->
    <div class="breadcrumb-container">
      <div class="container">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="/website/four/index">首页</a>
            </li>
            <li class="breadcrumb-item">
              <a href="/website/four/about">关于我们</a>
            </li>
          </ol>
        </nav>
      </div>
    </div>
    <!-- 核心价值观部分 -->
    <div class="core-values-section">
      <div class="container">
        <div class="row">
          <div class="col-md-12">
            <h2 class="section-title">{{companyInformations[0]?.title}}</h2>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <p class="section-description">
              {{companyInformations[0]?.description}}
            </p>
          </div>
        </div>
        <div class="row mt-5">
          <div class="col-md-12 text-center">
            <div class="image-container">
              <img :src="companyInformations[0]?.cover_image" class="core-values-image" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加页脚组件 -->
    <Footer :base-url="baseUrl2" />
  </div>
</template>
    
  <script setup>
import officialApi from "@/api/master/officialWebsiteModule";
import { ref, onMounted } from "vue";
import NavBar from "@/components/website/four/NavBar.vue";
import Footer from "@/components/website/four/Footer.vue";

// 定义页面元信息
definePageMeta({
  layout: false,
  path: "/website/four/about"
});

// 基础URL
const baseUrl = "/website/one/assets";
const baseUrl2 = "/website/two/assets";

const companyInformations = ref([]);
const getCaseList = () =>{
    officialApi.caseManagement.faceList({page:1,pageSize:5}).then(res => {
        if(res.code == 200){
            caseList.value = res.data.items;
        }
    })
    officialApi.getEnterpriseInformations({page:1,pageSize:1,cate_name:'公司简介'}).then(res => {
        if(res.code == 200){
          companyInformations.value = res.data.items;
        }
    })
    
}
// 页面加载完成后执行的逻辑
onMounted(() => {
  getCaseList();
  // 加载CSS
  function loadCSS(href, id) {
    if (document.getElementById(id)) return;

    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, "style-css");
  // 加载Bootstrap CSS
  loadCSS(`${baseUrl}/vendor/bootstrap/bootstrap.min.css`, "bootstrap-css");
});

// 添加页面元信息
useHead({
  link: [
    {
      rel: "stylesheet",
      href: `${baseUrl}/vendor/bootstrap/bootstrap.min.css`
    },
    {
      rel: "stylesheet",
      href:
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
    }
  ]
});
</script>
<style scoped lang="less">
.website-container {
  width: 100%;
  overflow-x: hidden;
  background-color: white;
  padding-top: 80px; /* 为固定导航栏留出空间 */
}
/* 面包屑导航样式 */
.breadcrumb-container {
  margin-top: -24px;
  background-color: #f8f8f8;
  padding: 15px 0;
  border-bottom: 1px solid #e1e5eb;
}

.breadcrumb {
  margin-bottom: 0;
  background-color: transparent;
  padding: 0;
}

.breadcrumb-item a {
  color: #666 !important;
  text-decoration: none;
}

.breadcrumb-item.active {
  color: #666 !important;
}
.core-values-section {
  padding: 60px 0;
  background-color: #ffffff;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.section-description {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #555;
  text-align: justify;
  margin-bottom: 20px;
}

.image-container {
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.core-values-image {
  width: 1200px;
  height: 468px;
  object-fit: cover;
  max-width: 100%;
  margin: 0 auto; /* 添加水平居中 */
  display: block; /* 确保图片作为块级元素 */
}
/* 小型移动设备 */
@media (max-width: 576px) {
  .section-title {
    font-size: 1.5rem;
  }

  .core-values-section {
    padding: 30px 0;
  }

  .home-news-title {
    font-size: 1.5rem;
  }

  .news-content h3 {
    font-size: 1.1rem;
  }

  .news-content p {
    font-size: 0.85rem;
    -webkit-line-clamp: 2;
  }

  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .product-image {
    height: 140px;
  }

  .product-img {
    width: 140px;
    height: 140px;
  }

  .product-name {
    font-size: 12px;
  }
  
  .products-section {
    padding: 30px 0;
  }
}
</style>
  