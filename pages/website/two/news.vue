<template>
  <div class="website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl2" />

    <!-- 新闻中心页面内容 -->
    <div class="news-center-container">
      <div class="news-center-header">
        <h1 class="news-center-title">新闻中心</h1>
        <p class="news-center-subtitle">News Center</p>
      </div>

      <!-- 新闻列表 -->
      <div class="news-list-container">
        <div class="container">
          <!-- 新闻项目 -->
          <a class="news-item" v-for="(item, index) in newsList" :key="index">
            <div class="news-item-content" @click="goToProductDetail(item.id)">
              <div class="news-item-info">
                <div class="news-date">
                  <div class="news-date-day" v-time="item.created_at" format="yyyy"></div>
                  <div class="news-date-year" v-time="item.created_at" format="MM-dd"></div>
                </div>
                <h3 class="news-item-title">{{ item.title }}</h3>
                <p class="news-item-desc">{{ item.summary }}</p>
              </div>
              <div class="news-item-image">
                <a-image :src="item.image" :alt="item.title" fit="cover" :preview="false"/>
              </div>
            </div>
          </a>
        </div>
      </div>
    </div>

    <!-- 添加页脚组件 -->
    <Footer :base-url="baseUrl2" />
  </div>
</template>

<script setup>
import officialApi from '@/api/master/officialWebsiteModule';
import { ref, onMounted } from "vue";
import NavBar from "@/components/website/two/NavBar.vue";
import Footer from "@/components/website/two/Footer.vue";

// 定义页面元信息
definePageMeta({
  layout: false,
  path: "/website/two/news"
});

// 基础URL
const baseUrl = "/website/one/assets";
const baseUrl2 = "/website/two/assets";

//获取新闻列表
const newsList = ref([]);

const getNewsList = () => {
    officialApi.newsManagement.list.getList({page:1,pageSize:4}).then(res => {
        if(res.code == 200){
          newsList.value = res.data.items;
        }
    })
}

// 分页数据
const currentPage = ref(1);
const totalItems = ref(20); // 假设总共有20条新闻
const pageSize = ref(5);

// 切换页面
const changePage = page => {
  currentPage.value = page;
  // 这里可以添加获取对应页面数据的逻辑
  console.log("切换到页面:", page);
};

const goToProductDetail = (id) => {
  navigateTo(`/website/two/newsdetail/${id}`);
};
// 页面加载完成后执行的逻辑
onMounted(() => {
  getNewsList();
  // 加载CSS
  function loadCSS(href, id) {
    if (document.getElementById(id)) return;

    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, 'style-css');
  // 加载Bootstrap CSS
  loadCSS(`${baseUrl}/vendor/bootstrap/bootstrap.min.css`, 'bootstrap-css');
});

// 添加页面元信息
useHead({
  link: [
    { rel: 'stylesheet', href: `${baseUrl}/vendor/bootstrap/bootstrap.min.css` },
    { rel: 'stylesheet', href: 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css' }
  ]
});
</script>

<style scoped>
/* 新闻中心页面样式 */
.website-container {
  width: 100%;
  overflow-x: hidden;
  background-color: white;
}

.news-center-container {
  padding-top: 80px; /* 为固定导航栏留出空间 */
}

.news-center-header {
  max-width: 1200px;
  margin: 0 auto;
  background-color: white;;
  padding: 50px 15px 10px;
  text-align: left;
}

.news-center-title {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.news-center-subtitle {
  font-size: 18px;
  color: #666;
  margin-bottom: 0;
}

.news-list-container {
  padding: 50px 0;
  max-width: 1200px;
  margin: 0 auto;
}

.news-item {
  width: 100%;
  display: block;
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-decoration: none;
  color: inherit;
}

.news-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.news-item-content {
  background-color: #F8F8F8;
  display: flex;
  align-items: center;
  padding: 35px;
}

.news-date {
  align-items: baseline;
  margin-bottom: 10px;
}

.news-date-day {
  font-weight: 600;
  font-size: 30px;
  color: #a5a5a5;
  line-height: 1;
  margin-right: 5px;
}

.news-date-year {
  font-weight: 400;
  font-size: 16px;
  color: #a5a5a5;
  margin-top: 4px;
  margin-bottom: 5px;
}

.news-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-right: 20px;
}

.news-item-image {
  width: 240px;
  height: 140px;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.news-item-image :deep(img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.news-item-image :deep(.arco-image) {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.news-item:hover .news-item-image :deep(img) {
  transform: scale(1.05);
}

.news-item-title {
  font-weight: 600;
  font-size: 24px;
  color: #262626;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: color 0.3s ease;
}

.news-item:hover .news-item-title {
  color: #1A65FF;
}

.news-item-desc {
  font-weight: 400;
  font-size: 16px;
  color: #7f7f7f;
  margin-bottom: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .news-list-container {
    padding: 30px 15px;
  }
  
  .news-item-content {
    padding: 25px;
  }
  
  .news-item-image {
    width: 180px;
    height: 120px;
  }
}

@media (max-width: 768px) {
  .news-center-header {
    padding: 30px 15px 10px;
  }
  
  .news-center-title {
    font-size: 28px;
  }

  .news-center-subtitle {
    font-size: 16px;
  }
  
  .news-item-content {
    flex-direction: column;
    padding: 20px;
  }
  
  .news-item-info {
    width: 100%;
    padding-right: 0;
    margin-bottom: 15px;
  }
  
  .news-item-image {
    width: 100%;
    height: 180px;
  }
  
  .news-item-title {
    font-size: 18px;
    margin-bottom: 10px;
    -webkit-line-clamp: 2;
  }

  .news-item-desc {
    font-size: 14px;
    -webkit-line-clamp: 2;
  }
  
  .news-date {
    margin-bottom: 5px;
  }
  
  .news-date-day {
    font-size: 24px;
  }
  
  .news-date-year {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .news-center-title {
    font-size: 24px;
  }
  
  .news-center-subtitle {
    font-size: 14px;
  }
  
  .news-item-content {
    padding: 15px;
  }
  
  .news-item-title {
    font-size: 16px;
  }
  
  .news-item-desc {
    font-size: 13px;
  }
  
  .news-item-image {
    height: 150px;
  }
}
</style>
