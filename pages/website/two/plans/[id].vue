<template>
  <div class="website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl2" />

    <!-- 面包屑导航 -->
    <div class="breadcrumb-container">
      <div class="container">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="/website/two/index">首页</a>
            </li>
            <li class="breadcrumb-item active">{{ newsDetail.title }}</li>
          </ol>
        </nav>
      </div>
    </div>

    <!-- 新闻详情主体 -->
    <div class="news-detail-container">
      <div class="container">
        <div class="news-detail-content">
          <h1 class="news-title">{{ newsDetail.title }}</h1>
          <div class="news-meta">
            <span class="news-date" v-time="newsDetail.created_at"></span>
          </div>
          <div class="news-text">
            <div class="news-content" v-html="newsDetail.content"></div>
          </div>
          <div class="news-image" v-if="newsDetail.cover_image">
              <a-image
                :preview="false"
                :src="newsDetail.cover_image"
                :alt="newsDetail.title"
                class="img-fluid"
              />
            </div>
        </div>
      </div>
    </div>

    <!-- 添加页脚组件 -->
    <Footer :base-url="baseUrl2" />
  </div>
</template>
  
  <script setup>
import officialApi from "@/api/master/officialWebsiteModule";
import { ref, onMounted, computed } from "vue";
import NavBar from "@/components/website/two/NavBar.vue";
import Footer from "@/components/website/two/Footer.vue";

// 定义页面元信息
definePageMeta({
  layout: false,
  path: "/website/two/plans/:id"
});

// 获取路由参数
const route = useRoute();
const newsId = computed(() => route.params.id);

// 基础URL
const baseUrl = "/website/one/assets";
const baseUrl2 = "/website/two/assets";

// 新闻数据
const newsDetail = ref({});

// 在实际应用中，这里应该根据ID从API获取新闻详情
const fetchNewsDetail = async id => {
  console.log("获取新闻ID:", id);
  // 这里应该是从API获取数据
  officialApi.caseManagement.detail(id).then(res => {
    if (res.code == 200) {
      newsDetail.value = res.data;
    }
  });

};

// 页面加载完成后执行的逻辑
onMounted(() => {
  fetchNewsDetail(newsId.value);
  // 加载CSS
  function loadCSS(href, id) {
    if (document.getElementById(id)) return;

    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, "style-css");
  // 加载Bootstrap CSS
  loadCSS(`${baseUrl}/vendor/bootstrap/bootstrap.min.css`, "bootstrap-css");
});

// 添加页面元信息
useHead({
  title: "芯片战场：巨头博弈四大关键市场！",
  meta: [
    {
      name: "description",
      content:
        "AI人工智能取得高速发展，有望重塑半导体产业发展格局，芯片设计行为产业链核心环节之一"
    }
  ],
  link: [
    {
      rel: "stylesheet",
      href: `${baseUrl}/vendor/bootstrap/bootstrap.min.css`
    },
    {
      rel: "stylesheet",
      href:
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
    }
  ]
});
</script>
    
  <style scoped>
.news-image {
  margin: 20px 0;
  text-align: center;
}

.news-image img {
  max-width: 100%;
  border-radius: 5px;
}
/* 网站容器样式 */
.website-container {
  width: 100%;
  overflow-x: hidden;
  background-color: white;
  padding-top: 80px; /* 为固定导航栏留出空间 */
}

/* 面包屑导航样式 */
.breadcrumb-container {
  margin-top: -24px;
  background-color: #f0f5ff;
  padding: 15px 0;
  border-bottom: 1px solid #e1e5eb;
}

.breadcrumb {
  margin-bottom: 0;
  background-color: transparent;
  padding: 0;
}

.breadcrumb-item a {
  color: #666 !important;
  text-decoration: none;
}

.breadcrumb-item.active {
  color: #666 !important;
}

/* 容器样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* 新闻详情容器 */
.news-detail-container {
  padding: 40px 0 60px;
  background-color: #f8f9fa;
}

.news-detail-content {
  background-color: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* 新闻标题样式 */
.news-title {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
  line-height: 1.4;
}

/* 新闻元数据样式 */
.news-meta {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 25px;
  font-size: 14px;
  color: #666;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
}

.news-date {
  margin-top: 0px !important;
  margin-right: 20px;
}

.news-views {
  display: flex;
  align-items: center;
}

.news-views :deep(svg) {
  margin-right: 5px;
}

/* 新闻内容样式 */
.news-text {
  font-size: 16px;
  line-height: 1.8;
  color: #333;
}

.news-content {
  margin-top: 20px;
}

.news-paragraph {
  margin-bottom: 25px;
}

.news-paragraph h3 {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #0052d9;
}

.news-paragraph p {
  margin-bottom: 15px;
  text-align: justify;
  line-height: 1.8;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .news-title {
    font-size: 24px;
  }

  .news-detail-content {
    padding: 20px;
  }

  .news-paragraph h3 {
    font-size: 20px;
  }
}
</style>
    