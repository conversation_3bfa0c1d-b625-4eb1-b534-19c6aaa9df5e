<template>
  <div class="website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl" />

    <!-- 页面主要内容 -->
    <main class="main-content">
      <div id="mainCarousel" class="carousel slide" data-bs-ride="carousel">
        <div class="carousel-inner">
          <div
            class="carousel-item"
            v-for="(item,index) in basisData.banner_url"
            :key="index"
            :class="{active: index === 0}"
          >
            <img :src="item" class="d-block w-100" alt="轮播图" v-if="item.indexOf('mp4') == -1" />
            <video class="d-block w-100" autoplay muted playsinline v-else>
              <source :src="item" type="video/mp4" />
            </video>
            <div class="carousel-overlay"></div>
          </div>
        </div>
        <!-- 左右切换箭头 -->
        <button class="carousel-control-prev" type="button" data-bs-target="#mainCarousel" data-bs-slide="prev">
          <span class="carousel-control-prev-icon" aria-hidden="true"></span>
          <span class="visually-hidden">上一张</span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#mainCarousel" data-bs-slide="next">
          <span class="carousel-control-next-icon" aria-hidden="true"></span>
          <span class="visually-hidden">下一张</span>
        </button>
      </div>
      <!-- 关于我们 -->
      <section class="about-section py-12">
        <div class="container mx-auto px-4">
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-3/5 mb-8 md:mb-0 md:pr-12 about-content" @mouseenter="aboutHovered = true" @mouseleave="aboutHovered = false">
              <h2 class="text-3xl font-bold mb-6" :class="{'text-red-600': aboutHovered}">关于我们</h2>
              <p
                class="mb-4 about-description"
                :class="{'text-gray-700': aboutHovered}"
              >{{companyInformations[0]?.description}}</p>
            
              <a
                href="/website/three/about"
                class="inline-block bg-red-600 text-white py-2 px-6 hover:bg-red-700 transition duration-300 text-decoration-none about-btn"
                :class="{'pulse-effect': aboutHovered}"
              >了解更多</a>
            </div>
            <div class="md:w-2/5 about-image-container" @mouseenter="aboutImageHovered = true" @mouseleave="aboutImageHovered = false">
              <img src="/website/three/assets/images/3.png" alt="关于我们" class="w-full about-image" :class="{'scale-effect': aboutImageHovered}" />
            </div>
          </div>
        </div>
      </section>
      <!-- 企业优势 -->
      <section
        class="company-advantages py-12"
        :style="{ backgroundImage: 'url(/website/three/assets/images/server_bg.png)' }">
        <div class="container mx-auto px-4">
          <h2 class="text-center text-3xl font-bold mb-12 " style="color: #fff;" >企业优势</h2>

          <div class="advantages-wrapper relative">
             <!-- 优势项目容器 -->
             <div class="advantages-container flex flex-wrap justify-between items-stretch w-full">
              <!-- 优势1 -->
              <div class="advantage-item md:w-1/4 w-full px-3 flex flex-col items-center mb-8 md:mb-0" @mouseenter="advantageHovered[0] = true" @mouseleave="advantageHovered[0] = false">
                <div class="md:hidden advantage-content text-left mb-4" v-if="isMobile">
                  <div class="advantage-number" :class="{'text-red-500': advantageHovered[0]}">01</div>
                  <h3 class="advantage-title" :class="{'text-red-500': advantageHovered[0]}">{{enterpriseInformations2[0]?.title}}</h3>
                  <p class="advantage-content">{{enterpriseInformations2[0]?.description}}</p>
                </div>
                <div class="advantage-image-wrapper mb-6 flex justify-center">
                  <img
                    class="advantage-image"
                    :class="{'advantage-image-hover': advantageHovered[0]}"
                    :src="enterpriseInformations2[0]?.cover_image || '/website/three/assets/images/1.png'"
                  />
                </div>
                <div class="hidden md:block advantage-content text-left" v-if="!isMobile">
                  <div class="advantage-number" :class="{'text-red-500': advantageHovered[0]}">01</div>
                  <h3 class="advantage-title" :class="{'text-red-500': advantageHovered[0]}">{{enterpriseInformations2[0]?.title}}</h3>
                  <p class="advantage-content">{{enterpriseInformations2[0]?.description}}</p>
                </div>
              </div>

              <!-- 优势2 -->
              <div class="advantage-item md:w-1/4 w-full px-3 flex flex-col items-center mb-8 md:mb-0" @mouseenter="advantageHovered[1] = true" @mouseleave="advantageHovered[1] = false">
                <div class="md:hidden advantage-content text-left mb-4" v-if="!isMobile">
                  <div class="advantage-number" :class="{'text-red-500': advantageHovered[1]}">02</div>
                  <h3 class="advantage-title" :class="{'text-red-500': advantageHovered[1]}">{{enterpriseInformations2[1]?.title}}</h3>
                  <p class="advantage-content">{{enterpriseInformations2[1]?.description}}</p>
                </div>
                <div class="hidden md:flex advantage-content mb-4" v-if="isMobile">
                  <div class="advantage-number" :class="{'text-red-500': advantageHovered[1]}">02</div>
                  <h3 class="advantage-title" :class="{'text-red-500': advantageHovered[1]}">{{enterpriseInformations2[1]?.title}}</h3>
                  <p class="advantage-content">{{enterpriseInformations2[1]?.description}}</p>
                </div>
                <div class="advantage-image-wrapper mb-6 flex justify-center">
                  <img
                    class="advantage-image"
                    :class="{'advantage-image-hover': advantageHovered[1]}"
                    :src="enterpriseInformations2[1]?.cover_image || '/website/three/assets/images/2.png'"
                  />
                </div>
              </div>

              <!-- 优势3 -->
              <div
                class="advantage-item md:w-1/4 w-full px-3 flex flex-col items-center mb-8 md:mb-0" @mouseenter="advantageHovered[2] = true" @mouseleave="advantageHovered[2] = false">
                <div class="md:hidden advantage-content text-left mb-4" v-if="isMobile">
                  <div class="advantage-number" :class="{'text-red-500': advantageHovered[2]}">03</div>
                  <h3 class="advantage-title" :class="{'text-red-500': advantageHovered[2]}">{{enterpriseInformations2[2]?.title}}</h3>
                  <p class="advantage-content">{{enterpriseInformations2[2]?.description}}</p>
                </div>

                <div class="advantage-image-wrapper mb-6 flex justify-center">
                  <img
                    class="advantage-image"
                    :class="{'advantage-image-hover': advantageHovered[2]}"
                    :src="enterpriseInformations2[2]?.cover_image || '/website/three/assets/images/1.png'"
                  />
                </div>
                <div class="hidden md:block advantage-content text-left" v-if="!isMobile">
                  <div class="advantage-number" :class="{'text-red-500': advantageHovered[2]}">03</div>
                  <h3 class="advantage-title" :class="{'text-red-500': advantageHovered[2]}">{{enterpriseInformations2[2]?.title}}</h3>
                  <p class="advantage-content">{{enterpriseInformations2[2]?.description}}</p>
                </div>
              </div>

              <!-- 优势4 -->
              <div class="advantage-item md:w-1/4 w-full px-3 flex flex-col items-center mb-8 md:mb-0" @mouseenter="advantageHovered[3] = true" @mouseleave="advantageHovered[3] = false">
                <div class="md:hidden advantage-content text-left mb-4" v-if="!isMobile">
                  <div class="advantage-number" :class="{'text-red-500': advantageHovered[3]}">04</div>
                  <h3 class="advantage-title" :class="{'text-red-500': advantageHovered[3]}">{{enterpriseInformations2[3]?.title}}</h3>
                  <p class="advantage-content">{{enterpriseInformations2[3]?.description}}</p>
                </div>
                <div class="hidden md:flex advantage-content text-left mb-4" v-if="isMobile">
                  <div class="advantage-number" :class="{'text-red-500': advantageHovered[3]}">04</div>
                  <h3 class="advantage-title" :class="{'text-red-500': advantageHovered[3]}">{{enterpriseInformations2[3]?.title}}</h3>
                  <p class="advantage-content">{{enterpriseInformations2[3]?.description}}</p>
                </div>
                <div class="advantage-image-wrapper mb-6 flex justify-center">
                  <img
                    class="advantage-image"
                    :class="{'advantage-image-hover': advantageHovered[3]}"
                    :src="enterpriseInformations2[3]?.cover_image || '/website/three/assets/images/2.png'"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <!-- 核心服务 -->
      <section class="core-services">
        <div class="container mx-auto px-4">
          <h2 class="text-center text-3xl font-bold mb-12 mt-12 animate-on-scroll">核心服务</h2>
          
          <div class="services-container">
            <!-- 服务项目 -->
            <div 
              class="service-item flex flex-col md:flex-row mb-5 service-animate" 
              v-for="(item,index) in enterpriseInformations" 
              :key="item.id"
              @mouseenter="serviceHovered[index] = true" 
              @mouseleave="serviceHovered[index] = false"
              :class="{'service-hovered': serviceHovered[index]}"
            >
              <div class="service-image md:w-2/3 overflow-hidden">
                <img
                  :preview="false"
                  :src="item.cover_image"
                  :alt="item.title"
                  :width="isMobile ? '100%' : 650"
                  :height="isMobile ? 'auto' : 338"
                  class="responsive-image transition-transform duration-500"
                  :class="{'scale-105': serviceHovered[index]}"
                />
              </div>
              <div 
                class="service-content md:w-1/2 p-8 flex flex-col justify-center relative transition-all duration-300" 
                :class="[`service-content-${index+1}`, {'service-content-active': serviceHovered[index]}]"
              >
                <h3 
                  class="text-2xl font-bold mb-4 transition-all duration-300" 
                  :class="index == 0 ? 'text-gray-700' : 'text-white'"
                >
                  {{item.title}}
                </h3>
                <p 
                  :class="[index == 0 ? 'text-gray-700' : 'text-white', {'fade-in-text': serviceHovered[index]}]"
                  class="transition-opacity duration-500"
                >
                  {{item.description}}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
      <!-- 成功案例 -->
      <section class="success-cases py-12">
        <div class="container mx-auto px-4">
          <h2 class="text-center text-3xl font-bold mb-12">成功案例</h2>
          
          <div class="cases-container grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- 商标案例 -->
            <div class="case-item relative overflow-hidden rounded-lg " v-for="(item,index) in caseList" :key="index">
              <a-image
                :preview="false"
                :src="item.cover_image"
                :alt="item.title"
                :width="'100%'"
                :height="'100%'"
                class="case-image"
                style="display: block; line-height: 0;"
              />
              <div class="case-overlay flex flex-col justify-center items-center p-6">
                <div class="case-content text-center">
                  <h3 class="text-xl font-bold text-white mb-2">{{item.title}}</h3>
                  <p class="text-white text-sm">{{item.description}}</p>
                </div>
              </div>
            </div>
            
            
          </div>
        </div>
      </section>
      <!-- 新闻咨询 -->
      <div class="news-list-container">
        <h2 class="text-center text-3xl font-bold mb-12">新闻咨询</h2>
        <div class="container">
          <!-- 新闻项目 -->
          <a class="news-item" v-for="(item, index) in newsList" :key="index">
            <div class="news-item-content" @click="goToProductDetail(item.id)">
              <div class="news-item-info">
                <div class="news-date">
                  <div class="news-date-day" v-time="item.created_at" format="yyyy"></div>
                  <div class="news-date-year" v-time="item.created_at" format="MM-dd"></div>
                </div>
                <h3 class="news-item-title">{{ item.title }}</h3>
                <p class="news-item-desc">{{ item.summary }}</p>
              </div>
              <div class="news-item-image">
                <a-image  :src="item.image" :alt="item.title" fit="cover" :preview="false" />
              </div>
            </div>
          </a>
        </div>
      </div>
      <!-- 加入我们 -->
      <section class="join-us" :style="{ backgroundImage: 'url(/website/three/assets/images/bg2.png)' }">
        <div class="container mx-auto px-4 py-16">
          <div class="join-us-container">
            <div class="join-us-content">
              <h2 class="join-us-title">专业的知识</h2>
              <h2 class="join-us-title">产权服务</h2>
              <p class="join-us-desc">我们为客户提供全方位、高品质的知识产权解决方案，助力企业提升核心竞争力，实现可持续发展。</p>
            </div>
            
            <div class="join-us-form">
              <h3 class="form-title">加入我们</h3>
              
              <div class="form-group">
                <span style="color: red;">* &nbsp;</span>
                <span class="form-label">姓名 </span>
                <a-input v-model="joinForm.submitter_name" placeholder="请输入姓名" />
              </div>
              
              <div class="form-group">
                <span style="color: red;">* &nbsp;</span>
                <span class="form-label">手机</span>
                <a-input v-model="joinForm.phone" placeholder="请输入手机" />
              </div>
              
              <div class="form-group">
                <div class="form-label">邮箱</div>
                <a-input v-model="joinForm.email" placeholder="请输入邮箱" />
              </div>
              
              <div class="form-group">
                <div class="form-label">留言</div>
                <a-textarea v-model="joinForm.message_details" placeholder="请输入留言" :auto-size="{ minRows: 3, maxRows: 5 }" />
              </div>
              
              <a-button type="primary" class="submit-btn" @click="submitJoinForm">提交申请</a-button>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 使用页脚组件 -->
    <Footer :base-url="baseUrl" />
  </div>
</template>


<script setup>
import officialApi from "@/api/master/officialWebsiteModule";
import { onMounted, onBeforeUnmount, ref, onUnmounted, reactive } from "vue";
import NavBar from "@/components/website/three/NavBar.vue";
import Footer from "@/components/website/three/Footer.vue";
import { Message } from "@arco-design/web-vue";
// 使用useRuntimeConfig来获取基础URL
const config = useRuntimeConfig();
const baseUrl = "/website/one/assets";

definePageMeta({
  layout: false,
  path: "/website/three/index"
});

// 使用useHead钩子管理头部元数据
useHead({
  title: "首页",
  meta: [
    { charset: "UTF-8" },
    { name: "viewport", content: "width=device-width, initial-scale=1.0" }
  ],
  link: [
    {
      rel: "stylesheet",
      href: `${baseUrl}/vendor/bootstrap/bootstrap.min.css`
    },
    {
      rel: "stylesheet",
      href:
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
    }
  ]
});

// 获取首页数据
const pageData = ref({});
// 获取供应链数据
const basisData = ref({});
// 移动端判断
const isMobile = ref(false);

// 加入我们表单数据
const joinForm = reactive({});

// 手机号码校验函数
const validatePhone = (phone) => {
  // 中国手机号码校验正则表达式，11位数字，以1开头
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

// 提交加入我们表单
const submitJoinForm = () => {
  // 表单验证
  if (!joinForm.submitter_name) {
    Message.warning("请填写姓名");
    return;
  }
  
  // 手机号码格式校验
  if (!validatePhone(joinForm.phone)) {
    Message.warning("请填写正确的手机号");
    return;
  }
  
  officialApi.messageManagement.create(joinForm).then(res => {
    if(res.code == 200){
      Message.success('申请提交成功！');
      // 重置表单
      joinForm.submitter_name = '';
      joinForm.phone = '';
      joinForm.email = '';
      joinForm.message_details = '';
    }else{
      Message.error(res.message);
    }
  })
};

// 检测屏幕大小的函数
const checkScreenSize = () => {
  isMobile.value = window.innerWidth < 768;
};
const getData = () => {
  officialApi.basisManagement.getData().then(res => {
    if (res.code == 200) {
      basisData.value = res.data;
    }
    if (res.data.banner_url) {
      basisData.value.banner_url = res.data.banner_url.split(",");
    }
    localStorage.setItem("basisData", JSON.stringify(basisData.value));
    console.log(basisData.value, "xxxxx");
  });
};
//获取新闻列表
const newsList = ref([]);
const getNewsList = () => {
    officialApi.newsManagement.list.getList({page:1,pageSize:3}).then(res => {
        if(res.code == 200){
          newsList.value = res.data.items;
        }
    })
}

// 获取案例列表
const caseList = ref([]);
const getCaseList = () =>{
    officialApi.caseManagement.faceList({page:1,pageSize:3}).then(res => {
        if(res.code == 200){
            caseList.value = res.data.items;
        }
    })
}

// 服务支持 企业优势 公司简介
const enterpriseInformations = ref([]);
const enterpriseInformations2 = ref([]);
const companyInformations = ref([]);
const getEnterpriseInformations = () => {
  officialApi.getEnterpriseInformations({page:1,pageSize:1,cate_name:'公司简介'}).then(res => {
        if(res.code == 200){
          companyInformations.value = res.data.items;
        }
    })
    officialApi.getEnterpriseInformations({page:1,pageSize:3,cate_name:'服务支持'}).then(res => {
        if(res.code == 200){
            enterpriseInformations.value = res.data.items;
        }
    })
    officialApi.getEnterpriseInformations({page:1,pageSize:4,}).then(res => {
        if(res.code == 200){
          enterpriseInformations2.value = res.data.items;
        }
    })
}
const goToProductDetail = (id) => {
  navigateTo(`/website/three/newsdetail/${id}`);
};

// 鼠标悬停状态变量
const aboutHovered = ref(false);
const aboutImageHovered = ref(false);
const advantageHovered = ref([false, false, false, false]);
const serviceHovered = ref(Array(10).fill(false)); // 核心服务悬停状态
// 页面加载完成后执行的逻辑
onMounted(() => {
  // 添加滚动动画观察器
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('service-animate');
        observer.unobserve(entry.target);
      }
    });
  }, { threshold: 0.1 });
  
  // 观察所有服务项
  setTimeout(() => {
    document.querySelectorAll('.service-item').forEach(item => {
      observer.observe(item);
    });
  }, 500);
  
  getData();
  getNewsList();
  getCaseList();
  getEnterpriseInformations();
  // 初始判断是否为移动端
  checkScreenSize();

    // 加载CSS
  function loadCSS(href, id) {
    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, "style-css");

  // 定义加载脚本的函数
  function loadScript(src, id) {
    return new Promise((resolve, reject) => {
      const script = document.createElement("script");
      script.src = src;
      script.id = id;
      script.onload = resolve;
      script.onerror = e => {
        console.error(`加载脚本失败: ${src}`, e);
        reject(e);
      };
      document.body.appendChild(script);
    });
  }
  // 添加窗口大小变化事件监听
  window.addEventListener("resize", checkScreenSize);


  // 加载Bootstrap和其他必要的脚本
  setTimeout(() => {
    try {
      // 加载Bootstrap
      loadScript(
        `${baseUrl}/vendor/bootstrap/bootstrap.bundle.min.js`,
        "bootstrap-script"
      )
        .then(() => {
          // 初始化轮播图
          const mainCarousel = document.getElementById("mainCarousel");
          if (mainCarousel && window.bootstrap) {
            const carousel = new window.bootstrap.Carousel(mainCarousel, {
              interval: 5000, // 设置为5秒切换一次
              wrap: true,
              ride: "carousel", // 显式指定自动轮播
              pause: "hover" // 鼠标悬停时暂停轮播
            });
          }

          // 添加滚动特效
          window.addEventListener("scroll", function() {
            const newsTitle = document.querySelector(".home-news-title");
            if (newsTitle) {
              const rect = newsTitle.getBoundingClientRect();
              if (rect.top < window.innerHeight * 0.8) {
                newsTitle.classList.add("animate__fadeInDown");
              }
            }
          });

          console.log("页面功能初始化完成");
        })
        .catch(error => {
          console.error("Bootstrap 加载失败:", error);
        });
    } catch (error) {
      console.error("初始化页面功能失败:", error);
    }
  }, 500);
});

// 组件卸载前清理
onBeforeUnmount(() => {
  // 移除窗口大小变化事件监听
  window.removeEventListener("resize", checkScreenSize);
});
</script>

<style scoped lang="less">
/* 核心服务动画效果 */
.service-animate {
  transition: all 0.5s ease;
  transform: translateY(0);
  opacity: 0.9;
}

.service-hovered {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  opacity: 1;
}

.service-content-active {
  background-color: rgba(0, 0, 0, 0.8);
}

.transform-text {
  transform: translateY(-5px);
}

.fade-in-text {
  opacity: 1;
}

.animate-on-scroll {
  animation: fadeInUp 1s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.news-list-container {
  padding: 50px 0;
  max-width: 1200px;
  margin: 0 auto;
}

.news-item {
  width: 100%;
  display: block;
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-decoration: none;
  color: inherit;
}

.news-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.news-item-content {
  background-color: #F8F8F8;
  display: flex;
  align-items: center;
  padding: 35px;
}

.news-date {
  align-items: baseline;
  margin-bottom: 10px;
}

.news-date-day {
  font-weight: 600;
  font-size: 30px;
  color: #a5a5a5;
  line-height: 1;
  margin-right: 5px;
}

.news-date-year {
  font-weight: 400;
  font-size: 16px;
  color: #a5a5a5;
  margin-top: 4px;
  margin-bottom: 5px;
}

.news-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-right: 20px;
}

.news-item-image {
  width: 240px;
  height: 140px;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.news-item-image :deep(img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.news-item-image :deep(.arco-image) {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.news-item:hover .news-item-image :deep(img) {
  transform: scale(1.05);
}

.news-item-title {
  font-weight: 600;
  font-size: 24px;
  color: #262626;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: color 0.3s ease;
}

.news-item:hover .news-item-title {
  color: #E70012 ;
}

.news-item-desc {
  font-weight: 400;
  font-size: 16px;
  color: #7f7f7f;
  margin-bottom: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
}


.service-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 1.25rem;
  
  @media (min-width: 48rem) {
    flex-direction: row;
    flex-wrap: nowrap;
    overflow: hidden;
    
    &:nth-child(even) {
      flex-direction: row-reverse;
    }
  }
  
  .service-content {
    position: relative;
    z-index: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 16px;
    
    @media (min-width: 48rem) {
      padding: 32px;
      height: 21.125rem;
    }
    
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: -1;
    }
    
    &.service-content-1::before {
      background-color: #f3f4f6; /* bg-gray-100 */
    }
    
    &.service-content-2::before {
      background-color: #333333;
    }
    
    &.service-content-3::before {
      background-color: #E70012;
    }
  }
}

.responsive-image {
  width: 100%;
  height: auto;
  max-width: 100%;
  display: block;
  
  @media (min-width: 48rem) {
    width: 100%;
    height: 21.125rem;
    object-fit: cover;
  }
}
.company-advantages {
  background-image: url("/website/three/assets/images/server_bg.png");
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

.advantage-image-wrapper {
  width: 241px;
  height: 187px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.advantage-image {
  object-fit: cover;
  width: 100%;
  height: 100%;
  position: relative;
}

.container-title {
  font-weight: 600;
  font-size: 2.75rem;
  color: #ffffff;
  line-height: 3.875rem;
  text-align: center;
  margin-bottom: 2.5rem;

  @media (max-width: 48rem) {
    font-size: 2rem;
    line-height: 2.75rem;
    margin-bottom: 1.875rem;
  }
}

.advantages-container {
  @media (max-width: 48rem) {
    flex-direction: column;
  }
}

.advantage-content {
  width: 100%;
  height: 14.375rem; /* 固定高度，仅在桌面端生效 */
  display: flex;
  flex-direction: column;
  text-align: left;

  @media (max-width: 48rem) {
    height: auto;
    min-height: auto;
    margin-bottom: 0;
  }

  .advantage-number {
    font-weight: 600;
    font-size: 3.125rem;
    color: #ffffff;

    @media (max-width: 48rem) {
      font-size: 2.25rem;
    }
  }

  .advantage-title {
    font-weight: 600;
    font-size: 1.625rem;
    color: #ffffff;
    margin-bottom: .625rem;

    @media (max-width: 48rem) {
      font-size: 1.375rem;
    }
  }

  .advantage-content {
    font-weight: 400;
    font-size: 1rem;
    color: #ffffff;

    @media (max-width: 48rem) {
      font-size: .875rem;
    }
  }
}

.carousel-overlay {
  background-color: none !important;
}
.website-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}


.hero-section {
  background-image: url("/placeholder/hero-bg.jpg");
  background-size: cover;
  background-position: center;
  height: 31.25rem;
  position: relative;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* background-color: rgba(0, 0, 0, 0.5); */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 确保 Bootstrap 和 Tailwind 的兼容性 */
.container {
  width: 100%;
  padding-right: .9375rem;
  padding-left: .9375rem;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 36rem) {
  .container {
    max-width: 33.75rem;
  }
}

@media (min-width: 48rem) {
  .container {
    max-width: 45rem;
  }
}

@media (min-width: 62rem) {
  .container {
    max-width: 60rem;
  }
}

@media (min-width: 75rem) {
  .container {
    max-width: 76.25rem;
  }
}

/* 成功案例样式 */
.success-cases {
  background-color: #f8f9fa;
}

.cases-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  
  @media (max-width: 48rem) {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }
  
  @media (max-width: 36rem) {
    grid-template-columns: repeat(3, 1fr);
    gap: 4px;
  }
}

.case-item {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 .25rem .375rem rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  line-height: 0;
  
  &:hover {
    transform: translateY(-0.3125rem);
    
    .case-overlay {
      opacity: 1;
      
      .case-content {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }
  
  .case-image {
    width: 100%;
    height: auto;
    object-fit: cover;
    transition: transform 0.3s ease;
    display: block;
    line-height: 0;
    vertical-align: middle;
  }
  
  &:hover .case-image {
    transform: scale(1.05);
  }
}

.case-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  padding: 24px;
  opacity: 0;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  
  .case-content {
    transform: translateY(1.25rem);
    transition: all 0.3s ease;
    opacity: 0;
  }
  
  h3 {
    margin-bottom: 8px;
    font-size: 20px;
    font-weight: bold;
    color: #ffffff;
    
    @media (max-width: 48rem) {
      font-size: 16px;
    }
  }
  
  p {
    color: #ffffff;
    font-size: 14px;
    margin: 0;
    
    @media (max-width: 48rem) {
      font-size: 12px;
    }
    
    @media (max-width: 36rem) {
      display: none;
    }
  }
}

/* 加入我们样式 */
.join-us {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  color: #ffffff;
}

.join-us-container {
  display: flex;
  flex-direction: column;
  
  @media (min-width: 48rem) {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

.join-us-content {
  flex: 1;
  margin-bottom: 2rem;
  
  @media (min-width: 48rem) {
    margin-bottom: 0;
    margin-right: 3rem;
  }
}

.join-us-title {
  font-size: 2.5rem;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.5rem;
  
  @media (max-width: 48rem) {
    font-size: 2rem;
  }
}

.join-us-desc {
  font-size: 1rem;
  line-height: 1.6;
  margin-top: 1rem;
  max-width: 30rem;
}

.join-us-form {
  background-color: #ffffff;
  padding: 2rem;
  border-radius: 0.5rem;
  width: 100%;
  max-width: 25rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
}

.form-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1.5rem;
  text-align: center;
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-label {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.submit-btn {
  width: 100%;
  height: 3rem;
  font-size: 1rem;
  font-weight: 500;
  background-color: #E70012;
  border-color: #E70012;
  
  &:hover {
    background-color: darken(#E70012, 10%);
    border-color: darken(#E70012, 10%);
  }
}
/* 企业优势部分的动态效果 */
.advantage-item {
  transition: all 0.3s ease;
  position: relative;
}

.advantage-item:hover {
  transform: translateY(-8px);
}

.advantage-image {
  transition: all 0.5s ease;
  border-radius: 8px;
}

.advantage-image-hover {
  transform: scale(1.08);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.advantage-image-wrapper {
  overflow: hidden;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.advantage-number, .advantage-title {
  transition: color 0.3s ease;
}

/* 关于我们部分的动态效果 */
.about-content {
  transition: all 0.4s ease;
}

.about-content:hover {
  transform: translateY(-5px);
}

.about-description {
  transition: color 0.3s ease;
}

.about-image {
  transition: transform 0.5s ease;
}

.scale-effect {
  transform: scale(1.05);
}

.about-image-container {
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.about-image-container:hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.pulse-effect {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(220, 38, 38, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0);
  }
}
</style>