<template>
  <div class="website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl" />

    <!-- 关于我们页面内容 -->
    <div class="joinus-banner">
    </div>
    <!-- 公司简介 -->
    <div class="company-intro profile-section" id="company-intro">
      <h2 class="section-titles">公司简介</h2>
      <div class="container" v-for="item in enterpriseInforList" :key="item.id">
        <div class="intro-text">
           <p v-if="item.cate_name == '公司简介'">{{item.description}}</p>
        </div>
        <!-- <div class="intro-image">
          <div class="mission-bg">
            <div class="mission-left">
              <h3>愿景及使命</h3>
            </div>
            <div class="mission-right">
              <div class="mission-item">
                <h4>企业愿景</h4>
                <p>引领微业库商新生态，连客户与互联网思维</p>
              </div>
              <div class="mission-item">
                <h4>企业使命</h4>
                <p>流转实业生意的网络，赋能中小微商本常效</p>
              </div>
            </div>
          </div>
        </div> -->
      </div>
    </div>

    <!-- 业务架构 -->
    <!-- <div class="business-structure profile-section" id="business-structure">
      <h2 class="section-title">业务架构</h2>
      <div class="structure-content">
        <div class="logo-center">
          <a-image :src="getImageUrl('baling.png')" alt="八灵科技" />
        </div>
        <div class="structure-tree">
          <a-image :src="getImageUrl('skeleton.png')" alt="组织架构图" />
        </div>
        <div class="department-list">
          <div 
            v-for="dept in departmentsOriginal" 
            :key="dept.id" 
            class="dept-item" 
            :class="{active: activeDept === dept.id}"
            @click="selectDepartment(dept.id)"
            :data-desc="dept.desc"
          >{{ dept.name }}</div>
        </div>
        <div class="dept-description">
          <p>{{ deptDescription }}</p>
        </div>
      </div>
    </div> -->

    <!-- 管理团队 -->
    <!-- <div id="team" class="container-content profile-section">
      <h1 class="section-title">管理团队</h1>
      <div class="team-container">
        <div v-for="(member, index) in teamMembers" :key="index" class="team-grid">
          <div class="team-info">
            <h3>{{ member.name }}</h3>
            <p>{{ member.position }}</p>
          </div>
          <div class="team-member">
            <div class="image-wrapper">
              <a-image :src="getImageUrl(member.image)" :alt="member.name" />
            </div>
          </div>
        </div>
      </div>
    </div> -->

    <!-- 企业文化 -->
 
<div id="value">
  <h1 class="section-title" style="padding-top: 2rem;">企业文化</h1>
  <div  class="value-section profile-section">
  <!-- 企业理念部分 -->
  <div class="culture-container">
    <div class="culture-row" v-for="(item, index) in enterpriseInforList" :key="index" :class="index % 2 == 1 ? 'reverse' : ''">
      <div class="culture-text">
        <h3 class="culture-title">{{item.title}}</h3>
        <p class="culture-description">
          {{item.description}}
        </p>
      </div>
      <div class="culture-image">
        <img :src="item.cover_image" alt="企业愿景" />
      </div>
    </div>
    
   

  </div>
</div>
</div>
    <!-- 办公地点 -->
    <div id="office" class="office-section profile-section" style="text-align: center;">
      <h2 class="section-title">办公地点</h2>
      <div class="office-grid" style="display: flex; justify-content: center;">
        <div class="office-item" style="text-align: center; max-width: 80%; margin: 0 auto;">
          <h3>{{ basisData.company_address }}</h3>
        </div>
      </div>
    </div>
    <!-- 使用页脚组件 -->
    <Footer :base-url="baseUrl" />
  </div>
</template>
  
<script setup>
import officialApi from '@/api/master/officialWebsiteModule';
import { onMounted, onBeforeUnmount, ref } from "vue";
import NavBar from "@/components/website/one/NavBar.vue";
import Footer from "@/components/website/one/Footer.vue";

// 使用useRuntimeConfig来获取基础URL
const config = useRuntimeConfig();
const baseUrl = "/website/one/assets";

// 定义获取资源URL的函数
const getAssetUrl = (type, path) => {
  return `${baseUrl}/${type}/${path}`;
};

// 定义获取图片URL的函数
const getImageUrl = path => {
  return getAssetUrl("images", path);
};

// 定义获取CSS URL的函数
const getCssUrl = path => {
  return getAssetUrl("css", path);
};

// 定义获取JS URL的函数
const getJsUrl = path => {
  return getAssetUrl("js", path);
};

definePageMeta({
  layout: false,
  path: "/website/one/profile"
});

// 使用useHead钩子管理头部元数据
useHead({
  title: "关于我们 ",
  meta: [
    { charset: "UTF-8" },
    { name: "viewport", content: "width=device-width, initial-scale=1.0" }
  ],
  link: [
    {
      rel: "stylesheet",
      href: `${baseUrl}/vendor/bootstrap/bootstrap.min.css`
    },
    {
      rel: "stylesheet",
      href:
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
    }
  ]
});

// 状态变量
const activeSection = ref("company-intro");
const activeDept = ref(null);
const deptDescription = ref("");

// 部门数据
const departments = ref([
  {
    id: "dept1",
    name: "技术研发部",
    desc: "负责公司产品的技术研发、创新和升级，确保技术先进性和可持续发展。"
  },
  {
    id: "dept2",
    name: "产品设计部",
    desc:
      "负责产品的用户体验设计、界面设计和功能规划，提升产品的用户体验和市场竞争力。"
  },
  {
    id: "dept3",
    name: "运营管理部",
    desc:
      "负责公司的日常运营、市场推广和客户服务，确保业务的持续增长和客户满意度。"
  },
  {
    id: "dept4",
    name: "人力资源部",
    desc:
      "负责公司的人才招聘、培训、绩效管理和企业文化建设，打造高效的人才队伍。"
  },
  {
    id: "dept5",
    name: "财务部",
    desc:
      "负责公司的财务规划、预算管理、成本控制和财务分析，确保公司财务健康发展。"
  },
  {
    id: "dept6",
    name: "战略发展部",
    desc: "负责公司的长期发展规划、市场调研和战略合作，引领公司未来发展方向。"
  },
  {
    id: "dept7",
    name: "品牌管理部",
    desc:
      "负责公司的品牌建设、形象维护和媒体关系，提升公司的品牌价值和社会影响力。"
  },
  {
    id: "dept8",
    name: "法务部",
    desc: "负责公司的法律事务、合同管理和知识产权保护，降低公司的法律风险。"
  }
]);

// 原始部门数据
const departmentsOriginal = ref([
  { id: 'dept1', name: '财务管理中心', desc: '公司战略管理规划、负责部署资金运作计划、财务规则制定与执行、成本管理及分析控制管理、资产运营管理' },
  { id: 'dept2', name: '投资者关系中心', desc: '负责公司投资者关系管理、资本市场战略规划、投资者沟通、信息披露、股权管理等工作' },
  { id: 'dept3', name: '人力资源中心', desc: '负责公司人力资源战略规划、人才招聘、培训发展、绩效管理、薪酬福利、企业文化建设等工作' },
  { id: 'dept4', name: '技术研发部门', desc: '负责公司技术研发战略规划、产品开发、技术创新、研发管理、知识产权保护等工作' },
  { id: 'dept5', name: '项目管理中心', desc: '负责公司项目管理、进度控制、质量管理、风险管理、项目交付等工作' },
  { id: 'dept6', name: '电商事业部', desc: '负责公司电商平台运营、商品管理、客户服务、营销推广、数据分析等工作' },
  { id: 'dept7', name: '销售事业部', desc: '负责公司销售战略规划、客户开发、销售管理、渠道建设、市场拓展等工作' },
  { id: 'dept8', name: '合作开发部', desc: '负责公司合作伙伴管理、业务拓展、战略合作、资源整合等工作' },
  { id: 'dept9', name: '市场部', desc: '负责公司市场战略规划、品牌建设、营销策划、市场调研、媒体关系等工作' }
]);

// 管理团队数据
const teamMembers = ref([
  { name: "阮银春", position: "总经理", image: "p1.png" },
  { name: "黄锐钊", position: "营销副总", image: "p2.png" },
  { name: "黄玲", position: "常务副总", image: "p3.png" },
  { name: "章瑶瑶", position: "人力资源总监", image: "p4.png" },
  { name: "蔡红丽", position: "总助兼财务中心总监", image: "p5.png" },
  { name: "沈均", position: "技术总监", image: "p6.png" },
  { name: "黄博锋", position: "合作事业部经理", image: "p7.png" },
  { name: "于淼", position: "电商总监", image: "p8.png" },
  { name: "崔玉", position: "供应链总监", image: "p9.png" },
  { name: "方瑶琴", position: "区域总监", image: "p10.png" },
  { name: "洪小影", position: "区域总监", image: "p11.png" },
  { name: "吴镇标", position: "区域总监", image: "p12.png" }
]);
//企业信息
const enterpriseInforList= ref([]);
const getEnterpriseInformations = () =>{
    officialApi.getEnterpriseInformations({page:1,pageSize:3,cate_name:'公司简介'}).then(res => {
        if(res.code == 200){
            enterpriseInforList.value = res.data.items;
        }
    })
}

// 获取供应链数据
const basisData = ref({});
const getData = () =>{
    officialApi.basisManagement.getData().then(res => {
        if(res.code == 200){
            basisData.value = res.data;
        }
    })
}


// 页面加载完成后执行的逻辑
onMounted(() => {
  getEnterpriseInformations()
  getData()
  // 加载CSS
  function loadCSS(href, id) {
    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, "style-css");
  loadCSS(`${baseUrl}/css/profile.css`, "profile-css");

  // 定义加载脚本的函数
  function loadScript(src, id) {
    return new Promise((resolve, reject) => {
      const script = document.createElement("script");
      script.src = src;
      script.id = id;
      script.onload = resolve;
      script.onerror = reject;
      document.body.appendChild(script);
    });
  }

  // 加载Bootstrap和其他必要的脚本
  setTimeout(() => {
    try {
      // 加载Bootstrap
      loadScript(
        `${baseUrl}/vendor/bootstrap/bootstrap.bundle.min.js`,
        "bootstrap-script"
      )
        .then(() => {
          // 初始化滚动监听
          initScrollSpy();

          // 默认选中第一个部门
          if (departmentsOriginal.value.length > 0) {
            activeDept.value = departmentsOriginal.value[0].id;
            deptDescription.value = departmentsOriginal.value[0].desc;
          }

          console.log("页面功能初始化完成");
        })
        .catch(error => {
          console.error("Bootstrap 加载失败:", error);
        });
    } catch (error) {
      console.error("初始化页面功能失败:", error);
    }
  }, 500);
});

// 清理资源
onBeforeUnmount(() => {
  // 移除滚动监听
  window.removeEventListener("scroll", handleScroll);
});

// 初始化滚动监听
function initScrollSpy() {
  window.addEventListener("scroll", handleScroll);
  // 初始调用一次确保初始状态正确
  handleScroll();
}

// 处理滚动事件
function handleScroll() {
  const sections = document.querySelectorAll(".profile-section");
  const navbarHeight = 56; // 导航栏高度

  let current = "";

  sections.forEach(section => {
    const sectionTop = section.offsetTop - navbarHeight - 100;
    const sectionHeight = section.offsetHeight;

    if (
      window.scrollY >= sectionTop &&
      window.scrollY < sectionTop + sectionHeight
    ) {
      current = section.getAttribute("id");
    }
  });

  if (current && current !== activeSection.value) {
    activeSection.value = current;
  }
}

// 切换部门显示
function selectDepartment(deptId) {
  activeDept.value = deptId;
  const dept = departmentsOriginal.value.find(d => d.id === deptId);
  if (dept) {
    deptDescription.value = dept.desc;
  }
}

// 滚动到指定区域
function scrollToSection(sectionId) {
  const section = document.getElementById(sectionId);
  if (section) {
    const navbarHeight = 56;
    const targetPosition = section.offsetTop - navbarHeight - 20;
    window.scrollTo({
      top: targetPosition,
      behavior: "smooth"
    });
  }
}
</script>

<style scoped lang="less">
.section-titles, .section-title {
  font-size: 32px !important;
  font-weight: bold;
}
</style>