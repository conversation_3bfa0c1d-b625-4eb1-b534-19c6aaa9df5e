<template>
  <div class="website-container">
    <!-- 使用导航栏组件 -->
    <NavBar :base-url="baseUrl" />

    <!-- 面包屑导航 -->
    <div class="breadcrumb-container">
      <div class="container">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="/website/one/index">首页</a>
            </li>
            <li class="breadcrumb-item">
              <a href="/website/one/index">供应链解决方案
              </a>
            </li>
            <li class="breadcrumb-item active">{{ newsDetail.title }}</li>
          </ol>
        </nav>
      </div>
    </div>

    <!-- 新闻内容 -->
    <div class="news-detail-container">
      <div class="container">
        <div class="news-detail-content">
          <h1 class="news-title">{{ newsDetail.title }}</h1>
          <div class="news-meta"></div>
          <div class="news-text">
            <p v-html="newsDetail.content"></p>
            <div class="news-image" v-if="newsDetail.cover_image">
              <a-image
                :preview="false"
                :src="newsDetail.cover_image"
                :alt="newsDetail.title"
                class="img-fluid"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加页脚组件 -->
    <Footer :base-url="baseUrl" />
  </div>
</template>
    
<script setup>
import officialApi from '@/api/master/officialWebsiteModule';
import { ref, onMounted, computed } from "vue";
import { useRoute } from "vue-router";
import NavBar from "@/components/website/one/NavBar.vue";
import Footer from "@/components/website/one/Footer.vue";

// 定义页面元信息
definePageMeta({
  layout: false,
  path: '/website/one/plans/:id'
});

// 获取路由参数
const route = useRoute();
const newsId = computed(() => route.params.id);

// 基础URL
const baseUrl = "/website/one/assets";

const newsDetail = ref({});
// 在实际应用中，这里应该根据ID从API获取新闻详情
const fetchNewsDetail = async id => {
  console.log("获取新闻ID:", id);
  officialApi.caseManagement.detail(id).then(res => {
    if(res.code == 200){
       newsDetail.value = res.data;
    }
  })
};

// 定义获取资源URL的函数
const getAssetUrl = (type, path) => {
  return `${baseUrl}/${type}/${path}`;
};

// 定义获取图片URL的函数
const getImageUrl = path => {
  return getAssetUrl("images", path);
};

// 页面加载完成后执行的逻辑
onMounted(() => {
  // 获取新闻详情
  fetchNewsDetail(newsId.value);

  // 加载CSS
  function loadCSS(href, id) {
    if (document.getElementById(id)) return;

    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }

  // 先加载样式文件
  loadCSS(`${baseUrl}/css/style.css`, "style-css");
  loadCSS(`${baseUrl}/css/newsdetail.css`, "newsdetail-css");
  loadCSS(`${baseUrl}/vendor/bootstrap/bootstrap.min.css`, "bootstrap-css");

  // 定义加载脚本的函数
  function loadScript(src, id) {
    return new Promise((resolve, reject) => {
      if (document.getElementById(id)) {
        resolve();
        return;
      }

      const script = document.createElement("script");
      script.src = src;
      script.id = id;
      script.onload = resolve;
      script.onerror = reject;
      document.body.appendChild(script);
    });
  }

  // 加载Bootstrap和其他必要的脚本
  setTimeout(() => {
    try {
      // 加载Bootstrap
      loadScript(
        `${baseUrl}/vendor/bootstrap/bootstrap.bundle.min.js`,
        "bootstrap-script"
      )
        .then(() => {
          console.log("页面功能初始化完成");
        })
        .catch(error => {
          console.error("Bootstrap 加载失败:", error);
        });
    } catch (error) {
      console.error("脚本加载错误:", error);
    }
  }, 100);
});
</script>
    
    <style scoped>
/* 页面样式会从外部CSS文件加载 */
.website-container {
  width: 100%;
  overflow-x: hidden;
}

/* 新闻详情样式 */
.news-detail-container {
  padding: 40px 0;
  background-color: #f5f7fa;
}

.news-detail-content {
  background-color: #fff;
  padding: 30px;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.news-title {
  font-size: 34px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
}

.news-meta {
  margin-bottom: 20px;
  color: #666;
  font-size: 14px;
}

.news-text p {
  margin-bottom: 15px;
  line-height: 1.8;
  color: #333;
}

.news-image {
  margin: 20px 0;
  text-align: center;
}

.news-image img {
  max-width: 100%;
  border-radius: 5px;
}

/* 面包屑导航样式 */
.breadcrumb-container {
  background-color: #f5f7fa;
  padding: 15px 0;
  border-bottom: 1px solid #e1e5eb;
}

.breadcrumb {
  margin-bottom: 0;
  background-color: transparent;
  padding: 0;
}

.breadcrumb-item a {
  color: #666 !important;
  text-decoration: none;
}

.breadcrumb-item.active {
  color: #666 !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .news-detail-content {
    padding: 20px;
  }

  .news-title {
    font-size: 20px;
  }
}
</style>