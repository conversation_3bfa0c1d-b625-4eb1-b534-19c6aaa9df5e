<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<script setup>
import { reactive, ref } from "vue";
import { useUserStore } from "~/store/index.js";
import { useTagStore } from "~/store/index.js";
import commonApi from "~/api/common.js";
import authApi from "~/api/master/auth.js";
import providerAuthApi from "@/api/provider/auth";
import { Message } from '@arco-design/web-vue'
import providerMenuMock from '@/mock/provider'
definePageMeta({
  layout: false,
  name: "providerlogin",
  path: "/provider/login",
  
});

// request({
//   url: "system/getBingBackgroundImage",
//   timeout: 10000,
//   method: "get",
// }).then((res) => {
//   document.getElementById(
//     "background"
//   ).style.backgroundImage = `url(${res.data.url})`;
// });

const router = useRouter();
const route = useRoute();
const Verify = ref(null);

const loading = ref(false);

// 移除环境判断，统一使用开发环境逻辑
// let isDevelop = import.meta.env.VITE_APP_ENV === "development";

// 直接使用开发环境的数据
const form = reactive({ 
  username: "admin", 
  password: "admin123", 
  code: "",
  captcha: "",
  captchaId: ""
});

// 验证码相关
const captchaImg = ref('');

// 获取验证码
const getCaptcha = async () => {
  try {
    const res = await providerAuthApi.getCaptcha();
    // 检查响应结构
    if (res.code === 200) {
        // 兼容SVG字符串，转为data URI防止URI malformed
        captchaImg.value = 'data:image/svg+xml;utf8,' + encodeURIComponent(res.data.image);
        form.captchaId = res.data.id;
      } else {
        console.error('验证码数据结构不符合预期:', res.data);
      }
  } catch (error) {
    console.error('获取验证码出错:', error);
  }
};

const userStore = useUserStore();

const redirect = route.query.redirect;

// 页面加载时自动获取验证码
// 登录方式
const loginType = ref('account');

// 手机号和验证码
const phoneForm = reactive({
  phone: '',
  code: ''
});

// 发送手机验证码
const smsSending = ref(false);
const countdown = ref(0);
const timer = ref(null);

const sendSmsCode = async () => {
  if (smsSending.value) return;
  
  // 验证手机号
  if (!phoneForm.phone || !/^1[3-9]\d{9}$/.test(phoneForm.phone)) {
    Message.error('请输入正确的手机号');
    return;
  }
  
  smsSending.value = true;
  
  try {
    // 调用发送短信验证码的API
    const params = {
      phoneNumber: phoneForm.phone,
      type: 'login' // 登录类型
    };
    
    const res = await providerAuthApi.sendSmsCode(params);
    
    if (res.code === 200) {
      countdown.value = 60;
      Message.success('验证码已发送');
      
      // 倒计时
      timer.value = setInterval(() => {
        countdown.value--;
        if (countdown.value <= 0) {
          clearInterval(timer.value);
          smsSending.value = false;
        }
      }, 1000);
    } else {
      Message.error(res.message || '发送短信验证码失败');
      smsSending.value = false;
    }
  } catch (error) {
    console.error('发送短信验证码失败:', error);
    Message.error(error.message || '发送短信验证码失败');
    smsSending.value = false;
  }
};

// 切换登录方式时重置状态
const handleLoginTypeChange = (key) => {
  loginType.value = key;
  getCaptcha();
};

// 跳转到服务商注册页面
const goToRegister = () => {
  router.push("/provider/register");
  navigateTo('/provider/register');
};

onMounted(() => {
  getCaptcha();
});

onBeforeUnmount(() => {
  // 清除定时器
  if (timer.value) {
    clearInterval(timer.value);
  }
});

const handleSubmit = async ({ values, errors }) => {
  if (loading.value) {
    return;
  }
  loading.value = true;
  
  try {
    if (loginType.value === 'account') {
      // 账号密码登录
      // 验证码校验
      if (!form.captcha) {
        Message.error('请输入验证码');
        loading.value = false;
        return;
      }
      
      // 使用真实登录接口
      if (!errors) {
        try {
          // 调用服务商登录接口获取真实token
          const loginResult = await providerAuthApi.login({
            username: form.username,
            password: form.password,
            captcha: form.captcha,
            captchaId: form.captchaId
          });
          
          if (!loginResult || loginResult.code !== 200 || !loginResult.data || !loginResult.data.token) {
            throw new Error(loginResult.message || '登录接口返回数据格式不正确');
          }
          
          console.log('服务商登录成功，获取到真实token');
          
          // 使用真实token和菜单数据
          const realToken = loginResult.data.token;
          const menuTree = providerMenuMock.routers; // 仍使用模拟的菜单数据
          const userInfo = loginResult.data.user;
          
          // 设置token过期时间（默认1天）
          const expiresTime = new Date().getTime() + 24 * 60 * 60 * 1000;
         
          // 存储数据
          const tool = await import('@/utils/tool');
          tool.default.local.set('token_provider', realToken);
          tool.default.local.set('token_provider_expires', expiresTime);
          tool.default.local.set('menu_provider', menuTree);
          tool.default.local.set('user_provider', userInfo);
          
          // 清除标签
          useTagStore().clearTags();
          
          Message.success('服务商登录成功');
          
          // 根据用户状态进行跳转
          let targetPath = '/provider/workbench';
          // 从本地存储读取用户信息，避免变量作用域问题
          const userInfoFromStorage = JSON.parse(localStorage.getItem('user_provider') || '{}');
          
          // 根据用户状态决定跳转路径
          if (userInfoFromStorage.status === 0) {
            // 未审核状态，跳转到入驻页面
            targetPath = '/provider/register';
          } else if (userInfoFromStorage.status === 1) {
            // 审核通过状态，跳转到工作台
            targetPath = '/provider/workbench';
          } else if (userInfoFromStorage.status === 2) {
            // 审核拒绝状态，跳转到入驻页面
            targetPath = '/provider/register';
          } else {
            // 默认跳转到工作台
            targetPath = '/provider/workbench';
          }
          
          // 跳转到目标页面
          try {
            if (redirect && redirect !== "/") {
              await router.push(redirect);
            } else {
              await router.push(targetPath);
            }
          } catch (error) {
            console.error("导航错误:", error);
            // 如果导航失败，尝试使用navigateTo
            await navigateTo(targetPath);
          }
        } catch (error) {
          console.error('登录失败:', error);
          Message.error(error.message || '登录失败，请稍后重试');
          loading.value = false;
          getCaptcha();
          return;
        }
      }
    } else {
      // 手机验证码登录
      if (!phoneForm.phone || !/^1[3-9]\d{9}$/.test(phoneForm.phone)) {
        Message.error('请输入正确的手机号');
        loading.value = false;
        return;
      }
      
      if (!phoneForm.code) {
        Message.error('请输入短信验证码');
        loading.value = false;
        return;
      }
      
      try {
        // 调用手机登录API
        const params = {
          phone: phoneForm.phone,
          code: phoneForm.code,
          type: 'login'
        };
        
        const response = await providerAuthApi.phoneLogin(params);
        console.log('手机登录响应:', response);
        
        if (response.code === 200) {
          // 登录成功，处理token等信息
          const realToken = response.data.token;
          const menuTree = providerMenuMock.routers; // 仍使用模拟的菜单数据
          const userInfo = response.data.user;
          
          // 设置token过期时间（默认1天）
          const expiresTime = new Date().getTime() + 24 * 60 * 60 * 1000;
          
          // 存储数据
          const tool = await import('@/utils/tool');
          tool.default.local.set('token_provider', realToken);
          tool.default.local.set('token_provider_expires', expiresTime);
          tool.default.local.set('menu_provider', menuTree);
          tool.default.local.set('user_provider', userInfo);
          
          // 清除标签页数据
          useTagStore().clearTags();
          
          Message.success('服务商登录成功');
          
          // 根据用户状态进行跳转
          let targetPath = '/provider/dashboard';
          // 从本地存储读取用户信息，避免变量作用域问题
          const userInfoFromStorage = JSON.parse(localStorage.getItem('user_provider') || '{}');
          
          // 根据用户状态决定跳转路径
          if (userInfoFromStorage.status === 0) {
            // 未审核状态，跳转到入驻页面
            targetPath = '/provider/register';
          } else if (userInfoFromStorage.status === 1) {
            // 审核通过状态，跳转到仪表盘
            targetPath = '/provider/dashboard';
          } else if (userInfoFromStorage.status === 2) {
            // 审核拒绝状态，跳转到入驻页面
            targetPath = '/provider/register';
          } else {
            // 默认跳转到仪表盘
            targetPath = '/provider/dashboard';
          }
          
          // 跳转到目标页面
          try {
            if (redirect && redirect !== "/") {
              await router.push(redirect);
            } else {
              await router.push(targetPath);
            }
          } catch (error) {
            console.error("导航错误:", error);
            await navigateTo(targetPath);
          }
        } else {
          // 登录失败
          Message.error(response.message || '手机号登录失败');
          // 刷新验证码
          getCaptcha();
        }
      } catch (error) {
        console.error('手机登录失败:', error);
        Message.error(error.message || '手机号登录失败，请稍后重试');
        // 刷新验证码
        getCaptcha();
      }
      
      loading.value = false;
      return;
    }
  } catch (error) {
    console.error("登录处理错误:", error);
    Message.error("登录失败，请稍后重试");
    // 登录失败时刷新验证码
    getCaptcha();
  } finally {
    loading.value = false;
  }
};
</script>
<template>
  <div id="background" class="fixed"></div>
  <div class="bg-backdrop-layout"></div>
  <div class="login-container">
    <div
      class="login-width md:w-10/12 w-11/12 mx-auto flex justify-between h-full items-center"
    >
      <div
        class="w-6/12 mx-auto left-panel rounded-l pl-5 pr-5 hidden md:block"
      >
        <div class="logo">
          <img  src="/assets/logo.svg"  width="30" /><span>{{ $title }}</span>
        </div>
        <div class="slogan flex justify-end">
          <span>---- {{ $t("sys.login.slogan") }}</span>
        </div>
      </div>

      <div class="md:w-6/12 w-11/12 md:rounded-r mx-auto pl-5 pr-5 pb-10">
        <!-- <h2 class="mt-10 text-3xl pb-0 mb-5">{{ $t("sys.login.title") }}</h2> -->
        
        <!-- 登录方式切换 -->
        <a-tabs :active-key="loginType" @update:active-key="(val) => loginType = val" @change="handleLoginTypeChange" class="mb-5">
          <a-tab-pane key="account" title="账号密码登录"></a-tab-pane>
          <a-tab-pane key="phone" title="手机号登录"></a-tab-pane>
        </a-tabs>
        
        <a-form :model="loginType === 'account' ? form : phoneForm" @submit="handleSubmit">
          <!-- 账号密码登录表单 -->
          <template v-if="loginType === 'account'">
            <a-form-item field="username" :hide-label="true">
              <a-input
                v-model="form.username"
                class="w-full"
                size="large"
                allow-clear
                placeholder="请输入用户名"
              >
                <template #prefix><icon-user /></template>
              </a-input>
            </a-form-item>

            <a-form-item field="password" :hide-label="true">
              <a-input-password v-model="form.password" size="large" allow-clear placeholder="请输入密码">
                <template #prefix><icon-lock /></template>
              </a-input-password>
            </a-form-item>

            <!-- 验证码输入框 -->
            <a-form-item field="captcha" :hide-label="true">
              <div class="flex items-center">
                <a-input v-model="form.captcha" size="large" allow-clear placeholder="请输入验证码" class="mr-2">
                  <template #prefix><icon-safe /></template>
                </a-input>
                <div class="captcha-img cursor-pointer" @click="getCaptcha" v-if="captchaImg">
                  <img :src="captchaImg" alt="验证码" class="captchaImg" />
                </div>
              </div>
            </a-form-item>
          </template>
          
          <!-- 手机号登录表单 -->
          <template v-else>
            <a-form-item field="phone" :hide-label="true">
              <a-input
                v-model="phoneForm.phone"
                class="w-full"
                size="large"
                allow-clear
                placeholder="请输入手机号"
                maxlength="11"
              >
                <template #prefix><icon-phone /></template>
              </a-input>
            </a-form-item>
            <!-- 短信验证码 -->
            <a-form-item field="captcha" :hide-label="true">
              <div class="flex" style="align-items:center;width:100%">
                <a-input
                  v-model="phoneForm.code"
                  size="large"
                  allow-clear
                  placeholder="请输入短信验证码"
                  class="flex-1"
                >
                  <template #prefix><icon-message /></template>
                </a-input>
                <a-button
                  type="primary"
                  :disabled="smsSending || countdown > 0"
                  @click="sendSmsCode"
                  class="ml-3"
                  style="min-width:110px;white-space:nowrap;"
                >
                  {{ countdown > 0 ? `${countdown}秒后重发` : '获取验证码' }}
                </a-button>
              </div>
            </a-form-item>
          </template>

          <a-form-item :hide-label="true" class="mt-5">
            <a-button
              html-type="submit"
              type="primary"
              long
              size="large"
              :loading="loading"
            >
             登录服务商端
            </a-button>
          </a-form-item>
          <a-form-item :hide-label="true" >
            <a-button
              type="primary"
              long
              status="success"
              @click="goToRegister"
            >
             服务商入驻
            </a-button>
          </a-form-item>
          <a-divider orientation="center">{{
            $t("sys.login.otherLoginType")
          }}</a-divider>
          <div class="flex w-3/4 pt-2 mx-auto items-stretch justify-around">
            <a-avatar class="other-login wechat"><icon-wechat /></a-avatar>
            <a-avatar class="other-login alipay"
              ><icon-alipay-circle
            /></a-avatar>
            <a-avatar class="other-login qq"><icon-qq /></a-avatar>
            <a-avatar class="other-login weibo"><icon-weibo /></a-avatar>
          </div>
        </a-form>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.captchaImg {
  width: 215px;
  height: 35px;
}
#background {
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
}
.bg-backdrop-layout {
  top: 0;
  left: 0;
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 2;
  backdrop-filter: blur(25px);
}
.login-container {
  width: 100%;
  height: 100%;
  position: absolute;
  background-size: cover;
  z-index: 3;
  .login-width {
    max-width: 950px;
    background: #fff;
    padding: 10px;
    height: 500px;
    position: relative;
    top: 50%;
    margin-top: -255px;
    border-radius: 10px;
  }

  .left-panel {
    height: 491px;
    background-image: url(/assets/login_picture.svg);
    background-repeat: no-repeat;
    background-position: center 60px;
    background-size: contain;
  }

  .logo {
    display: flex;
    margin-top: 20px;
    color: #333;
    span {
      font-size: 28px;
      margin-left: 15px;
      color: rgb(var(--primary-6));
    }
  }
  .slogan {
    font-size: 16px;
    line-height: 50px;
  }

  :deep(.arco-input-append) {
    padding: 0 !important;
  }

  .other-login {
    cursor: pointer;
  }

  .qq:hover,
  .alipay:hover {
    background: #165dff;
  }
  .wechat:hover {
    background: #0f9c02;
  }

  .weibo:hover {
    background: #f3ce2b;
  }
}
</style>
