<template>
  <div>
    <div class="brand-list-container">
      <!-- 品牌详情抽屉 -->
      <BrandDetailDrawer
        v-model:visible="drawerVisible"
        :brand-data="currentBrand"
        @close="handleDrawerClose"
      />
      <!-- 顶部标签页 -->
      <!-- <div class="brand-tabs">
        <div 
          class="tab-item" 
          :class="{ active: currentTab === 'all' }" 
          @click="switchTab('all')"
        >全部品牌</div>
        <div 
          class="tab-item" 
          :class="{ active: currentTab === 'mine' }" 
          @click="switchTab('mine')"
        >我的品牌</div>
      </div> -->
      <div class="search-area">
        <!-- 状态标签切换 -->
        <div class="status-tabs-container">
          <a-tabs type="line" :active-key="activeStatsCard" @change="selectStatsCard">
            <a-tab-pane key="all" title="全部品牌">
              <template #title>
                <span class="status-tab-title">全部品牌</span>
                <span class="status-tab-count">{{ statsCards.find(card => card.id === 'all')?.value }}</span>
              </template>
            </a-tab-pane>
            <a-tab-pane key="approved" title="审核通过">
              <template #title>
                <span class="status-tab-title">审核通过</span>
                <span class="status-tab-count">{{ statsCards.find(card => card.id === 'approved')?.value }}</span>
              </template>
            </a-tab-pane>
            <a-tab-pane key="pending" title="待审核">
              <template #title>
                <span class="status-tab-title">待审核</span>
                <span class="status-tab-count">{{ statsCards.find(card => card.id === 'pending')?.value }}</span>
              </template>
            </a-tab-pane>
            <a-tab-pane key="rejected" title="审核不通过">
              <template #title>
                <span class="status-tab-title">审核不通过</span>
                <span class="status-tab-count">{{ statsCards.find(card => card.id === 'rejected')?.value }}</span>
              </template>
            </a-tab-pane>
          </a-tabs>
        </div>

        <!-- 搜索表单 -->
        <ma-crud
          ref="crudRef"
          :options="options"
          :columns="columns"
          :search="search"
          v-model:search-params="searchParams"
          :data="brandData"
          @search="handleSearch"
          @page-change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        >
          <!-- 自定义左侧按钮 -->
          <template #toolbar-left>
            <a-button type="primary" @click="handleAdd">
              <template #icon>
                <icon-plus />
              </template>
              新增品牌
            </a-button>
          </template>

          <!-- 操作列 -->
          <template #operation="{ record }">
            <a-space>
              <a-link @click="handleView(record)">查看详情</a-link>
            </a-space>
          </template>

          <!-- 商标状态列 -->
          <template #trademarkStatus="{ record }">
            <a-tag :color="getTrademarkStatusColor(record.trademarkStatus)">
              {{ getTrademarkStatusText(record.trademarkStatus) }}
            </a-tag>
          </template>

          <!-- 经营状态列 -->
          <template #businessStatus="{ record }">
            <a-tag :color="getBusinessStatusColor(record.businessStatus)">
              {{ getBusinessStatusText(record.businessStatus) }}
            </a-tag>
          </template>

          <!-- 商标Logo列 -->
          <template #logo="{ record }">
            <a-image :src="record.logo" width="40" height="40" fit="cover" />
          </template>
        </ma-crud>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import { useRouter } from "#app";
import BrandDetailDrawer from "./components/BrandDetailDrawer.vue";

// 定义页面路由元信息
definePageMeta({
  title: "自主品牌管理",
  icon: "ma-icon-brand",
  name: "provider-brandManage",
  path: "/provider/goods/brandManage",
});

const crudRef = ref();
const router = useRouter();
const currentTab = ref("all"); // 默认选择全部品牌
const brandData = ref([]); // 品牌数据
const allBrandData = ref([]); // 所有品牌数据（未分页）
const currentPage = ref(1); // 当前页码
const pageSize = ref(10); // 每页显示数量

// 抽屉相关
const drawerVisible = ref(false);
const currentBrand = ref({});

// 统计卡片数据
const statsCards = reactive([
  { id: "all", title: "全部品牌", value: "0" },
  { id: "approved", title: "审核通过", value: "0" },
  { id: "pending", title: "待审核", value: "0" },
  { id: "rejected", title: "审核不通过", value: "0" },
]);

// 当前选中的状态标签
const activeStatsCard = ref("all"); // 当前选中的状态标签

// 选择状态标签
const selectStatsCard = (id) => {
  activeStatsCard.value = id;
  
  // 根据选中的标签设置审核状态过滤条件
  searchParams.value.auditStatus = "";
  if (id === "approved") {
    searchParams.value.auditStatus = "approved";
  } else if (id === "pending") {
    searchParams.value.auditStatus = "pending";
  } else if (id === "rejected") {
    searchParams.value.auditStatus = "rejected";
  }
  
  // 重置分页
  currentPage.value = 1;
  
  // 刷新表格数据
  filterBrandData();
};

// 切换标签页
const switchTab = (tab) => {
  currentTab.value = tab;
  activeStatsCard.value = "all";
  searchParams.value.auditStatus = "";
  // 重置分页
  currentPage.value = 1;
  filterBrandData();
};

// 表格列配置
const columns = reactive([
  {
    title: "商标名称",
    dataIndex: "name",
    align: "center",
    width: 150,
    search: true,
    searchField: "name",
    placeholder: "请输入商标名称",
  },
  {
    title: "商标Logo",
    dataIndex: "logo",
    align: "center",
    width: 100,
    slotName: "logo",
  },
  // {
  //   title: "品牌代码",
  //   dataIndex: "code",
  //   align: "center",
  //   width: 120,
  // },
  {
    title: "商标号",
    dataIndex: "trademarkNo",
    align: "center",
    width: 150,
    search: true,
    searchField: "trademarkNo",
    placeholder: "请输入商标号",
  },
  // {
  //   title: "品牌所属地",
  //   dataIndex: "location",
  //   align: "center",
  //   width: 120,
  // },
  {
    title: "商标注册类别",
    dataIndex: "registrationCategory",
    align: "center",
    width: 150,
    search: true,
    searchField: "registrationCategory",
    placeholder: "请输入商标注册类别",
  },
  
  {
    title: "自主品牌",
    dataIndex: "registrationCategory",
    align: "center",
    width: 150,
    search: true,
    searchField: "registrationCategory",
    placeholder: "请输入自主品牌",
  },
  {
    title: "品牌授权",
    dataIndex: "registrationCategory",
    align: "center",
    width: 150,
    search: true,
    searchField: "registrationCategory",
    placeholder: "请输入品牌授权",
  },
  
    
  // {
  //   title: "商标小类",
  //   dataIndex: "registrationSubCategory",
  //   align: "center",
  //   width: 150,
  //   search: true,
  //   searchField: "registrationSubCategory",
  //   placeholder: "请输入商标小类",
  // },
  {
    title: "注册日期",
    dataIndex: "registrationDate",
    align: "center",
    width: 120,
  },
  {
    title: "有效期",
    dataIndex: "validityPeriod",
    align: "center",
    width: 120,
  },
  {
    title: "持有人",
    dataIndex: "owner",
    align: "center",
    width: 150,
    search: true,
    searchField: "owner",
    placeholder: "请输入持有人",
  },
  {
    title: "商标状态",
    dataIndex: "trademarkStatus",
    align: "center",
    width: 120,
    slotName: "trademarkStatus",
  },
  {
    title: "经营状态",
    dataIndex: "businessStatus",
    align: "center",
    width: 120,
    slotName: "businessStatus",
  },
  {
    title: "操作",
    dataIndex: "operation",
    align: "center",
    width: 120,
    fixed: "right",
    slotName: "operation",
  },
]);

// 搜索表单配置
const search = reactive({
  labelWidth: 80,
  formProps: {
    layout: "inline",
  },
  schemas: [
    {
      field: "name",
      label: "品牌名称",
      component: "Input",
      componentProps: {
        placeholder: "请输入品牌名称",
      },
    },
    {
      field: "registrationCategory",
      label: "商标注册类别",
      component: "Select",
      componentProps: {
        placeholder: "请选择商标注册类别",
        options: [
          { label: "第1类", value: "1" },
          { label: "第2类", value: "2" },
          { label: "第3类", value: "3" },
          { label: "第4类", value: "4" },
          { label: "第5类", value: "5" },
        ],
      },
    },
    {
      field: "registrationSubCategory",
      label: "商标小类",
      component: "Select",
      componentProps: {
        placeholder: "请选择商标小类",
        options: [
          { label: "小类A", value: "A" },
          { label: "小类B", value: "B" },
          { label: "小类C", value: "C" },
        ],
      },
    },
    {
      field: "trademarkNo",
      label: "商标号",
      component: "Input",
      componentProps: {
        placeholder: "请输入商标号",
      },
    },
    {
      field: "owner",
      label: "持有人",
      component: "Input",
      componentProps: {
        placeholder: "请输入持有人",
      },
    },
  ],
});

// 搜索参数
const searchParams = ref({
  name: "",
  registrationCategory: "",
  registrationSubCategory: "",
  trademarkNo: "",
  owner: "",
  auditStatus: "",
});

// 表格配置
const options = reactive({
  id: "id",
  border: true,
  stripe: true,
  pageLayout: "total, sizes, prev, pager, next, jumper",
  exportable: false,
  pagination: {
    currentPage: 1,
    pageSize: 10,
    pageSizes: [10, 20, 50, 100],
    background: true,
    total: 0
  }
});

// 页码变更处理
const handlePageChange = (page) => {
  currentPage.value = page;
  updatePageData();
};

// 每页数量变更处理
const handlePageSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1; // 重置为第一页
  updatePageData();
};

// 更新分页数据
const updatePageData = () => {
  if (!allBrandData.value.length) return;
  
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  brandData.value = allBrandData.value.slice(start, end);
  
  // 更新分页信息
  options.pagination.currentPage = currentPage.value;
  options.pagination.pageSize = pageSize.value;
  options.pagination.total = allBrandData.value.length;
};

// 获取商标状态文本
const getTrademarkStatusText = (status) => {
  const statusMap = {
    valid: "有效",
    expired: "已过期",
    pending: "审核中",
    rejected: "已驳回",
  };
  return statusMap[status] || "未知状态";
};

// 获取商标状态颜色
const getTrademarkStatusColor = (status) => {
  const colorMap = {
    valid: "green",
    expired: "red",
    pending: "orange",
    rejected: "gray",
  };
  return colorMap[status] || "blue";
};

// 获取经营状态文本
const getBusinessStatusText = (status) => {
  const statusMap = {
    active: "经营中",
    inactive: "已停用",
    suspended: "已暂停",
  };
  return statusMap[status] || "未知状态";
};

// 获取经营状态颜色
const getBusinessStatusColor = (status) => {
  const colorMap = {
    active: "green",
    inactive: "red",
    suspended: "orange",
  };
  return colorMap[status] || "blue";
};

// 查看品牌详情
const handleView = (record) => {
  currentBrand.value = record;
  drawerVisible.value = true;
};

// 关闭抽屉
const handleDrawerClose = () => {
  drawerVisible.value = false;
  currentBrand.value = {};
};

// 新增品牌
const handleAdd = () => {
  Message.info("新增品牌");
};

// 处理搜索
const handleSearch = () => {
  // 根据搜索条件过滤数据
  filterBrandData();
};

// 过滤品牌数据
const filterBrandData = () => {
  const allData = getBrandData();
  
  // 根据当前标签页过滤
  let filteredData = allData;
  if (currentTab.value === 'mine') {
    // 假设前20条是我的品牌
    filteredData = allData.slice(0, 20);
  }
  
  // 根据审核状态过滤
  if (searchParams.value.auditStatus) {
    const statusMap = {
      'approved': 'valid',
      'pending': 'pending',
      'rejected': 'rejected'
    };
    filteredData = filteredData.filter(item => item.trademarkStatus === statusMap[searchParams.value.auditStatus]);
  }
  
  // 根据搜索条件过滤
  if (searchParams.value.name) {
    filteredData = filteredData.filter(item => item.name.includes(searchParams.value.name));
  }
  
  if (searchParams.value.registrationCategory) {
    filteredData = filteredData.filter(item => item.registrationCategory === searchParams.value.registrationCategory);
  }
  
  if (searchParams.value.registrationSubCategory) {
    filteredData = filteredData.filter(item => item.registrationSubCategory === searchParams.value.registrationSubCategory);
  }
  
  if (searchParams.value.trademarkNo) {
    filteredData = filteredData.filter(item => item.trademarkNo.includes(searchParams.value.trademarkNo));
  }
  
  if (searchParams.value.owner) {
    filteredData = filteredData.filter(item => item.owner.includes(searchParams.value.owner));
  }
  
  // 重置为第一页
  currentPage.value = 1;
  
  // 保存过滤后的所有数据
  allBrandData.value = filteredData;
  
  // 更新分页数据
  updatePageData();
};

// 模拟获取品牌数据
const getBrandData = () => {
  // 模拟品牌数据
  const generateBrandItem = (index) => {
    const statuses = ["valid", "expired", "pending", "rejected"];
    const businessStatuses = ["active", "inactive", "suspended"];
    const categories = ["1", "2", "3", "4", "5"];
    const subCategories = ["A", "B", "C"];
    const brandNames = [
      "优品汇", "绿源", "科技星", "健康家", "美食坊", 
      "时尚派", "智能家", "环保源", "品质生活", "创新科技",
      "自然美", "舒适家", "乐活派", "品味生活", "科技未来"
    ];
    const locations = ["中国", "美国", "日本", "韩国", "德国", "法国", "英国", "意大利", "西班牙", "澳大利亚"];
    const companies = [
      "科技有限公司", "贸易有限公司", "电子科技有限公司", "食品有限公司", "服装有限公司",
      "家居有限公司", "健康科技有限公司", "环保科技有限公司", "数码科技有限公司", "生活用品有限公司"
    ];
    
    // 随机生成品牌名称
    const brandName = brandNames[index % brandNames.length] + (Math.floor(index / brandNames.length) > 0 ? Math.floor(index / brandNames.length) : "");
    // 随机生成公司名称
    const companyName = brandName + companies[index % companies.length];
    
    return {
      id: `brand-${index + 1}`,
      name: brandName,
      logo: `https://img12.360buyimg.com/imagetools/jfs/t1/158054/3/45410/37057/662b1030Fddb8470d/a20b6af2770d2632.png`,
      code: `BM${10000 + index}`,
      traceRule: `TR-${index % 3 + 1}`,
      trademarkNo: `TM${100000 + index}`,
      location: locations[index % locations.length],
      registrationCategory: categories[index % categories.length],
      registrationSubCategory: subCategories[index % subCategories.length],
      registrationDate: `2023-${(index % 12) + 1}-${(index % 28) + 1}`,
      validityPeriod: `${10 + (index % 10)}年`,
      owner: companyName,
      trademarkStatus: statuses[index % statuses.length],
      businessStatus: businessStatuses[index % businessStatuses.length],
    };
  };

  // 生成模拟数据
  const mockData = Array.from({ length: 100 }, (_, index) => generateBrandItem(index));
  
  // 更新统计卡片数据
  const approvedCount = mockData.filter(item => item.trademarkStatus === "valid").length;
  const pendingCount = mockData.filter(item => item.trademarkStatus === "pending").length;
  const rejectedCount = mockData.filter(item => item.trademarkStatus === "rejected").length;
  
  statsCards.find(card => card.id === "all").value = mockData.length.toString();
  statsCards.find(card => card.id === "approved").value = approvedCount.toString();
  statsCards.find(card => card.id === "pending").value = pendingCount.toString();
  statsCards.find(card => card.id === "rejected").value = rejectedCount.toString();
  
  return mockData;
};

// 页面加载时获取数据
onMounted(() => {
  // 初始化表格数据
  filterBrandData();
});
</script>

<style scoped lang="less">
.brand-list-container {
  padding: 16px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.brand-tabs {
  display: flex;
  margin-bottom: 16px;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tab-item {
  padding: 12px 24px;
  cursor: pointer;
  font-size: 14px;
  color: #4e5969;
  transition: all 0.3s;
  
  &:hover {
    color: #165dff;
    background-color: #f2f3f5;
  }
  
  &.active {
    color: #165dff;
    font-weight: 500;
    background-color: #e8f3ff;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: #165dff;
    }
  }
}

.search-area {
  background-color: #fff;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.status-tabs-container {
  margin-bottom: 20px;
}

.status-tab-title {
  margin-right: 8px;
}

.status-tab-count {
  display: inline-block;
  padding: 0 8px;
  height: 20px;
  line-height: 20px;
  background-color: #f2f3f5;
  border-radius: 10px;
  font-size: 12px;
  color: #4e5969;
}

.stats-scroll-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background-color: #fff;
  border-radius: 50%;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  z-index: 1;
  
  &:hover {
    background-color: #f2f3f5;
  }
  
  &.stats-scroll-left {
    margin-right: 8px;
  }
  
  &.stats-scroll-right {
    margin-left: 8px;
  }
}
:deep(.arco-form-item-label){
 white-space: nowrap; 
}
</style>
