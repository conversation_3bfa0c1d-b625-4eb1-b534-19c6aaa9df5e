<template>
  <a-modal
    :visible="visible"
    :width="700"
    :title="drawerTitle"
    @cancel="close"
    :footer="false"
    :mask-closable="false"
    unmountOnClose
    modal-class="brand-auth-modal"
  >
    <div class="brand-auth-form-container">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <!-- 授权公司 -->
          <a-col :span="12">
            <a-form-item field="authCompany" label="授权公司" required>
              <a-select
                v-model="formData.authCompany"
                placeholder="请选择授权公司"
                allow-clear
              >
                <a-option v-for="company in companyOptions" :key="company.value" :value="company.value">
                  {{ company.label }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
          
          <!-- 证书类型 -->
          <a-col :span="12">
            <a-form-item field="certificateType" label="证书类型">
              <a-input v-model="formData.certificateType" placeholder="证书类型" disabled />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <!-- 被授权单位 -->
          <a-col :span="12">
            <a-form-item field="authorizedUnit" label="被授权单位" required>
              <a-input v-model="formData.authorizedUnit" placeholder="请输入被授权单位" />
            </a-form-item>
          </a-col>
          
          <!-- 授权编号 -->
          <a-col :span="12">
            <a-form-item field="authCode" label="授权编号" required>
              <a-input v-model="formData.authCode" placeholder="请输入授权编号" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <!-- 授权品牌 -->
          <a-col :span="12">
            <a-form-item field="brandName" label="授权品牌" required>
              <a-select
                v-model="formData.brandName"
                placeholder="请选择授权品牌"
                allow-clear
              >
                <a-option v-for="brand in brandOptions" :key="brand.value" :value="brand.value">
                  {{ brand.label }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
          
          <!-- 授权有效期 -->
          <a-col :span="12">
            <a-form-item field="validPeriod" label="授权有效期" required>
              <a-range-picker
                v-model="formData.validPeriod"
                style="width: 100%"
                format="YYYY-MM-DD"
                placeholder="请选择授权有效期"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <!-- 授权图片 -->
        <a-form-item field="authImage" label="授权图片" required>
          <div class="auth-image-upload-container">
            <a-upload
              :file-list="fileList"
              :custom-request="customUploadRequest"
              :limit="5"
              @change="handleUploadChange"
              multiple
              list-type="picture-card"
              class="custom-upload"
            >
              <template #upload-button>
                <div class="upload-button-content">
                  <div class="upload-icon-wrapper">
                    <icon-plus class="upload-icon" />
                  </div>
                  <div class="upload-text">上传图片</div>
                </div>
              </template>
            </a-upload>
            <div class="upload-tip">
              支持格式：png、jpg、jpeg，单个文件大小不超过2MB
            </div>
          </div>
        </a-form-item>
      </a-form>
      
      <!-- 底部按钮区域 -->
      <div class="form-actions">
        <a-space>
          <a-button @click="close">取消</a-button>
          <a-button type="primary" :loading="submitting" @click="handleSubmit">确定</a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits, computed, watch, nextTick } from 'vue';
import { Message } from '@arco-design/web-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  editData: {
    type: Object,
    default: () => null
  }
});

const emit = defineEmits(['update:visible', 'close', 'submit']);

// 抽屉标题
const drawerTitle = computed(() => {
  return props.editData ? '编辑品牌授权' : '新增品牌授权';
});

// 表单引用
const formRef = ref(null);

// 提交状态
const submitting = ref(false);

// 文件列表
const fileList = ref([]);

// 表单数据
const formData = reactive({
  authCompany: '',
  certificateType: '授权书', // 默认值
  authorizedUnit: '',
  authCode: '',
  brandName: '',
  validPeriod: [],
  authImage: []
});

// 重置表单数据
const resetForm = () => {
  // 重置所有字段
  formData.authCompany = '';
  formData.certificateType = '授权书'; // 默认值
  formData.authorizedUnit = '';
  formData.authCode = '';
  formData.brandName = '';
  formData.validPeriod = [];
  formData.authImage = [];
  fileList.value = [];
  
  // 重置表单验证状态
  nextTick(() => {
    formRef.value?.resetFields();
  });
};

// 监听编辑数据变化
watch(() => props.editData, (newVal) => {
  if (newVal) {
    console.log('编辑数据变化', newVal);
    // 先重置表单
    resetForm();
    
    // 填充表单数据
    formData.authCompany = newVal.authCompany || '';
    formData.certificateType = newVal.certificateType || '授权书';
    formData.authorizedUnit = newVal.authorizedUnit || '';
    formData.authCode = newVal.authCode || '';
    formData.brandName = newVal.brandName || '';
    
    // 处理有效期
    if (newVal.validPeriod) {
      try {
        // 假设有效期格式为 "2023-01-01 至 2023-12-31"
        const [startDate, endDate] = newVal.validPeriod.split(' 至 ');
        if (startDate && endDate) {
          formData.validPeriod = [startDate.trim(), endDate.trim()];
        }
      } catch (error) {
        console.error('解析有效期失败', error);
        formData.validPeriod = [];
      }
    }
    
    // 处理图片
    if (newVal.authImage && Array.isArray(newVal.authImage)) {
      newVal.authImage.forEach((url, index) => {
        const fileItem = {
          uid: `existing-${index}`,
          name: `授权图片${index + 1}`,
          url: url
        };
        fileList.value.push(fileItem);
        formData.authImage.push(fileItem);
      });
    } else if (newVal.authImage && typeof newVal.authImage === 'string') {
      const fileItem = {
        uid: 'existing-0',
        name: '授权图片',
        url: newVal.authImage
      };
      fileList.value.push(fileItem);
      formData.authImage.push(fileItem);
    }
  } else {
    // 新增模式，重置表单
    resetForm();
    console.log('新增模式，表单已重置');
  }
}, { immediate: true, deep: true });

// 监听弹出框可见性变化
watch(() => props.visible, (newVal) => {
  if (newVal && !props.editData) {
    // 当打开弹出框且是新增模式时，确保表单被重置
    resetForm();
    console.log('打开新增弹出框，表单已重置');
  }
});

// 表单验证规则
const rules = {
  authCompany: [{ required: true, message: '请选择授权公司' }],
  authorizedUnit: [{ required: true, message: '请输入被授权单位' }],
  authCode: [{ required: true, message: '请输入授权编号' }],
  brandName: [{ required: true, message: '请选择授权品牌' }],
  validPeriod: [{ required: true, message: '请选择授权有效期' }],
  authImage: [{ required: true, message: '请上传授权图片' }]
};

// 授权公司选项
const companyOptions = ref([
  { label: '阿里巴巴', value: '阿里巴巴' },
  { label: '腾讯', value: '腾讯' },
  { label: '百度', value: '百度' },
  { label: '京东', value: '京东' },
  { label: '小米', value: '小米' }
]);

// 品牌选项
const brandOptions = ref([
  { label: 'Apple', value: 'Apple' },
  { label: 'Samsung', value: 'Samsung' },
  { label: 'Huawei', value: 'Huawei' },
  { label: 'Xiaomi', value: 'Xiaomi' },
  { label: 'OPPO', value: 'OPPO' },
  { label: 'vivo', value: 'vivo' }
]);

// 自定义上传请求
const customUploadRequest = (options) => {
  const { file, onProgress, onSuccess, onError } = options;
  
  // 文件类型验证
  const validTypes = ['image/png', 'image/jpeg', 'image/jpg'];
  if (!validTypes.includes(file.type)) {
    Message.error('只支持 png、jpg、jpeg 格式的图片');
    onError();
    return;
  }
  
  // 文件大小验证（2MB = 2 * 1024 * 1024 bytes）
  if (file.size > 2 * 1024 * 1024) {
    Message.error('图片大小不能超过 2MB');
    onError();
    return;
  }
  
  // 模拟上传进度
  let percent = 0;
  const interval = setInterval(() => {
    percent += 10;
    onProgress(percent);
    if (percent >= 100) {
      clearInterval(interval);
      // 模拟上传成功
      const fileUrl = URL.createObjectURL(file);
      onSuccess({ url: fileUrl });
      
      // 将文件添加到表单数据
      formData.authImage.push({
        uid: Date.now(),
        name: file.name,
        url: fileUrl
      });
    }
  }, 100);
};

// 处理上传变化
const handleUploadChange = (fileList) => {
  console.log('文件列表变化', fileList);
};

// 关闭弹出框
const close = () => {
  emit('update:visible', false);
  emit('close');
  // 使用重置函数清空表单
  resetForm();
  console.log('关闭弹出框，表单已重置');
};

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate();
    
    submitting.value = true;
    
    // 构建提交数据
    const submitData = {
      ...formData,
      validPeriod: formData.validPeriod.length === 2 
        ? `${formData.validPeriod[0]} 至 ${formData.validPeriod[1]}`
        : '',
      authImage: formData.authImage.map(item => item.url)
    };
    
    console.log('提交数据', submitData);
    
    // 模拟提交延迟
    setTimeout(() => {
      emit('submit', submitData);
      Message.success('提交成功');
      close();
    }, 1000);
  } catch (error) {
    console.error('表单验证失败', error);
  } finally {
    submitting.value = false;
  }
};
</script>

<style scoped>
.brand-auth-form-container {
  padding: 20px 0;
}

.form-actions {
  margin-top: 32px;
  display: flex;
  justify-content: center;
  gap: 16px;
}

.auth-image-upload-container {
  display: flex;
  flex-direction: column;
}

.custom-upload :deep(.arco-upload-list) {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.custom-upload :deep(.arco-upload-list-item),
.custom-upload :deep(.arco-upload-trigger) {
  width: 120px !important;
  height: 120px !important;
  margin: 0 !important;
  border-radius: 8px;
  overflow: hidden;
  border: 1px dashed #c9cdd4;
  transition: all 0.3s;
}

.custom-upload :deep(.arco-upload-trigger) {
  background-color: #f7f8fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-upload :deep(.arco-upload-trigger):hover {
  border-color: #165dff;
  background-color: #f2f3ff;
}

.upload-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.upload-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #e8f3ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.upload-icon {
  color: #165dff;
  font-size: 20px;
}

.upload-text {
  color: #4e5969;
  font-size: 14px;
}

.upload-tip {
  color: #86909c;
  font-size: 12px;
  margin-top: 8px;
  line-height: 1.5;
}
</style>
