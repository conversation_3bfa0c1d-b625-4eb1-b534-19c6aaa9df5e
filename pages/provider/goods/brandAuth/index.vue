<template>
  <div class="brand-auth-container">
    <div class="brand-auth-content">
      <!-- 品牌授权详情抽屉 -->
      <BrandAuthDetailDrawer
        v-model:visible="detailDrawerVisible"
        :brand-auth-data="currentBrandAuth"
        @close="handleDetailDrawerClose"
        @status-change="handleStatusChange"
      />
      
      <!-- 品牌授权表单抽屉 -->
      <BrandAuthFormDrawer
        v-model:visible="formDrawerVisible"
        :edit-data="editBrandAuth"
        @close="handleFormDrawerClose"
        @submit="handleFormSubmit"
      />
      
      <div class="search-area">
        <!-- 搜索表单 -->
        <ma-crud
          ref="crudRef"
          :options="crudOptions"
          :columns="columns"
          :search="search"
          v-model:search-params="searchParams"
          :data="tableData"
          :loading="loading"
        >
          <!-- 自定义左侧按钮 -->
          <template #toolbar-left>
            <a-button type="primary" @click="handleAdd">
              <template #icon>
                <icon-plus />
              </template>
              新增品牌授权
            </a-button>
          </template>

          <!-- 操作列 -->
          <template #operation="{ record }">
            <a-space>
              <a-link @click="handleEdit(record)">编辑</a-link>
              <a-link @click="handleView(record)">详情</a-link>
            </a-space>
          </template>

          <!-- 审核状态列 -->
          <template #auditStatus="{ record }">
            <a-tag :color="getStatusColor(record.auditStatus)">
              {{ getStatusText(record.auditStatus) }}
            </a-tag>
          </template>
        </ma-crud>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { Message } from "@arco-design/web-vue";
import { useRouter } from "#app";
import BrandAuthDetailDrawer from "./components/BrandAuthDetailDrawer.vue";
import BrandAuthFormDrawer from "./components/BrandAuthFormDrawer.vue";

// 定义页面路由元信息
definePageMeta({
  name: 'provider-brandAuth',
  title: "品牌授权提交",
  icon: "icon-stamp",
});

// 路由
const router = useRouter();

// ma-crud组件引用
const crudRef = ref(null);

// 详情抽屉可见性
const detailDrawerVisible = ref(false);
// 当前选中的品牌授权(查看详情)
const currentBrandAuth = ref(null);

// 表单抽屉可见性
const formDrawerVisible = ref(false);
// 当前编辑的品牌授权
const editBrandAuth = ref(null);

// 表格数据
const tableData = ref([]);
// 表格加载状态
const loading = ref(false);
// 当前页码
const currentPage = ref(1);
// 每页条数
const pageSize = ref(10);
// 总条数
const total = ref(0);

// 表格列配置
const columns = reactive([
  {
    title: "授权编号",
    dataIndex: "authCode",
    align: "center",
    width: 120,
    search: true,
    searchField: "authCode",
    placeholder: "请输入授权编号",
  },
  {
    title: "授权品牌",
    dataIndex: "brandName",
    align: "center",
    width: 120,
    search: true,
    searchField: "brandName",
    placeholder: "请输入授权品牌",
  },
  {
    title: "授权公司",
    dataIndex: "authCompany",
    align: "center",
    width: 150,
    search: true,
    searchField: "authCompany",
    placeholder: "请输入授权公司",
  },
  {
    title: "被授权单位",
    dataIndex: "authorizedUnit",
    align: "center",
    width: 150,
    search: true,
    searchField: "authorizedUnit",
    placeholder: "请输入被授权单位",
  },
  {
    title: "授权有效期",
    dataIndex: "validPeriod",
    align: "center",
    width: 180,
    search: true,
    searchField: "validPeriod",
    placeholder: "请选择授权有效期",
  },
  {
    title: "审核状态",
    dataIndex: "auditStatus",
    align: "center",
    width: 120,
    slotName: "auditStatus",
    search: true,
    searchField: "auditStatus",
    placeholder: "请选择审核状态",
  },
  {
    title: "操作",
    dataIndex: "operation",
    align: "center",
    width: 120,
    fixed: "right",
    slotName: "operation",
  },
]);

// 搜索表单配置
const search = reactive({
  labelWidth: 80,
  formProps: {
    layout: "inline",
  },
  schemas: [
    {
      field: "authCode",
      label: "授权编号",
      component: "Input",
      componentProps: {
        placeholder: "请输入授权编号",
      },
    },
    {
      field: "brandName",
      label: "授权品牌",
      component: "Input",
      componentProps: {
        placeholder: "请输入授权品牌",
      },
    },
    {
      field: "authCompany",
      label: "授权公司",
      component: "Input",
      componentProps: {
        placeholder: "请输入授权公司",
      },
    },
    {
      field: "authorizedUnit",
      label: "被授权单位",
      component: "Input",
      componentProps: {
        placeholder: "请输入被授权单位",
      },
    },
    {
      field: "validPeriod",
      label: "授权有效期",
      component: "RangePicker",
      componentProps: {
        placeholder: ["开始日期", "结束日期"],
        style: {
          width: "100%",
        },
      },
    },
    {
      field: "auditStatus",
      label: "审核状态",
      component: "Select",
      componentProps: {
        placeholder: "请选择审核状态",
        options: [
          { label: "待审核", value: "pending" },
          { label: "初审通过", value: "firstPass" },
          { label: "复审通过", value: "finalPass" },
          { label: "初审不通过", value: "firstReject" },
          { label: "复审不通过", value: "finalReject" },
        ],
      },
    },
  ],
});

// 搜索参数
const searchParams = ref({
  authCode: "",
  brandName: "",
  authCompany: "",
  authorizedUnit: "",
  validPeriod: [],
  auditStatus: "",
});

// 新增品牌授权
const handleAdd = () => {
  console.log("新增品牌授权");
  editBrandAuth.value = null; // 清空编辑数据
  formDrawerVisible.value = true; // 打开表单抽屉
};

// 编辑品牌授权
const handleEdit = (record) => {
  console.log("编辑品牌授权", record);
  editBrandAuth.value = { ...record }; // 设置编辑数据
  formDrawerVisible.value = true; // 打开表单抽屉
};

// 查看品牌授权详情
const handleView = (record) => {
  console.log("查看品牌授权详情", record);
  currentBrandAuth.value = record;
  detailDrawerVisible.value = true;
};

// 关闭详情抽屉
const handleDetailDrawerClose = () => {
  detailDrawerVisible.value = false;
  currentBrandAuth.value = null;
};

// 关闭表单抽屉
const handleFormDrawerClose = () => {
  formDrawerVisible.value = false;
  editBrandAuth.value = null;
};

// 处理表单提交
const handleFormSubmit = (formData) => {
  console.log('表单提交数据', formData);
  
  // 模拟添加新数据
  if (!editBrandAuth.value) {
    // 新增操作
    const newRecord = {
      id: Date.now().toString(),
      ...formData,
      auditStatus: 'pending', // 默认待审核
      createTime: new Date().toISOString().split('T')[0]
    };
    
    // 将新数据添加到表格头部
    tableData.value.unshift(newRecord);
    Message.success('添加成功');
  } else {
    // 编辑操作
    const index = tableData.value.findIndex(item => item.id === editBrandAuth.value.id);
    if (index !== -1) {
      tableData.value[index] = {
        ...tableData.value[index],
        ...formData,
        updateTime: new Date().toISOString().split('T')[0]
      };
      Message.success('编辑成功');
    }
  }
};

// 处理状态变更
const handleStatusChange = (data) => {
  console.log('状态变更', data);
  
  // 在表格数据中查找并更新对应记录
  const index = tableData.value.findIndex(item => item.id === data.id);
  if (index !== -1) {
    tableData.value[index].status = data.status;
    
    // 显示状态变更提示
    Message.success(`${data.actionText}成功`);
  }
};

// 表格配置
const crudOptions = reactive({
  title: "品牌授权提交",
  border: true,
  stripe: true,
  rowKey: "id",
  pageLayout: 'fixed',
  exportable: false,
  searchColNumber: 3,
  add: {
    show: true,
    text: "新增品牌授权",
    action: handleAdd
  },
  pagination: {
    currentPage: 1,
    pageSize: 10,
    pageSizes: [10, 20, 50, 100],
    background: true,
    total: 0
  },
  // 搜索前处理参数
  beforeSearch: (params) => {
    console.log("搜索参数", params);
    return params;
  },
  // 分页事件
  onPageChange: (page) => {
    console.log("页码变化", page);
    currentPage.value = page.currentPage;
    pageSize.value = page.pageSize;
    fetchData();
  }
});

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: "待审核",
    firstPass: "初审通过",
    finalPass: "复审通过",
    firstReject: "初审不通过",
    finalReject: "复审不通过",
  };
  return statusMap[status] || "未知状态";
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    pending: "orange",
    firstPass: "blue",
    finalPass: "green",
    firstReject: "red",
    finalReject: "red",
  };
  return colorMap[status] || "gray";
};

// 获取模拟数据
const getMockBrandAuthData = () => {
  const brands = ["苹果", "三星", "华为", "小米", "OPPO", "vivo", "耐克", "阿迪达斯", "优衣库", "H&M", "无印良品"];
  const companies = ["苹果公司", "三星电子", "华为技术有限公司", "小米科技", "OPPO广东移动通信有限公司", "维沃移动通信有限公司", "耐克体育", "阿迪达斯运动用品", "优衣库服饰", "H&M服装", "无印良品"];
  const authorizedUnits = ["京东商城", "天猫商城", "君网科技", "苏宁易购", "国美电器", "唯品会", "拼多多", "小红书", "抖音电商", "快手小店"];
  const statuses = ["pending", "firstPass", "finalPass", "firstReject", "finalReject"];
  
  // 生成模拟数据
  const generateBrandAuthItem = (index) => {
    const now = new Date();
    const startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - (index % 30));
    const endDate = new Date(now.getFullYear() + 1 + (index % 3), now.getMonth(), now.getDate() - (index % 30));
    
    const brandIndex = index % brands.length;
    const auditStatus = statuses[index % statuses.length];
    
    return {
      id: `ba-${index + 1}`,
      authCode: `AUTH${String(index + 10001).padStart(6, '0')}`,
      brandName: brands[brandIndex],
      authCompany: companies[brandIndex],
      authorizedUnit: authorizedUnits[index % authorizedUnits.length],
      validPeriod: `${startDate.toISOString().split('T')[0]} 至 ${endDate.toISOString().split('T')[0]}`,
      validStartDate: startDate.toISOString().split('T')[0],
      validEndDate: endDate.toISOString().split('T')[0],
      auditStatus: auditStatus,
      // 为复审通过的记录添加状态字段
      status: auditStatus === 'finalPass' ? (index % 3 === 0 ? 'inactive' : 'active') : undefined,
      // 添加审核相关字段
      firstAuditTime: auditStatus !== 'pending' ? new Date(now.getFullYear(), now.getMonth(), now.getDate() - (index % 10) + 1).toISOString().split('T')[0] + ' 14:30:00' : undefined,
      firstAuditor: auditStatus !== 'pending' ? getAuditor(index, 'firstAuditor') : undefined,
      finalAuditTime: (auditStatus === 'finalPass' || auditStatus === 'finalReject') ? new Date(now.getFullYear(), now.getMonth(), now.getDate() - (index % 10) + 2).toISOString().split('T')[0] + ' 16:45:00' : undefined,
      finalAuditor: (auditStatus === 'finalPass' || auditStatus === 'finalReject') ? getAuditor(index + 1, 'finalAuditor') : undefined,
      rejectReason: (auditStatus === 'firstReject') ? '资料不完整' : (auditStatus === 'finalReject' ? '授权文件有效期与申请不符' : undefined),
      createTime: new Date(now.getFullYear(), now.getMonth() - 1, now.getDate() - (index % 30)).toISOString().split('T')[0],
      updateTime: new Date(now.getFullYear(), now.getMonth(), now.getDate() - (index % 10)).toISOString().split('T')[0],
    };
  };
  
  // 获取审核人（模拟数据）
  function getAuditor(idNum, role) {
    const operators = {
      firstAuditor: ['张初审', '李初审', '王初审', '赵初审', '钱初审'],
      finalAuditor: ['张复审', '李复审', '王复审', '赵复审', '钱复审']
    };
    return operators[role][idNum % operators[role].length];
  }
  
  return Array.from({ length: 100 }, (_, index) => generateBrandAuthItem(index));
};

// 获取数据
const fetchData = () => {
  loading.value = true;
  console.log('正在获取数据...');
  try {
      // 生成模拟数据
      const allData = getMockBrandAuthData();
      console.log('模拟数据生成完成，总条数：', allData.length);
      
      // 分页处理
      const start = (currentPage.value - 1) * pageSize.value;
      const end = start + pageSize.value;
      
      tableData.value = allData.slice(start, end);
      total.value = allData.length;
      crudOptions.pagination.total = allData.length;
      
      console.log('当前页数据：', tableData.value.length);
    } catch (error) {
      console.error('获取数据出错：', error);
      Message.error('获取数据失败');
    } finally {
      loading.value = false;
    }
};

// 页面加载时获取数据
onMounted(() => {
  console.log('页面加载完成，开始获取数据');
  // 初始化表格数据
  fetchData();
});
</script>

<style scoped lang="less">
.brand-auth-container {
  padding: 16px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.search-area {
  background-color: #fff;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
:deep(.arco-form-item-label){
 white-space: nowrap; 
}
</style>
