<template>
  <div>
    <div class="invoice-container">
      <!-- 开票申请详情抽屉 -->
      <InvoiceDetailDrawer
        v-model:visible="drawerVisible"
        :invoice-data="currentInvoice"
        @close="handleDrawerClose"
      />
      
      <div class="search-area">
        <!-- 状态标签列表 -->
        <div class="status-tabs-container">
          <a-tabs type="line" :active-key="activeStatus" @change="selectStatusTab">
            <a-tab-pane v-for="tab in statusTabs" :key="tab.key" :title="tab.name">
              <template #title>
                <span class="status-tab-title">{{ tab.name }}</span>
                <span class="status-tab-count">{{ tab.count || 0 }}</span>
              </template>
            </a-tab-pane>
          </a-tabs>
        </div>

        <!-- 搜索表单 -->
        <ma-crud
          ref="crudRef"
          :options="crudOptions"
          :columns="columns"
          :search="search"
          v-model:search-params="searchParams"
          :data="tableData"
          :loading="loading"
        >
          <!-- 操作列 -->
          <template #operation="{ record }">
            <a-space>
              <a-link @click="handleView(record)">查看详情</a-link>
              
              <!-- 待申请、财务审核驳回状态：显示申请开票按钮 -->
              <a-link 
                v-if="record.invoiceStatus === 'pending' || record.invoiceStatus === 'financeRejected'"
                @click="handleApplyInvoice(record)"
                type="primary"
              >
                申请开票
              </a-link>
              
              <!-- 待审核、商务审核通过、财务审核通过状态：显示申请作废按钮 -->
              <a-link 
                v-if="record.invoiceStatus === 'auditing' || record.invoiceStatus === 'businessApproved' || record.invoiceStatus === 'financeApproved'"
                @click="handleCancelInvoice(record)"
                type="warning"
              >
                申请作废
              </a-link>
              
              <!-- 已开票状态：显示下载发票按钮 -->
              <a-link 
                v-if="record.invoiceStatus === 'invoiced'"
                @click="handleDownloadInvoice(record)"
                type="success"
              >
                下载发票
              </a-link>
            </a-space>
          </template>

          <!-- 发票状态列 -->
          <template #invoiceStatus="{ record }">
            <a-tag :color="getInvoiceStatusColor(record.invoiceStatus)">
              {{ getInvoiceStatusText(record.invoiceStatus) }}
            </a-tag>
          </template>

          <!-- 发票类型列 -->
          <template #invoiceType="{ record }">
            <a-tag :color="getInvoiceTypeColor(record.invoiceType)">
              {{ record.invoiceType }}
            </a-tag>
          </template>
        </ma-crud>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from "vue";
import { Message } from "@arco-design/web-vue";
import { useRouter } from "#app";
import InvoiceDetailDrawer from "./components/InvoiceDetailDrawer.vue";

// 定义页面路由元信息
definePageMeta({
  name: 'provider-finance-invoice',
  title: "开票申请",
  icon: "icon-file-invoice",
});

// 路由
const router = useRouter();
const route = useRoute();

// 抽屉可见性
const drawerVisible = ref(false);
const currentInvoice = ref({});

// 表格数据
const tableData = ref([]);
// 表格加载状态
const loading = ref(false);
// 当前页码
const currentPage = ref(1);
// 每页条数
const pageSize = ref(10);
// 总条数
const total = ref(0);

// 当前选中的状态标签
const activeStatus = ref(route.query.activeStatus || 'all');

// 状态标签列表
const statusTabs = [
  { key: 'all', name: '全部', count: 0 },
  { key: 'pending', name: '待申请', count: 0 },
  { key: 'auditing', name: '待审核', count: 0 },
  { key: 'businessApproved', name: '商务审核通过', count: 0 },
  { key: 'businessRejected', name: '商务审核驳回', count: 0 },
  { key: 'financeApproved', name: '财务审核通过', count: 0 },
  { key: 'financeRejected', name: '财务审核驳回', count: 0 },
  { key: 'invoiced', name: '已开票', count: 0 },
  { key: 'offlineProcessed', name: '已线下处理', count: 0 },
];

// 表格列配置
const columns = reactive([
  {
    title: "订单号",
    dataIndex: "orderNo",
    align: "center",
    width: 120,
    search: true,
    searchField: "orderNo",
    placeholder: "请输入订单号",
  },
  {
    title: "订单来源",
    dataIndex: "orderSource",
    align: "center",
    width: 120,
    search: true,
    searchField: "orderSource",
    placeholder: "请选择订单来源",
  },
  {
    title: "买家/收货人",
    dataIndex: "buyer",
    align: "center",
    width: 120,
    search: true,
    searchField: "buyer",
    placeholder: "请输入买家/收货人",
  },
  {
    title: "业务员",
    dataIndex: "salesman",
    align: "center",
    width: 120,
    search: true,
    searchField: "salesman",
    placeholder: "请输入业务员",
  },
  {
    title: "订单状态",
    dataIndex: "orderStatus",
    align: "center",
    width: 120,
    search: true,
    searchField: "orderStatus",
    placeholder: "请选择订单状态",
  },
  {
    title: "开票申请时间",
    dataIndex: "applyTime",
    align: "center",
    width: 150,
    search: true,
    searchField: "applyTime",
    placeholder: "请选择开票申请时间",
  },
  {
    title: "发票号",
    dataIndex: "invoiceNo",
    align: "center",
    width: 150,
    search: true,
    searchField: "invoiceNo",
    placeholder: "请输入发票号",
  },
  {
    title: "发票抬头",
    dataIndex: "invoiceTitle",
    align: "center",
    width: 150,
    search: true,
    searchField: "invoiceTitle",
    placeholder: "请输入发票抬头",
  },
  {
    title: "跟单员",
    dataIndex: "merchandiser",
    align: "center",
    width: 120,
    search: true,
    searchField: "merchandiser",
    placeholder: "请输入跟单员",
  },
  {
    title: "价税合计金额",
    dataIndex: "totalAmount",
    align: "center",
    width: 120,
  },
  {
    title: "税额",
    dataIndex: "taxAmount",
    align: "center",
    width: 120,
  },
  {
    title: "不含税金额",
    dataIndex: "amountWithoutTax",
    align: "center",
    width: 120,
  },
  {
    title: "发票类型",
    dataIndex: "invoiceType",
    align: "center",
    width: 120,
    slotName: "invoiceType",
    search: true,
    searchField: "invoiceType",
    placeholder: "请选择发票类型",
  },
  {
    title: "发票状态",
    dataIndex: "invoiceStatus",
    align: "center",
    width: 120,
    slotName: "invoiceStatus",
    search: true,
    searchField: "invoiceStatus",
    placeholder: "请选择发票状态",
  },
  {
    title: "操作",
    dataIndex: "operation",
    align: "center",
    width: 200,
    fixed: "right",
    slotName: "operation",
  },
]);

// 搜索表单配置
const search = reactive({
  labelWidth: 80,
  formProps: {
    layout: "inline",
  },
  schemas: [
    {
      field: "orderSource",
      label: "订单来源",
      component: "Select",
      componentProps: {
        placeholder: "请选择订单来源",
        options: [
          { label: "君网BG", value: "julingBG" },
          { label: "京东", value: "jd" },
          { label: "商城", value: "mall" },
        ],
      },
    },
    {
      field: "buyer",
      label: "买家/收货人",
      component: "Input",
      componentProps: {
        placeholder: "请输入买家/收货人",
      },
    },
    {
      field: "salesman",
      label: "业务员",
      component: "Input",
      componentProps: {
        placeholder: "请输入业务员",
      },
    },
    {
      field: "orderStatus",
      label: "订单状态",
      component: "Select",
      componentProps: {
        placeholder: "请选择订单状态",
        options: [
          { label: "待付款", value: "pending" },
          { label: "已付款", value: "paid" },
          { label: "已发货", value: "shipped" },
          { label: "已完成", value: "completed" },
          { label: "已取消", value: "cancelled" },
        ],
      },
    },
    {
      field: "applyTime",
      label: "申请时间",
      component: "DatePicker",
      componentProps: {
        placeholder: "请选择申请时间",
        style: {
          width: "100%",
        },
      },
    },
    {
      field: "invoiceType",
      label: "发票类型",
      component: "Select",
      componentProps: {
        placeholder: "请选择发票类型",
        options: [
          { label: "电子普票", value: "电子普票" },
          { label: "电子专票", value: "电子专票" },
          { label: "纸质普票", value: "纸质普票" },
          { label: "纸质专票", value: "纸质专票" },
        ],
      },
    },
    {
      field: "invoiceStatus",
      label: "发票状态",
      component: "Select",
      componentProps: {
        placeholder: "请选择发票状态",
        options: [
          { label: "待申请", value: "pending" },
          { label: "待审核", value: "auditing" },
          { label: "商务审核通过", value: "businessApproved" },
          { label: "商务审核驳回", value: "businessRejected" },
          { label: "财务审核通过", value: "financeApproved" },
          { label: "财务审核驳回", value: "financeRejected" },
          { label: "已开票", value: "invoiced" },
          { label: "已线下处理", value: "offlineProcessed" },
        ],
      },
    },
  ],
});

// 搜索参数
const searchParams = ref({
  orderNo: "",
  orderSource: "",
  buyer: "",
  salesman: "",
  orderStatus: "",
  applyTime: "",
  invoiceNo: "",
  invoiceTitle: "",
  merchandiser: "",
  invoiceType: "",
  invoiceStatus: "",
});

// 查看开票申请详情
const handleView = (record) => {
  console.log('查看详情：', record);
  currentInvoice.value = record;
  drawerVisible.value = true;
};

// 申请开票
const handleApplyInvoice = (record) => {
  console.log('申请开票：', record);
  router.push({
    path: '/provider/finance/invoice/apply',
    query: { orderId: record.orderNo }
  });
};

// 关闭抽屉
const handleDrawerClose = () => {
  drawerVisible.value = false;
  currentInvoice.value = {};
};

// 注意：handleApplyInvoice 函数已在上方定义，此处不再重复定义

// 申请作废
const handleCancelInvoice = (record) => {
  // 在实际应用中，这里应该打开一个确认对话框
  Message.warning(`确定要申请作废该开票申请吗？订单号：${record.orderNo}`);
  
  // 模拟操作成功
  setTimeout(() => {
    Message.success('作废申请提交成功！');
    // 更新状态
    record.invoiceStatus = 'pending';
    // 重新加载数据
    loadData();
  }, 1000);
};

// 下载发票
const handleDownloadInvoice = (record) => {
  if (!record.invoiceNo) {
    Message.error('发票号不存在，无法下载发票');
    return;
  }
  
  Message.info(`正在下载发票，发票号：${record.invoiceNo}`);
  // 在实际应用中，这里应该调用下载接口
  
  // 模拟下载成功
  setTimeout(() => {
    Message.success('发票下载成功！');
  }, 1000);
};

// 获取发票状态颜色
const getInvoiceStatusColor = (status) => {
  const statusColorMap = {
    pending: "orange",
    auditing: "blue",
    businessApproved: "cyan",
    businessRejected: "red",
    financeApproved: "green",
    financeRejected: "red",
    invoiced: "purple",
    offlineProcessed: "gray",
  };
  return statusColorMap[status] || "default";
};

// 获取发票状态文本
const getInvoiceStatusText = (status) => {
  const statusTextMap = {
    pending: "待申请",
    auditing: "待审核",
    businessApproved: "商务审核通过",
    businessRejected: "商务审核驳回",
    financeApproved: "财务审核通过",
    financeRejected: "财务审核驳回",
    invoiced: "已开票",
    offlineProcessed: "已线下处理",
  };
  return statusTextMap[status] || "未知状态";
};

// 获取发票类型颜色
const getInvoiceTypeColor = (type) => {
  const typeColorMap = {
    "电子普票": "blue",
    "电子专票": "green",
    "纸质普票": "orange",
    "纸质专票": "purple",
  };
  return typeColorMap[type] || "default";
};

// 搜索前处理参数
const beforeSearch = (params) => {
  console.log('搜索参数处理前:', params);
  // 处理日期格式
  if (params.applyTime && params.applyTime instanceof Date) {
    params.applyTime = params.applyTime.toISOString().split('T')[0];
  }
  console.log('搜索参数处理后:', params);
  return params;
};

// 分页事件
const onPageChange = (page) => {
  currentPage.value = page;
  loadData();
};

// 每页条数变化事件
const onPageSizeChange = (size) => {
  pageSize.value = size;
  loadData();
};

// 搜索事件
const onSearch = () => {
  currentPage.value = 1;
  loadData();
};

// 表格配置
const crudOptions = reactive({
  title: "开票申请",
  border: true,
  stripe: true,
  rowKey: "id",
  pageLayout: 'fixed',
  exportable: false,
  searchColNumber: 3,
  pagination: {
    currentPage: 1,
    pageSize: 10,
    pageSizes: [10, 20, 50, 100],
    background: true,
    total: 0
  },
  beforeSearch: beforeSearch,
  onPageChange: onPageChange,
  onPageSizeChange: onPageSizeChange,
  onSearch: onSearch,
});

// 状态标签切换事件
watch(activeStatus, (newStatus) => {
  console.log('状态切换为:', newStatus);
  searchParams.value.invoiceStatus = newStatus === 'all' ? '' : newStatus;
  currentPage.value = 1;
  loadData();
});

// 生成模拟数据
const getMockInvoiceData = () => {
  const orderSources = ["julingBG", "jd", "mall"];
  const orderStatuses = ["pending", "paid", "shipped", "completed", "cancelled"];
  const invoiceTypes = ["电子普票", "电子专票", "纸质普票", "纸质专票"];
  const invoiceStatuses = ["pending", "auditing", "businessApproved", "businessRejected", "financeApproved", "financeRejected", "invoiced", "offlineProcessed"];
  const buyers = ["张三", "李四", "王五", "赵六", "刘七"];
  const salesmen = ["王销售", "李销售", "张销售", "赵销售"];
  const merchandisers = ["王跟单", "李跟单", "张跟单", "赵跟单"];
  
  // 生成模拟数据
  const generateInvoiceItem = (index) => {
    const now = new Date();
    const applyTime = new Date(now.getFullYear(), now.getMonth(), now.getDate() - (index % 30));
    const invoiceStatus = invoiceStatuses[index % invoiceStatuses.length];
    
    // 计算金额
    const amountWithoutTax = Math.round(Math.random() * 10000) / 100;
    const taxRate = 0.13; // 13%的税率
    const taxAmount = Math.round(amountWithoutTax * taxRate * 100) / 100;
    const totalAmount = Math.round((amountWithoutTax + taxAmount) * 100) / 100;
    
    // 生成发票号
    let invoiceNo = '';
    if (invoiceStatus === 'invoiced' || invoiceStatus === 'offlineProcessed') {
      invoiceNo = `FP${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(index).padStart(6, '0')}`;
    }
    
    return {
      id: `inv-${index + 1}`,
      orderNo: `DD${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(index).padStart(6, '0')}`,
      orderSource: orderSources[index % orderSources.length],
      buyer: buyers[index % buyers.length],
      salesman: salesmen[index % salesmen.length],
      orderStatus: orderStatuses[index % orderStatuses.length],
      applyTime: applyTime.toISOString().split('T')[0],
      invoiceNo: invoiceNo,
      invoiceTitle: `公司${index % 5 + 1}`,
      merchandiser: merchandisers[index % merchandisers.length],
      totalAmount: totalAmount,
      taxAmount: taxAmount,
      amountWithoutTax: amountWithoutTax,
      invoiceType: invoiceTypes[index % invoiceTypes.length],
      invoiceStatus: invoiceStatus,
      businessAuditStatus: ['businessApproved', 'businessRejected', 'financeApproved', 'financeRejected', 'invoiced', 'offlineProcessed'].includes(invoiceStatus) ? 
        (invoiceStatus === 'businessRejected' ? '驳回' : '通过') : '',
      businessAuditTime: ['businessApproved', 'businessRejected', 'financeApproved', 'financeRejected', 'invoiced', 'offlineProcessed'].includes(invoiceStatus) ? 
        new Date(applyTime.getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0] : '',
      businessAuditor: ['businessApproved', 'businessRejected', 'financeApproved', 'financeRejected', 'invoiced', 'offlineProcessed'].includes(invoiceStatus) ? 
        '商务审核员' : '',
      financeAuditStatus: ['financeApproved', 'financeRejected', 'invoiced', 'offlineProcessed'].includes(invoiceStatus) ? 
        (invoiceStatus === 'financeRejected' ? '驳回' : '通过') : '',
      financeAuditTime: ['financeApproved', 'financeRejected', 'invoiced', 'offlineProcessed'].includes(invoiceStatus) ? 
        new Date(applyTime.getTime() + 48 * 60 * 60 * 1000).toISOString().split('T')[0] : '',
      financeAuditor: ['financeApproved', 'financeRejected', 'invoiced', 'offlineProcessed'].includes(invoiceStatus) ? 
        '财务审核员' : '',
    };
  };
  
  return Array.from({ length: 100 }, (_, index) => generateInvoiceItem(index));
};

// 加载数据
const loadData = () => {
  console.log('加载数据');
  loading.value = true;
  
  // 生成模拟数据
  const allData = getMockInvoiceData();
  
  // 根据搜索参数过滤
  let filteredData = allData;
  
  // 如果有状态筛选
  if (searchParams.value.invoiceStatus) {
    filteredData = filteredData.filter(item => item.invoiceStatus === searchParams.value.invoiceStatus);
  }
  
  // 其他搜索条件过滤
  Object.keys(searchParams.value).forEach(key => {
    if (searchParams.value[key] && key !== 'invoiceStatus') {
      filteredData = filteredData.filter(item => {
        if (typeof item[key] === 'string') {
          return item[key].includes(searchParams.value[key]);
        }
        return item[key] === searchParams.value[key];
      });
    }
  });
  
  // 更新状态标签数量
  statusTabs.forEach(tab => {
    if (tab.key === 'all') {
      tab.count = allData.length;
    } else {
      tab.count = allData.filter(item => item.invoiceStatus === tab.key).length;
    }
  });
  
  // 分页处理
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  
  tableData.value = filteredData.slice(start, end);
  total.value = filteredData.length;
  crudOptions.pagination.total = filteredData.length;
  loading.value = false;
};

// 选择状态标签
const selectStatusTab = (key) => {
  console.log('状态标签变化：', key);
  activeStatus.value = key;
  
  // 更新搜索参数
  if (key === 'all') {
    searchParams.value.invoiceStatus = '';
  } else {
    searchParams.value.invoiceStatus = key;
  }
  
  // 重置分页到第一页
  currentPage.value = 1;
  
  // 重新加载数据
  loadData();
};

// 页面加载时获取数据
onMounted(() => {
  console.log('页面加载完成，开始获取数据');
  // 根据URL参数设置初始状态
  if (activeStatus.value !== 'all') {
    searchParams.value.invoiceStatus = activeStatus.value;
  }
  // 初始化表格数据
  loadData();
});
</script>

<style scoped lang="less">
.invoice-container {
  padding: 16px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.search-area {
  background-color: #fff;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.status-tabs-container {
  margin-bottom: 16px;
  
  :deep(.arco-tabs-nav) {
    margin-bottom: 0;
  }
  
  :deep(.arco-tabs-nav-tab) {
    padding: 0;
  }
  
  .status-tab-title {
    margin-right: 4px;
  }
  
  .status-tab-count {
    display: inline-block;
    min-width: 16px;
    padding: 0 4px;
    font-size: 12px;
    line-height: 16px;
    text-align: center;
    background-color: #f2f3f5;
    border-radius: 8px;
  }
}

.status-tab-title {
  margin-right: 8px;
}

.status-tab-count {
  display: inline-block;
  padding: 0 8px;
  height: 20px;
  line-height: 20px;
  background-color: #f2f3f5;
  border-radius: 10px;
  font-size: 12px;
  color: #4e5969;
}
</style>
