<template>
  <div class="manual-receipt-form">
    <a-form :model="formData" ref="formRef" :rules="rules" layout="vertical">
      <div class="form-row grid grid-cols-2 gap-4">
        <div class="form-col">
          <a-form-item field="payerName" label="付款人名称" required>
            <a-input v-model="formData.payerName" placeholder="请输入付款人名称" />
          </a-form-item>
        </div>
        <div class="form-col">
          <a-form-item field="payerAccount" label="付款人账号" required>
            <a-input v-model="formData.payerAccount" placeholder="请输入付款人账号" />
          </a-form-item>
        </div>
      </div>
      
      <div class="form-row grid grid-cols-2 gap-4">
        <div class="form-col">
          <a-form-item field="payerBank" label="付款人开户行" required>
            <a-input v-model="formData.payerBank" placeholder="请输入付款人开户行" />
          </a-form-item>
        </div>
        <div class="form-col">
          <a-form-item field="receiverName" label="收款人名称" required>
            <a-input v-model="formData.receiverName" placeholder="请输入收款人名称" />
          </a-form-item>
        </div>
      </div>
      
      <div class="form-row grid grid-cols-2 gap-4">
        <div class="form-col">
          <a-form-item field="receiverAccount" label="收款人账号" required>
            <a-input v-model="formData.receiverAccount" placeholder="请输入收款人账号" />
          </a-form-item>
        </div>
        <div class="form-col">
          <a-form-item field="receiptDate" label="收款日期" required>
            <a-date-picker v-model="formData.receiptDate" style="width: 100%" />
          </a-form-item>
        </div>
      </div>
      
      <div class="form-row grid grid-cols-2 gap-4">
        <div class="form-col">
          <a-form-item field="amount" label="收款金额" required>
            <a-input-number v-model="formData.amount" placeholder="请输入收款金额" style="width: 100%" :precision="2" />
          </a-form-item>
        </div>
        <div class="form-col">
          <a-form-item field="transactionNo" label="交易流水号" required>
            <a-input v-model="formData.transactionNo" placeholder="请输入交易流水号" />
          </a-form-item>
        </div>
      </div>
      
      <a-form-item field="voucher" label="上传凭证" required>
        <div class="voucher-upload-container">
          <a-upload
            :custom-request="handleUploadVoucher"
            :file-list="formData.voucherList"
            @change="handleVoucherChange"
            @remove="handleRemoveVoucher"
            multiple
            list-type="picture-card"
            :limit="5"
          >
            <div class="upload-button">
              <icon-plus />
              <div class="upload-text">上传凭证</div>
            </div>
          </a-upload>
          <div class="upload-tip text-gray-500 text-xs mt-2">最多上传5张凭证图片，支持jpg、jpeg、png、pdf格式，大小不超过2MB</div>
        </div>
      </a-form-item>
    </a-form>
    
    <div class="flex justify-center mt-6 space-x-4">
      <a-button @click="handleReset">重置</a-button>
      <a-button type="primary" @click="handleSubmit">添加</a-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, defineEmits, defineProps } from 'vue';
import { Message } from '@arco-design/web-vue';

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:modelValue', 'submit', 'reset']);

// 表单数据
const formData = reactive({
  payerName: '',
  payerAccount: '',
  payerBank: '',
  receiverName: '',
  receiverAccount: '',
  receiptDate: null,
  amount: null,
  transactionNo: '',
  voucherList: []
});

// 表单引用
const formRef = ref();

// 表单验证规则
const rules = {
  payerName: [{ required: true, message: '请输入付款人名称' }],
  payerAccount: [{ required: true, message: '请输入付款人账号' }],
  payerBank: [{ required: true, message: '请输入付款人开户行' }],
  receiverName: [{ required: true, message: '请输入收款人名称' }],
  receiverAccount: [{ required: true, message: '请输入收款人账号' }],
  receiptDate: [{ required: true, message: '请选择收款日期' }],
  amount: [{ required: true, message: '请输入收款金额' }],
  transactionNo: [{ required: true, message: '请输入交易流水号' }],
  voucher: [{ required: true, message: '请上传收款凭证' }]
};

// 自定义上传请求
const handleUploadVoucher = (options) => {
  const { file, onSuccess, onError } = options;
  
  // 模拟上传
  setTimeout(() => {
    // 检查文件类型和大小
    const validTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
    const maxSize = 2 * 1024 * 1024; // 2MB
    
    if (!validTypes.includes(file.type)) {
      Message.error('只支持 jpg、jpeg、png、pdf 格式的文件');
      onError();
      return;
    }
    
    if (file.size > maxSize) {
      Message.error('文件大小不能超过2MB');
      onError();
      return;
    }
    
    // 模拟成功上传
    const fileUrl = URL.createObjectURL(file);
    onSuccess({ url: fileUrl, name: file.name, uid: Date.now() });
  }, 500);
};

// 处理凭证上传变化
const handleVoucherChange = (fileList) => {
  formData.voucherList = fileList;
};

// 移除凭证
const handleRemoveVoucher = (file) => {
  const index = formData.voucherList.findIndex(item => item.uid === file.uid);
  if (index !== -1) {
    formData.voucherList.splice(index, 1);
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate();
    
    if (formData.voucherList.length === 0) {
      Message.error('请上传收款凭证');
      return;
    }
    
    // 构建收款记录数据
    const receiptData = {
      id: `MANUAL-${Date.now()}`, // 生成唯一ID
      payerName: formData.payerName,
      payerAccount: formData.payerAccount,
      payerBank: formData.payerBank,
      receiverName: formData.receiverName,
      receiverAccount: formData.receiverAccount,
      receiptDate: formData.receiptDate ? formData.receiptDate.format('YYYY-MM-DD') : '',
      amount: formData.amount,
      transactionNo: formData.transactionNo,
      voucherList: formData.voucherList,
      isManual: true // 标记为手动添加
    };
    
    // 提交数据
    emit('submit', receiptData);
    
    // 重置表单
    handleReset();
    
    Message.success('收款记录添加成功');
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields();
  Object.keys(formData).forEach(key => {
    if (key === 'voucherList') {
      formData[key] = [];
    } else if (typeof formData[key] === 'string') {
      formData[key] = '';
    } else {
      formData[key] = null;
    }
  });
  
  emit('reset');
};
</script>

<style scoped lang="less">
.manual-receipt-form {
  .voucher-upload-container {
    .upload-button {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      
      .upload-text {
        margin-top: 8px;
        font-size: 14px;
      }
    }
  }
}
</style>
