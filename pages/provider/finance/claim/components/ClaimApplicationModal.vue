<template>
  <a-modal
    :visible="visible"
    :title="'认款申请'"
    :mask-closable="false"
    :unmount-on-close="false"
    @cancel="handleCancel"
    @before-ok="handleSubmit"
    width="1200px"
  >
    <a-tabs v-model:active-key="activeTab">
      <!-- 关联系统收款记录标签页 -->
      <a-tab-pane key="system" title="关联系统收款记录">
        <div class="system-records-container">
          <a-table
            :data="systemRecords"
            :pagination="{
              total: systemRecords.length,
              pageSize: 10,
              showTotal: true,
              showJumper: true,
              showPageSize: true
            }"
            :bordered="true"
            :row-selection="{
              type: 'checkbox',
              showCheckedAll: true,
              selectedRowKeys: selectedRowKeys,
              onChange: onSelectChange
            }"
          >
            <template #columns>
              <a-table-column title="收款日期" data-index="receiptDate" align="center" />
              <a-table-column title="订单编号" data-index="orderNo" align="center" />
              <a-table-column title="付款金额" data-index="amount" align="center" />
              <a-table-column title="付款人" data-index="payerName" align="center" />
              <a-table-column title="付款人账号" data-index="payerAccount" align="center" />
              <a-table-column title="收款人" data-index="receiverName" align="center" />
              <a-table-column title="收款人账号" data-index="receiverAccount" align="center" />
              <a-table-column title="交易状态" data-index="status" align="center">
                <template #cell="{ record }">
                  <a-tag :color="record.status === '已完成' ? 'green' : 'blue'">{{ record.status }}</a-tag>
                </template>
              </a-table-column>
              <a-table-column title="操作人" data-index="operator" align="center" />
              <a-table-column title="收款单位" data-index="receiverCompany" align="center" />
            </template>
          </a-table>
        </div>
      </a-tab-pane>
      
      <!-- 手动添加收款记录标签页 -->
      <a-tab-pane key="manual" title="手动添加收款记录">
        <a-form :model="formData" ref="formRef" :rules="rules" layout="vertical">
          <div class="form-row">
            <div class="form-col">
              <a-form-item field="payerName" label="付款人名称" required>
                <a-input v-model="formData.payerName" placeholder="请输入付款人名称" />
              </a-form-item>
            </div>
            <div class="form-col">
              <a-form-item field="payerAccount" label="付款人账号" required>
                <a-input v-model="formData.payerAccount" placeholder="请输入付款人账号" />
              </a-form-item>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-col">
              <a-form-item field="receiverName" label="收款人名称" required>
                <a-input v-model="formData.receiverName" placeholder="请输入收款人名称" />
              </a-form-item>
            </div>
            <div class="form-col">
              <a-form-item field="receiverAccount" label="收款人账号" required>
                <a-input v-model="formData.receiverAccount" placeholder="请输入收款人账号" />
              </a-form-item>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-col">
              <a-form-item field="receiptDate" label="收款日期" required>
                <a-date-picker v-model="formData.receiptDate" style="width: 100%" placeholder="请选择收款日期" />
              </a-form-item>
            </div>
            <div class="form-col">
              <a-form-item field="amount" label="收款金额" required>
                <a-input-number v-model="formData.amount" placeholder="请输入收款金额" style="width: 100%" :precision="2" />
              </a-form-item>
            </div>
          </div>
          
          <a-form-item field="voucher" label="上传凭证" required>
            <div class="voucher-upload-container">
              <a-upload
                :custom-request="handleUploadVoucher"
                :show-file-list="false"
                :multiple="true"
                :accept="'.jpg,.jpeg,.png,.pdf'"
                :limit="5"
              >
                <template #upload-button>
                  <div class="upload-button">
                    <icon-plus />
                    <span>上传凭证</span>
                  </div>
                </template>
              </a-upload>
              
              <div class="voucher-list">
                <div v-for="(item, index) in formData.voucherList" :key="index" class="voucher-item">
                  <div class="voucher-preview">
                    <a-image
                      width="80"
                      height="80"
                      :src="item.url"
                      :preview="true"
                    />
                    <div class="voucher-mask" v-if="item.status === 'uploading'">
                      <a-spin />
                      <div class="upload-text">{{ item.percent || 0 }}%</div>
                    </div>
                  </div>
                  <a-button class="delete-btn" type="text" @click="removeVoucher(index)">
                    <template #icon><icon-delete /></template>
                  </a-button>
                </div>
              </div>
            </div>
            <div class="upload-tip text-gray-500 text-xs">最多上传5张凭证图片，支持jpg、jpeg、png、pdf格式，大小不超过2MB</div>
          </a-form-item>
          
          <a-form-item field="remark" label="认款备注">
            <a-textarea 
              v-model="formData.remark" 
              placeholder="请输入认款备注信息" 
              :max-length="500" 
              show-word-limit 
              :auto-size="{ minRows: 4, maxRows: 6 }" 
            />
          </a-form-item>
        </a-form>
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  orderData: {
    type: Object,
    default: () => ({})
  }
});

// 定义事件
const emit = defineEmits(['update:visible', 'submit']);

// 当前激活的标签页
const activeTab = ref('system');

// 表单引用
const formRef = ref(null);

// 系统收款记录数据
const systemRecords = ref([]);

// 选中的行
const selectedRowKeys = ref([]);
const selectedRecord = ref(null);

// 表单数据
const formData = reactive({
  payerName: '',
  payerAccount: '',
  receiverName: '',
  receiverAccount: '',
  receiptDate: null,
  amount: null,
  remark: '',
  voucherList: [] // 凭证列表
});

// 表单验证规则
const rules = {
  payerName: [
    { required: true, message: '请输入付款人名称' }
  ],
  payerAccount: [
    { required: true, message: '请输入付款人账号' }
  ],
  receiverName: [
    { required: true, message: '请输入收款人名称' }
  ],
  receiverAccount: [
    { required: true, message: '请输入收款人账号' }
  ],
  receiptDate: [
    { required: true, message: '请选择收款日期' }
  ],
  amount: [
    { required: true, message: '请输入收款金额' }
  ],
  voucher: [
    { 
      required: true, 
      validator: (value, cb) => {
        if (formData.voucherList && formData.voucherList.length > 0) {
          cb();
        } else {
          cb('请上传凭证');
        }
      } 
    }
  ]
};

// 初始化系统收款记录数据
onMounted(() => {
  // 模拟从系统获取收款记录数据
  systemRecords.value = [
    {
      id: 'SDJK20231000002',
      receiptDate: '2024-04-12 15:15:00',
      orderNo: '超市',
      amount: '18.72',
      payerName: '张三',
      payerAccount: '**************',
      receiverName: '王小二',
      receiverAccount: '****************',
      status: '已完成',
      operator: 'test/Admin',
      receiverCompany: '工商'
    },
    {
      id: 'SDJK20231000010',
      receiptDate: '2024-01-15 14:47:12',
      orderNo: '超市',
      amount: '1030.00',
      payerName: '中国工商银行股份有限公司',
      payerAccount: '01190142000000000930',
      receiverName: '中国工商银行股份有限公司',
      receiverAccount: '**************',
      status: '已完成',
      operator: 'test/Admin',
      receiverCompany: '中国工商银行股份有限公司'
    },
    {
      id: 'SDJK20231000006',
      receiptDate: '2024-12-27 10:19:42',
      orderNo: '银行',
      amount: '1029.00',
      payerName: '中国工商银行股份有限公司',
      payerAccount: '01190142000000000930',
      receiverName: '中国工商银行股份有限公司',
      receiverAccount: '**************',
      status: '已完成',
      operator: 'test/Admin',
      receiverCompany: '中国工商银行股份有限公司'
    },
    {
      id: 'SDJK20231000005',
      receiptDate: '2024-12-28 01:19:01',
      orderNo: '银行',
      amount: '403.57',
      payerName: '中国工商银行股份有限公司',
      payerAccount: '01190142000000000930',
      receiverName: '中国工商银行股份有限公司',
      receiverAccount: '**************',
      status: '已完成',
      operator: 'test/Admin',
      receiverCompany: '中国工商银行股份有限公司'
    },
    {
      id: 'SDJK20231000007',
      receiptDate: '2024-12-17 11:13:13',
      orderNo: '超市',
      amount: '1030.00',
      payerName: '中国工商银行股份有限公司',
      payerAccount: '01190142000000000930',
      receiverName: '中国工商银行股份有限公司',
      receiverAccount: '**************',
      status: '已完成',
      operator: 'test/Admin',
      receiverCompany: '中国工商银行股份有限公司'
    },
    {
      id: 'SDJK20231000006',
      receiptDate: '2024-11-28 01:19:01',
      orderNo: '银行',
      amount: '403.57',
      payerName: '中国工商银行股份有限公司',
      payerAccount: '01190142000000000930',
      receiverName: '中国工商银行股份有限公司',
      receiverAccount: '**************',
      status: '已完成',
      operator: 'test/Admin',
      receiverCompany: '中国工商银行股份有限公司'
    },
    {
      id: 'SDJK20231000005',
      receiptDate: '2024-12-27 10:15:00',
      orderNo: '超市',
      amount: '1030.00',
      payerName: '中国工商银行股份有限公司',
      payerAccount: '01190142000000000930',
      receiverName: '中国工商银行股份有限公司',
      receiverAccount: '**************',
      status: '已完成',
      operator: 'test/Admin',
      receiverCompany: '中国工商银行股份有限公司'
    },
    {
      id: 'SDJK20231000004',
      receiptDate: '2024-12-27 10:19:32',
      orderNo: '银行',
      amount: '403.57',
      payerName: '中国工商银行股份有限公司',
      payerAccount: '01190142000000000930',
      receiverName: '中国工商银行股份有限公司',
      receiverAccount: '**************',
      status: '已完成',
      operator: 'test/Admin',
      receiverCompany: '中国工商银行股份有限公司'
    },
    {
      id: 'SDJK20231000003',
      receiptDate: '2024-12-27 10:19:32',
      orderNo: '银行',
      amount: '1029.00',
      payerName: '中国工商银行股份有限公司',
      payerAccount: '01190142000000000930',
      receiverName: '中国工商银行股份有限公司',
      receiverAccount: '**************',
      status: '已完成',
      operator: 'test/Admin',
      receiverCompany: '中国工商银行股份有限公司'
    },
    {
      id: 'SDJK20231000002',
      receiptDate: '2024-12-27 10:19:42',
      orderNo: '银行',
      amount: '403.57',
      payerName: '中国工商银行股份有限公司',
      payerAccount: '01190142000000000930',
      receiverName: '中国工商银行股份有限公司',
      receiverAccount: '**************',
      status: '已完成',
      operator: 'test/Admin',
      receiverCompany: '中国工商银行股份有限公司'
    }
  ];
});

// 选择系统收款记录
const onSelectChange = (rowKeys, rowData) => {
  selectedRowKeys.value = rowKeys;
  selectedRecord.value = rowData.length > 0 ? rowData[0] : null;
};

// 处理凭证上传
const handleUploadVoucher = (options) => {
  const { file, onProgress, onSuccess, onError } = options;
  
  // 验证文件类型
  const acceptTypes = ['.jpg', '.jpeg', '.png', '.pdf'];
  const fileType = file.name.substring(file.name.lastIndexOf('.'));
  if (!acceptTypes.includes(fileType.toLowerCase())) {
    Message.error('只支持jpg、jpeg、png、pdf格式的文件');
    onError();
    return;
  }
  
  // 验证文件大小
  if (file.size > 2 * 1024 * 1024) { // 2MB
    Message.error('文件大小不能超过2MB');
    onError();
    return;
  }
  
  // 验证文件数量
  if (formData.voucherList.length >= 5) {
    Message.error('最多只能上传5个凭证文件');
    onError();
    return;
  }
  
  // 创建一个新的凭证项
  const newVoucher = {
    uid: Date.now() + Math.random().toString(36).substring(2),
    name: file.name,
    status: 'uploading',
    percent: 0,
    file
  };
  
  // 添加到凭证列表
  formData.voucherList.push(newVoucher);
  
  // 模拟上传进度
  const simulateProgress = () => {
    const currentVoucher = formData.voucherList.find(item => item.uid === newVoucher.uid);
    if (!currentVoucher) return;
    
    if (currentVoucher.percent < 99) {
      currentVoucher.percent += 10;
      onProgress(currentVoucher.percent);
      setTimeout(simulateProgress, 300);
    } else {
      // 模拟上传完成
      currentVoucher.percent = 100;
      currentVoucher.status = 'done';
      
      // 模拟获取文件URL
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = (e) => {
        currentVoucher.url = e.target.result;
        onSuccess();
      };
      reader.onerror = () => {
        onError();
      };
    }
  };
  
  // 开始模拟上传
  setTimeout(simulateProgress, 300);
};

// 删除凭证
const removeVoucher = (index) => {
  formData.voucherList.splice(index, 1);
};

// 取消操作
const handleCancel = () => {
  formRef.value?.resetFields();
  selectedRowKeys.value = [];
  selectedRecord.value = null;
  activeTab.value = 'system';
  // 清空凭证列表
  formData.voucherList = [];
  emit('update:visible', false);
};

// 提交表单
const handleSubmit = async (done) => {
  // 根据当前激活的标签页处理不同的提交逻辑
  if (activeTab.value === 'system') {
    // 系统收款记录提交逻辑
    if (!selectedRecord.value) {
      Message.error('请选择一条系统收款记录');
      done(false);
      return;
    }
    
    try {
      // 构建提交数据
      const submitData = {
        ...selectedRecord.value,
        orderNo: props.orderData.orderNo,
        orderTime: props.orderData.orderTime,
        merchantName: props.orderData.merchantName,
        orderSource: props.orderData.orderSource,
        orderStatus: props.orderData.orderStatus,
        actualAmount: props.orderData.actualAmount,
        merchandiser: props.orderData.merchandiser,
        receiver: props.orderData.receiver,
        receiverAddress: props.orderData.receiverAddress,
        claimType: 'system' // 标记为系统关联认款
      };
      
      // 提交表单
      emit('submit', submitData);
      Message.success('系统关联认款申请提交成功');
      handleCancel();
      done();
    } catch (error) {
      Message.error('提交失败，请重试');
      done(false);
    }
  } else {
    // 手动添加收款记录提交逻辑
    const validResult = await formRef.value.validate();
    if (validResult) {
      try {
        // 构建提交数据
        const submitData = {
          ...formData,
          orderNo: props.orderData.orderNo,
          orderTime: props.orderData.orderTime,
          merchantName: props.orderData.merchantName,
          orderSource: props.orderData.orderSource,
          orderStatus: props.orderData.orderStatus,
          actualAmount: props.orderData.actualAmount,
          merchandiser: props.orderData.merchandiser,
          receiver: props.orderData.receiver,
          receiverAddress: props.orderData.receiverAddress,
          claimType: 'manual', // 标记为手动添加认款
          // 处理凭证数据，只保留必要的信息
          vouchers: formData.voucherList.map(item => ({
            name: item.name,
            url: item.url
          }))
        };
        
        // 提交表单
        emit('submit', submitData);
        Message.success('手动认款申请提交成功');
        handleCancel();
        done();
      } catch (error) {
        Message.error('提交失败，请重试');
        done(false);
      }
    } else {
      done(false);
    }
  }
};
</script>

<style scoped lang="less">
.system-records-container {
  margin-bottom: 16px;
}

.form-row {
  display: flex;
  margin: 0 -8px;
  
  .form-col {
    flex: 1;
    padding: 0 8px;
  }
}

.voucher-upload-container {
  .upload-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    border: 1px dashed var(--color-neutral-3);
    border-radius: 2px;
    cursor: pointer;
    color: var(--color-text-3);
    
    &:hover {
      color: rgb(var(--primary-6));
      border-color: rgb(var(--primary-6));
    }
  }
  
  .voucher-list {
    display: flex;
    flex-wrap: wrap;
    margin-top: 8px;
    gap: 8px;
    
    .voucher-item {
      position: relative;
      width: 80px;
      height: 80px;
      border-radius: 2px;
      overflow: hidden;
      
      .voucher-preview {
        width: 100%;
        height: 100%;
        position: relative;
      }
      
      .delete-btn {
        position: absolute;
        top: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.5);
        color: #fff;
        border-radius: 0 0 0 4px;
        padding: 2px;
        display: none;
      }
      
      &:hover .delete-btn {
        display: block;
      }
      
      .voucher-mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.6);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: #fff;
        z-index: 10;
        
        .upload-text {
          margin-top: 8px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
