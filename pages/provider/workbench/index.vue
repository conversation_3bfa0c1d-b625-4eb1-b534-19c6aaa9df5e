<template>
  <div class="ma-content-block p-4">
    <!-- <div class="mb-4">
      <div class="text-xl font-bold">工作台</div>
    </div> -->
    
    <!-- 左右布局容器 -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
      <!-- 左侧内容区域 -->
      <div class="lg:col-span-3">
        <div class="h-full flex flex-col">
          <!-- 待办事项 -->
        <a-card class="mb-6">
          <div class="mb-4">
            <h2 class="text-lg font-bold">待办事项</h2>
          </div>

          <!-- 第一行待办事项 -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <!-- 待接单 -->
        <div
          class="bg-indigo-50 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-shadow"
          @click="navigateTo('/provider/ordermanagement/orderlist?activeTab=pending')"
        >
          <div class="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mb-2">
            <icon-shopping-cart class="text-indigo-500 text-xl" />
          </div>
          <div class="text-gray-500 text-sm">待接单</div>
          <div class="text-xl font-bold">{{ todoData.pendingOrders || 0 }}</div>
        </div>

        <!-- 待发货 -->
        <div
          class="bg-blue-50 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-shadow"
          @click="navigateTo('/provider/ordermanagement/orderlist?activeTab=toShip')"
        >
          <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-2">
            <icon-send class="text-blue-500 text-xl" />
          </div>
          <div class="text-gray-500 text-sm">待发货</div>
          <div class="text-xl font-bold">{{ todoData.pendingShipments }}</div>
        </div>

        <!-- 待收货 -->
        <div
          class="bg-cyan-50 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-shadow"
          @click="navigateTo('/provider/ordermanagement/orderlist?activeTab=shipped')"
        >
          <div class="w-12 h-12 rounded-full bg-cyan-100 flex items-center justify-center mb-2">
            <icon-inbox class="text-cyan-500 text-xl" />
          </div>
          <div class="text-gray-500 text-sm">待收货</div>
          <div class="text-xl font-bold">{{ todoData.pendingDeliveries || 0 }}</div>
        </div>

        <!-- 待认款申请 -->
        <div
          class="bg-green-50 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-shadow"
          @click="navigateTo('/provider/finance/claim?activeStatus=pending')"
        >
          <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-2">
            <icon-money class="text-green-500 text-xl" />
          </div>
          <div class="text-gray-500 text-sm">待认款申请</div>
          <div class="text-xl font-bold">{{ todoData.pendingPaymentRequests }}</div>
        </div>
          </div>

          <!-- 第二行待办事项 -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- 待申请开票 -->
        <div
          class="bg-amber-50 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-shadow"
          @click="navigateTo('/provider/finance/invoice?activeStatus=pending')"
        >
          <div class="w-12 h-12 rounded-full bg-amber-100 flex items-center justify-center mb-2">
            <icon-file-add class="text-amber-500 text-xl" />
          </div>
          <div class="text-gray-500 text-sm">待申请开票</div>
          <div class="text-xl font-bold">{{ todoData.pendingInvoices }}</div>
        </div>

        <!-- 发票申请驳回 -->
        <div
          class="bg-red-50 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-shadow"
          @click="navigateTo('/provider/finance/invoice?status=rejected')"
        >
          <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mb-2">
            <icon-close-circle class="text-red-500 text-xl" />
          </div>
          <div class="text-gray-500 text-sm">发票申请驳回</div>
          <div class="text-xl font-bold">{{ todoData.rejectedInvoices }}</div>
        </div>

        <!-- 发货审核驳回 -->
        <div
          class="bg-orange-50 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-shadow"
          @click="navigateTo('/provider/order/list?status=shipment_rejected')"
        >
          <div class="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center mb-2">
            <icon-undo class="text-orange-500 text-xl" />
          </div>
          <div class="text-gray-500 text-sm">发货审核驳回</div>
          <div class="text-xl font-bold">{{ todoData.rejectedShipments }}</div>
        </div>

        <!-- 待上传签收单 -->
        <div
          class="bg-purple-50 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-shadow"
          @click="navigateTo('/provider/ordermanagement/orderlist?status=shipped')"
        >
          <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mb-2">
            <icon-upload class="text-purple-500 text-xl" />
          </div>
          <div class="text-gray-500 text-sm">待上传签收单</div>
          <div class="text-xl font-bold">{{ todoData.pendingReceipts }}</div>
        </div>
          </div>
        </a-card>

        <!-- 今日数据统计 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- 今日订单数 -->
          <a-card class="shadow-sm border border-gray-200 p-4">
            <div class="flex justify-between items-center mb-3">
              <span class="text-base text-gray-700">今日订单数</span>
              <a-tag style="background-color: #1D4ED8; color: white;">总数</a-tag>
            </div>
            <div class="text-4xl font-bold text-gray-800">
              {{ todayStats.orderCount }}
            </div>
          </a-card>

          <!-- 今日订单金额 -->
          <a-card class="shadow-sm border border-gray-200 p-4">
            <div class="flex justify-between items-center mb-3">
              <span class="text-base text-gray-700">今日订单金额</span>
              <a-tag style="background-color: #10B981; color: white;">总数</a-tag>
            </div>
            <div class="text-4xl font-bold text-gray-800">
              {{ formatAmount(todayStats.orderAmount) }}
            </div>
          </a-card>
        </div>

        <!-- 订单趋势 -->
        <a-card class="mb-6">
          <div class="mb-4">
            <h2 class="text-lg font-bold">订单趋势</h2>
          </div>
      
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 订单金额趋势图 -->
            <a-card class="border border-gray-200 shadow-sm">
              <template #title>
                <div class="flex justify-between items-center">
                  <h3 class="text-base font-medium">订单金额趋势</h3>
                  <a-radio-group
                    v-model="amountTimeRange"
                    type="button"
                    size="small"
                    @change="handleAmountTimeRangeChange"
                  >
                    <a-radio value="day">日</a-radio>
                    <a-radio value="week">周</a-radio>
                    <a-radio value="month">月</a-radio>
                    <a-radio value="year">年</a-radio>
                  </a-radio-group>
                </div>
              </template>
              <div ref="orderAmountChartRef" style="width: 100%; height: 300px;"></div>
            </a-card>
            
            <!-- 订单数量趋势图 -->
            <a-card class="border border-gray-200 shadow-sm">
              <template #title>
                <div class="flex justify-between items-center">
                  <h3 class="text-base font-medium">订单数量趋势</h3>
                  <a-radio-group
                    v-model="countTimeRange"
                    type="button"
                    size="small"
                    @change="handleCountTimeRangeChange"
                  >
                    <a-radio value="day">日</a-radio>
                    <a-radio value="week">周</a-radio>
                    <a-radio value="month">月</a-radio>
                    <a-radio value="year">年</a-radio>
                  </a-radio-group>
                </div>
              </template>
              <div ref="orderCountChartRef" style="width: 100%; height: 300px;"></div>
            </a-card>
          </div>
        </a-card>
        
        </div>

      </div>
      
      <!-- 右侧边栏 -->
      <div class="lg:col-span-1 flex flex-col" style="height: 97.5%;">
        <!-- 服务商信息卡片 -->
        <a-card class="mb-6 text-center p-6 shadow-sm border border-gray-200">
          <div class="flex flex-col items-center">
            <a-avatar :size="72" shape="circle" class="mb-4 bg-blue-500 text-white">
              <icon-home :size="36" />
            </a-avatar>
            <h3 class="text-xl font-semibold mb-2 text-gray-800">{{ providerInfo.name }}</h3>
            <p class="text-base text-gray-500 mb-6">ID: {{ providerInfo.id }}</p>
            <a-button type="outline" @click="goToProfile" class="mb-8 px-8">查看资料</a-button>
            
            <div class="flex items-center text-gray-400">
              <icon-shield class="mr-2 text-xl" />
              <span class="text-base">{{ providerInfo.rating }}</span>
            </div>
          </div>
        </a-card>

        <a-card class="flex flex-col flex-grow">
          <div class="mb-4 flex justify-between items-center">
            <h2 class="text-lg font-bold">竞价公告</h2>
            <a-button type="text" size="small" @click="navigateTo('/provider/bid/bidList')">
              查看更多
              <template #icon><icon-right /></template>
            </a-button>
          </div>
          
          <div class="announcement-list flex-1 overflow-auto">
            <a-list :data="displayAnnouncements">
              <template #item="{ item }">
                <a-list-item class="hover:bg-gray-50 rounded p-2 transition-colors">
                  <div class="w-full">
                    <div class="flex items-center gap-2" style="width: 343px;">
                      <a-tag :color="item.status === '未开始' ? 'gray' : 'red'" class="shrink-0">{{ item.status }}</a-tag>
                      <a-tooltip :content="item.title">
                        <a-link @click="viewAnnouncementDetail(item.id)" class="text-base truncate block max-w-[calc(100%-80px)]" style="justify-content: flex-start;">{{ item.title }}</a-link>
                      </a-tooltip>
                    </div>
                    <div class="flex justify-between text-gray-500 text-sm mt-2">
                      <span>{{ item.time }}</span>
                    </div>
                  </div>
                </a-list-item>
              </template>
            </a-list>
          </div>
          <div v-if="displayAnnouncements.length === 0" class="py-8 text-center text-gray-500">
            <icon-info-circle class="text-2xl mb-2" />
            <div>暂无公告</div>
          </div>
        </a-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from "vue";
import { useRouter } from "vue-router";
import * as echarts from 'echarts/core';
import { BarChart, LineChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  BarChart,
  LineChart,
  CanvasRenderer
]);

// 页面元数据
definePageMeta({
  name: "provider-workbench",
  path: "/provider/workbench",
  title:"工作台"
});

const router = useRouter();

// 计算属性：只显示最新的7条公告
const displayAnnouncements = computed(() => {
  return announcements.value.slice(0, 7);
});

const providerInfo = ref({
  name: '广东八灵科技发展有限公司', // Placeholder
  id: 'SUP1407374883560904',    // Placeholder
  // rating: '暂无评分'
});

const todayStats = ref({
  orderCount: 19,       // Placeholder
  orderAmount: 40899.18 // Placeholder
});

const goToProfile = () => {
  navigateTo('/provider/system/center'); // 使用 Nuxt 的 navigateTo
};

// 待办事项数据
const todoData = ref({
  pendingOrders: 10,
  pendingShipments: 15,
  pendingDeliveries: 7,
  pendingPaymentRequests: 8,
  pendingInvoices: 12,
  rejectedInvoices: 3,
  rejectedShipments: 2,
  pendingReceipts: 6
});

// 竞价公告数据
const announcements = ref([
  {
    id: 1,
    title: "关于货架的FPJJ202403290008反拍竞价采购项目公告",
    status: "未开始",
    time: "2024-03-29 09:07:44"
  },
  {
    id: 2,
    title: "关于货架的FPJJ202403290006反拍竞价采购项目公告",
    status: "未开始",
    time: "2024-03-29 09:00:10"
  },
  {
    id: 3,
    title: "关于洗衣机的FPJJ202403290005反拍竞价采购项目公告",
    status: "未开始",
    time: "2024-03-29 08:33:33"
  },
  {
    id: 4,
    title: "关于轮胎的FPJJ202403290002反拍竞价采购项目公告",
    status: "未开始",
    time: "2024-03-29 08:30:00"
  }
]);

// 竞价项目数据
const biddingLoading = ref(false);
const biddingProjects = ref([
  {
    id: 'BID202505001',
    name: '办公用品采购项目',
    initiator: '某政府机构',
    budget: 120000,
    endTime: '2025-06-05 18:00:00',
    status: 1, // 1: 进行中, 2: 已结束, 3: 已中标, 4: 未中标
    createdAt: '2025-05-25 10:30:00'
  },
  {
    id: 'BID202505002',
    name: '企业IT设备更新计划',
    initiator: '某企业集团',
    budget: 350000,
    endTime: '2025-06-10 23:59:59',
    status: 1,
    createdAt: '2025-05-26 14:20:00'
  },
  {
    id: 'BID202505003',
    name: '学校多媒体教室设备采购',
    initiator: '某教育局',
    budget: 280000,
    endTime: '2025-05-28 17:00:00',
    status: 2,
    createdAt: '2025-05-20 09:15:00'
  },
  {
    id: 'BID202504001',
    name: '医院信息化设备采购',
    initiator: '某医院',
    budget: 450000,
    endTime: '2025-05-15 16:00:00',
    status: 3,
    createdAt: '2025-04-30 11:20:00'
  },
  {
    id: 'BID202504002',
    name: '军队办公设备采购',
    initiator: '某军区',
    budget: 680000,
    endTime: '2025-05-10 12:00:00',
    status: 4,
    createdAt: '2025-04-25 08:30:00'
  }
]);

// 时间范围筛选
const amountTimeRange = ref('month');
const countTimeRange = ref('month');

// 处理时间范围变化
const handleAmountTimeRangeChange = (value) => {
  // 根据选择的时间范围重新获取数据
  getOrderAmountTrend(value);
};

const handleCountTimeRangeChange = (value) => {
  // 根据选择的时间范围重新获取数据
  getOrderCountTrend(value);
};

// 获取订单金额趋势数据
const getOrderAmountTrend = async (timeRange) => {
  try {
    // 模拟数据
    const mockData = {
      day: [
        { month: "6-5", value: 15600 },
        { month: "6-6", value: 12300 },
        { month: "6-7", value: 18900 },
        { month: "6-8", value: 25200 },
        { month: "6-9", value: 22400 },
        { month: "6-10", value: 28500 },
        { month: "6-11", value: 20500 }
      ],
      week: [
        { month: "5月第1周", value: 85600 },
        { month: "5月第2周", value: 92300 },
        { month: "5月第3周", value: 88900 },
        { month: "5月第4周", value: 95200 },
        { month: "6月第1周", value: 102400 },
        { month: "6月第2周", value: 98500 }
      ],
      month: [
        { month: "1月", value: 45600 },
        { month: "2月", value: 52300 },
        { month: "3月", value: 48900 },
        { month: "4月", value: 65200 },
        { month: "5月", value: 72400 },
        { month: "6月", value: 68500 }
      ],
      year: [
        { month: "2020", value: 545600 },
        { month: "2021", value: 652300 },
        { month: "2022", value: 748900 },
        { month: "2023", value: 865200 },
        { month: "2024", value: 972400 },
        { month: "2025", value: 868500 }
      ]
    };

    orderTrends.value.amountTrend = mockData[timeRange];
    initOrderAmountChart();
  } catch (error) {
    console.error('获取订单金额趋势数据失败:', error);
  }
};

// 获取订单数量趋势数据
const getOrderCountTrend = async (timeRange) => {
  try {
    // 模拟数据
    const mockData = {
      day: [
        { month: "6-5", value: 25 },
        { month: "6-6", value: 18 },
        { month: "6-7", value: 32 },
        { month: "6-8", value: 45 },
        { month: "6-9", value: 38 },
        { month: "6-10", value: 42 },
        { month: "6-11", value: 35 }
      ],
      week: [
        { month: "5月第1周", value: 125 },
        { month: "5月第2周", value: 148 },
        { month: "5月第3周", value: 132 },
        { month: "5月第4周", value: 165 },
        { month: "6月第1周", value: 182 },
        { month: "6月第2周", value: 170 }
      ],
      month: [
        { month: "1月", value: 125 },
        { month: "2月", value: 148 },
        { month: "3月", value: 132 },
        { month: "4月", value: 165 },
        { month: "5月", value: 182 },
        { month: "6月", value: 170 }
      ],
      year: [
        { month: "2020", value: 1525 },
        { month: "2021", value: 1848 },
        { month: "2022", value: 2132 },
        { month: "2023", value: 2465 },
        { month: "2024", value: 2782 },
        { month: "2025", value: 2570 }
      ]
    };

    orderTrends.value.countTrend = mockData[timeRange];
    initOrderCountChart();
  } catch (error) {
    console.error('获取订单数量趋势数据失败:', error);
  }
};

// 订单趋势数据
const orderTrends = ref({
  // 订单金额趋势数据
  amountTrend: [
    { month: "1月", value: 45600 },
    { month: "2月", value: 52300 },
    { month: "3月", value: 48900 },
    { month: "4月", value: 65200 },
    { month: "5月", value: 72400 },
    { month: "6月", value: 68500 }
  ],
  // 订单数量趋势数据
  countTrend: [
    { month: "1月", value: 125 },
    { month: "2月", value: 148 },
    { month: "3月", value: 132 },
    { month: "4月", value: 165 },
    { month: "5月", value: 182 },
    { month: "6月", value: 170 }
  ]
});

// 导航到指定页面
const navigateTo = path => {
  router.push(path);
};

// 查看竞价项目详情
const viewBiddingDetail = (id) => {
  router.push(`/provider/bidding/detail/${id}`);
};

// 提交竞价报价
const submitBidding = (id) => {
  router.push(`/provider/bidding/quote/${id}`);
};

// 查看公告详情
const viewAnnouncementDetail = (id) => {
  navigateTo(`/provider/announcement/detail?id=${id}`);
};

// 图表DOM引用
const orderAmountChartRef = ref(null);
const orderCountChartRef = ref(null);

// 图表实例
let orderAmountChart = null;
let orderCountChart = null;

// 窗口大小变化时重新调整图表大小
const resizeHandler = () => {
  orderAmountChart?.resize();
  orderCountChart?.resize();
};

// 格式化金额，超过万元时显示为"x.xx万"
const formatAmountWithUnit = (amount) => {
  if (amount >= 10000000) {
    return (amount / 10000000).toFixed(2) + '千万';
  } else if (amount >= 10000) {
    return (amount / 10000).toFixed(2) + '万';
  } else {
    return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
};

// 格式化金额显示
const formatAmount = (amount) => {
  return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// 初始化所有图表
const initAllCharts = () => {
  initOrderAmountChart();
  initOrderCountChart();
};

// 初始化订单金额趋势图
const initOrderAmountChart = () => {
  if (!orderAmountChartRef.value) return;
  
  orderAmountChart = echarts.init(orderAmountChartRef.value);
  orderAmountChart.setOption({
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: orderTrends.value.amountTrend.map(item => item.month)
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value} 元'
      }
    },
    series: [{
      data: orderTrends.value.amountTrend.map(item => item.value),
      type: 'line',
      smooth: true,
      lineStyle: {
        color: '#165DFF',
        width: 3
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(22, 93, 255, 0.3)'
            },
            {
              offset: 1,
              color: 'rgba(22, 93, 255, 0.05)'
            }
          ]
        }
      },
      symbol: 'circle',
      symbolSize: 8,
      itemStyle: {
        color: '#165DFF',
        borderWidth: 2,
        borderColor: '#FFF'
      }
    }]
  });
};

// 初始化订单数量趋势图
const initOrderCountChart = () => {
  if (!orderCountChartRef.value) return;
  
  orderCountChart = echarts.init(orderCountChartRef.value);
  orderCountChart.setOption({
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: orderTrends.value.countTrend.map(item => item.month)
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value} 单'
      }
    },
    series: [{
      data: orderTrends.value.countTrend.map(item => item.value),
      type: 'line',
      smooth: true,
      lineStyle: {
        color: '#00B42A',
        width: 3
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(0, 180, 42, 0.3)'
            },
            {
              offset: 1,
              color: 'rgba(0, 180, 42, 0.05)'
            }
          ]
        }
      },
      symbol: 'circle',
      symbolSize: 8,
      itemStyle: {
        color: '#00B42A',
        borderWidth: 2,
        borderColor: '#FFF'
      }
    }]
  });
};

// 组件挂载时初始化图表
onMounted(() => {
  // 获取初始数据
  getOrderAmountTrend(amountTimeRange.value);
  getOrderCountTrend(countTimeRange.value);

  // 延迟初始化图表，确保DOM已经渲染
  setTimeout(() => {
    try {
      initAllCharts();
      window.addEventListener('resize', resizeHandler);
    } catch (error) {
      console.error('初始化图表错误:', error);
    }
  }, 300);
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', resizeHandler);
  orderAmountChart?.dispose();
  orderCountChart?.dispose();
  
  orderAmountChart = null;
  orderCountChart = null;
});
</script>

<style lang="less" scoped>
.arco-card {
  // height: 97.5%;
  display: flex;
  flex-direction: column;
}

.arco-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 200px;
}

// 待办事项卡片样式
.rounded-lg {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
  }
}

// 公告列表样式
.announcement-list {
  .arco-list {
    height: 100%;
  }
  
  .arco-list-item {
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 8px;
    
    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
  }
}
</style>