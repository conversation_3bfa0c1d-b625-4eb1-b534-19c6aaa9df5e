<!--
 - <PERSON><PERSON><PERSON><PERSON> is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div>
    <a-modal
      :visible="visible"
      :title="title"
      @cancel="handleCancel"
      @before-ok="handleSubmit"
      :width="800"
    >
      <!-- 基本信息表单 -->
      <div class="basic-info-section mb-4">
        <a-form :model="formData" layout="horizontal" :label-col-props="{ span: 4 }" :wrapper-col-props="{ span: 20 }">
          <a-form-item field="name" label="模板名称" required>
            <a-input :model-value="formData.name" @update:model-value="value => formData.name = value" placeholder="请输入运费模板名称" />
          </a-form-item>
          <a-form-item field="chargeType" label="计费类型" required>
            <a-radio-group :model-value="formData.chargeType" @update:model-value="value => formData.chargeType = value">
              <a-radio :value="1">按件计费</a-radio>
              <a-radio :value="2">按重量计费</a-radio>
              <a-radio :value="3">按体积计费</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-form>
      </div>
      
      <!-- 配送区域表格 -->
      <div class="delivery-area-section mt-4">
        <div class="section-header flex justify-between items-center mb-2">
          <div class="text-base font-medium">配送区域</div>
          <a-button type="primary" size="small" @click="addDeliveryArea">
            <template #icon><icon-plus /></template>
            添加配送区域
          </a-button>
        </div>
        
        <a-table :data="deliveryAreas" :bordered="true" :pagination="false">
          <template #columns>
            <a-table-column title="配送区域" data-index="area">
              <template #cell="{ record }">
                {{ record.area || '全国' }}
              </template>
            </a-table-column>
            <a-table-column :title="firstItemLabel" align="center">
              <template #cell="{ record }">
                <a-input-number
                  :model-value="record.firstItem"
                  @update:model-value="value => record.firstItem = value"
                  :min="1"
                  :precision="0"
                  :step="1"
                  style="width: 100px"
                />
              </template>
            </a-table-column>
            <a-table-column title="首件运费(元)" align="center">
              <template #cell="{ record }">
                <a-input-number
                  :model-value="record.firstItemFee"
                  @update:model-value="value => record.firstItemFee = value"
                  :min="0"
                  :precision="2"
                  :step="0.1"
                  style="width: 100px"
                />
              </template>
            </a-table-column>
            <a-table-column :title="additionalItemLabel" align="center">
              <template #cell="{ record }">
                <a-input-number
                  :model-value="record.additionalItem"
                  @update:model-value="value => record.additionalItem = value"
                  :min="1"
                  :precision="0"
                  :step="1"
                  style="width: 100px"
                />
              </template>
            </a-table-column>
            <a-table-column title="续建运费(元)" align="center">
              <template #cell="{ record }">
                <a-input-number
                  :model-value="record.additionalItemFee"
                  @update:model-value="value => record.additionalItemFee = value"
                  :min="0"
                  :precision="2"
                  :step="0.1"
                  style="width: 100px"
                />
              </template>
            </a-table-column>
            <a-table-column title="操作" align="center" width="80">
              <template #cell="{ record, index }">
                <a-button
                  type="text"
                  status="danger"
                  size="small"
                  @click="removeDeliveryArea(index)"
                >
                  <template #icon><icon-delete /></template>
                  删除
                </a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </div>
      
      <!-- 配送设置 -->
      <div class="delivery-settings mt-4">
        <a-row>
          <a-col :span="12">
            <a-form-item label="指定区域配送" :label-col-props="{ span: 8 }" :wrapper-col-props="{ span: 16 }">
              <a-switch 
                :model-value="formData.useSpecificAreas" 
                @update:model-value="value => formData.useSpecificAreas = value" 
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="不配送区域" :label-col-props="{ span: 8 }" :wrapper-col-props="{ span: 16 }">
              <a-switch 
                :model-value="formData.hasNoDeliveryAreas" 
                @update:model-value="value => formData.hasNoDeliveryAreas = value" 
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </a-modal>
    
    <!-- 添加配送区域弹窗 -->
    <a-modal
      :visible="areaModalVisible"
      @update:visible="value => areaModalVisible = value"
      title="添加配送区域"
      @ok="confirmAddArea"
      @cancel="areaModalVisible = false"
      :width="500"
    >
      <a-form layout="vertical">
        <a-form-item label="选择区域" required>
          <a-cascader
            :model-value="newAreaForm.selectedAreas"
            @update:model-value="value => newAreaForm.selectedAreas = value"
            :options="areaOptions"
            placeholder="请选择配送区域"
            expand-trigger="hover"
            multiple
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from "vue";
import { Message } from "@arco-design/web-vue";
import { IconPlus, IconDelete } from '@arco-design/web-vue/es/icon';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '新增运费模板'
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'submit']);

const formData = reactive({
  name: '',
  chargeType: 1, // 默认按件计费
  useSpecificAreas: true, // 是否指定区域配送
  hasNoDeliveryAreas: false // 是否有不配送区域
});

// 配送区域数据
const deliveryAreas = ref([
  {
    id: 1,
    area: '全国',
    firstItem: 1,
    firstItemFee: 10,
    additionalItem: 1,
    additionalItemFee: 5
  }
]);

// 根据计费类型动态显示文本
const firstItemLabel = computed(() => {
  return formData.chargeType === 1 ? '首件数量(件)' : formData.chargeType === 2 ? '首重(kg)' : '首体积(m³)';
});

const additionalItemLabel = computed(() => {
  return formData.chargeType === 1 ? '续件数量(件)' : formData.chargeType === 2 ? '续重(kg)' : '续体积(m³)';
});

// 添加配送区域相关
const areaModalVisible = ref(false);
const newAreaForm = reactive({
  selectedAreas: []
});

// 模拟的地区数据
const areaOptions = [
  {
    value: '全国',
    label: '全国'
  },
  {
    value: '华东地区',
    label: '华东地区',
    children: [
      { value: '上海', label: '上海' },
      { value: '江苏', label: '江苏' },
      { value: '浙江', label: '浙江' },
      { value: '安徽', label: '安徽' },
      { value: '福建', label: '福建' }
    ]
  },
  {
    value: '华南地区',
    label: '华南地区',
    children: [
      { value: '广东', label: '广东' },
      { value: '广西', label: '广西' },
      { value: '海南', label: '海南' }
    ]
  },
  {
    value: '华北地区',
    label: '华北地区',
    children: [
      { value: '北京', label: '北京' },
      { value: '天津', label: '天津' },
      { value: '河北', label: '河北' },
      { value: '山西', label: '山西' },
      { value: '内蒙古', label: '内蒙古' }
    ]
  }
];

// 监听数据变化，初始化表单
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.assign(formData, {
      name: newVal.name || '',
      chargeType: newVal.chargeType || 1,
      useSpecificAreas: newVal.useSpecificAreas !== undefined ? newVal.useSpecificAreas : true,
      hasNoDeliveryAreas: newVal.hasNoDeliveryAreas !== undefined ? newVal.hasNoDeliveryAreas : false
    });
    
    // 如果有配送区域数据，则初始化
    if (newVal.deliveryAreas && Array.isArray(newVal.deliveryAreas)) {
      deliveryAreas.value = [...newVal.deliveryAreas];
    } else if (newVal.deliveryAreas) {
      // 如果是字符串，尝试解析为数组
      try {
        const areas = newVal.deliveryAreas.split(',').map(area => ({
          area,
          firstItem: newVal.firstItem || 1,
          firstItemFee: newVal.firstItemFee || 0,
          additionalItem: newVal.additionalItem || 1,
          additionalItemFee: newVal.additionalItemFee || 0
        }));
        deliveryAreas.value = areas;
      } catch (e) {
        deliveryAreas.value = [
          {
            area: '全国',
            firstItem: newVal.firstItem || 1,
            firstItemFee: newVal.firstItemFee || 0,
            additionalItem: newVal.additionalItem || 1,
            additionalItemFee: newVal.additionalItemFee || 0
          }
        ];
      }
    } else {
      // 默认值
      deliveryAreas.value = [
        {
          area: '全国',
          firstItem: 1,
          firstItemFee: 10,
          additionalItem: 1,
          additionalItemFee: 5
        }
      ];
    }
  }
}, { immediate: true, deep: true });

// 打开添加配送区域弹窗
const addDeliveryArea = () => {
  newAreaForm.selectedAreas = [];
  areaModalVisible.value = true;
};

// 确认添加配送区域
const confirmAddArea = () => {
  if (!newAreaForm.selectedAreas || newAreaForm.selectedAreas.length === 0) {
    Message.warning('请选择配送区域');
    return;
  }
  
  // 获取选中的最后一级区域
  const selectedAreas = Array.isArray(newAreaForm.selectedAreas[0]) 
    ? newAreaForm.selectedAreas 
    : [newAreaForm.selectedAreas];
  
  selectedAreas.forEach(areaPath => {
    const areaName = areaPath[areaPath.length - 1];
    // 检查是否已存在
    const exists = deliveryAreas.value.some(item => item.area === areaName);
    if (!exists) {
      deliveryAreas.value.push({
        area: areaName,
        firstItem: 1,
        firstItemFee: 10,
        additionalItem: 1,
        additionalItemFee: 5
      });
    }
  });
  
  areaModalVisible.value = false;
};

// 删除配送区域
const removeDeliveryArea = (index) => {
  deliveryAreas.value.splice(index, 1);
};

// 取消处理
const handleCancel = () => {
  emit('update:visible', false);
};

// 提交处理
const handleSubmit = async (done) => {
  // 表单验证
  if (!formData.name) {
    Message.warning('请输入运费模板名称');
    return false;
  }

  if (deliveryAreas.value.length === 0) {
    Message.warning('请至少添加一个配送区域');
    return false;
  }

  const dataToSubmit = { 
    ...formData,
    deliveryAreas: deliveryAreas.value
  };
  
  try {
    emit('submit', dataToSubmit);
    done(true);
  } catch (error) {
    console.error('提交失败:', error);
    Message.error('操作失败，请重试');
    done(false);
  }
};
</script>

<style scoped>
.basic-info-section {
  margin-bottom: 16px;
}

.delivery-area-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.section-header {
  margin-bottom: 12px;
}

.delivery-settings {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}
</style>
