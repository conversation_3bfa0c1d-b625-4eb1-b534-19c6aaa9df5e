<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <!-- 商品搜索表单 -->
  <a-card title="商品搜索" class="mb-4">
    <div class="flex items-center mb-4">
      <a-input v-model="searchForm.keyword" placeholder="请输入商品名称或关键词" class="mr-2" style="width: 300px;" />
      <a-button type="primary" @click="searchProducts">
        <template #icon><icon-search /></template>
        搜索
      </a-button>
    </div>
    
    <div v-if="searchResults.length > 0" class="mt-4">
      <a-table 
        :columns="searchResultColumns" 
        :data="searchResults" 
        :pagination="{ pageSize: 5 }"
        row-key="id"
        @row-click="selectProduct"
      >
        <template #name="{ record }">
          <div class="flex items-center">
            <a-avatar shape="square" :size="40" :image-url="record.image" class="mr-2" />
            <span>{{ record.name }}</span>
          </div>
        </template>
        <template #action="{ record }">
          <a-button type="primary" size="small" @click="selectProduct(record)">选择</a-button>
        </template>
      </a-table>
    </div>
    
    <a-empty v-else-if="hasSearched" description="未找到相关商品" />
    
    <div v-if="selectedProduct" class="mt-4 p-4 border rounded bg-gray-50">
      <div class="text-lg font-semibold mb-2">已选商品</div>
      <div class="flex items-center">
        <a-avatar shape="square" :size="60" :image-url="selectedProduct.image" class="mr-4" />
        <div>
          <div class="text-base font-medium">{{ selectedProduct.name }}</div>
          <div class="text-gray-500">分类：{{ selectedProduct.categoryName }}</div>
          <div class="text-gray-500">品牌：{{ selectedProduct.brandName }}</div>
        </div>
      </div>
      <div class="mt-4 flex justify-end">
        <a-button type="primary" @click="useSelectedProduct">使用此商品信息</a-button>
      </div>
    </div>
  </a-card>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconSearch } from '@arco-design/web-vue/es/icon';

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:modelValue', 'use-product', 'search-products']);

// 搜索相关
const searchForm = reactive({
  keyword: ''
});
const searchResults = ref([]);
const hasSearched = ref(false);
const selectedProduct = ref(null);

// 搜索结果表格列配置
const searchResultColumns = [
  {
    title: '商品名称',
    dataIndex: 'name',
    slotName: 'name'
  },
  {
    title: '分类',
    dataIndex: 'categoryName'
  },
  {
    title: '品牌',
    dataIndex: 'brandName'
  },
  {
    title: '价格',
    dataIndex: 'price',
    render: ({ record }) => `¥${record.price}`
  },
  {
    title: '操作',
    slotName: 'action',
    width: 100
  }
];

// 搜索商品
const searchProducts = async () => {
  if (!searchForm.keyword.trim()) {
    Message.warning('请输入搜索关键词');
    return;
  }
  
  try {
    // 这里应该调用实际的API接口，这里使用模拟数据
    // const res = await api.searchProducts(searchForm.keyword);
    // searchResults.value = res.data;
    
    // 模拟数据
    searchResults.value = [
      {
        id: 1,
        name: '华为 Mate 60 Pro',
        image: 'https://img.arco.design/avatar.jpg',
        categoryName: '手机数码',
        categoryId: '1',
        brandName: '华为',
        brandId: '1',
        price: 6999,
        unit: '台',
        description: '华为最新旗舰手机'
      },
      {
        id: 2,
        name: '小米 14 Ultra',
        image: 'https://img.arco.design/avatar.jpg',
        categoryName: '手机数码',
        categoryId: '1',
        brandName: '小米',
        brandId: '2',
        price: 5999,
        unit: '台',
        description: '小米影像旗舰'
      },
      {
        id: 3,
        name: 'iPhone 15 Pro Max',
        image: 'https://img.arco.design/avatar.jpg',
        categoryName: '手机数码',
        categoryId: '1',
        brandName: '苹果',
        brandId: '3',
        price: 8999,
        unit: '台',
        description: '苹果最新旗舰手机'
      }
    ];
    
    hasSearched.value = true;
    emit('search-products', searchForm.keyword);
  } catch (error) {
    console.error('搜索商品失败:', error);
    Message.error('搜索商品失败，请重试');
  }
};

// 选择商品
const selectProduct = (product) => {
  selectedProduct.value = product;
};

// 使用选中的商品信息
const useSelectedProduct = () => {
  if (!selectedProduct.value) {
    Message.warning('请先选择一个商品');
    return;
  }
  
  emit('use-product', selectedProduct.value);
};

// 暴露方法给父组件
defineExpose({
  searchProducts,
  selectProduct,
  useSelectedProduct,
  searchForm,
  searchResults,
  selectedProduct,
  hasSearched
});
</script>
