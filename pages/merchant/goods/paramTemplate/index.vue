<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef" :data="tableData">
      <!-- 操作列 -->
      <template #operationCell="{ record }">
        <div>
          <a-button type="text" size="small" @click="handleEdit(record)">
            <template #icon><icon-edit /></template>
            编辑
          </a-button>
          <a-button type="text" size="small" @click="handleParams(record)">
            <template #icon><icon-list /></template>
            参数项目
          </a-button>
          <a-popconfirm content="确定要删除该属性吗?" position="bottom" @ok="handleDelete(record.id)">
            <a-button type="text" status="danger" size="small">
              <template #icon><icon-delete /></template>
              删除
            </a-button>
          </a-popconfirm>
        </div>
      </template>

      <!-- 表格顶部按钮 -->
      <template #tableButtons>
        <a-button type="primary" @click="handleAdd">
          <template #icon><icon-plus /></template>新增
        </a-button>
      </template>
    </ma-crud>

    <!-- 参数项目组件 -->
    <param-item-list v-model:visible="paramsVisible" :record="currentRecord" @save="handleParamsSave" />

    <!-- 自定义弹窗组件 -->
    <a-modal v-model:visible="modalVisible" :title="modalTitle" @cancel="modalVisible = false" @before-ok="handleSubmit"
      :width="600">
      <ma-form ref="formRef" v-model="formData" :columns="formColumns" :options="{ showButtons: false }" />
    </a-modal>

    <!-- 参数项编辑组件 -->
    <param-item-edit v-model:visible="paramEditVisible" :title="paramEditTitle" :data="currentParamItem"
      @submit="handleParamSubmit" />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from "vue";
import { Message } from "@arco-design/web-vue";
import MaForm from '~/components/base/ma-form/index.vue'
import ParamItemEdit from './components/ParamItemEdit.vue';
import ParamItemList from './components/ParamItemList.vue';

definePageMeta({
  name: "merchant-paramTemplate",
  path: "/merchant/goods/paramTemplate",
})

// 分类数据
const categories = ref([
  { id: 1, name: "手机数码" },
  { id: 2, name: "电脑办公" },
  { id: 3, name: "家用电器" },
  { id: 4, name: "服装鞋包" },
  { id: 5, name: "食品生鲜" }
]);

const crudRef = ref();
const formRef = ref();
const modalVisible = ref(false);
const modalTitle = ref('新增属性');
const currentId = ref(null);
const paramsVisible = ref(false);
const currentRecord = ref({});
const paramItems = ref([]);
const paramEditVisible = ref(false);
const paramEditTitle = ref('编辑参数项');
const currentParamItem = ref({});
const paramsCrudRef = ref();

const formData = reactive({
  name: '',
  category_id: null,
  sort: 0
});

const formColumns = reactive([
  {
    dataIndex: 'name',
    title: '模板名称',
    labelWidth: '80px',
    rules: [{ required: true, message: '模板名称必填' }],
    formType: 'input',
    placeholder: '请输入模板名称'
  },
  {
    dataIndex: 'category_id',
    title: '关联分类',
    labelWidth: '80px',
    rules: [{ required: true, message: '关联分类必选' }],
    formType: 'select',
    placeholder: '请选择关联分类',
    dict: { data: categories } // 使用 categories 作为选项
  },
  {
    dataIndex: 'sort',
    title: '排序',
    labelWidth: '80px',
    formType: 'input-number',
    defaultValue: 0,
    min: 0,
    max: 9999
  }
])

const tableData = ref([
  {
    id: 1,
    name: "手机参数",
    category_id: 1,
    category_name: "手机数码",
    params_count: 8,
    sort: 0,
    created_at: "2025-04-01 10:30:04"
  },
  {
    id: 2,
    name: "电脑参数",
    category_id: 2,
    category_name: "电脑办公",
    params_count: 12,
    sort: 0,
    created_at: "2025-04-02 14:20:15"
  },
  {
    id: 3,
    name: "冰箱参数",
    category_id: 3,
    category_name: "家用电器",
    params_count: 6,
    sort: 0,
    created_at: "2025-04-03 09:45:30"
  }
]);

// 打开新增弹窗
const handleAdd = () => {
  modalTitle.value = '新增属性';
  Object.assign(formData, {
    name: '',
    category_id: null,
    sort: 0
  });
  currentId.value = null;
  modalVisible.value = true;
};

// 打开编辑弹窗
const handleEdit = (record) => {
  modalTitle.value = '编辑属性';
  Object.assign(formData, {
    name: record.name,
    category_id: record.category_id,
    sort: record.sort
  });
  currentId.value = record.id;
  modalVisible.value = true;
};

// 处理删除
const handleDelete = (id) => {
  tableData.value = tableData.value.filter(item => item.id !== id);
  Message.success('删除成功');
};

// 处理表单提交
const handleSubmit = async (done) => {
  const valid = await formRef.value.validate();
  if (valid) return false; // 验证失败则阻止关闭弹窗

  const dataToSubmit = { ...formData };

  let response;
  if (currentId.value === null) { // 新增
    // TODO: 调用新增属性API
    response = { success: true, message: '属性添加成功 (模拟)' }; // 模拟成功
    tableData.value.push({
      id: Date.now(),
      ...dataToSubmit,
      category_name: categories.value.find(c => c.id === dataToSubmit.category_id)?.name,
      params_count: 0,
      created_at: new Date().toLocaleString()
    });
    console.log("新增属性", dataToSubmit);
  } else {
    // TODO: 调用更新属性 API
    response = { success: true, message: '属性更新成功 (模拟)' }; // 模拟成功
    const index = tableData.value.findIndex(item => item.id === currentId.value);
    if (index !== -1) {
      const params_count = tableData.value[index].params_count;
      Object.assign(tableData.value[index], dataToSubmit, {
        category_name: categories.value.find(c => c.id === dataToSubmit.category_id)?.name,
        params_count
      });
    }
    console.log("更新属性", currentId.value, dataToSubmit);
  }

  if (response.success) {
    Message.success(response.message);
    done();
  } else {
    Message.error(response.message);
    done(false);
  }
};

// 打开参数项目弹窗
const handleParams = (record) => {
  currentRecord.value = record;
  paramsVisible.value = true;
};

// 处理参数项目保存
const handleParamsSave = (data) => {
  console.log('保存参数项目', data);
  // 更新参数项数量
  const index = tableData.value.findIndex(item => item.id === data.id);
  if (index !== -1) {
    tableData.value[index].params_count = data.params_count;
  }
  Message.success('参数项目保存成功');
};

const crud = reactive({
  api: () => Promise.resolve({ rows: tableData.value }),
  showIndex: true,
  pageLayout: 'fixed',
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 250,
  add: { show: false },
  edit: { show: false },
  delete: { show: false }
});

const columns = reactive([
  { title: 'ID', dataIndex: 'id', width: 80 },
  {
    title: '模板名称',
    dataIndex: 'name',
    search: true,
    width: 150
  },
  {
    title: '关联分类',
    dataIndex: 'category_name',
    search: true,
    width: 120
  },
  {
    title: '参数项数量',
    dataIndex: 'params_count',
    width: 120
  },
  {
    title: '排序',
    dataIndex: 'sort',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    search: true,
    width: 180
  },
]);

// 参数项表格配置
const paramsCrud = reactive({
  showIndex: true,
  pageLayout: 'fixed',
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 150,
  add: { show: false },
  edit: { show: false },
  delete: { show: false },
  pagination: false // 禁用分页
});

// 参数项表格列定义
const paramsColumns = reactive([
  {
    title: '参数名称',
    dataIndex: 'name',
    width: 120,
    search: true
  },
  {
    title: '参数类型',
    dataIndex: 'type',
    width: 100,
    dict: {
      data: [
        { label: '文本', value: 'text' },
        { label: '数字', value: 'number' },
        { label: '单选', value: 'radio' },
        { label: '多选', value: 'checkbox' },
        { label: '下拉选择', value: 'select' }
      ]
    }
  },
  {
    title: '可选值/单位',
    dataIndex: 'options',
    width: 150
  },
  {
    title: '必填',
    dataIndex: 'required',
    width: 80,
    slotName: 'required'
  },
  {
    title: '可筛选',
    dataIndex: 'filterable',
    width: 80,
    slotName: 'filterable'
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    slotName: 'status'
  }
]);
</script>

<script>
export default { name: "master-paramTemplate", };
</script>

<style scoped>
.params-container {
  padding: 0 16px;
}
</style>