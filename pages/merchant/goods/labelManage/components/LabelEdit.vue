<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <a-modal
    :visible="visible"
    :title="title"
    @cancel="handleCancel"
    @before-ok="handleSubmit"
    :width="600"
  >
    <ma-form ref="formRef" v-model="formData" :columns="formColumns" :options="{ showButtons: false }"/>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from "vue";
import { Message } from "@arco-design/web-vue";
import MaForm from '~/components/base/ma-form/index.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '新增标签'
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'submit']);

const formRef = ref();
const formData = reactive({
  name: '',
  image: '',
  description: '',
  sort: 0,
  status: 1
});

// 表单列配置
const formColumns = reactive([
  {
    dataIndex: 'name',
    title: '标签名称',
    labelWidth: '100px',
    rules: [{ required: true, message: '标签名称必填' }],
    formType: 'input',
    placeholder: '请输入标签名称'
  },
  {
    dataIndex: 'image',
    title: '标签图片',
    labelWidth: '100px',
    rules: [{ required: true, message: '标签图片必传' }],
    formType: 'upload',
    placeholder: '请上传标签图片',
    uploadType: 'image',
    limit: 1,
    multiple: false
  },
  {
    dataIndex: 'description',
    title: '标签说明',
    labelWidth: '100px',
    formType: 'textarea',
    placeholder: '请输入标签说明'
  },
  {
    dataIndex: 'sort',
    title: '排序',
    labelWidth: '100px',
    formType: 'inputNumber',
    placeholder: '请输入排序值',
    min: 0,
    max: 9999
  }
]);

// 监听数据变化，初始化表单
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.assign(formData, {
      name: newVal.name || '',
      image: newVal.image || '',
      description: newVal.description || '',
      sort: newVal.sort || 0,
      status: newVal.status || 1
    });
  }
}, { immediate: true, deep: true });

// 取消处理
const handleCancel = () => {
  emit('update:visible', false);
};

// 提交处理
const handleSubmit = async (done) => {
  const valid = await formRef.value.validate();
  if (valid) return false; // 验证失败则阻止关闭弹窗

  const dataToSubmit = { ...formData };
  
  try {
    emit('submit', dataToSubmit);
    done(true);
  } catch (error) {
    console.error('提交失败:', error);
    Message.error('操作失败，请重试');
    done(false);
  }
};
</script>

<style scoped>
/* 可以添加自定义样式 */
</style>
